<a name="module_miot/system"></a>

## miot/system
手机音量

**Export**: public  
**Doc_name**: 手机音量模块  
**Doc_index**: 13  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
import {VolumeChangeEvent} from "miot"
...
System.volume.startVolume().then((res) => {
        alert(`getStartVolume: ${ JSON.stringify(res) }`);
    });
...
 System.volume.stopVolume().then(() => {})
...
```

* [miot/system](#module_miot/system)
    * _static_
        * [.VolumeChangeEvent](#module_miot/system.VolumeChangeEvent) ⇒ <code>number</code>
    * _inner_
        * [~IVolume](#module_miot/system..IVolume)
            * [.getVolumeInfo()](#module_miot/system..IVolume+getVolumeInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
            * [.startVolume(hideSystemSlider)](#module_miot/system..IVolume+startVolume) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
            * [.stopVolume()](#module_miot/system..IVolume+stopVolume) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system.VolumeChangeEvent"></a>

### miot/system.VolumeChangeEvent ⇒ <code>number</code>
监听音量变化事件

**Kind**: static constant of [<code>miot/system</code>](#module_miot/system)  
**Returns**: <code>number</code> - volume 当前音量  
**Since**: 10045  
**Example**  
```js
VolumeChangeEvent.onVolumeChange.addListener((result) => {
      console.log(result);
    });
```

* * *

<a name="module_miot/system..IVolume"></a>

### miot/system~IVolume
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* [~IVolume](#module_miot/system..IVolume)
    * [.getVolumeInfo()](#module_miot/system..IVolume+getVolumeInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
    * [.startVolume(hideSystemSlider)](#module_miot/system..IVolume+startVolume) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.stopVolume()](#module_miot/system..IVolume+stopVolume) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system..IVolume+getVolumeInfo"></a>

#### iVolume.getVolumeInfo() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
获取音量信息

**Kind**: instance method of [<code>IVolume</code>](#module_miot/system..IVolume)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - result:
成功时：{"code":0, "data":xxx},data.volume:number,设备当前音量，取值在0.0-1.0之间
失败时：{"code":-1, "message":"xxx" }；  
**Since**: 10045  
**Example**  
```js
System.volume.getVolumeInfo().then((res) => {
    if (res && res.data) {
      alert(`getSystemVolumeInfo success,volume:${ res.data.volume }`);
    } else {
      alert(`getSystemVolumeInfo fail,${ JSON.stringify(res) }`);
    }
  }).catch((error) => {
    alert(`getSystemVolumeInfo fail,${ JSON.stringify(error) }`);
  });
  });
```

* * *

<a name="module_miot/system..IVolume+startVolume"></a>

#### iVolume.startVolume(hideSystemSlider) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
开始监听音量变化

**Kind**: instance method of [<code>IVolume</code>](#module_miot/system..IVolume)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10045  

| Param | Type | Description |
| --- | --- | --- |
| hideSystemSlider | <code>Object</code> | 是否隐藏系统的音量进度条，默认不隐藏 |

**Example**  
```js
System.volume.startVolume({hideSystemSlider:true}).then((res) => {
    alert(`getStartVolume: ${ JSON.stringify(res) }`);
  }).catch((error) => {
    alert(`getStartVolume: ${ JSON.stringify(error) }`);
  });
```

* * *

<a name="module_miot/system..IVolume+stopVolume"></a>

#### iVolume.stopVolume() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
停止监听量变化

**Kind**: instance method of [<code>IVolume</code>](#module_miot/system..IVolume)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10045  
**Example**  
```js
System.volume.stopVolume().then((res) => {
    alert(`getStopVolume: ${ JSON.stringify(res) }`);
  }).catch((error) => {
    alert(`getStopVolume: ${ JSON.stringify(error) }`);
  });
```

* * *

