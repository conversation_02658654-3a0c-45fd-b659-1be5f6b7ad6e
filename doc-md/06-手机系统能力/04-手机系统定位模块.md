<a name="module_miot/system"></a>

## miot/system
扩展程序运行时手机系统提供的定位功能，主要包括定位权限的获取，获取当前定位（不同精度），位置信息更新事件等方法

**Export**: public  
**Doc_name**: 手机系统定位模块  
**Doc_index**: 4  
**Doc_directory**: system  
**Example**  
```js
import {System} from 'miot'

 System.location.getLocation({accuracy:1}).then(res=>{
     console.log(res)
 })
```

* * *

<a name="module_miot/system..HostEvent"></a>

### miot/system~HostEvent : <code>object</code>
Host事件集合

**Kind**: inner namespace of [<code>miot/system</code>](#module_miot/system)  
**Example**  
```js
import { HostEvent } from 'miot/host';
   const subscription = HostEvent.cellPhoneNetworkStateChanged.addListener(
      (event)=>{
         ...
      }
    )
   ...
   subscription.remove()
   ...
```

* * *

