<a name="module_miot/system"></a>

## miot/system
手机的电量

**Export**: public  
**Doc_name**: 手机电量模块  
**Doc_index**: 2  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
...
System.battery.getBatteryInfo().then(res => {//return result})
...
```

* [miot/system](#module_miot/system)
    * [~IBattery](#module_miot/system..IBattery)
        * [.getBatteryInfo()](#module_miot/system..IBattery+getBatteryInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>


* * *

<a name="module_miot/system..IBattery"></a>

### miot/system~IBattery
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* * *

<a name="module_miot/system..IBattery+getBatteryInfo"></a>

#### iBattery.getBatteryInfo() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
获取设备电量信息

**Kind**: instance method of [<code>IBattery</code>](#module_miot/system..IBattery)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - res:
成功时：{"code":0, "data":xxx},data.level:string,设备电量,取值范围0-100,data.isCharging:boolean,是否正在充电中
失败时：{"code":-1, "message":"xxx" }；  
**Since**: 10043  
**Example**  
```js
System.battery.getBatteryInfo().then((res) => {
    if (res && res.data) {
      alert(`getBatteryInfo success,level:${ res.data.level },isCharging:${ res.data.isCharging }`);
    } else {
      alert(`getBatteryInfo fail,${ JSON.stringify(res) }`);
    }
  }).catch((error) => {
    alert(`getBatteryInfo fail,${ JSON.stringify(error) }`);
  });
```

* * *

