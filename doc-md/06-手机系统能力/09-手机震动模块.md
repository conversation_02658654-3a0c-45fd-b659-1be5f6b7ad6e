<a name="module_miot/system"></a>

## miot/system
手机的长震与短震

**Export**: public  
**Doc_name**: 手机震动模块  
**Doc_index**: 9  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
...
System.vibrate.vibrateShort();
...
```

* [miot/system](#module_miot/system)
    * [~IVibrate](#module_miot/system..IVibrate)
        * [.vibrateShort()](#module_miot/system..IVibrate+vibrateShort)
        * [.vibrateLong()](#module_miot/system..IVibrate+vibrateLong)


* * *

<a name="module_miot/system..IVibrate"></a>

### miot/system~IVibrate
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* [~IVibrate](#module_miot/system..IVibrate)
    * [.vibrateShort()](#module_miot/system..IVibrate+vibrateShort)
    * [.vibrateLong()](#module_miot/system..IVibrate+vibrateLong)


* * *

<a name="module_miot/system..IVibrate+vibrateShort"></a>

#### iVibrate.vibrateShort()
使手机发生较短时间的振动（15 ms）。仅在 iPhone 7 / 7 Plus 以上及 Android 机型生效

**Kind**: instance method of [<code>IVibrate</code>](#module_miot/system..IVibrate)  
**Since**: 10043  
**Example**  
```js
System.vibrate.vibrateShort();
```

* * *

<a name="module_miot/system..IVibrate+vibrateLong"></a>

#### iVibrate.vibrateLong()
使手机发生较长时间的振动（400 ms)

**Kind**: instance method of [<code>IVibrate</code>](#module_miot/system..IVibrate)  
**Since**: 10043  
**Example**  
```js
System.vibrate.vibrateLong();
```

* * *

