<a name="module_miot/system"></a>

## miot/system
手机的罗盘

**Export**: public  
**Doc_name**: 手机罗盘模块  
**Doc_index**: 5  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
import {CompassChangeEvent} from "miot"
...
System.compass.startCompass(//interval).then(() => {
    alert(`startCompass: ${ JSON.stringify(res) }`);
   })
...
   System.compass.stopCompass().then(() => {})
...
```

* [miot/system](#module_miot/system)
    * _static_
        * [.CompassChangeEvent](#module_miot/system.CompassChangeEvent) ⇒ <code>number</code>
    * _inner_
        * [~ICompass](#module_miot/system..ICompass)
            * [.startCompass(interval)](#module_miot/system..ICompass+startCompass) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
            * [.stopCompass()](#module_miot/system..ICompass+stopCompass) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system.CompassChangeEvent"></a>

### miot/system.CompassChangeEvent ⇒ <code>number</code>
监听罗盘数据变化事件。频率：5 次/秒，接口调用后会自动开始监听，可使用 wx.stopCompass 停止监听。

**Kind**: static constant of [<code>miot/system</code>](#module_miot/system)  
**Returns**: <code>number</code> - direction 面对的方向度数  
**Since**: 10043  
**Example**  
```js
CompassChangeEvent.onCompassChange.addListener((result) => {
      console.log(result);
    });
```

* * *

<a name="module_miot/system..ICompass"></a>

### miot/system~ICompass
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* [~ICompass](#module_miot/system..ICompass)
    * [.startCompass(interval)](#module_miot/system..ICompass+startCompass) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.stopCompass()](#module_miot/system..ICompass+stopCompass) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system..ICompass+startCompass"></a>

#### iCompass.startCompass(interval) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
开始监听罗盘数据

**Kind**: instance method of [<code>ICompass</code>](#module_miot/system..ICompass)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10043  

| Param | Type | Description |
| --- | --- | --- |
| interval | <code>string</code> | 监听罗盘数据回调函数的执行频率。其合法值如下： game 适用于更新游戏的回调频率，在 20ms/次 左右； ui 适用于更新 UI 的回调频率，在 60ms/次 左右； normal 普通的回调频率，在 200ms/次 左右。 |

**Example**  
```js
System.compass.startCompass(interval.c).then((res) => {
        alert(`startCompass: ${ JSON.stringify(res) }`);
      }).catch((error) => {
        console.log(error);
      });
```

* * *

<a name="module_miot/system..ICompass+stopCompass"></a>

#### iCompass.stopCompass() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
停止监听罗盘数据

**Kind**: instance method of [<code>ICompass</code>](#module_miot/system..ICompass)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10043  
**Example**  
```js
System.compass.stopCompass().then((res) => {
          alert(`stopCompass: ${ JSON.stringify(res) }`);
        }).catch((error) => {
          console.log(error);
        });
      }
```

* * *

