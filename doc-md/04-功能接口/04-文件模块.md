<a name="module_miot/host/file"></a>

## miot/host/file
本地文件访问及处理服务
注意插件文件处理皆处于插件沙盒目录下

**Export**: public  
**Doc_name**: 文件模块  
**Doc_index**: 4  
**Doc_directory**: host  
**Example**  
```js
//给定文件名后下载或者截图后被放到本地目录里, 在<Image/>等标签需要引用时, 使用{local:"filename"}的方式引入
const myfile = "testpicture.png"
Host.file.downloadFile("http://..../...png", myfile)
.then(res=>{
    const myimg = <Image source={{local:myfile}} ... />
    ...
})
.catch(err=>{...})

...
const myshotfile = "testshot.png"
Host.file.screenShot(myshotfile)
.then(res=>{
   const myshotpic = <Image source={{local:myshotfile}} ... />
   ...
});
...
```

* [miot/host/file](#module_miot/host/file)
    * [~IFile](#module_miot/host/file..IFile)
        * [.storageBasePath](#module_miot/host/file..IFile+storageBasePath)
        * [.readFileList()](#module_miot/host/file..IFile+readFileList) ⇒ <code>Promise</code>
        * [.isFileExists(fileName)](#module_miot/host/file..IFile+isFileExists) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
        * [.readFile(fileName, [opt])](#module_miot/host/file..IFile+readFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;String&gt;</code>
        * [.readFileToHexString(fileName)](#module_miot/host/file..IFile+readFileToHexString) ⇒ <code>Promise</code>
        * [.readFileToBase64(fileName)](#module_miot/host/file..IFile+readFileToBase64) ⇒ <code>Promise</code>
        * [.readFileSegmentToBase64(fileName, off, len)](#module_miot/host/file..IFile+readFileSegmentToBase64) ⇒ <code>Promise</code>
        * [.writeFile(fileName, utf8Content)](#module_miot/host/file..IFile+writeFile) ⇒ <code>Promise</code>
        * [.saveFileToNotesAppOnMIUI(fileName)](#module_miot/host/file..IFile+saveFileToNotesAppOnMIUI)
        * [.writeFileThroughBase64(fileName, fileContent)](#module_miot/host/file..IFile+writeFileThroughBase64) ⇒ <code>Promise</code>
        * [.appendFile(fileName, utf8Content)](#module_miot/host/file..IFile+appendFile) ⇒ <code>Promise</code>
        * [.appendFileThroughBase64(fileName, fileContent)](#module_miot/host/file..IFile+appendFileThroughBase64) ⇒ <code>Promise</code>
        * [.deleteFile(fileName)](#module_miot/host/file..IFile+deleteFile) ⇒ <code>Promise</code>
        * [.generateObjNameAndUrlForFDSUpload(did, suffix)](#module_miot/host/file..IFile+generateObjNameAndUrlForFDSUpload)
        * [.generateObjNameAndUrlForFDSUploadV3(did, suffix)](#module_miot/host/file..IFile+generateObjNameAndUrlForFDSUploadV3)
        * [.generateObjNameAndUrlForLogFileFDSUpload(did, suffix)](#module_miot/host/file..IFile+generateObjNameAndUrlForLogFileFDSUpload)
        * [.getFDSFileInfoWithObjName(obj_name)](#module_miot/host/file..IFile+getFDSFileInfoWithObjName)
        * [.getFDSFileInfoWithObjNameV3(obj_name)](#module_miot/host/file..IFile+getFDSFileInfoWithObjNameV3)
        * [.uploadFile(params)](#module_miot/host/file..IFile+uploadFile) ⇒ <code>Promise</code>
        * [.uploadFileToFDS(params)](#module_miot/host/file..IFile+uploadFileToFDS) ⇒ <code>Promise</code>
        * [.downloadFile(url, fileName, params)](#module_miot/host/file..IFile+downloadFile) ⇒ <code>Promise</code>
        * [.registerFontEnable(param)](#module_miot/host/file..IFile+registerFontEnable)
        * [.downloadFontFile(param)](#module_miot/host/file..IFile+downloadFontFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.readFontFileList(param)](#module_miot/host/file..IFile+readFontFileList) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.deleteFontFile(param)](#module_miot/host/file..IFile+deleteFontFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.cancelDownloadFile(taskID)](#module_miot/host/file..IFile+cancelDownloadFile) ⇒ <code>Promise</code>
        * [.dataLengthOfBase64Data(base64Data)](#module_miot/host/file..IFile+dataLengthOfBase64Data) ⇒ <code>Promise</code>
        * [.subBase64DataOfBase64Data(base64Data, loc, len)](#module_miot/host/file..IFile+subBase64DataOfBase64Data) ⇒ <code>Promise</code>
        * [.unzipFile(fileName)](#module_miot/host/file..IFile+unzipFile) ⇒ <code>Promise</code>
        * [.ungzipFileToString(params)](#module_miot/host/file..IFile+ungzipFileToString) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.ungzFile(fileName)](#module_miot/host/file..IFile+ungzFile) ⇒ <code>Promise</code>
        * [.ungzYunMiFile(fileName)](#module_miot/host/file..IFile+ungzYunMiFile) ⇒ <code>Promise</code>
        * [.saveImageToPhotosAlbum(fileName)](#module_miot/host/file..IFile+saveImageToPhotosAlbum) ⇒ <code>Promise</code>
        * [.saveFileToPhotosAlbum(fileName)](#module_miot/host/file..IFile+saveFileToPhotosAlbum) ⇒ <code>Promise</code>
        * [.saveImageToPhotosDidAlbum(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveImageToPhotosDidAlbum) ⇒ <code>Promise</code>
        * [.saveImageToPhotosDidAlbumV2(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveImageToPhotosDidAlbumV2) ⇒ <code>Promise</code>
        * [.saveVideoToPhotosDidAlbum(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveVideoToPhotosDidAlbum) ⇒ <code>Promise</code>
        * [.saveVideoToPhotosDidAlbumV2(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveVideoToPhotosDidAlbumV2) ⇒ <code>Promise</code>
        * [.mergeVideos(firstVideoPath, secondVideoPath, deviceID)](#module_miot/host/file..IFile+mergeVideos) ⇒ <code>Promise</code>
        * [.mergeImages(firstImagePath, secondImagePath, deviceID)](#module_miot/host/file..IFile+mergeImages) ⇒ <code>Promise</code>
        * [.mergeVideosV2(firstVideoPath, secondVideoPath, params, deviceID, callbackEvent)](#module_miot/host/file..IFile+mergeVideosV2) ⇒ <code>Promise</code>
        * [.mergeImagesV2(firstImagePath, secondImagePath, params, deviceID)](#module_miot/host/file..IFile+mergeImagesV2) ⇒ <code>Promise</code>
        * [.fetchLocalVideoFilePathFromDidAlbumByUrl(url, customDirName, deviceID)](#module_miot/host/file..IFile+fetchLocalVideoFilePathFromDidAlbumByUrl) ⇒ <code>Promise</code>
        * [.getAllSourceFromPhotosDidAlbum(customDirName, deviceID)](#module_miot/host/file..IFile+getAllSourceFromPhotosDidAlbum) ⇒ <code>Promise</code>
        * [.getAlbums()](#module_miot/host/file..IFile+getAlbums) ⇒ <code>Promise</code>
        * [.getAssets(albumID)](#module_miot/host/file..IFile+getAssets) ⇒ <code>Promise</code>
        * [.deleteAssetsFromAlbumByUrls(urls)](#module_miot/host/file..IFile+deleteAssetsFromAlbumByUrls) ⇒ <code>Promise</code>
        * [.screenShot(imageName)](#module_miot/host/file..IFile+screenShot) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
        * [.screenShotInRect(imageName, rect)](#module_miot/host/file..IFile+screenShotInRect) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
        * [.longScreenShot(viewRef, imageName)](#module_miot/host/file..IFile+longScreenShot) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
        * [.amapScreenShot(viewRef, imageName)](#module_miot/host/file..IFile+amapScreenShot) ⇒ <code>Promise</code>
        * [.getRGBAValueFromImageAtPath(imagePath, points)](#module_miot/host/file..IFile+getRGBAValueFromImageAtPath) ⇒ <code>Promise</code>
        * [.mkdir(params)](#module_miot/host/file..IFile+mkdir) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.queryFile(params)](#module_miot/host/file..IFile+queryFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.writePdfFile(utf8Content, filename, params)](#module_miot/host/file..IFile+writePdfFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.pdfToImage(params)](#module_miot/host/file..IFile+pdfToImage) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.readPdfMetaData(params)](#module_miot/host/file..IFile+readPdfMetaData) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.copyFile(params)](#module_miot/host/file..IFile+copyFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.getStorageInfo()](#module_miot/host/file..IFile+getStorageInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.readFolderSize()](#module_miot/host/file..IFile+readFolderSize) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.cropImage(targetFileName, sourceFilename, params:)](#module_miot/host/file..IFile+cropImage)
        * [.readFileInfo(fileName, type)](#module_miot/host/file..IFile+readFileInfo) ⇒ <code>Promise</code>
        * [.generateQRCodeAndSave(params)](#module_miot/host/file..IFile+generateQRCodeAndSave) ⇒ <code>Promise</code>
    * [~FileEvent](#module_miot/host/file..FileEvent) : <code>object</code>
        * [.fileDownloadProgress](#module_miot/host/file..FileEvent.fileDownloadProgress)
        * [.fileUploadProgress](#module_miot/host/file..FileEvent.fileUploadProgress)


* * *

<a name="module_miot/host/file..IFile"></a>

### miot/host/file~IFile
**Kind**: inner interface of [<code>miot/host/file</code>](#module_miot/host/file)  

* [~IFile](#module_miot/host/file..IFile)
    * [.storageBasePath](#module_miot/host/file..IFile+storageBasePath)
    * [.readFileList()](#module_miot/host/file..IFile+readFileList) ⇒ <code>Promise</code>
    * [.isFileExists(fileName)](#module_miot/host/file..IFile+isFileExists) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
    * [.readFile(fileName, [opt])](#module_miot/host/file..IFile+readFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;String&gt;</code>
    * [.readFileToHexString(fileName)](#module_miot/host/file..IFile+readFileToHexString) ⇒ <code>Promise</code>
    * [.readFileToBase64(fileName)](#module_miot/host/file..IFile+readFileToBase64) ⇒ <code>Promise</code>
    * [.readFileSegmentToBase64(fileName, off, len)](#module_miot/host/file..IFile+readFileSegmentToBase64) ⇒ <code>Promise</code>
    * [.writeFile(fileName, utf8Content)](#module_miot/host/file..IFile+writeFile) ⇒ <code>Promise</code>
    * [.saveFileToNotesAppOnMIUI(fileName)](#module_miot/host/file..IFile+saveFileToNotesAppOnMIUI)
    * [.writeFileThroughBase64(fileName, fileContent)](#module_miot/host/file..IFile+writeFileThroughBase64) ⇒ <code>Promise</code>
    * [.appendFile(fileName, utf8Content)](#module_miot/host/file..IFile+appendFile) ⇒ <code>Promise</code>
    * [.appendFileThroughBase64(fileName, fileContent)](#module_miot/host/file..IFile+appendFileThroughBase64) ⇒ <code>Promise</code>
    * [.deleteFile(fileName)](#module_miot/host/file..IFile+deleteFile) ⇒ <code>Promise</code>
    * [.generateObjNameAndUrlForFDSUpload(did, suffix)](#module_miot/host/file..IFile+generateObjNameAndUrlForFDSUpload)
    * [.generateObjNameAndUrlForFDSUploadV3(did, suffix)](#module_miot/host/file..IFile+generateObjNameAndUrlForFDSUploadV3)
    * [.generateObjNameAndUrlForLogFileFDSUpload(did, suffix)](#module_miot/host/file..IFile+generateObjNameAndUrlForLogFileFDSUpload)
    * [.getFDSFileInfoWithObjName(obj_name)](#module_miot/host/file..IFile+getFDSFileInfoWithObjName)
    * [.getFDSFileInfoWithObjNameV3(obj_name)](#module_miot/host/file..IFile+getFDSFileInfoWithObjNameV3)
    * [.uploadFile(params)](#module_miot/host/file..IFile+uploadFile) ⇒ <code>Promise</code>
    * [.uploadFileToFDS(params)](#module_miot/host/file..IFile+uploadFileToFDS) ⇒ <code>Promise</code>
    * [.downloadFile(url, fileName, params)](#module_miot/host/file..IFile+downloadFile) ⇒ <code>Promise</code>
    * [.registerFontEnable(param)](#module_miot/host/file..IFile+registerFontEnable)
    * [.downloadFontFile(param)](#module_miot/host/file..IFile+downloadFontFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [.readFontFileList(param)](#module_miot/host/file..IFile+readFontFileList) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [.deleteFontFile(param)](#module_miot/host/file..IFile+deleteFontFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [.cancelDownloadFile(taskID)](#module_miot/host/file..IFile+cancelDownloadFile) ⇒ <code>Promise</code>
    * [.dataLengthOfBase64Data(base64Data)](#module_miot/host/file..IFile+dataLengthOfBase64Data) ⇒ <code>Promise</code>
    * [.subBase64DataOfBase64Data(base64Data, loc, len)](#module_miot/host/file..IFile+subBase64DataOfBase64Data) ⇒ <code>Promise</code>
    * [.unzipFile(fileName)](#module_miot/host/file..IFile+unzipFile) ⇒ <code>Promise</code>
    * [.ungzipFileToString(params)](#module_miot/host/file..IFile+ungzipFileToString) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.ungzFile(fileName)](#module_miot/host/file..IFile+ungzFile) ⇒ <code>Promise</code>
    * [.ungzYunMiFile(fileName)](#module_miot/host/file..IFile+ungzYunMiFile) ⇒ <code>Promise</code>
    * [.saveImageToPhotosAlbum(fileName)](#module_miot/host/file..IFile+saveImageToPhotosAlbum) ⇒ <code>Promise</code>
    * [.saveFileToPhotosAlbum(fileName)](#module_miot/host/file..IFile+saveFileToPhotosAlbum) ⇒ <code>Promise</code>
    * [.saveImageToPhotosDidAlbum(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveImageToPhotosDidAlbum) ⇒ <code>Promise</code>
    * [.saveImageToPhotosDidAlbumV2(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveImageToPhotosDidAlbumV2) ⇒ <code>Promise</code>
    * [.saveVideoToPhotosDidAlbum(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveVideoToPhotosDidAlbum) ⇒ <code>Promise</code>
    * [.saveVideoToPhotosDidAlbumV2(fileName, customDirName, deviceID)](#module_miot/host/file..IFile+saveVideoToPhotosDidAlbumV2) ⇒ <code>Promise</code>
    * [.mergeVideos(firstVideoPath, secondVideoPath, deviceID)](#module_miot/host/file..IFile+mergeVideos) ⇒ <code>Promise</code>
    * [.mergeImages(firstImagePath, secondImagePath, deviceID)](#module_miot/host/file..IFile+mergeImages) ⇒ <code>Promise</code>
    * [.mergeVideosV2(firstVideoPath, secondVideoPath, params, deviceID, callbackEvent)](#module_miot/host/file..IFile+mergeVideosV2) ⇒ <code>Promise</code>
    * [.mergeImagesV2(firstImagePath, secondImagePath, params, deviceID)](#module_miot/host/file..IFile+mergeImagesV2) ⇒ <code>Promise</code>
    * [.fetchLocalVideoFilePathFromDidAlbumByUrl(url, customDirName, deviceID)](#module_miot/host/file..IFile+fetchLocalVideoFilePathFromDidAlbumByUrl) ⇒ <code>Promise</code>
    * [.getAllSourceFromPhotosDidAlbum(customDirName, deviceID)](#module_miot/host/file..IFile+getAllSourceFromPhotosDidAlbum) ⇒ <code>Promise</code>
    * [.getAlbums()](#module_miot/host/file..IFile+getAlbums) ⇒ <code>Promise</code>
    * [.getAssets(albumID)](#module_miot/host/file..IFile+getAssets) ⇒ <code>Promise</code>
    * [.deleteAssetsFromAlbumByUrls(urls)](#module_miot/host/file..IFile+deleteAssetsFromAlbumByUrls) ⇒ <code>Promise</code>
    * [.screenShot(imageName)](#module_miot/host/file..IFile+screenShot) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
    * [.screenShotInRect(imageName, rect)](#module_miot/host/file..IFile+screenShotInRect) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
    * [.longScreenShot(viewRef, imageName)](#module_miot/host/file..IFile+longScreenShot) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
    * [.amapScreenShot(viewRef, imageName)](#module_miot/host/file..IFile+amapScreenShot) ⇒ <code>Promise</code>
    * [.getRGBAValueFromImageAtPath(imagePath, points)](#module_miot/host/file..IFile+getRGBAValueFromImageAtPath) ⇒ <code>Promise</code>
    * [.mkdir(params)](#module_miot/host/file..IFile+mkdir) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.queryFile(params)](#module_miot/host/file..IFile+queryFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.writePdfFile(utf8Content, filename, params)](#module_miot/host/file..IFile+writePdfFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [.pdfToImage(params)](#module_miot/host/file..IFile+pdfToImage) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.readPdfMetaData(params)](#module_miot/host/file..IFile+readPdfMetaData) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.copyFile(params)](#module_miot/host/file..IFile+copyFile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.getStorageInfo()](#module_miot/host/file..IFile+getStorageInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.readFolderSize()](#module_miot/host/file..IFile+readFolderSize) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.cropImage(targetFileName, sourceFilename, params:)](#module_miot/host/file..IFile+cropImage)
    * [.readFileInfo(fileName, type)](#module_miot/host/file..IFile+readFileInfo) ⇒ <code>Promise</code>
    * [.generateQRCodeAndSave(params)](#module_miot/host/file..IFile+generateQRCodeAndSave) ⇒ <code>Promise</code>


* * *

<a name="module_miot/host/file..IFile+storageBasePath"></a>

#### iFile.storageBasePath
沙盒路径

**Kind**: instance property of [<code>IFile</code>](#module_miot/host/file..IFile)  

* * *

<a name="module_miot/host/file..IFile+readFileList"></a>

#### iFile.readFileList() ⇒ <code>Promise</code>
读取沙盒内文件列表, 返回文件的名称和文件的大小， 注意文件夹大小为：-1, 大小单位为B（字节）;
从10047起，新增 modifyTime 字段：文件保存时的时间戳，单位秒
* @param {string} subFolder 读取沙盒文件夹下某子文件夹中文件内容，用于解压缩文件中带有文件夹，或者读取指定文件夹解压后的文件,标准path结构，不以'/'开头

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：[{name:'xxx', size: 'xxx' , 'modifyTime': xxx(文件保存时的时间戳，单位秒)}, {name:'xxx', size: 'xxx', 'modifyTime': xxx(文件保存时的时间戳，单位秒)}, ...]  数组的形式返回数组
失败时：result: {"code":xxx, "message":"xxx" }  
**Example**  
```js
import {Host} from 'miot'
...
Host.file.readFileList().then(res => {
 console.log('read fiel list:', res)
}).catch((isOk, result)=>{
  console.log(isOk, result)
});

Host.file.readFileList('mysubfolder/aaa').then(res => {
 console.log('read fiel list:', res)
})
```

* * *

<a name="module_miot/host/file..IFile+isFileExists"></a>

#### iFile.isFileExists(fileName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
判断文件是否存在

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code> - 成功时：直接返回true or false
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |

**Example**  
```js
import {Host} from 'miot'
...
let fileExist = await Host.file.isFileExists('fileName')
//or
Host.file.isFileExists('fileName').then(res => {
console.log('file exist at path:', res)
}).catch(err => {
// file name error or get file path with error
})
```

* * *

<a name="module_miot/host/file..IFile+readFile"></a>

#### iFile.readFile(fileName, [opt]) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;String&gt;</code>
读本地文件， 读取普通字符串， 与之对应的写文件为Host.file.writeFile(fileName, content)

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;String&gt;</code> - 成功时：直接返回文件内容
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| fileName | <code>string</code> |  | 文件名,可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |
| [opt] | <code>json</code> | <code>{}</code> | 其他设置项 |

**Example**  
```js
import {Host} from 'miot'
...
Host.filereadFile('name').then(content =>{
 console.log('file content:', content)
})
```

* * *

<a name="module_miot/host/file..IFile+readFileToHexString"></a>

#### iFile.readFileToHexString(fileName) ⇒ <code>Promise</code>
读本地文件， 通常用于读取蓝牙设备需要的文件数据

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：直接返回文件内容
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |

**Example**  
```js
import {Host} from 'miot'
...
Host.filereadFileToHexString('name').then(content =>{
 console.log('file content:', content)
})
```

* * *

<a name="module_miot/host/file..IFile+readFileToBase64"></a>

#### iFile.readFileToBase64(fileName) ⇒ <code>Promise</code>
读文件，并转换为 Base64 编码

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：直接返回文件内容
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |


* * *

<a name="module_miot/host/file..IFile+readFileSegmentToBase64"></a>

#### iFile.readFileSegmentToBase64(fileName, off, len) ⇒ <code>Promise</code>
读取一定字节的文件，并转换为 Base64 编码

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：{content:"xxx",totalLength:xxx},
content为读取到的经过Base64编码后的文件内容，类型为string
totalLength为文件总长度，类型为number
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10045  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |
| off | <code>number</code> | 在文件中读取数据的起始位置的偏移 |
| len | <code>number</code> | 读取的最大字节数 |


* * *

<a name="module_miot/host/file..IFile+writeFile"></a>

#### iFile.writeFile(fileName, utf8Content) ⇒ <code>Promise</code>
写文件， 与之对应的读文件为Host.file.readFile(fileName)

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：直接返回true
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |
| utf8Content | <code>string</code> | 文件内容字符串 |

**Example**  
```js
import {Host} from 'miot'
...
Host.filewriteFile('name', 'content').then(_ =>{
 //写入成功
 console.log('write success')
})
...
```

* * *

<a name="module_miot/host/file..IFile+saveFileToNotesAppOnMIUI"></a>

#### iFile.saveFileToNotesAppOnMIUI(fileName)
**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Since**: 10051
保存文件内容到小米便签,仅支持特定的model  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |

**Example**  
```js
let extra = 'test_data.txt';
Host.ui.saveFileToNotesAppOnMIUI(fileName).then((isSuccess) => {
 }).catch((error) => {
 });
```

* * *

<a name="module_miot/host/file..IFile+writeFileThroughBase64"></a>

#### iFile.writeFileThroughBase64(fileName, fileContent) ⇒ <code>Promise</code>
写文件，输入为 Base64 编码的字符串， api内部会对字符串做 Base64 解码后存放到文件中

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：直接返回true
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |
| fileContent | <code>string</code> | 需要写入的文件内容 |

**Example**  
```js
import {Host} from 'miot'
...
Host.filewriteFileThroughBase64('name', 'base64').then(_ =>{
 //写入成功
 console.log('write success')
})
...
```

* * *

<a name="module_miot/host/file..IFile+appendFile"></a>

#### iFile.appendFile(fileName, utf8Content) ⇒ <code>Promise</code>
向已存在的文件追加内容, 通常是通过使用writeFile接口来写的文件

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：直接返回true
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |
| utf8Content | <code>string</code> | 文件内容字符串 |

**Example**  
```js
import {Host} from 'miot'
...
Host.fileappendFile('name', 'base64').then(_ =>{
 //写入成功
 console.log('write success')
})
...
```

* * *

<a name="module_miot/host/file..IFile+appendFileThroughBase64"></a>

#### iFile.appendFileThroughBase64(fileName, fileContent) ⇒ <code>Promise</code>
向已存在的文件追加内容，输入为 Base64 编码的字符串， api内部会对字符串做 Base64 解码后存放到文件中

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：直接返回true
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |
| fileContent | <code>string</code> | 需要写入的文件内容 |

**Example**  
```js
import {Host} from 'miot'
...
Host.fileappendFileThroughBase64('name', 'base64').then(_ =>{
 //写入成功
 console.log('write success')
})
...
```

* * *

<a name="module_miot/host/file..IFile+deleteFile"></a>

#### iFile.deleteFile(fileName) ⇒ <code>Promise</code>
删除文件

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：直接返回true
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |

**Example**  
```js
import {Host} from 'miot'
...
Host.filedeleteFile('name').then(_ =>{
 console.log('delete success')
})
...
```

* * *

<a name="module_miot/host/file..IFile+generateObjNameAndUrlForFDSUpload"></a>

#### iFile.generateObjNameAndUrlForFDSUpload(did, suffix)
上传普通文件，需要申请权限使用
获取用于上传FDS文件的obj_name以及用于上传的url
访问接口：/home/<USER>

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>string</code> | 设备did |
| suffix | <code>string</code> | 文件后缀 例如 'mp3', 'txt' |

**Example**  
```js
let did = Device.deviceID;
      let suffix = "mp3";
      Host.file.generateObjNameAndUrlForFDSUpload(did, suffix).then(res => {
      if (res.hasOwnProperty(suffix) && res[suffix]) {
          let obj = res[suffix];
          let obj_name = obj.obj_name;
          let name = obj_name.substring(obj_name.length - 22)
          let content = "AC";
          let time = obj.time;
          this.file_obj_name = obj_name;
          console.log("pre upload", res)

          Host.file.writeFile(name, content).then(r => {
              let param = {
                  uploadUrl: obj.url,
                  method: obj.method,
                  headers: { "Content-Type": "" },
                  files: [{ filename: name }]
              }
              Host.file.uploadFileToFDS(param).then(rr => {
                  alert('上传成功' + JSON.stringify(rr))
                  console.log('upload file success', rr)
              }).catch(err => {
                  alert('上传失败' + JSON.stringify(err))
                  console.log('upload file failed', err)
              })
          }).catch(err => {
              alert('存储临时文件失败' + JSON.stringify(err))
              console.log("write file failed", err)
          })
      }
      })
```

* * *

<a name="module_miot/host/file..IFile+generateObjNameAndUrlForFDSUploadV3"></a>

#### iFile.generateObjNameAndUrlForFDSUploadV3(did, suffix)
上传普通文件，需要申请权限使用,V3版本
获取用于上传FDS文件的obj_name以及用于上传的url
访问接口：/v2/home/<USER>

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Since**: 10056  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>string</code> | 设备did |
| suffix | <code>string</code> | 文件后缀 例如 'mp3', 'txt' |

**Example**  
```js
let did = Device.deviceID;
let suffix = "mp3";
*** Host.file.uploadFileToFDS(param) param = {headers: { "Content-Type": "application/octet-stream" }},上传FDS需要配置Content-Type
*** 如果是KS3 headers: { "Content-Type": "application/octet-stream", "X-Amz-Acl" : "private"}
   Host.file.generateObjNameAndUrlForFDSUploadV3(did, suffix).then(res => {
      if (res.hasOwnProperty(suffix) && res[suffix]) {
          let obj = res[suffix];
          let obj_name = obj.obj_name;
          let name = obj_name.substring(obj_name.length - 22)
          let content = "AC";
          let time = obj.time;
          this.file_obj_name = obj_name;
          console.log("pre upload", res)

          Host.file.writeFile(name, content).then(r => {
              let param = {
                  uploadUrl: obj.url,
                  method: obj.method,
                  headers: { "Content-Type": "application/octet-stream" },
                  files: [{ filename: name }]
              }
              Host.file.uploadFileToFDS(param).then(rr => {
                  alert('上传成功' + JSON.stringify(rr))
                  console.log('upload file success', rr)
              }).catch(err => {
                  alert('上传失败' + JSON.stringify(err))
                  console.log('upload file failed', err)
              })
          }).catch(err => {
              alert('存储临时文件失败' + JSON.stringify(err))
              console.log("write file failed", err)
          })
      }
      })
```

* * *

<a name="module_miot/host/file..IFile+generateObjNameAndUrlForLogFileFDSUpload"></a>

#### iFile.generateObjNameAndUrlForLogFileFDSUpload(did, suffix)
上传日志文件。
具体使用参考generateObjNameAndUrlForFDSUpload

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Since**: 10011  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>string</code> |  |
| suffix | <code>string</code> | string or array<string> |


* * *

<a name="module_miot/host/file..IFile+getFDSFileInfoWithObjName"></a>

#### iFile.getFDSFileInfoWithObjName(obj_name)
获取FDS文件的信息，包含下载地址等信息

对于手动上传到fds的文件(没有genObjName ,在平台端直接上传的)，可直接设置成public，生成url。插件端需要用这个文件时，用通用下载接口下载此url即可。
getFDSFileInfoWithObjName,这个接口只是用来下载通过插件接口(Host.file.uploadFileToFDS)上传到FDS的文件
访问接口:/home/<USER>

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| obj_name | <code>string</code> | generateObjNameAndUrlForFDSUpload 生成的 obj_name |

**Example**  
```js
let did = Device.deviceID;
      let suffix = "mp3";
      let file_obj_name = this.file_obj_name //从服务端获取或者本地获取,通过generateObjNameAndUrlForFDSUpload 生成
      if (file_obj_name) {
      Host.file.getFDSFileInfoWithObjName(file_obj_name).then(res => {
          console.log('getfileurl success', res)
          alert('获取成功' + JSON.stringify(res))
      }).catch(err => {
          console.log('getfileurl failed', err)
      })
      } else {
      alert("先上传文件")
      }
```

* * *

<a name="module_miot/host/file..IFile+getFDSFileInfoWithObjNameV3"></a>

#### iFile.getFDSFileInfoWithObjNameV3(obj_name)
获取FDS文件的信息，包含下载地址等信息 V3版本

对于手动上传到fds的文件(没有genObjName ,在平台端直接上传的)，可直接设置成public，生成url。插件端需要用这个文件时，用通用下载接口下载此url即可。
getFDSFileInfoWithObjNameV3,这个接口只是用来下载通过插件接口(Host.file.uploadFileToFDS)上传到FDS的文件
访问接口:/v2/home/<USER>

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Since**: 10056  

| Param | Type | Description |
| --- | --- | --- |
| obj_name | <code>string</code> | generateObjNameAndUrlForFDSUploadV3 生成的 obj_name |

**Example**  
```js
let did = Device.deviceID;
   let suffix = "mp3";
   let file_obj_name = this.file_obj_name //从服务端获取或者本地获取,generateObjNameAndUrlForFDSUploadV3 生成
   if (file_obj_name) {
      Host.file.getFDSFileInfoWithObjNameV3(file_obj_name).then(res => {
          console.log('getfileurl success', res)
          alert('获取成功' + JSON.stringify(res))
      }).catch(err => {
          console.log('getfileurl failed', err)
      })
      } else {
      alert("先上传文件")
      }
```

* * *

<a name="module_miot/host/file..IFile+uploadFile"></a>

#### iFile.uploadFile(params) ⇒ <code>Promise</code>
上传文件

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>UploadParams</code> | 参数字典 |

**Example**  
```js
import {Host} from 'miot'
...
let params = {
 uploadUrl: 'http://127.0.0.1:3000',
 method: 'POST', // default 'POST',support 'POST' and 'PUT'
 headers: {
     'Accept': 'application/json',
 },
 fields: {
     'hello': 'world',
 },
 files: [
     {
         filename: 'fileName.png', // 必选， 只能上传插件sandbox里的文件
         range: {start: 10, length: 100} // 可选， since 10037， 从start开始读取lengt长度的文件，可选，不配置则表示文件从头到尾
         formdata: {name: 'name1.png', filename: 'customFileName.png'} // 可选， since 10038， 用于自定义formdata中的name和filename
     },
 ]
};
Host.file.uploadFile(params).then(res => {
 console.log('upload success with res:', res)
}).catch(err => {
 console.log('upload failed with err:', err)
})
...
```

* * *

<a name="module_miot/host/file..IFile+uploadFileToFDS"></a>

#### iFile.uploadFileToFDS(params) ⇒ <code>Promise</code>
上传文件到小米云FDS

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>UploadParams</code> | 参数字典 |

**Example**  
```js
same as Host.file.uploadFile
```

* * *

<a name="module_miot/host/file..IFile+downloadFile"></a>

#### iFile.downloadFile(url, fileName, params) ⇒ <code>Promise</code>
下载文件到插件沙盒目录, 文件下载完成后才会回调，只支持下载单个文件

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：{header:{}, path:xxx, filename:xxx,status:xxx}
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| url | <code>string</code> |  | 文件地址 |
| fileName | <code>string</code> |  | 存储到本地的文件名 |
| params | <code>DownloadParams</code> | <code></code> | 参数字典 可选 since 10038 |

**Example**  
```js
import {Host} from 'miot'
...
Host.file.downloadFile('url', 'targetName').then(res =>{
 console.log('download success with res:', res)
}).catch(err => {
 console.log('download failed with err:', err)
})
...
```

* * *

<a name="module_miot/host/file..IFile+registerFontEnable"></a>

#### iFile.registerFontEnable(param)
**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Since**: 10083
注册字体
Android:
注意！！！对于Android而言，必须保证fontFamily在还未被使用之前注册进来，否者即使注册了也会无效
建议一进插件就注册，如果非要延时注册的话，请确保触发这个方法逻辑之前，插件从未尝试使用该fontFamily渲染过任何文字
说明：
在Android的RN中，字体是有缓存机制的，当一个插件需要使用的某个fontFamily首次被加载时，它会被存在一个Map中
key为fontFamily，value为它的字体对象
这样在下次还要用到此字体时可以避免重复去磁盘中读取字体文件
但是如果第一次使用这个字体时RN找不到这个字体，它就会使用系统默认的字体，同时也会把这个kv存入缓存，并且不会更新。
而这个方法就是要在RN第一次去加载这个字体之前给它权限允许它去加载，否者当RN无权限加载时，它会使用系统默认字体，后续因为缓存已经生效，导致再调用这个方法也不会有任何效果了
(注意这个方法只是给予权限去加载，到了真正要渲染字体的时候第一步会判断有没有权限，第二步就会去字体文件夹下读字体文件，而如果字体文件不存在的话，也会加载失败，那么也会使用默认字体，
所以这个方法必须保证fontFamily在还未被使用之前注册进来)

iOS:
iOS是可以做到使用时注册的，而且iOS要求字体文件必须存在才能注册成功（Android没这要求，因为对于Android而言它只是给予权限的一个操作），所以在使用此方法时要判断系统类型。  

| Param | Type | Description |
| --- | --- | --- |
| param |  |  |
| param.fontFamily | <code>string</code> | <Text>组件用到的fontFamily 注册成功时 iOS返回 {code:0, data:{fontFamily:xxxx}} // iOS上应该在<Text>组件中传入这里面的fontFamily 失败时：{"code":xxx, "message":"xxx" } Android没有返回值 |

**Example**  
```js
请查看com.xiaomi.demo里的DownloadFontDemo.js文件
```

* * *

<a name="module_miot/host/file..IFile+downloadFontFile"></a>

#### iFile.downloadFontFile(param) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 返回的数据和downloadFile方法的返回值一致  
**Since**: 10083
下载字体文件，下载过来的字体可以通过[Host.file.registerFontEnable](Host.file.registerFontEnable)注册
然后插件可以使用该字体，下载进度可以通过监听FileEvent.fileDownloadProgress事件实现  

| Param | Type | Description |
| --- | --- | --- |
| param |  |  |
| param.url | <code>string</code> | 下载链接，文件下载完成后才会回调，只支持下载单个文件 |
| param.fileName | <code>string</code> | 字体文件名称，名字必须规范!!! 1：必须以fontFamily命名 2：后缀必须为.otf或者.ttf 如：MiSansW-Light.otf |
| param.taskID | <code>string</code> | 任务ID，可选项，可用于cancelDownloadFile取消下载任务 |


* * *

<a name="module_miot/host/file..IFile+readFontFileList"></a>

#### iFile.readFontFileList(param) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功返回
    [
      {
        name:xxx,
        size:xxx,
        modifyTime:xxxx
      }
    ]
失败时返回
{
  code:-1,
  message:xxxx
}  
**Since**: 10083
读取已下载了的字体列表  

| Param | Description |
| --- | --- |
| param | 预留参数 |


* * *

<a name="module_miot/host/file..IFile+deleteFontFile"></a>

#### iFile.deleteFontFile(param) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功时返回:
"delete complete"
失败时返回：{
  code:-1,
  message:xxxx
}  
**Since**: 10083
删除下载了的字体文件  

| Param | Type | Description |
| --- | --- | --- |
| param |  |  |
| param.fileName | <code>string</code> | 字体文件名，跟下载时传入的名字一样 |


* * *

<a name="module_miot/host/file..IFile+cancelDownloadFile"></a>

#### iFile.cancelDownloadFile(taskID) ⇒ <code>Promise</code>
取消指定的下载任务

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：{code:0, data:{}}
失败时：{code:-1, message:'xxx'}  

| Param | Type | Description |
| --- | --- | --- |
| taskID | <code>string</code> | since 10038 下载任务的唯一ID， 与 downloadFile 传入的 taskID 一致 |


* * *

<a name="module_miot/host/file..IFile+dataLengthOfBase64Data"></a>

#### iFile.dataLengthOfBase64Data(base64Data) ⇒ <code>Promise</code>
获取 base64 编码的数据长度

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 返回具体的长度  

| Param | Type | Description |
| --- | --- | --- |
| base64Data | <code>string</code> | base64 编码的字符串 |

**Example**  
```js
import {Host} from 'miot'
...
let len = await Host.file.dataLengthOfBase64Data('data')
//or
Host.file.dataLengthOfBase64Data('data').then(len => console.log('len:', len))
...
```

* * *

<a name="module_miot/host/file..IFile+subBase64DataOfBase64Data"></a>

#### iFile.subBase64DataOfBase64Data(base64Data, loc, len) ⇒ <code>Promise</code>
获取一个data的子data（base64编码）

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  

| Param | Type | Description |
| --- | --- | --- |
| base64Data | <code>string</code> | base64 编码的数据 |
| loc | <code>number</code> | 起始位置 |
| len | <code>number</code> | 长度 |


* * *

<a name="module_miot/host/file..IFile+unzipFile"></a>

#### iFile.unzipFile(fileName) ⇒ <code>Promise</code>
解压缩一个zip文件，解压缩后的文件会直接存储在插件存储空间的根目录下

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名（插件存储空间内的文件） * @param {string} desitinationPath - 目标解压缩文件夹，默认解压到当前文件夹，如果指定名称，压缩包内容会解压到指定文件夹 |


* * *

<a name="module_miot/host/file..IFile+ungzipFileToString"></a>

#### iFile.ungzipFileToString(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
解压缩一个gzip文件为指定格式的字符串；文件首先被解压为一个字节数组，然后将字节数组转换为string(string可以是utf-8、base-64、16进制)

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时返回：{code:0,data:'xxxxxxxxxxx'}
失败时返回：
{code:-1,message:’${fileName} is not valid’}
{code:-2,message:’${fileName} is not exist‘}
{code:-3,message:’${fileName} is not a file}
{code:-4,message: 'unzipToString failed:internal error'}  
**Since**: 10054  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {  fileName: 'cache/test.zip', //沙盒内的相对路径,必填  charsetName:'[utf-8|base-64|hex-string|int-array]',//指定解压后的字符串的格式: utf-8表示将文件解压为utf-8格式的字符串； base-64表示解压为base-64格式的字符串；hex-string表示解压为16进制字符串；int-array表示将解压后的数据以JSONArray(数组元素为int类型)的格式的字符串返回 charsetName可不传，不传默认为base-64 } |

**Example**  
```js
let params = {
 fileName: 'cache/test.zip',
 charsetName: 'base-64',
}
Host.file.ungzipFileToString(params).then(res=>{
 console.log("file content:",res);
}).catch(err=>{
 console.log("ungzipFileToString error:",err);
})
```

* * *

<a name="module_miot/host/file..IFile+ungzFile"></a>

#### iFile.ungzFile(fileName) ⇒ <code>Promise</code>
解压缩一个gz文件, 并以base64编码的形式直接返回给插件, 不做本地存储

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回文件的内容
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名（插件存储空间内的文件） |


* * *

<a name="module_miot/host/file..IFile+ungzYunMiFile"></a>

#### iFile.ungzYunMiFile(fileName) ⇒ <code>Promise</code>
为云米扫地机的地图文件解压提供，私有

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名（插件存储空间内的文件） |


* * *

<a name="module_miot/host/file..IFile+saveImageToPhotosAlbum"></a>

#### iFile.saveImageToPhotosAlbum(fileName) ⇒ <code>Promise</code>
保存指定照片文件到系统相册

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
import {Host} from 'miot'
...
Host.file.saveImageToPhotosAlbum('name').then(_ =>{
 console.log('successful save to PhotosAlbum')
})
...
```

* * *

<a name="module_miot/host/file..IFile+saveFileToPhotosAlbum"></a>

#### iFile.saveFileToPhotosAlbum(fileName) ⇒ <code>Promise</code>
保存指定文件到系统相册

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10037  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+saveImageToPhotosDidAlbum"></a>

#### iFile.saveImageToPhotosDidAlbum(fileName, customDirName, deviceID) ⇒ <code>Promise</code>
保存指定图片文件到以did命名的相册中
该方法会在系统相册中创建一个以did[-customDirName]命名的相册（如果不存在），并将图片保存在其中

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"did cannot be empty" }
 {"code":-2, "message":"did cannot be empty" }
 {"code":-3, "message":"path is ilegal or file not exist" }
 {"code":-5, "message":"filepath cannot convert to a image, please check" }
 {"code":-100, "message":"failed to save image" }
 {"code":-101, "message":"failed to create album" }  
**Since**: 10037  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| fileName | <code>string</code> |  | 图片在沙盒中的文件名 |
| customDirName | <code>string</code> | <code>null</code> | 自定义相册名称，默认为null，since 10042 |
| deviceID | <code>string</code> |  | 指定的deviceID，不传默认使用本设备id， since 10082 |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+saveImageToPhotosDidAlbumV2"></a>

#### iFile.saveImageToPhotosDidAlbumV2(fileName, customDirName, deviceID) ⇒ <code>Promise</code>
保存指定图片文件到以did命名的相册中，返回系统相册中的路径
该方法会在系统相册中创建一个以did[-customDirName]命名的相册（如果不存在），并将图片保存在其中

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"did cannot be empty" }
 {"code":-2, "message":"did cannot be empty" }
 {"code":-3, "message":"path is ilegal or file not exist" }
 {"code":-5, "message":"filepath cannot convert to a image, please check" }
 {"code":-100, "message":"failed to save image" }
 {"code":-101, "message":"failed to create album" }  
**Since**: 10096  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| fileName | <code>string</code> |  | 图片在沙盒中的文件名 |
| customDirName | <code>string</code> | <code>null</code> | 自定义相册名称，默认为null，since 10042 |
| deviceID | <code>string</code> |  | 指定的deviceID，不传默认使用本设备id， since 10082 |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+saveVideoToPhotosDidAlbum"></a>

#### iFile.saveVideoToPhotosDidAlbum(fileName, customDirName, deviceID) ⇒ <code>Promise</code>
保存指定照片文件到以did命名的相册中
该方法会在系统相册中创建一个以did命名的相册（如果不存在），并将视频保存在其中

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"did cannot be empty" }
 {"code":-2, "message":"did cannot be empty" }
 {"code":-3, "message":"path is ilegal or file not exist" }
 {"code":-4, "message":"filepath cannot seek to be video file" }
 {"code":-6, "message":"file cannot save to album as a video" }
 {"code":-100, "message":"failed to save video" }
 {"code":-101, "message":"failed to create album" }  
**Since**: 10037  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| fileName | <code>string</code> |  |  |
| customDirName | <code>string</code> | <code>null</code> | 自定义相册名称，默认为null, since 10042 |
| deviceID | <code>string</code> |  | 指定的deviceID，不传默认使用本设备id， since 10082 |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+saveVideoToPhotosDidAlbumV2"></a>

#### iFile.saveVideoToPhotosDidAlbumV2(fileName, customDirName, deviceID) ⇒ <code>Promise</code>
保存指定照片文件到以did命名的相册中，返回系统相册中的路径
该方法会在系统相册中创建一个以did命名的相册（如果不存在），并将视频保存在其中

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"did cannot be empty" }
 {"code":-2, "message":"did cannot be empty" }
 {"code":-3, "message":"path is ilegal or file not exist" }
 {"code":-4, "message":"filepath cannot seek to be video file" }
 {"code":-6, "message":"file cannot save to album as a video" }
 {"code":-100, "message":"failed to save video" }
 {"code":-101, "message":"failed to create album" }  
**Since**: 10096  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| fileName | <code>string</code> |  |  |
| customDirName | <code>string</code> | <code>null</code> | 自定义相册名称，默认为null, since 10042 |
| deviceID | <code>string</code> |  | 指定的deviceID，不传默认使用本设备id， since 10082 |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+mergeVideos"></a>

#### iFile.mergeVideos(firstVideoPath, secondVideoPath, deviceID) ⇒ <code>Promise</code>
拼接视频文件

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：
  {"code": 0, "message":"merge success", "fileName": "mergeVideo-11111.mp4" }
失败时：code < 0
 {"code":xx, "message":"merge failure" }  
**Since**: 10095  

| Param | Type | Description |
| --- | --- | --- |
| firstVideoPath | <code>string</code> |  |
| secondVideoPath | <code>string</code> |  |
| deviceID | <code>string</code> | 指定的deviceID，不传默认使用本设备id |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+mergeImages"></a>

#### iFile.mergeImages(firstImagePath, secondImagePath, deviceID) ⇒ <code>Promise</code>
拼接图片文件

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：
  {"code": 0, "message":"merge success", "fileName": "mergeImage-11111.png" }
失败时：code < 0
 {"code":xx, "message":"merge failure" }  
**Since**: 10095  

| Param | Type | Description |
| --- | --- | --- |
| firstImagePath | <code>string</code> |  |
| secondImagePath | <code>string</code> |  |
| deviceID | <code>string</code> | 指定的deviceID，不传默认使用本设备id |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+mergeVideosV2"></a>

#### iFile.mergeVideosV2(firstVideoPath, secondVideoPath, params, deviceID, callbackEvent) ⇒ <code>Promise</code>
拼接视频文件(支持拼接格式dir：1:上下，2:左右、黑边space)

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：
  {"code": 0, "message":"merge success", "fileName": "mergeVideo-11111.mp4" }
失败时：code < 0
 {"code":xx, "message":"merge failure" }  
**Since**: 10102  

| Param | Type | Description |
| --- | --- | --- |
| firstVideoPath | <code>string</code> |  |
| secondVideoPath | <code>string</code> |  |
| params | <code>object</code> | direction: {1:上下，2:左右} |
| deviceID | <code>string</code> | 指定的deviceID，不传默认使用本设备id |
| callbackEvent | <code>string</code> | 拼接进度回调事件名 |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+mergeImagesV2"></a>

#### iFile.mergeImagesV2(firstImagePath, secondImagePath, params, deviceID) ⇒ <code>Promise</code>
拼接图片文件(支持拼接格式dir：1:上下，2:左右、黑边space)

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：
  {"code": 0, "message":"merge success", "fileName": "mergeImage-11111.png" }
失败时：code < 0
 {"code":xx, "message":"merge failure" }  
**Since**: 10102  

| Param | Type | Description |
| --- | --- | --- |
| firstImagePath | <code>string</code> |  |
| secondImagePath | <code>string</code> |  |
| params | <code>object</code> | directtion 1:上下，2:左右 {dir=1, space=2} |
| deviceID | <code>string</code> | 指定的deviceID，不传默认使用本设备id |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+fetchLocalVideoFilePathFromDidAlbumByUrl"></a>

#### iFile.fetchLocalVideoFilePathFromDidAlbumByUrl(url, customDirName, deviceID) ⇒ <code>Promise</code>
从did命名的相册中 通过url获取视频文件的filepath

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"did cannot be empty" }
 {"code":-2, "message":"did cannot be empty" }
 {"code":-3, "message":"url cannot be empty" }  
**Since**: 10037  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| url | <code>string</code> |  |  |
| customDirName | <code>string</code> | <code>null</code> | 自定义相册名称，默认为null, since 10042 |
| deviceID | <code>string</code> |  | 指定的deviceID，不传默认使用本设备id， since 10082 |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+getAllSourceFromPhotosDidAlbum"></a>

#### iFile.getAllSourceFromPhotosDidAlbum(customDirName, deviceID) ⇒ <code>Promise</code>
获取指定以did命名的相册中所有的图片和视频
如果不存在该相册，返回空数组

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：{"code":0, "data":[] }
     返回图片和视频信息
         ios 返回 图片scheme协议 miotph:// 视频scheme miotvideo://
         android 返回图片和视频文件的fileurl
     每个图片信息包含key
     {'url':<'miotph://XXXXXX'(ios) 'file://XXXXXX' (android)>,
     'mediaType' : <number>, // 0 : unknowntype, 1: image, 2:video, 3: audio(10037暂不支持)
     'pixelWidth' :<number>, // width信息，0 代表unknown
     'pixelHeight' :<number>, // height 0 代表unknown
     'creationDate' :<number>, // 创建时间信息，unix时间戳
     'modificationDate' : <number>, // 修改时间信息， unix时间戳
     'duration' : <number>, // 持续时间 信息 图片文件返回0  单位ms 10042之前ios返回的是秒，安卓返回的是ms 在10042 之后ios修正为ms
     'uti' : <string>, // 资源类型 since 10050 参考 https://zh.wikipedia.org/wiki/%E7%BB%9F%E4%B8%80%E7%B1%BB%E5%9E%8B%E6%A0%87%E8%AF%86
     }
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"did cannot be empty" }
 {"code":-2, "message":"did cannot be empty" }  
**Since**: 10037  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| customDirName | <code>string</code> | <code>null</code> | 自定义相册名称，默认为null, since 10042 |
| deviceID | <code>string</code> |  | 指定的deviceID，不传默认使用本设备id， since 10081 |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+getAlbums"></a>

#### iFile.getAlbums() ⇒ <code>Promise</code>
获取所有相册列表, 仅支持部分设备，使用前需申请权限

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：{"code":0, "data":[] }
     所有相册信息
     每个相册信息包含key
     {
       'albumID': <string>, // 相册唯一 ID，用于获取区别唯一相册
       'thumb' : <object>, // 与 getAllSourceFromPhotosDidAlbum 中成功返回 data 数组元素一致
       'name' : <string>, // 相册名
       'size' :<number>, // 相册中元素个数
     }
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-2, "message":"model has no permission to access photo library" }  
**Since**: 10050  
**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+getAssets"></a>

#### iFile.getAssets(albumID) ⇒ <code>Promise</code>
获取指定相册中所有的图片和视频

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：与 getAllSourceFromPhotosDidAlbum 成功时一致
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"albumID is not valid" }
 {"code":-2, "message":"model has no permission to access photo library" }  
**Since**: 10050  

| Param | Type | Description |
| --- | --- | --- |
| albumID | <code>string</code> | 相册唯一 ID |

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+deleteAssetsFromAlbumByUrls"></a>

#### iFile.deleteAssetsFromAlbumByUrls(urls) ⇒ <code>Promise</code>
在相册中通过url 删除指定的assets

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回true
失败时：
 {"code":-401, "message":"access to photo library denied" }
 {"code":-1, "message":"did cannot be empty" }
 {"code":-2, "message":"did cannot be empty" }
 {"code":-3, "message":"urls cannot be parsed to a Array or it is empty" }
 {"code":-100, "message":"delete assets failed" }  
**Since**: 10037  

| Param | Type |
| --- | --- |
| urls | <code>array</code> | 

**Example**  
```js
参考com.xiaomi.demo Host-->PhotoDemo.js
```

* * *

<a name="module_miot/host/file..IFile+screenShot"></a>

#### iFile.screenShot(imageName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
屏幕全屏截图

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code> - - 截图成功回调函数返回存储图片的绝对路径，加载图片时直接使用即可
成功时：返回图片的路径
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| imageName | <code>string</code> | 图片名称，png, |

**Example**  
```js
<Image source={{local:imageName, scale:PixelRatio.get()}} />
```

* * *

<a name="module_miot/host/file..IFile+screenShotInRect"></a>

#### iFile.screenShotInRect(imageName, rect) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
自定义范围的屏幕截图

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code> - -  截图成功 返回图片地址
成功时：返回图片的路径
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| imageName | <code>string</code> | 图片名称，png |
| rect | <code>Object</code> | 截屏范围 |


* * *

<a name="module_miot/host/file..IFile+longScreenShot"></a>

#### iFile.longScreenShot(viewRef, imageName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
长截屏，用来截scrollView，会把超出屏幕的部分也截到

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code> - 成功时：返回图片的路径
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| viewRef | <code>number</code> | scrollView的引用 |
| imageName | <code>string</code> | 图片名称，png |

**Example**  
```js
var findNodeHandle = require('findNodeHandle');
 var myScrollView = findNodeHandle(this.refs.myScrollView);
 Host.file.longScreenShot(myScrollView, 'test2.png').then(imagePath=>{
     console.log(imagePath);
 });
```

* * *

<a name="module_miot/host/file..IFile+amapScreenShot"></a>

#### iFile.amapScreenShot(viewRef, imageName) ⇒ <code>Promise</code>
高德地图截屏

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回图片的路径
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Description |
| --- | --- | --- |
| viewRef | <code>number</code> | MAMapView(MHMapView的父类)的引用 |
| imageName | <code>string</code> | 图片名称，自动添加后缀png |

**Example**  
```js
const findNodeHandle = require('findNodeHandle');
const myMapViewRef = findNodeHandle(this.refs.myMapView);
const imageName = 'mapToShare.png';
let imageToShow = null;
Host.file.amapScreenShot(myMapViewRef, imageName).then(() => {
   imageToShow = <Image source={{local:imageName}}>
   console.log("ok");
});
```

* * *

<a name="module_miot/host/file..IFile+getRGBAValueFromImageAtPath"></a>

#### iFile.getRGBAValueFromImageAtPath(imagePath, points) ⇒ <code>Promise</code>
获取图片指定点的色值, 传空数组将返回所有点的色值

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  

| Param | Type | Description |
| --- | --- | --- |
| imagePath | <code>string</code> | 图片文件路径 |
| points | <code>[ &#x27;Array&#x27; ].&lt;{x:int, y:int}&gt;</code> | 位置数组 |


* * *

<a name="module_miot/host/file..IFile+mkdir"></a>

#### iFile.mkdir(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
创建目录

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'},
             失败时可能的返回值有：{code:-1,message:'directory name is not valid'},
                               {code:-2,message:'file ${dirPath} already exist'},
                               {code:-3,message:'parent directory is not exist:${dirPath}'},
                               {code:-4,message:'permission denied,cannot access dir:${dirPath}'},  
**Since**: 10042  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {dirPath:‘xxx’,//本地路径如：dir0,/dir0/dir1                       recursive: [true/false],//是否递归创建目录。如果为 true，则创建该目录和该目录下的所有子目录                      } |

**Example**  
```js
let params ={
 dirPath: 'dir0/dir1',
 recursive: true,
};
Host.file.mkdir(params)
     .then(res=>{alert(JSON.stringify(res))})
     .catch(err=>{alert(JSON.stringify(err))})
```

* * *

<a name="module_miot/host/file..IFile+queryFile"></a>

#### iFile.queryFile(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
搜索文件（只在Android可使用）

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 返回值：
    成功时：{ code:0,
      data:[{
        relativePath:'相对路径',
        name:'文件名',
        url:'文件地址'
        size: xxx//'文件大小',
        modifacationDate:xxxxx,//上次修改时间
        }]
    }
    失败时：{
      code:-xxx,
      message:'xxxxx'
    }  
**Since**: 10052  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {     mimeTypes:[],//需要搜索的文件类型     pageSize: xxx,//分页大小,number类型(如100)；如果需要分页，pageSize必须大于0，不传或者传0表示不分页     pageNum: xxx,//分页编号,number类型(如0,1,2...)，pageSize大于0时有效 } mimeType的可选值如下： ["application/pdf",//pdf     "application/msword",//word     "application/vnd.openxmlformats-officedocument.wordprocessingml.document",//docx     "application/vnd.ms-excel",//xls,xlt     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",//xlsx     "application/vnd.ms-powerpoint",//ppt,pot,pps     "application/vnd.openxmlformats-officedocument.presentationml.presentation",//pptx     "text/text",//text     "text/html",//html     "text/xml",//xml     "image/jpeg",] |

**Example**  
```js
let params = {
      mimeTypes: ["application/pdf", // pdf
        "application/msword", // word
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // docx
        "application/vnd.ms-excel", // xls,xlt
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // xlsx
        "application/vnd.ms-powerpoint", // ppt,pot,pps
        "application/vnd.openxmlformats-officedocument.presentationml.presentation", // pptx
      ],
      pageSize: 2,
      pageNo: 0
    };
    Host.file.queryFile(params).then((res) => {
      alert(JSON.stringify(res));
    }).catch((err) => {
      alert(JSON.stringify(err));
    });
```

* * *

<a name="module_miot/host/file..IFile+writePdfFile"></a>

#### iFile.writePdfFile(utf8Content, filename, params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
将 utf8Content 转成 PDF 文件

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功时: {code:0, data: filepath} 绝对路径，可直接用于展示
  失败时: {
         code:100xx,     // 错误码，非0数字
         message:""      // 错误信息
       }  
**Since**: 10054  

| Param | Type | Description |
| --- | --- | --- |
| utf8Content | <code>string</code> | 需要被转换的文本内容 |
| filename | <code>string</code> | 存储为的文件名，与 isFileExists 相同 |
| params | <code>json</code> | 样式参数 |
| params.color | <code>string</code> | 文本颜色 如 '#FFF', 'red' |
| params.fontSize | <code>number</code> | 字体大小 |
| params.pageSize | <code>object</code> | 页面大小 如 {width: 200, height:100} |
| params.marginHorizontal | <code>string</code> | 水平边距 |
| params.marginVertical | <code>string</code> | 竖直边距 |


* * *

<a name="module_miot/host/file..IFile+pdfToImage"></a>

#### iFile.pdfToImage(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
将PDF指定页转换为图片

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时返回如下：
{
  code:0,
  data:{
          imageName: 'xxxxx',//图片名称，imageDir +'/' + imageName即为图片的路径
       }
}
失败是返回如下：
{code:-1,message:'invalid srcPath or imageDir'}
{code:-2,message:'no permission to access source file'}
{code:-3,message:'password required or incorrect password'}
{code:-4,message:'out of memory,set highQuality=false can reduce memory cost'}
{code:-5,message:'genarate image failed'}
{code:-6,message:'write image failed'}
{code:-7,message:'invalid input params pageIndex'}  
**Since**: 10052  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {   srcPath:'xxxxx',//pdf文件路径   imageDir:'xxxx',//转换后的图片保存目录（目录名，不含文件名）   pageIndex: xx(如：1),//需要将PDF的那一页转换为图片，从0开始   password:'xxxxx',//PDF密码，如果没有加密传空即可   highQuality: true/false,//是否需要高质量图片:如果为true图片格式为ARGB_8888,反之为RGB565;高质量图片会占用更多的内存和磁盘 } |

**Example**  
```js
let params = {
      mimeTypes: ["application/pdf", // pdf
      ],
      pageSize: 1,
      pageNo: 0
    };
    Host.file.queryFile(params).then((res) => {
      if(res && res.data){
        let pdf_params ={
          srcPath:res.data[0].url,
          imageDir: 'pdf_image',
          pageIndex: 0,
          password:'',
          highQuality:false,
        }
        Host.file.pdfToImage(pdf_params).then(res=>{
          alert(JSON.stringify(res));
        }).catch(res=>{
          alert(JSON.stringify(res));
        })

      }
    }).catch((err) => {
      alert(JSON.stringify(err));
    });
```

* * *

<a name="module_miot/host/file..IFile+readPdfMetaData"></a>

#### iFile.readPdfMetaData(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
读PDF文件信息

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时返回如下：
{
  code:0,
  data:{
          pageCount: xxx(如：30),//PDF的总页数
       }
}
失败是返回如下：
{code:-1,message:'invalid srcPath or imageDir'}
{code:-2,message:'no permission to access source file'}
{code:-3,message:'password required or incorrect password'}  
**Since**: 10052  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {   srcPath:'xxxxx',//pdf文件路径   password:'xxxxx',//PDF密码，如果没有加密传空即可 } |

**Example**  
```js
Host.file.queryFile(params).then((res) => {
      if(res && res.data){
        let pdf_params ={
          srcPath:res.data[0].url,
          password:'',
        }
        Host.file.readPdfMetaData(pdf_params).then(res=>{
          alert(JSON.stringify(res));
        }).catch(res=>{
          alert(JSON.stringify(res));
        })

      }
    }).catch((err) => {
      alert(JSON.stringify(err));
    });
```

* * *

<a name="module_miot/host/file..IFile+copyFile"></a>

#### iFile.copyFile(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
复制文件
since 10048

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:success}
         失败时：{code:-1,message:'invalid srcPath or dstPath'}
               {code:-2,message:'file ${dstPath} already exist'}
               {code:-3,message:'file not found,xxx'}
               {code:-4,message:'copy file error,xxx'}
               {code:-5,message:'copy file error,detail: create file error'}  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {  srcPath:'xxxxx',//源文件文件路径  dstPath:'xxxx', //目标文件路径：dstDir不为空时，可以传相对路径；dstDir不为空时，这里传文件名  dstDir:'xxx',//目标文件保存路径父目录，沙盒内复制文件时传空即可；如果是往沙盒外复制，dstDiir传目标文件的父目录(不能为空) } |

**Example**  
```js
沙盒内复制
let copy_params={
      srcPath:'test.pdf',
      dstPath:'test_copy.pdf',
    }
    Host.file.copyFile(copy_params).then((res) => {
      alert(JSON.stringify(res));
      Host.file.readFileList('').then(res=>{
        alert(JSON.stringify(res))
      })
    }).catch((res) => {
      alert(JSON.stringify(res));
    });
沙盒外复制
let copy_params={
      srcPath:'test.pdf',
      dstPath:'test_copy.pdf',
      dstDir:'content://xxxxxxx'
    }
    Host.file.copyFile(copy_params).then((res) => {
      alert(JSON.stringify(res));
      Host.file.readFileList('').then(res=>{
        alert(JSON.stringify(res))
      })
    }).catch((res) => {
      alert(JSON.stringify(res));
    });
```

* * *

<a name="module_miot/host/file..IFile+getStorageInfo"></a>

#### iFile.getStorageInfo() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
获取当前磁盘的可用空间和总存储空间
since 10048

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 返回当前磁盘的可用空间和总存储空间：{code: 0 ,data: { totalSpace: 123456, freeSpace: 23456} }，
其中totalSpace：总存储空间；freeSpace：剩余可用空间；单位都字节(byte)  
**Example**  
```js
Host.file.getStorageInfo().then(res=>{
 alert(JSON.stringify(res))
}).catch(err=>{
 alert(JSON.stringify(err));
});
```

* * *

<a name="module_miot/host/file..IFile+readFolderSize"></a>

#### iFile.readFolderSize() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
获取指定目录的占用空间 目录必须在插件沙盒或者是插件沙盒子目录
参数 folderName 为 '' 时获取插件沙盒目录
获取子目录需要传递相对路径，sdk会自动对目录做拼接
since 10062

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 返回目录必须在插件沙盒或者是插件沙盒子目录占用的存储空间：{code: 0 ,data: { size: 123456} }，
单位都字节(byte)  
**Example**  
```js
// 参数 folderName 为 '' 时获取插件沙盒目录
Host.file.readFolderSize('folder').then(res=>{
 alert(JSON.stringify(res))
}).catch(err=>{
 alert(JSON.stringify(err));
});
```

* * *

<a name="module_miot/host/file..IFile+cropImage"></a>

#### iFile.cropImage(targetFileName, sourceFilename, params:)
裁剪图片
since 10054

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns{promise&lt;string&gt;}**: 成功时返回裁剪后的图片路径，失败返回 {code:-1,message:'xxx'}  

| Param | Type | Description |
| --- | --- | --- |
| targetFileName | <code>string</code> | 裁剪后生成的文件的文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.jpg' |
| sourceFilename | <code>string</code> | 要裁剪的源图片名 |
| params: | <code>Object</code> | 裁剪参数 |
| params.offset: | <code>Object</code> | 裁剪图像的左上角坐标，在原始图像的坐标空间中指定. e.g :{x:0,y:0} type int |
| params.size: | <code>Object</code> | 裁切后的图像的尺寸，在原始图像的坐标空间中指定. e.g :{width:400,height:400} type int |
| params.displaySize: | <code>Object</code> | 将裁切后的图像缩放到指定大小(Optional).  e.g :{width:200,height:200} type int |


* * *

<a name="module_miot/host/file..IFile+readFileInfo"></a>

#### iFile.readFileInfo(fileName, type) ⇒ <code>Promise</code>
获取文件的信息

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时：返回文件信息
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10067  

| Param | Type | Description |
| --- | --- | --- |
| fileName | <code>string</code> | 文件名, 可以是多重文件夹嵌套文件， e.g 'path/path2/filename.txt' |
| type | <code>string</code> | 文件类型,区分micloud_file_create或其它 |


* * *

<a name="module_miot/host/file..IFile+generateQRCodeAndSave"></a>

#### iFile.generateQRCodeAndSave(params) ⇒ <code>Promise</code>
生成二维码并保存到插件路径

**Kind**: instance method of [<code>IFile</code>](#module_miot/host/file..IFile)  
**Returns**: <code>Promise</code> - 成功时: {code:0, data: "xxxxx"} 绝对路径filePath
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10103  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> |  |
| params.qrStr: | <code>string</code> | 二维码数据 |
| params.size: | <code>int</code> | 二维码宽度 int 只穿宽度就行 二维码默认正方形 |


* * *

<a name="module_miot/host/file..FileEvent"></a>

### miot/host/file~FileEvent : <code>object</code>
文件事件名集合

**Kind**: inner namespace of [<code>miot/host/file</code>](#module_miot/host/file)  

* [~FileEvent](#module_miot/host/file..FileEvent) : <code>object</code>
    * [.fileDownloadProgress](#module_miot/host/file..FileEvent.fileDownloadProgress)
    * [.fileUploadProgress](#module_miot/host/file..FileEvent.fileUploadProgress)


* * *

<a name="module_miot/host/file..FileEvent.fileDownloadProgress"></a>

#### FileEvent.fileDownloadProgress
文件下载时的进度事件通知

**Kind**: static property of [<code>FileEvent</code>](#module_miot/host/file..FileEvent)  

| Param | Description |
| --- | --- |
| filename | 文件名 |
| url | 下载地址 |
| totalBytes | 下载总大小 |
| downloadBytes | 已下载文件大小 |


* * *

<a name="module_miot/host/file..FileEvent.fileUploadProgress"></a>

#### FileEvent.fileUploadProgress
文件上传时的进度事件通知， 支持Host.file.uploadFile 和 Host.file.uploadFileToFDS 文件上传接口进度回调

**Kind**: static property of [<code>FileEvent</code>](#module_miot/host/file..FileEvent)  

| Param | Description |
| --- | --- |
| uploadUrl | 上传地址 |
| totalBytes | 上传总大小 |
| uploadBytes | 已上传文件大小 |


* * *

