<a name="module_miot/service/Account"></a>

## miot/service/Account
用于获取当前用户信息,通过Service.account获取当前用户对象实例。
其中Service.account.ID可直接使用，其余属性需要使用Service.account.load().then()来进行获取，可参考下方Example。
具体的可用属性与方法请参考Interface -> IAccount类API说明。
IAccount不支持直接创建使用，如需使用请调用：
Service.account.load().then((info)=>{info 中各个字段才有值}}）)

**Export**: public  
**Doc_name**: 账户模块  
**Doc_index**: 9  
**Doc_directory**: service  
**Example**  
```js
import {Service} from 'miot'
console.log(Service.account.ID)
Service.account.load().then(account=>{
 console.log(Service.account.nickName)
})
```

* [miot/service/Account](#module_miot/service/Account)
    * [~IAccount](#module_miot/service/Account..IAccount)
        * [.isLoaded](#module_miot/service/Account..IAccount+isLoaded) : <code>boolean</code>
        * [.ID](#module_miot/service/Account..IAccount+ID) : <code>string</code>
        * [.nickName](#module_miot/service/Account..IAccount+nickName) : <code>string</code>
        * [.avatarURL](#module_miot/service/Account..IAccount+avatarURL) : <code>string</code>
        * [.birth](#module_miot/service/Account..IAccount+birth) : <code>string</code>
        * [.email](#module_miot/service/Account..IAccount+email) : <code>string</code>
        * [.phone](#module_miot/service/Account..IAccount+phone) : <code>string</code>
        * [.sex](#module_miot/service/Account..IAccount+sex) : <code>string</code>
        * [.shareTime](#module_miot/service/Account..IAccount+shareTime) : <code>string</code>
        * [.load(force)](#module_miot/service/Account..IAccount+load) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IAccount&gt;</code>
        * [.getAccountInfoById(accountId)](#module_miot/service/Account..IAccount+getAccountInfoById) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code>
        * [.getAccountInfoList(ids)](#module_miot/service/Account..IAccount+getAccountInfoList) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;object&gt;&gt;</code>


* * *

<a name="module_miot/service/Account..IAccount"></a>

### miot/service/Account~IAccount
用户信息属性与方法说明

**Kind**: inner interface of [<code>miot/service/Account</code>](#module_miot/service/Account)  
**Example**  
```js
import {Service} from 'miot'
...
console.log(Service.account.ID)
if (Service.account.isLoaded) {
 console.log(Service.account.nickName)
}else {
 Service.account.load().then(account=>{
     console.log(Service.account.nickName)
     ...
 })
}
...
```

* [~IAccount](#module_miot/service/Account..IAccount)
    * [.isLoaded](#module_miot/service/Account..IAccount+isLoaded) : <code>boolean</code>
    * [.ID](#module_miot/service/Account..IAccount+ID) : <code>string</code>
    * [.nickName](#module_miot/service/Account..IAccount+nickName) : <code>string</code>
    * [.avatarURL](#module_miot/service/Account..IAccount+avatarURL) : <code>string</code>
    * [.birth](#module_miot/service/Account..IAccount+birth) : <code>string</code>
    * [.email](#module_miot/service/Account..IAccount+email) : <code>string</code>
    * [.phone](#module_miot/service/Account..IAccount+phone) : <code>string</code>
    * [.sex](#module_miot/service/Account..IAccount+sex) : <code>string</code>
    * [.shareTime](#module_miot/service/Account..IAccount+shareTime) : <code>string</code>
    * [.load(force)](#module_miot/service/Account..IAccount+load) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IAccount&gt;</code>
    * [.getAccountInfoById(accountId)](#module_miot/service/Account..IAccount+getAccountInfoById) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code>
    * [.getAccountInfoList(ids)](#module_miot/service/Account..IAccount+getAccountInfoList) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;object&gt;&gt;</code>


* * *

<a name="module_miot/service/Account..IAccount+isLoaded"></a>

#### iAccount.isLoaded : <code>boolean</code>
用户详情是否已经加载,不依赖于load方法。
如果已加载则所有属性可直接使用。

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+ID"></a>

#### iAccount.ID : <code>string</code>
当前登录账户userid,不依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+nickName"></a>

#### iAccount.nickName : <code>string</code>
用户昵称,依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+avatarURL"></a>

#### iAccount.avatarURL : <code>string</code>
用户头像的下载地址,依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+birth"></a>

#### iAccount.birth : <code>string</code>
用户生日,依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+email"></a>

#### iAccount.email : <code>string</code>
用户邮箱,依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+phone"></a>

#### iAccount.phone : <code>string</code>
用户电话,依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+sex"></a>

#### iAccount.sex : <code>string</code>
用户性别,依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+shareTime"></a>

#### iAccount.shareTime : <code>string</code>
用户分享时间,依赖于load方法

**Kind**: instance property of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Read only**: true  

* * *

<a name="module_miot/service/Account..IAccount+load"></a>

#### iAccount.load(force) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IAccount&gt;</code>
加载用户信息，所有依赖于load的用户信息需要在回调方法中会返回时才有值

**Kind**: instance method of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;IAccount&gt;</code> - 成功时：{IAccount}  可以查看 IAccount 类(位于Account.js中)具体信息
失败时：{"code":xxx, "message":"xxx" }  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| force | <code>boolean</code> | <code>false</code> | 是否从网络获取信息， true:表示从网络进行获取数据  false：表示从缓存获取数据; 默认为false |


* * *

<a name="module_miot/service/Account..IAccount+getAccountInfoById"></a>

#### iAccount.getAccountInfoById(accountId) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code>
获取指定某一账号id的信息

**Kind**: instance method of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code> - 成功时：{IAccount}  可以查看 IAccount 类(位于Account.js中)具体信息
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10032  

| Param | Description |
| --- | --- |
| accountId | 账号id 或 手机号 |


* * *

<a name="module_miot/service/Account..IAccount+getAccountInfoList"></a>

#### iAccount.getAccountInfoList(ids) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;object&gt;&gt;</code>
批量获取账号信息

**Kind**: instance method of [<code>IAccount</code>](#module_miot/service/Account..IAccount)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;object&gt;&gt;</code> - 账号信息列表(数组结构)
成功时：[{ID:xxx, avatarURL: {size_75:xxx,size_90:xxx,...}, icon:xxx, nickName:xxx, userName:xxx}, ...]
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10032  

| Param | Type | Description |
| --- | --- | --- |
| ids | <code>[ &#x27;Array&#x27; ].&lt;string&gt;</code> | 数组，仅支持账号id，不支持手机号查询 |


* * *

