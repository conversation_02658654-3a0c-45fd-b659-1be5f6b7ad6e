<a name="module_miot/service/security"></a>

## miot/service/security
安全相关服务

**Export**: public  
**Doc_name**: 安全模块  
**Doc_index**: 3  
**Doc_directory**: service  
**Example**  
```js
import {Service} from 'miot'

Service.security.shareSecureKey(deviceID, shareUid, {})
 .then(secureKey=>{
    ...
 })

Service.security.loadSecureKeys(deviceID).then(secureKeys=>{
   if(secureKeys.length > 0){
      const key = secureKeys[0];
      key.status = 1;
      key.save().then(key=>{...})
   }
})
```

* [miot/service/security](#module_miot/service/security)
    * _static_
        * [.ISecureKey](#module_miot/service/security.ISecureKey)
            * [.deviceID](#module_miot/service/security.ISecureKey+deviceID) : <code>long</code>
            * [.keyID](#module_miot/service/security.ISecureKey+keyID) : <code>long</code>
            * [.shareUserID](#module_miot/service/security.ISecureKey+shareUserID) : <code>string</code>
            * [.activeTime](#module_miot/service/security.ISecureKey+activeTime) : <code>long</code>
            * [.expireTime](#module_miot/service/security.ISecureKey+expireTime)
            * [.weekdays](#module_miot/service/security.ISecureKey+weekdays) : <code>[ &#x27;Array&#x27; ].&lt;int&gt;</code>
            * [.status](#module_miot/service/security.ISecureKey+status) : <code>int</code>
            * [.isOutOfDate()](#module_miot/service/security.ISecureKey+isOutOfDate) ⇒
            * [.save()](#module_miot/service/security.ISecureKey+save) ⇒ <code>Promise</code>
            * [.remove()](#module_miot/service/security.ISecureKey+remove) ⇒ <code>Promise</code>
    * _inner_
        * [~ISecurity](#module_miot/service/security..ISecurity)
            * [.loadSecureKeys(deviceID)](#module_miot/service/security..ISecurity+loadSecureKeys) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;ISecureKey&gt;&gt;</code>
            * [.shareSecureKey(deviceID, shareUid, [settings])](#module_miot/service/security..ISecurity+shareSecureKey) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;ISecureKey&gt;</code>
            * [.getLockBindInfo(deviceID)](#module_miot/service/security..ISecurity+getLockBindInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/service/security.ISecureKey"></a>

### miot/service/security.ISecureKey
**Kind**: static interface of [<code>miot/service/security</code>](#module_miot/service/security)  

* [.ISecureKey](#module_miot/service/security.ISecureKey)
    * [.deviceID](#module_miot/service/security.ISecureKey+deviceID) : <code>long</code>
    * [.keyID](#module_miot/service/security.ISecureKey+keyID) : <code>long</code>
    * [.shareUserID](#module_miot/service/security.ISecureKey+shareUserID) : <code>string</code>
    * [.activeTime](#module_miot/service/security.ISecureKey+activeTime) : <code>long</code>
    * [.expireTime](#module_miot/service/security.ISecureKey+expireTime)
    * [.weekdays](#module_miot/service/security.ISecureKey+weekdays) : <code>[ &#x27;Array&#x27; ].&lt;int&gt;</code>
    * [.status](#module_miot/service/security.ISecureKey+status) : <code>int</code>
    * [.isOutOfDate()](#module_miot/service/security.ISecureKey+isOutOfDate) ⇒
    * [.save()](#module_miot/service/security.ISecureKey+save) ⇒ <code>Promise</code>
    * [.remove()](#module_miot/service/security.ISecureKey+remove) ⇒ <code>Promise</code>


* * *

<a name="module_miot/service/security.ISecureKey+deviceID"></a>

#### iSecureKey.deviceID : <code>long</code>
设备 ID

**Kind**: instance property of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  
**Read only**: true  

* * *

<a name="module_miot/service/security.ISecureKey+keyID"></a>

#### iSecureKey.keyID : <code>long</code>
电子钥匙 ID

**Kind**: instance property of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  
**Read only**: true  

* * *

<a name="module_miot/service/security.ISecureKey+shareUserID"></a>

#### iSecureKey.shareUserID : <code>string</code>
分享目标的uid

**Kind**: instance property of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  
**Read only**: true  

* * *

<a name="module_miot/service/security.ISecureKey+activeTime"></a>

#### iSecureKey.activeTime : <code>long</code>
生效时间 UTC时间戳，单位为s, active_time

**Kind**: instance property of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  

* * *

<a name="module_miot/service/security.ISecureKey+expireTime"></a>

#### iSecureKey.expireTime
过期时间 UTC时间戳，单位为s, expire_time

**Kind**: instance property of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  

* * *

<a name="module_miot/service/security.ISecureKey+weekdays"></a>

#### iSecureKey.weekdays : <code>[ &#x27;Array&#x27; ].&lt;int&gt;</code>
生效日期（星期几，例如周一和周三对应1和3，[1, 3]，星期天对应0），仅在status=2时不可为空

**Kind**: instance property of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  

* * *

<a name="module_miot/service/security.ISecureKey+status"></a>

#### iSecureKey.status : <code>int</code>
分享类别，1：暂时，2：周期，3：永久

**Kind**: instance property of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  

* * *

<a name="module_miot/service/security.ISecureKey+isOutOfDate"></a>

#### iSecureKey.isOutOfDate() ⇒
是否过期

**Kind**: instance method of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  
**Returns**: boolean  

* * *

<a name="module_miot/service/security.ISecureKey+save"></a>

#### iSecureKey.save() ⇒ <code>Promise</code>
保存 /share/bluetoothkeyshare

**Kind**: instance method of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  

* * *

<a name="module_miot/service/security.ISecureKey+remove"></a>

#### iSecureKey.remove() ⇒ <code>Promise</code>
删除 /share/bluetoothkeyshare

**Kind**: instance method of [<code>ISecureKey</code>](#module_miot/service/security.ISecureKey)  

* * *

<a name="module_miot/service/security..ISecurity"></a>

### miot/service/security~ISecurity
**Kind**: inner class of [<code>miot/service/security</code>](#module_miot/service/security)  
**Export**:   

* [~ISecurity](#module_miot/service/security..ISecurity)
    * [.loadSecureKeys(deviceID)](#module_miot/service/security..ISecurity+loadSecureKeys) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;ISecureKey&gt;&gt;</code>
    * [.shareSecureKey(deviceID, shareUid, [settings])](#module_miot/service/security..ISecurity+shareSecureKey) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;ISecureKey&gt;</code>
    * [.getLockBindInfo(deviceID)](#module_miot/service/security..ISecurity+getLockBindInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/service/security..ISecurity+loadSecureKeys"></a>

#### iSecurity.loadSecureKeys(deviceID) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;ISecureKey&gt;&gt;</code>
加载设备的安全锁 /share/bluetoothkeyshare

**Kind**: instance method of [<code>ISecurity</code>](#module_miot/service/security..ISecurity)  

| Param | Type | Description |
| --- | --- | --- |
| deviceID | <code>\*</code> | 设备ID |


* * *

<a name="module_miot/service/security..ISecurity+shareSecureKey"></a>

#### iSecurity.shareSecureKey(deviceID, shareUid, [settings]) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;ISecureKey&gt;</code>
分享蓝牙锁的钥匙 /share/bluetoothkeyshare
锁固件版本在 2.0.0 及以上， 不支持钥匙的分享

**Kind**: instance method of [<code>ISecurity</code>](#module_miot/service/security..ISecurity)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;ISecureKey&gt;</code> - 分享成功返回锁的信息；reject的时候返回的是object，如果该锁已经分享给被分享人会返回失败，错误码为-101；其他错误情形留意返回的错误信息  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| deviceID |  |  | 被分享设备ID |
| shareUid | <code>string</code> |  | 被分享人 |
| [settings] | <code>Object</code> | <code>{}</code> | readonly = true, 则被分享人不可接收锁push，false则被分享人可接收锁push，（family关系用户不受这个字段影响）。status:分享类别，1：暂时，2：周期，3：永久; weekdays 生效日期（星期几，例如周一和周三对应1和3，[1, 3]），仅在status=2时不可为空 |


* * *

<a name="module_miot/service/security..ISecurity+getLockBindInfo"></a>

#### iSecurity.getLockBindInfo(deviceID) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
获取锁绑定信息, /device/blelockbindinfo 返回数据格式：{"bindtime":1505180216}，bindtime是锁的绑定时间

**Kind**: instance method of [<code>ISecurity</code>](#module_miot/service/security..ISecurity)  

| Param | Type |
| --- | --- |
| deviceID | <code>string</code> | 


* * *

