## Modules

<dl>
<dt><a href="#module_CommonSetting">CommonSetting</a></dt>
<dd><p>米家通用设置项</p>
</dd>
</dl>

## Constants

<dl>
<dt><a href="#firstSharedOptions">firstSharedOptions</a></dt>
<dd><p>分享设备的设置项
0: 不显示
1: 显示</p>
</dd>
<dt><a href="#excludeManagerShowedOptions">excludeManagerShowedOptions</a></dt>
<dd><p>家庭成员不能看到，管理员可以看到的设置项</p>
</dd>
<dt><a href="#AllOptionsWeight">AllOptionsWeight</a></dt>
<dd><p>20190708 / SDK_10023
所有设置项顺序固定
权重值越大，排序越靠后，为了可扩展性，权重不能依次递增+1</p>
</dd>
<dt><a href="#excludeOptions">excludeOptions</a></dt>
<dd><p>某些特殊设备类型不显示某些设置项
key: 设置项的key
value: 不显示该设置项的设备类型列表, 用 pid 表示设备类型, [] 表示支持所有设备
0:  wifi单模设备
1:  yunyi设备
2:  云接入设备
3:  zigbee设备
5:  虚拟设备
6:  蓝牙单模设备
7:  本地AP设备
8:  蓝牙wifi双模设备
9:  其他
10: 功能插件
11: SIM卡设备
12: 网线设备
13: NB-IoT
14: 第三方云接入
15: 红外遥控器
16: BLE Mesh
17: 虚拟设备（新设备组）</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#ItemStyle">ItemStyle</a> : <code>Object</code></dt>
<dd><p>ItemStyle - 10040新增 可参考 ListItem组件的部分样式</p>
</dd>
<dt><a href="#moreSettingPageStyle">moreSettingPageStyle</a> : <code>Object</code></dt>
<dd><p>moreSettingPageStyle - 10040新增 二级页面 更多设置 页面的样式</p>
</dd>
<dt><a href="#CommonSettingStyle">CommonSettingStyle</a> : <code>Object</code></dt>
<dd><ul>
<li>10040新增</li>
</ul>
</dd>
</dl>

<a name="module_CommonSetting"></a>

## CommonSetting
米家通用设置项

**Export**: public  
**Doc_name**: 通用设置  
**Doc_index**: 3  
**Doc_directory**: ui  
**See**: com.xiaomi.demo->教程->插件通用设置项  
**Since**: 10004  
**Author**: Geeook  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| firstOptions | <code>array</code> | 一级菜单列表项的keys，keys的顺序代表显示的顺序，不传将显示全部，传空数组将显示必选项，其中产品百科的配置请参考: https://iot.mi.com/new/doc/direct-access/productcenter/advance-configure#%E9%85%8D%E7%BD%AE%E2%80%9C%E4%BA%A7%E5%93%81%E7%99%BE%E7%A7%91%E2%80%9D |
| secondOptions | <code>array</code> | 二级菜单列表项的keys，keys的顺序代表显示的顺序，不传将显示全部，传空数组将显示必选项 |
| showDot | <code>array</code> | 定义哪些列表项需要显示小红点。为了便于扩展每个列表项都可以显示小红点，默认全部**不显示**，需要显示传入该列表项的key即可。 |
| commonSettingStyle | [<code>CommonSettingStyle</code>](#CommonSettingStyle) | - 10040新增 CommonSetting 中有关字体样式相关设置 |
| extraOptions | <code>object</code> | 其他特殊配置项 ```js // extraOptions extraOptions: {   showUpgrade: bool // 「固件升级」是否跳转原生固件升级页面。默认值true。一般来说，wifi设备跳转原生固件升级页面，蓝牙设备（传入bleOtaAuthType除外）不跳转原生固件升级页面   upgradePageKey: string // 「固件升级」如果不跳转原生固件升级页面，请传入想跳转页面的key(定义在 index.js 的 RootStack 中)   licenseUrl: 资源id, // 见 miot/Host.ui.privacyAndProtocolReview 的传参说明，SDK_10023 开始废弃   policyUrl: 资源id, // 见 miot/Host.ui.privacyAndProtocolReview 的传参说明，SDK_10023 开始废弃   deleteDeviceMessage: string // 删除设备的弹窗中自定义提示文案，见 miot/Host.ui.openDeleteDevice 的传参说明   ZXhjbHVkZVJlcXVpcmVkT3B0aW9ucw==: [] // %E5%A6%82%E6%9E%9C%E6%83%B3%E8%A6%81%E5%B1%8F%E8%94%BD%E5%BF%85%E9%80%89%E9%A1%B9%EF%BC%8C%E5%9C%A8%E8%BF%99%E9%87%8C%E4%BC%A0%E5%85%A5%20key%20%E5%8D%B3%E5%8F%AF%EF%BC%8C%E4%B8%80%E7%BA%A7%20or%20%E4%BA%8C%E7%BA%A7%E8%8F%9C%E5%8D%95%E7%9A%84%20key%20%E9%83%BD%E5%8F%AF%E4%BB%A5%E3%80%82%E7%89%B9%E6%AE%8A%E9%9C%80%E8%A6%81%EF%BC%8C%E8%B0%A8%E6%85%8E%E4%BD%BF%E7%94%A8   option: object // 见 miot/Host.ui.previewLegalInformationAuthorization 的传参说明   syncDevice: bool // 插件端设置时区后是否需要后台同步到设备端, 见 miot/Host.ui.openDeviceTimeZoneSettingPage 的传参说明   networkInfoConfig: number // 「更多设置」页面是否显示「网络信息」设置项。0：不显示；1：显示；-1：米家默认配置（蓝牙设备不显示，Wi-Fi设备显示）   bleOtaAuthType: number // 打开通用的蓝牙固件OTA的原生页面。指定设备的协议类型 0: 普通小米蓝牙协议设备(新接入设备已废弃该类型)，1: 安全芯片小米蓝牙设备（比如锁类产品） 4: Standard Auth 标准蓝牙认证协议(通常2019.10.1之后上线的新蓝牙设备) 5: Mesh 设备   10059新增   preOperations: object { AllOptions.SHARE: function, AllOptions.FIRMWARE_UPGRADE: function, AllOptions.CREATE_GROUP: function, AllOptions.MANAGE_GROUP: function  } // 打开分享、ota、创建组、编辑组页面的前置操作，只会在resolve中执行打开页面 } ``` |
| navigation | <code>object</code> | 必须传入当前插件的路由，即 `this.props.navigation`，否则无法跳转二级页面 **注意：** **1. 如果需要显示「更多设置」「固件升级」的二级菜单页面，需要从 miot/ui/CommonSetting 中导出 MoreSetting 和 FirmwareUpgrade 页面，**    **并放在项目入口文件index.js的RootStack中。** ```js // index.js snippet import { FirmwareUpgrade, MoreSetting } from "miot/ui/CommonSetting"; ... const RootStack = createStackNavigator( {     Setting, // 设置页     MoreSetting, // 二级菜单——更多设置     FirmwareUpgrade, // 二级菜单——固件升级 } ... ) ``` **2. 必须传入当前插件的路由，即 `this.props.navigation`，否则无法跳转二级页面** ```js <CommonSetting   navigation={this.props.navigation} /> ``` |


* [CommonSetting](#module_CommonSetting)
    * [.chooseFirmwareUpgrade()](#module_CommonSetting+chooseFirmwareUpgrade)
    * [.createGroup()](#module_CommonSetting+createGroup)
    * [.manageGroup()](#module_CommonSetting+manageGroup)
    * [.removeKeyFromShowDot(key)](#module_CommonSetting+removeKeyFromShowDot)
    * [.openSubPage(page)](#module_CommonSetting+openSubPage)
    * [.openDeleteDevice()](#module_CommonSetting+openDeleteDevice)


* * *

<a name="module_CommonSetting+chooseFirmwareUpgrade"></a>

### commonSetting.chooseFirmwareUpgrade()
点击「固件升级」，选择性跳转

**Kind**: instance method of [<code>CommonSetting</code>](#module_CommonSetting)  

* * *

<a name="module_CommonSetting+createGroup"></a>

### commonSetting.createGroup()
创建组设备

**Kind**: instance method of [<code>CommonSetting</code>](#module_CommonSetting)  

* * *

<a name="module_CommonSetting+manageGroup"></a>

### commonSetting.manageGroup()
管理组设备

**Kind**: instance method of [<code>CommonSetting</code>](#module_CommonSetting)  

* * *

<a name="module_CommonSetting+removeKeyFromShowDot"></a>

### commonSetting.removeKeyFromShowDot(key)
从 this.state.showDot 移除某key，从而隐藏小红点

**Kind**: instance method of [<code>CommonSetting</code>](#module_CommonSetting)  

| Param | Type |
| --- | --- |
| key | <code>string</code> | 


* * *

<a name="module_CommonSetting+openSubPage"></a>

### commonSetting.openSubPage(page)
打开二级菜单

**Kind**: instance method of [<code>CommonSetting</code>](#module_CommonSetting)  

| Param | Type | Description |
| --- | --- | --- |
| page | <code>string</code> | index.js的RootStack中页面定义的key |


* * *

<a name="module_CommonSetting+openDeleteDevice"></a>

### commonSetting.openDeleteDevice()
弹出「删除设备」弹窗

**Kind**: instance method of [<code>CommonSetting</code>](#module_CommonSetting)  

* * *

<a name="firstSharedOptions"></a>

## firstSharedOptions
分享设备的设置项
0: 不显示
1: 显示

**Kind**: global constant  

* * *

<a name="excludeManagerShowedOptions"></a>

## excludeManagerShowedOptions
家庭成员不能看到，管理员可以看到的设置项

**Kind**: global constant  

* * *

<a name="AllOptionsWeight"></a>

## AllOptionsWeight
20190708 / SDK_10023
所有设置项顺序固定
权重值越大，排序越靠后，为了可扩展性，权重不能依次递增+1

**Kind**: global constant  

* * *

<a name="excludeOptions"></a>

## excludeOptions
某些特殊设备类型不显示某些设置项
key: 设置项的key
value: 不显示该设置项的设备类型列表, 用 pid 表示设备类型, [] 表示支持所有设备
0:  wifi单模设备
1:  yunyi设备
2:  云接入设备
3:  zigbee设备
5:  虚拟设备
6:  蓝牙单模设备
7:  本地AP设备
8:  蓝牙wifi双模设备
9:  其他
10: 功能插件
11: SIM卡设备
12: 网线设备
13: NB-IoT
14: 第三方云接入
15: 红外遥控器
16: BLE Mesh
17: 虚拟设备（新设备组）

**Kind**: global constant  

* * *

<a name="ItemStyle"></a>

## ItemStyle : <code>Object</code>
ItemStyle - 10040新增 可参考 ListItem组件的部分样式

**Kind**: global typedef  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| titleStyle | <code>style</code> | 标题的自定义样式 |
| subtitleStyle | <code>style</code> | 副标题的自定义样式 |
| valueStyle | <code>style</code> | 右侧文案的自定义样式 |
| dotStyle | <code>bool</code> | 10040新增 title右上角红点的style  建议设置宽高为8，以免图片失真 |
| titleNumberOfLines | <code>number</code> | 10040新增 设置title字体显示的最大行数 默认为1 |
| subtitleNumberOfLines | <code>number</code> | 10040新增 设置subtitle字体显示的最大行数 默认为2 |
| valueNumberOfLines | <code>number</code> | 10040新增 设置value字体显示的最大行数 默认为2 |
| valueMaxWidth | <code>number</code> | 10051新增 设置value文案的最大宽度 默认为有箭头时30%，无箭头时35% |
| useNewType | <code>bool</code> | 10045新增 是否使用新样式 10045以后*!必须!*使用新样式 |


* * *

<a name="moreSettingPageStyle"></a>

## moreSettingPageStyle : <code>Object</code>
moreSettingPageStyle - 10040新增 二级页面 更多设置 页面的样式

**Kind**: global typedef  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| navigationBarStyle | <code>style</code> | 标题的自定义样式 -可参考 NavigationBar 样式 |
| itemStyle | <code>style</code> | 列表中 item样式 |
|  | <code>style</code> | 10053新增 containerStyle - 标题栏下方内容的样式 |


* * *

<a name="CommonSettingStyle"></a>

## CommonSettingStyle : <code>Object</code>
- 10040新增

**Kind**: global typedef  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| allowFontScaling | <code>bool</code> | 10040新增 设置字体是否随系统设置的字体大小的设置改变而改变 默认为true。 |
| unlimitedHeightEnable | <code>bool</code> | 10040新增 设置控件高度是否自适应。 默认为false，即默认高度 |
| titleStyle | <code>style</code> | 10040新增 CommonSetting中 "通用设置" 字体的样式 |
| itemStyle | [<code>ItemStyle</code>](#ItemStyle) | 10040新增 CommonSetting中 列表item 的样式 |
| deleteTextStyle | <code>object</code> | 10040新增 CommonSetting中 "删除设备" 字体的样式 |
| moreSettingPageStyle | <code>object</code> | 10040新增 CommonSetting中 二级页面 更多设置 页面的样式 |
| titleContainer | <code>object</code> | 10053新增 CommonSetting中 "通用设置" 所在item的样式 |
| bottomContainer | <code>object</code> | 10053新增 CommonSetting中 "删除设备" 所在item的样式 |


* * *

