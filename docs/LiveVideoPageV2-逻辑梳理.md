# LiveVideoPageV2.js 详细逻辑梳理

## 📋 概述

LiveVideoPageV2.js 是小米摄像头直播视频页面的核心组件，文件长度9821行，是一个功能非常完整和复杂的React Native组件。该组件承载了用户与摄像头交互的主要功能。

## 🏗️ 整体架构

### 基本信息
- **组件类型**: React.Component
- **文件大小**: 9821行代码
- **主要功能**: 摄像头直播视频播放和控制
- **支持平台**: iOS/Android
- **导航配置**: 透明导航栏，无标题栏

```javascript
export default class LiveVideoPageV2 extends React.Component {
  static navigationOptions = (navigation) => {
    return {
      headerTransparent: true,
      header: null
    };
  };
}
```

## 🔧 核心功能模块

### 1. 状态管理 (State)

组件包含大量状态变量，主要分为以下几类：

#### 设备控制状态
- `isSleep`: 设备休眠状态
- `isPowerOn`: 设备电源状态  
- `isRecording`: 录制状态
- `isCalling`: 通话状态
- `isMute`: 静音状态

#### UI显示状态
- `showPlayToolBar`: 播放工具栏显示
- `fullScreen`: 全屏模式
- `showDirectCtr`: 方向控制显示
- `showErrorView`: 错误视图显示
- `showLoadingView`: 加载视图显示

#### 网络连接状态
- `pstate`: 连接状态
- `error`: 错误码
- `bps`: 比特率

#### 功能开关状态
- `useLenCorrent`: 畸变纠正
- `isFlip`: 图像翻转
- `resolution`: 视频分辨率
- `enableAIFrame`: AI框架开关

### 2. 生命周期管理

#### 组件挂载 (componentDidMount)
```javascript
componentDidMount() {
  // 1. 检查国际服务器
  this.checkIsInternationalServer();
  
  // 2. 隐私授权检查
  if (this.fromOneKeyCall) {
    this.onPrivacyAuthed();
  } else {
    this.checkPrivacyDialog();
  }
  
  // 3. 绑定各种监听器和回调
  CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
  CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);
  CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
  
  // 4. 设置屏幕常亮
  Host.ui.keepScreenNotLock(true);
  
  // 5. 加载本地设置
  this.loadLocalSetttings();
}
```

#### 组件卸载 (componentWillUnmount)
```javascript
componentWillUnmount() {
  // 1. 清理所有定时器
  this._clearTimer(this.angleViewTimeout);
  this._clearTimer(this.mFirmwareUpdatingTimer);
  
  // 2. 移除所有事件监听器
  this.didFocusListener && this.didFocusListener.remove();
  this.bpsListener && this.bpsListener.remove();
  
  // 3. 断开摄像头连接
  Service.miotcamera.disconnectToDevice();
  
  // 4. 销毁相关实例
  CameraPlayer.destroyInstance();
  DldMgr.clear();
}
```

### 3. 页面焦点管理

通过多个监听器管理页面的前后台状态：

#### 页面焦点监听器
- `didFocusListener`: 页面获得焦点时的处理
- `willBlurListener`: 页面失去焦点时的处理
- `didResumeListener`: 应用恢复前台时的处理  
- `willPauseListener`: 应用进入后台时的处理

#### iOS特殊处理
- `willAppearListener`: iOS插件显示时的处理
- `willDisappearListener`: iOS插件隐藏时的处理

### 4. 网络连接管理

#### 网络状态查询
```javascript
queryNetworkJob() {
  CameraPlayer.getInstance().queryShouldPauseOn4G()
    .then(({ state, pauseOnCellular }) => {
      // 检查网络类型
      if (state == "NONE" || state == "UNKNOWN") {
        this.setState({ showErrorView: true });
        return;
      }
      
      // 流量提醒逻辑
      if (state == "CELLULAR" && pauseOnCellular && !this.skipDataWarning) {
        this._stopAll(true);
        return;
      }
      
      // 开始连接
      this._startConnect();
    })
}
```

#### 连接状态处理
```javascript
_connectionHandler = (connectionState) => {
  switch(connectionState.state) {
    case MISSConnectState.MISS_Connection_Connected:
      this._realStartVideo(); // 连接成功，启动视频
      break;
    case MISSConnectState.MISS_Connection_Disconnected:
      this.handleDisconnected(connectionState.error); // 处理断开连接
      break;
    case MISSConnectState.MISS_Connection_ReceivedFirstFrame:
      // 收到首帧，隐藏加载界面
      this.setState({ showDefaultBgView: false, showLoadingView: false });
      break;
  }
}
```

### 5. 视频播放控制

#### 启动视频播放
```javascript
_realStartVideo() {
  // 1. 停止并重新启动渲染
  this.cameraGLView && this.cameraGLView.stopRender();
  this.cameraGLView && this.cameraGLView.startRender();
  
  // 2. 发送视频开始命令
  Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
    .then((retCode) => {
      // 3. 处理音频
      if (!this.state.isMute || this.state.isRecording) {
        this.cameraGLView.startAudioPlay();
      }
      
      // 4. 设置视频质量
      this.sendResolutionCmd(this.state.resolution);
    })
}
```

#### 停止所有操作
```javascript
_stopAll(showPauseView = false, setUnitMute = true, needSetPlayToolBar = true) {
  this._stopRecord();           // 停止录制
  this._stopCall();            // 停止通话
  this._toggleAudio(true, setUnitMute); // 停止音频
  CameraPlayer.getInstance().stopVideoPlay(); // 停止视频播放
  this.cameraGLView.stopRender(); // 停止渲染
}
```

#### 分辨率控制
```javascript
_setIndexOfResolution(resolution) {
  // 根据不同设备型号设置分辨率索引
  if (CameraConfig.Model_chuangmi_086ac1 == Device.model) {
    // 086设备：3档画质 (自动/480P/2.5K)
  } else if (CameraConfig.Model_xiaomi_096ac1 == Device.model) {
    // 096设备：4档画质 (自动/480P/1080P/2.5K)
  } else {
    // 其他设备的原有逻辑
  }
}
```

## 🎮 用户交互功能

### 1. 音频通话功能

#### 开始通话
```javascript
_startCall() {
  this.setState({ isCalling: true });
  this.cameraGLView.startAudioRecord();
  // 发送通话开始命令
  Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {});
}
```

#### 停止通话  
```javascript
_stopCall() {
  this.setState({ isCalling: false });
  this.cameraGLView.stopAudioRecord();
  // 发送通话停止命令
  Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {});
}
```

### 2. 录制和截图

#### 开始录制
```javascript
_startRecord() {
  // 1. 检查权限
  if (!this._checkStoragePermission()) return;
  
  // 2. 设置录制状态
  this.setState({ isRecording: true });
  
  // 3. 开始录制
  this.cameraGLView.startRecord();
}
```

#### 截图功能
```javascript
_startSnapshot() {
  // 1. 检查权限
  if (!this._checkStoragePermission()) return;
  
  // 2. 执行截图
  this.cameraGLView.snapshot()
    .then((path) => {
      this.setState({ screenshotPath: path, screenshotVisiblity: true });
    });
}
```

### 3. 云台控制 (PTZ)

#### 方向控制
- 支持上下左右移动
- 支持连续移动和点击移动
- 支持速度控制

#### 预设位置
```javascript
_getPreSetPosition() {
  // 获取预设位置列表
  Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_PTZ_REQ, {
    operation: 'get_preset'
  });
}
```

#### 角度查询
```javascript
_getRotateAngle() {
  if (CameraConfig.isXiaomiCamera(Device.model)) {
    // 小米摄像头使用MOTOR_REQ命令
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_MOTOR_REQ, {
      operation: 6
    });
  } else {
    // 其他摄像头使用RDT命令
    Service.miotcamera.sendRDTCommandToDevice(base64Data);
  }
}
```

### 4. 手势交互

#### 拖拽手势 (方向盘控制)
```javascript
_createPanResponder() {
  this.panResponder = PanResponder.create({
    onPanResponderMove: (evt) => {
      // 计算拖拽距离，控制方向盘显示/隐藏
      let delta = pageY - this._touchStartPageY;
      let newHeight = this._touchStartCtrHeight + delta;
      this.state.controlViewHeight.setValue(newHeight);
    },
    onPanResponderRelease: () => {
      // 根据拖拽距离决定最终状态
      let shouldShow = this._controlViewHeight >= threshold;
      this._showDirectionViewAnimated(shouldShow);
    }
  });
}
```

#### 缩放手势 (视频缩放)
```javascript
_createScalePanResponder() {
  this.scalePanResponder = PanResponder.create({
    onPanResponderMove: (evt) => {
      if (evt.nativeEvent.changedTouches.length == 2) {
        // 双指缩放逻辑
        let dist = Math.sqrt(dx * dx + dy * dy);
        let videoHeightScale = this._panCurLen / this._panStartLen;
        this.setState({ videoHeightScale });
      }
    }
  });
}
```

## 🎨 UI渲染结构

### 主要渲染方法

```javascript
render() {
  return (
    <View style={styles.main}>
      {this._renderVideoLayout()}      // 视频显示区域
      {this._renderControlLayout()}    // 控制按钮区域  
      {this._renderGlobalLoading()}    // 全局加载状态
      {this._renderTimeoutDialog()}    // 超时对话框
      
      {/* 各种功能对话框 */}
      <DeviceOfflineDialog ref="powerOfflineDialog" />
      <NoNetworkDialog ref="noNetworkDialog" />
      {this._renderLogUploaderDialog()}
      {this._renderPermissionDialog()}
      {this._renderVisitInfoDialog()}
      {this._renderSDCardFormatDialog()}
      {this._renderCallDialog()}
      {this._renderToEmpowerDialog()}
      {this._renderAlarmGuideDialog()}
      {this._renderServerWarning()}
    </View>
  );
}
```

### 功能按钮渲染

#### 语音通话按钮
```javascript
_renderVoiceButton(style) {
  return (
    <MHLottieVoiceButton
      style={style}
      onPress={() => {
        if (this.state.isCalling) {
          this._stopCall();
        } else {
          this._startCall();
        }
      }}
      displayState={this.state.isCalling ? 
        MHLottieVoiceBtnDisplayState.CHATTING : 
        MHLottieVoiceBtnDisplayState.NORMAL}
      disabled={this.state.isSleep || this.state.showErrorView}
    />
  );
}
```

#### 截图按钮
```javascript
_renderSnapButton(style) {
  return (
    <MHLottieSnapButton
      style={style}
      onPress={() => {
        this._startSnapshot();
      }}
      disabled={this.state.isSleep || this.state.showErrorView}
    />
  );
}
```

#### 录制按钮
```javascript
_renderRecordButton(style) {
  return (
    <MHLottieRecordButton
      style={style}
      onPress={() => {
        if (this.state.isRecording) {
          this._stopRecord();
        } else {
          this._startRecord();
        }
      }}
      displayState={this.state.isRecording ? 
        MHLottieRecordBtnDisplayState.RECORDING : 
        MHLottieRecordBtnDisplayState.NORMAL}
    />
  );
}
```

### 选项视图渲染

#### 主要功能入口
```javascript
_renderOptionsView() {
  // 看家功能
  let item1 = {
    source: require('../../Resources/Images/icon_kanjia.png'),
    title: LocalizedStrings["camera_housekeeping"],
    subTitle: kanjiaSubtitle,
    onPress: () => {
      this.props.navigation.navigate("AlarmVideoUI", params);
    }
  };
  
  // 回看功能
  let item2 = {
    source: require('../../Resources/Images/icon_huikan.png'),
    title: LocalizedStrings["camera_playback"],
    onPress: () => {
      this._handleSdcardClick();
    }
  };
  
  // 云存储功能
  let item3 = {
    source: require('../../Resources/Images/icon_yuncun.png'),
    title: LocalizedStrings["camera_cloud"],
    onPress: () => {
      this._showCloundStorage();
    }
  };
}
```

## 🔐 权限和安全管理

### 隐私授权管理

#### 隐私对话框检查
```javascript
checkPrivacyDialog() {
  // 1. 检查是否需要弹出隐私授权对话框
  if (StorageKeys.IS_PRIVACY_NEEDED) {
    this.setState({ showPrivacyDialog: true });
  } else {
    this.onPrivacyAuthed();
  }
}
```

#### 权限检查
```javascript
_checkStoragePermission() {
  if (Platform.OS === "android") {
    return PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
    );
  } else {
    return System.permission.request("photos");
  }
}
```

### 用户权限控制

#### 权限级别
- **设备所有者** (`Device.isOwner`): 完整功能权限
- **共享用户**: 受限功能权限，部分功能不可用
- **VIP用户** (`this.isVip`): 云存储等高级功能
- **只读共享** (`Device.isReadonlyShared`): 仅查看权限

#### PIN码验证
```javascript
_bindPinCodeSwitchChangedEvent() {
  this.pinCodeSwitchChangedListener = DeviceEvent.pinCodeSwitchChanged.addListener(
    (device, result) => {
      this.isPinCodeSet = result.isSetPinCode;
      if (!this.isPinCodeSet) {
        Package.exit(); // 未设置密码则退出插件
      }
    }
  );
}
```

## 📊 数据管理

### 本地存储管理

#### 设置加载
```javascript
loadLocalSetttings() {
  // VIP状态
  StorageKeys.IS_VIP_STATUS.then((res) => {
    this.isVip = res;
    CameraConfig.isVip = res;
  });
  
  // 视频分辨率
  StorageKeys.LIVE_VIDEO_RESOLUTION.then((result) => {
    this.setState({ resolution: result });
  });
  
  // 畸变纠正
  StorageKeys.IS_LENS_DISTORTION_CORREECTION.then((res) => {
    this.setState({ useLenCorrent: res });
  });
  
  // 云台手势转动
  StorageKeys.IS_PTZ_ROTATION_ENABLE.then((res) => {
    this.enablePtzRotation = res;
  });
}
```

### 设备特性检测

#### 设备型号适配
```javascript
constructor(props) {
  // 设备特性检测
  this.isPtz = CameraConfig.isPTZCamera(Device.model);
  this.isHorizontalPTZ = CameraConfig.isHorizontalPTZ(Device.model);
  this.isNewChuangmi = CameraConfig.isNewChuangmi(Device.model);
  this.isSupportPhysicalCover = CameraConfig.isSupportPhysicalCover(Device.model);
  
  // 分辨率支持检测
  this.isSupport2K = CameraConfig.isSupport2K(Device.model);
  this.isSupport25K = CameraConfig.isSupport25K(Device.model);
  this.isSupport3K = CameraConfig.isSupport3K(Device.model);
}
```

#### 功能支持检测
- **云台功能**: `CameraConfig.isPTZCamera()`
- **云存储**: `CameraConfig.isSupportCloud()`
- **人脸识别**: `CameraConfig.isSupportFaceRecognition()`
- **AI功能**: `CameraConfig.isSupportAI()`
- **物理遮挡**: `CameraConfig.isSupportPhysicalCover()`

## 🚨 错误处理和状态提示

### 连接错误处理

#### 断开连接处理
```javascript
handleDisconnected(error) {
  switch(error) {
    case MISSError.MISS_NO_ERROR:
      // 正常断开
      break;
    case MISSError.MISS_ERR_NETWORK:
      // 网络错误
      this.setState({ showErrorView: true, errTextString: "网络连接失败" });
      break;
    case MISSError.MISS_ERR_TIMEOUT:
      // 超时错误
      this.setState({ showTimeoutDialog: true });
      break;
  }
}
```

### 状态提示对话框

#### 设备状态对话框
- **设备离线**: `DeviceOfflineDialog`
- **网络异常**: `NoNetworkDialog`  
- **设备休眠**: `_renderSleepNotifyDialog()`
- **固件更新**: 显示更新进度和状态

#### SD卡状态对话框
- **SD卡格式化**: `_renderSDCardFormatDialog()`
- **SD卡已满**: `_renderSDCardFullDialog()`
- **SD卡容量小**: `_renderSDCardSmallDialog()`

#### 权限提示对话框
- **存储权限**: `_renderPermissionDialog()`
- **相册权限**: 截图和录制需要的权限
- **授权提示**: `_renderToEmpowerDialog()`

## 🔄 业务流程

### 典型启动流程

```mermaid
graph TD
    A[组件初始化] --> B[检查隐私授权]
    B --> C{需要授权?}
    C -->|是| D[显示隐私对话框]
    C -->|否| E[权限验证]
    D --> E
    E --> F{需要PIN码?}
    F -->|是| G[PIN码验证]
    F -->|否| H[网络检查]
    G --> H
    H --> I[设备连接]
    I --> J[视频启动]
    J --> K[功能就绪]
```

### 页面切换流程

```mermaid
graph TD
    A[页面失焦] --> B[停止渲染和播放]
    B --> C[保存状态到本地]
    C --> D[清理资源]
    D --> E[移除监听器]
    E --> F[页面恢复]
    F --> G[重新建立连接]
    G --> H[恢复播放状态]
```

## 📈 性能优化

### 资源管理
- **定时器清理**: 组件卸载时清理所有定时器
- **监听器管理**: 及时移除事件监听器
- **内存释放**: 销毁不需要的实例和对象

### 连接优化
- **连接复用**: 复用摄像头P2P连接
- **重连机制**: 自动重连和错误恢复
- **状态缓存**: 缓存连接状态和设备信息

### UI优化
- **懒加载**: 按需渲染UI组件
- **动画优化**: 使用原生动画提升性能
- **状态更新**: 避免不必要的状态更新

## 🎯 总结

LiveVideoPageV2.js是一个功能完整、架构复杂的摄像头直播页面组件，具有以下特点：

### 优势
1. **功能全面**: 涵盖视频播放、音频通话、录制截图、云台控制等所有核心功能
2. **架构清晰**: 模块化设计，职责分离明确
3. **兼容性强**: 支持多种设备型号和平台
4. **用户体验**: 丰富的交互方式和状态提示
5. **安全可靠**: 完善的权限管理和错误处理

### 技术特点
- **复杂的生命周期管理**: 处理多种页面状态切换
- **多平台适配**: iOS/Android平台差异处理
- **丰富的手势交互**: 拖拽、缩放、长按等手势支持
- **动画效果**: 流畅的UI动画和状态转换
- **设备适配**: 支持多种摄像头型号和功能特性
- **完善的错误处理**: 网络异常、设备离线等各种异常情况

这个组件是小米摄像头插件的核心，承载了用户与摄像头交互的主要功能，代码质量和功能完整性都很高，是一个优秀的大型React Native组件实现。
