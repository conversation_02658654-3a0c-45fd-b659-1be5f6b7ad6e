# CardCamera v2 米家插件功能文档

## 📋 项目概述

**CardCamera v2** 是小米智能摄像头的米家插件应用，基于 React Native 开发，提供完整的摄像头控制、监控、AI功能等服务。该插件支持多种型号的小米摄像头设备，为用户提供统一的智能摄像头管理体验。

### 基本信息

| 项目属性 | 值 |
|---------|-----|
| **插件名称** | CardCamera v2 |
| **包名** | com.xiaomi.cardcamera.v2 |
| **当前版本** | 1.0.28 |
| **最低SDK版本** | 10072 |
| **React Native版本** | 0.61.0 |
| **开发框架** | MIOT SDK |

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     米家APP     │ ←→ │   CardCamera    │ ←→ │   智能摄像头    │
│                 │    │     插件        │    │     设备        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   MIOT SDK      │
                    │  (原生能力层)    │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   云服务平台    │
                    │  (数据存储)     │
                    └─────────────────┘
```

### 核心模块架构

```
CardCamera v2
├── 🎥 直播监控模块
│   ├── 实时视频流
│   ├── 双向语音通话
│   └── 画质选择控制
├── 🤖 AI智能功能模块
│   ├── 人脸识别
│   ├── 宝宝哭声检测
│   ├── 宠物识别
│   ├── 运动检测
│   ├── 手势通话
│   └── 整点报时
├── 💾 存储管理模块
│   ├── 云存储服务
│   ├── SD卡存储
│   └── 本地相册
├── 🚨 报警消息模块
│   ├── 报警视频播放
│   ├── 报警列表查看
│   └── 推送消息处理
├── ⚙️ 设备设置模块
│   ├── 摄像头参数设置
│   ├── 网络配置
│   ├── 固件升级
│   └── 设备信息管理
└── 📱 用户界面模块
    ├── 导航路由系统
    ├── 通用UI组件
    └── 多语言支持
```

## 🔧 核心功能详解

### 1. 📹 直播监控功能

#### 主要特性
- **实时视频流播放**: 支持 H.264/H.265 视频编码
- **画质智能调节**: 自动、480P、720P、1080P、2.5K、3K 多档画质
- **双向语音通话**: 支持与设备端实时语音对讲
- **录制功能**: 支持实时录制保存到本地
- **截图功能**: 一键截取当前画面
- **云台控制**: 支持 PTZ 摄像头的方向控制
- **全屏模式**: 支持横屏全屏观看

#### 画质选择逻辑
插件根据设备型号智能提供不同的画质选项：

| 设备型号 | 支持画质 |
|---------|----------|
| **chuangmi.camera.086ac1** | 自动、480P流畅、2.5K高清 |
| **chuangmi.camera.069a01** | 自动、标清、高清HD、超清 |
| **chuangmi.camera.051a01** | 自动、480P流畅、720P高清、1080P超清 |
| **其他设备** | 自动、360P流畅、1080P高清 |

#### 技术实现
- 使用 `CameraPlayer` 类管理 P2P 连接和视频播放
- 通过 `sendP2PCommandToDevice` 发送画质切换命令
- 支持录制状态限制，录制中不允许切换画质

### 2. 🤖 AI智能功能

#### 2.1 人脸识别功能
- **人脸检测**: 自动识别画面中的人脸
- **家庭成员管理**: 添加、编辑、删除家庭成员信息
- **陌生人检测**: 识别未知人脸并发送警报
- **人脸事件记录**: 保存人脸识别历史记录
- **VIP功能**: 高级人脸识别功能需要VIP权限

#### 2.2 宝宝哭声检测
- **智能声音识别**: 专门针对婴儿哭声的AI算法
- **灵敏度调节**: 高、中、低三档灵敏度设置
- **实时推送**: 检测到哭声立即发送推送通知
- **录制保存**: 自动录制哭声前后的视频片段

#### 2.3 宠物识别功能
- **宠物运动检测**: 识别宠物的移动和行为
- **智能推送**: 宠物活动异常时发送通知
- **行为分析**: 分析宠物的日常行为模式

#### 2.4 运动检测功能
- **物体运动检测**: 识别画面中的移动物体
- **人员运动检测**: 专门识别人员的移动
- **声音检测**: 检测环境中的异常声音
- **咳嗽检测**: 特殊的健康监测功能
- **检测区域设置**: 可自定义检测的区域范围

#### 2.5 手势通话功能
- **手势识别**: 识别特定的手势动作
- **自动拨号**: 识别到手势后自动拨打预设电话
- **联系人管理**: 管理手势通话的联系人列表

#### 2.6 整点报时功能
- **定时语音提醒**: 在整点时发出语音提醒
- **时间段设置**: 可设置报时的时间范围
- **语音内容自定义**: 支持自定义报时内容

### 3. 💾 存储管理系统

#### 3.1 云存储服务
- **云端视频上传**: 自动上传重要视频到云端
- **云存储空间管理**: 显示云存储使用情况
- **VIP云存储**: 提供更大的云存储空间
- **视频下载**: 支持将云端视频下载到本地
- **多格式支持**: 支持 M3U8、MP4 等视频格式

#### 3.2 SD卡存储
- **SD卡状态监控**: 实时监控SD卡状态和剩余空间
- **本地录像管理**: 管理存储在SD卡中的录像文件
- **格式化功能**: 支持SD卡格式化操作
- **录制模式设置**: 支持连续录制和事件录制模式
- **时间轴回放**: 提供时间轴形式的录像回放

#### 3.3 本地相册
- **照片管理**: 管理设备拍摄的照片
- **视频管理**: 管理本地录制的视频文件
- **批量操作**: 支持批量删除、分享功能
- **文件分类**: 按时间、类型等方式分类文件

### 4. 🚨 报警消息系统

#### 4.1 报警类型
- **运动检测报警** (`smart_camera_motion`)
- **长时间无人报警** (`7.1`)
- **一键呼叫报警** (`oneKeyCall`)
- **微信通话权限请求** (`callEventRequirePermission`)
- **按键呼叫报警** (`26.3`)

#### 4.2 推送消息处理流程
```javascript
推送消息接收 → 消息类型解析 → 参数提取 → 页面跳转 → 视频播放
```

#### 4.3 报警视频功能
- **报警视频播放**: 播放触发报警的视频片段
- **相关视频推荐**: 显示同时段的其他视频
- **报警事件详情**: 显示报警的详细信息
- **快速操作**: 支持快速删除、分享等操作

### 5. ⚙️ 设备设置功能

#### 5.1 摄像头设置
- **分辨率设置**: 设置视频录制的分辨率
- **画质调节**: 调整视频的画质参数
- **水印设置**: 添加或移除视频水印
- **图像旋转**: 设置画面的旋转角度
- **WDR设置**: 宽动态范围调整
- **睡眠设置**: 定时休眠功能

#### 5.2 夜视功能设置
- **红外夜视**: 传统红外夜视模式
- **全彩夜视**: 彩色夜视模式
- **自动切换**: 根据环境光线自动切换夜视模式
- **灵敏度调节**: 调整夜视的灵敏度

#### 5.3 网络设置
- **WiFi配置**: 设置和管理WiFi连接
- **网络诊断**: 检测网络连接状态
- **带宽设置**: 调整视频传输带宽

#### 5.4 固件升级
- **自动检测**: 自动检测是否有新固件版本
- **一键升级**: 支持一键固件升级
- **升级进度**: 显示升级进度和状态
- **版本信息**: 显示当前固件版本信息

### 6. 📞 通话功能

#### 6.1 音视频通话
- **微信视频通话**: 通过微信进行视频通话
- **语音通话**: 普通语音通话功能
- **通话录制**: 支持通话过程录制
- **通话历史**: 查看通话历史记录

#### 6.2 一键呼叫
- **紧急呼叫**: 一键拨打紧急联系人
- **联系人管理**: 管理紧急联系人列表
- **呼叫记录**: 查看呼叫历史记录

## 🔧 技术架构详解

### 路由系统 (RouteProxy)
- **懒加载机制**: 避免初始化时加载所有页面，提升启动速度
- **80+ 页面管理**: 支持超过80个页面的路由管理
- **动态加载**: 首次调用返回 DummyPage，按需加载具体页面

### 框架层组件
- **Singletons**: 全局单例管理器
- **EventLoaderInf**: 定义数据加载规范的接口
- **UriPlayer**: 统一播放器接口
- **CloudEventLoader**: 云存储事件加载器
- **LocalEventLoader**: 本地事件加载器

### 工具类系统
- **CameraPlayer**: 摄像头播放控制核心类
- **CameraConfig**: 摄像头配置和设备适配管理
- **TrackUtil**: 用户行为埋点和统计工具
- **LogUtil**: 日志管理和调试工具
- **API**: 网络请求封装和错误处理

## 📱 用户界面设计

### 导航系统
- **Stack Navigation**: 基于 React Navigation 的页面导航
- **自定义动画**: 适配 Android 和 iOS 的转场动画
- **返回处理**: 智能的返回逻辑和页面栈管理

### UI组件库
- **通用组件**: 基于 mhui-rn 的UI组件库
- **自定义组件**: 针对摄像头功能定制的专用组件
- **响应式设计**: 适配不同屏幕尺寸的设备

### 多语言支持
- **国际化**: 支持中文、英文等多种语言
- **地区适配**: 根据用户地区显示相应内容
- **动态切换**: 支持运行时语言切换

## 🔒 安全与隐私

### 数据安全
- **加密传输**: 视频流和控制指令采用加密传输
- **权限验证**: 严格的设备所有者权限验证
- **VIP功能控制**: 高级功能需要VIP权限验证

### 隐私保护
- **数据匿名化**: 用户数据进行匿名化处理
- **本地存储加密**: 敏感信息采用本地加密存储
- **自动清理**: 定期清理本地缓存数据

## 📊 性能优化

### 内存管理
- **及时释放**: 视频资源使用后及时释放
- **图片缓存**: 智能的图片缓存机制
- **组件生命周期**: 合理的组件生命周期管理

### 网络优化
- **请求合并**: 合并多个相似的网络请求
- **断线重连**: 自动的网络断线重连机制
- **数据压缩**: 对传输数据进行压缩

### 渲染优化
- **虚拟列表**: 使用 FlatList 优化长列表渲染
- **图片懒加载**: 按需加载图片资源
- **避免重渲染**: 减少不必要的组件重新渲染

## 🛠️ 开发与部署

### 开发环境要求
- **Node.js**: 版本 12.13.1+
- **npm**: 版本 6.12.1+
- **React Native**: 0.61.0
- **MIOT SDK**: 最新版本

### 构建命令
```bash
# 安装依赖
npm install
cd projects/com.xiaomi.cardcamera.v2
npm install

# 启动调试
npm start com.xiaomi.cardcamera.v2

# 发布构建
npm run publish com.xiaomi.cardcamera.v2
```

### 第三方依赖
- **dayjs**: 时间处理库
- **react-native-canvas**: Canvas 绘图支持
- **react-native-image-pan-zoom**: 图片缩放功能
- **react-native-root-toast**: Toast 提示组件
- **patch-package**: 第三方库补丁管理

## 🔍 设备兼容性

### 支持的摄像头型号

| 设备型号 | 特殊功能支持 |
|---------|-------------|
| **chuangmi.camera.086ac1** | 2.5K高清、微信通话 |
| **chuangmi.camera.069a01** | 超清模式、多级画质 |
| **chuangmi.camera.051a01** | 720P高清、一键呼叫 |
| **AI摄像头系列** | 人脸识别、AI检测 |
| **PTZ摄像头系列** | 云台控制、预置位 |
| **门铃摄像头系列** | 一键呼叫、访客记录 |

### 功能特性检测
插件会根据设备型号自动检测和启用相应功能：
- PTZ 控制功能检测
- AI 功能支持检测
- 画质档位支持检测
- 云存储功能检测

## 📈 数据统计与埋点

### 用户行为统计
- **功能使用频率**: 统计各功能的使用情况
- **页面访问统计**: 记录用户的页面访问路径
- **错误日志**: 收集和分析应用错误信息
- **性能数据**: 监控应用的性能指标

### 埋点事件示例
- `Camera_Definition_ClickNum`: 画质选择点击次数
- `Camera_Connect_Time`: 设备连接耗时
- `Camera_Live_Duration`: 直播观看时长
- `Camera_AI_Usage`: AI功能使用统计

## 🐛 常见问题解决

### 视频播放问题
- **连接失败**: 检查设备网络状态和在线状态
- **画面卡顿**: 调整画质设置或检查网络带宽
- **声音异常**: 检查设备音频设置和权限

### 存储功能异常
- **云存储无法访问**: 验证VIP状态和网络连接
- **SD卡读取失败**: 检查SD卡状态和格式
- **下载失败**: 检查本地存储空间和网络状态

### AI功能不工作
- **功能未启用**: 检查AI功能开关状态
- **设备不支持**: 确认设备型号是否支持AI功能
- **固件版本过低**: 升级设备固件到最新版本

## 📞 技术支持

### 开发资源
- [MIOT SDK 官方文档](https://github.com/MiEcosystem/miot-plugin-sdk/wiki)
- [React Native 官方文档](https://reactnative.dev/docs/getting-started)
- [米家插件开发指南](https://github.com/MiEcosystem/miot-plugin-sdk)

### 问题反馈
- **技术支持**: 通过米家开发者平台提交工单
- **Bug反馈**: 在 GitHub Issues 中反馈问题
- **功能建议**: 加入米家开发者社区讨论

---

## 📋 更新日志

### v1.0.28 (当前版本)
- ✅ 优化AI功能性能和准确性
- ✅ 修复云存储管理的若干问题
- ✅ 增强国际化支持，新增多语言
- ✅ 改进用户界面设计和交互体验
- ✅ 提升视频播放的稳定性
- ✅ 优化内存使用和性能表现

### 未来规划
- 🔄 支持更多AI检测功能
- 🔄 优化视频播放性能
- 🔄 增强云存储功能
- 🔄 改进用户界面设计
- 🔄 支持更多设备型号

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**适用版本**: CardCamera v1.0.28

*本功能文档详细介绍了 CardCamera v2 米家插件的完整功能特性、技术架构和使用说明，为用户和开发者提供全面的功能参考。如有疑问或建议，欢迎通过官方渠道反馈。*
