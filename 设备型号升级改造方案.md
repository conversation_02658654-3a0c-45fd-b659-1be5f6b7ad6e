# 设备型号升级改造方案

## 概述
当前插件支持设备型号：`chuangmi.camera.086ac1`  
新增支持设备型号：`xiaomi.camera.096ac1`  
需要共用同一个插件，因此需要在代码中做model区分和固件版本判断。

## 1. 设备型号常量定义

### 1.1 需要修改的文件
- `Main/util/VersionUtil.js`
- `Main/util/CameraConfig.js`

### 1.2 具体修改点
```javascript
// VersionUtil.js 中添加
static Model_xiaomi_096ac1 = "xiaomi.camera.096ac1";

// CameraConfig.js 中添加
static Model_xiaomi_096ac1 = "xiaomi.camera.096ac1";
```

## 2. 功能支持判断需要修改的方法

### 2.1 视频功能相关

#### 2.1.1 480P支持 (`CameraConfig.js`)
```javascript
static isSupport480P(model) {
  return model == CameraConfig.Model_chuangmi_039a01 || model == CameraConfig.Model_chuangmi_049a01 
  || model == CameraConfig.Model_chuangmi_069a01 || model == CameraConfig.Model_chuangmi_051a01
    || model == CameraConfig.Model_chuangmi_086ac1
    || model == CameraConfig.Model_xiaomi_096ac1; // 新增
}
```

#### 2.1.2 视频参数配置 (`CameraConfig.js`)
需要在`getVideoParam`方法中为新设备添加OSD参数配置：
```javascript
case CameraConfig.Model_xiaomi_096ac1:
  videoParam.osdx = 0.xxx; // 需要根据实际设备调试确定
  videoParam.osdy = 0.xxx;
  videoParam.radius = 1.x;
  break;
```

### 2.2 通话功能相关

#### 2.2.1 新通话功能支持 (`CameraConfig.js`)
```javascript
static isNewCameraCall(model) {
  let isNew = false;
  if (model === CameraConfig.Model_chuangmi_086ac1
    || model === CameraConfig.Model_xiaomi_096ac1) { // 新增
    isNew = true;
  }
  return isNew;
}
```

#### 2.2.2 通话时间线显示 (`CameraConfig.js`)
```javascript
static displayCameraCallingTimeline(model) {
  if (VersionUtil.Model_chuangmi_051a01 == model
  || VersionUtil.Model_chuangmi_086ac1 == model
  || VersionUtil.Model_xiaomi_096ac1 == model) { // 新增
    return true;
  }
  return false;
}
```

### 2.3 存储功能相关

#### 2.3.1 SD卡V2支持 (`CameraConfig.js`)
```javascript
static supportSDCardV2(model) {
  let isSupport = false;
  if (model === CameraConfig.Model_chuangmi_069a01
    || model === CameraConfig.Model_chuangmi_086ac1
    || model === CameraConfig.Model_xiaomi_096ac1) { // 新增
    isSupport = true;
  }
  return isSupport;
}
```

### 2.4 智能功能相关

#### 2.4.1 访客信息支持 (`CameraConfig.js`)
```javascript
static isSupportVisitInfo(model) {
  if (model === this.Model_chuangmi_029 || model === this.Model_chuangmi_021
    || model === this.Model_chuangmi_069a01 || model === this.Model_chuangmi_051a01
    || model === this.Model_chuangmi_086ac1
    || model === this.Model_xiaomi_096ac1) { // 新增
    return true;
  }
  return false;
}
```

#### 2.4.2 大声音时间线显示 (`CameraConfig.js`)
```javascript
static displayLOUDER_SOUND_Timeline(model) {
  return CameraConfig.Model_chuangmi_086ac1 == model
    || CameraConfig.Model_xiaomi_096ac1 == model; // 新增
}
```

### 2.5 NAS功能相关

#### 2.5.1 NAS V2/V3升级提醒 (`CameraConfig.js`)
```javascript
static isNASV123(model) {
  if (model === 'chuangmi.camera.061a01' // c500 pro
    || model === this.Model_chuangmi_069a01
    || model === this.Model_chuangmi_086ac1 // m300
    || model === this.Model_xiaomi_096ac1) { // 新增
    return true;
  }
}
```

### 2.6 功能限制相关

#### 2.6.1 跨度敏感性支持 (`CameraConfig.js`)
```javascript
static isSupportSpanSensitivity(model) {
  let isSupport = true;
  if (model === CameraConfig.Model_chuangmi_086ac1
    || model === CameraConfig.Model_xiaomi_096ac1) { // 新增
    isSupport = false;
  }
  return isSupport;
}
```

#### 2.6.2 宝宝哭声时间线显示 (`CameraConfig.js`)
```javascript
static displayBabyCryInTimeline(model) {
  // ... 其他逻辑
  if (model == this.Model_chuangmi_086ac1
    || model == this.Model_xiaomi_096ac1) { // 新增
    return false;
  }
  return true;
}
```

#### 2.6.3 云端宝宝哭声功能 (`CameraConfig.js`)
```javascript
static CloudBabyCry(model = Device.model) {
  if (model === CameraConfig.Model_chuangmi_046c04 
    || model === CameraConfig.Model_chuangmi_039a04 
    || model === CameraConfig.Model_chuangmi_086ac1
    || model === CameraConfig.Model_xiaomi_096ac1 // 新增
    || !this.isVip) {
    return false;
  }
  return true;
}
```

## 3. 固件版本判断相关

### 3.1 音量功能版本检查 (`VersionUtil.js`)
```javascript
static CameraVolumeVersion(model, version) {
  // ... 其他设备逻辑
  if (model == CameraConfig.Model_chuangmi_086ac1
    || model == CameraConfig.Model_xiaomi_096ac1) { // 新增
    return true;
  }
  return true;
}
```

### 3.2 Spec协议使用判断 (`VersionUtil.js`)
```javascript
static isUsingSpec(model) {
  if (model === this.Model_Chuangmi_021a04 
    // ... 其他model判断
    || model === CameraConfig.Model_chuangmi_086ac1
    || model === CameraConfig.Model_xiaomi_096ac1 // 新增
  ) {
    return true;
  } else {
    return false;
  }
}
```

## 4. 需要确认的功能差异点

### 4.1 需要产品确认的功能
1. **AI功能支持**：新设备是否支持人脸识别、宠物识别等AI功能
2. **云存储功能**：是否支持云存储服务
3. **物理遮挡功能**：是否支持物理遮挡盖
4. **PTZ功能**：是否支持云台控制
5. **人形追踪**：是否支持人形追踪功能
6. **蓝牙网关**：是否支持蓝牙网关功能

### 4.2 需要技术确认的参数
1. **视频OSD参数**：osdx, osdy, radius具体数值
2. **固件版本要求**：各功能对应的最低固件版本
3. **硬件能力差异**：与086ac1相比的硬件能力差异

## 5. 建议的实施步骤

### 5.1 第一阶段：基础支持
1. 添加设备型号常量定义
2. 添加基础功能支持判断
3. 配置视频参数（需要实际设备调试）

### 5.2 第二阶段：功能验证
1. 逐个验证各功能在新设备上的表现
2. 根据实际情况调整功能支持列表
3. 完善固件版本要求

### 5.3 第三阶段：优化完善
1. 根据测试结果优化参数配置
2. 添加设备特有功能支持
3. 完善错误处理和兼容性

## 6. 风险点和注意事项

1. **向后兼容性**：确保修改不影响现有086ac1设备的功能
2. **功能一致性**：两个设备共用插件时的功能体验一致性
3. **性能影响**：model判断逻辑对性能的影响
4. **测试覆盖**：需要在两种设备上都进行充分测试

## 7. 相关文件清单

### 7.1 核心配置文件
- `Main/util/VersionUtil.js` - 版本和设备型号工具类
- `Main/util/CameraConfig.js` - 摄像头配置工具类

### 7.2 功能模块文件
- `Main/setting/Setting.js` - 设置页面
- `Main/setting/AISetting.js` - AI设置页面
- `Main/aicamera/` - AI功能相关页面
- `Main/alarm/AlarmPage.js` - 报警页面
- `Main/live/AudioVideoCallPage.js` - 音视频通话页面

### 7.3 其他相关文件
- `Main/API.js` - API调用封装
- `Main/util/RPC.js` - RPC调用封装
- 各种设置和功能页面
