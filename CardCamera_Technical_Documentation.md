# CardCamera 米家插件技术文档

## 项目概述

CardCamera 是一个基于 React Native 的米家智能摄像头插件项目，支持多种摄像头设备的实时监控、录像回放、AI 功能等核心功能。

### 基本信息
- **项目名称**: CardCamera v2
- **包名**: com.xiaomi.cardcamera.v2
- **版本**: 1.0.28
- **最低 SDK API Level**: 10072
- **React Native 版本**: 0.61.0
- **开发框架**: MIOT SDK

## 项目架构

### 整体架构图

```mermaid
graph TB
    A[米家APP] --> B[CardCamera插件]
    B --> C[主页面模块]
    B --> D[设置模块]
    B --> E[存储模块]
    B --> F[AI功能模块]
    B --> G[报警模块]
    
    C --> C1[直播页面 LiveVideoPageV2]
    C --> C2[音视频通话 AudioVideoCallPage]
    C --> C3[一键呼叫 OneKeyCallPage]
    
    D --> D1[摄像头设置 CameraSetting]
    D --> D2[存储设置 StorageSetting]
    D --> D3[图像设置 ImageSetting]
    D --> D4[夜视设置 NightVisionSetting]
    
    E --> E1[云存储 CloudStorage]
    E --> E2[SD卡存储 SdcardStorage]
    E --> E3[本地存储 LocalStorage]
    
    F --> F1[宝宝哭声检测 BabyCrying]
    F --> F2[宠物识别 PetIdentification]
    F --> F3[运动检测 MotionDetection]
    F --> F4[手势通话 GestureCall]
    F --> F5[整点报时 OnTimeAlarm]
    
    G --> G1[报警视频 AlarmVideoUI]
    G --> G2[报警列表 AlarmPage]
    G --> G3[长时间报警 LongTimeAlarm]
```

### 核心架构组件

#### 1. 路由系统 (RouteProxy)
- **文件**: `Main/RouteProxy.js`
- **功能**: 实现页面懒加载，避免初始化时加载所有页面
- **特点**: 
  - 首次调用返回 DummyPage
  - 按需加载具体页面组件
  - 支持 80+ 页面路由管理

#### 2. 框架层 (Framework)
- **Singletons**: 单例管理器，管理全局实例
- **EventLoaderInf**: 事件加载器接口，定义数据加载规范
- **UriPlayer**: 统一播放器接口
- **CloudEventLoader**: 云存储事件加载器
- **LocalEventLoader**: 本地事件加载器

#### 3. 工具层 (Utils)
- **CameraPlayer**: 摄像头播放控制
- **CameraConfig**: 摄像头配置管理
- **TrackUtil**: 埋点统计工具
- **LogUtil**: 日志管理工具
- **API**: 网络请求封装
- **RPC**: 设备通信接口

## 主要页面功能详解

### 1. 直播页面 (LiveVideoPageV2)
**文件**: `Main/live/LiveVideoPageV2.js`

**核心功能**:
- 实时视频流播放
- 双向语音通话
- 截图和录像
- 云台控制 (PTZ)
- 画质调节
- 全屏/横屏切换

**主要状态管理**:
```javascript
state = {
  isConnected: false,        // 连接状态
  isRecording: false,        // 录制状态
  fullScreen: false,         // 全屏状态
  videoScale: 1.0,          // 视频缩放
  showControlLayout: true,   // 控制界面显示
  // ... 更多状态
}
```

**关键方法**:
- `_handleVideoClick()`: 视频点击处理
- `_handleSdcardClick()`: SD卡回看
- `_showCloundStorage()`: 云存储访问
- `_handleSnapshot()`: 截图功能

### 2. 音视频通话页面 (AudioVideoCallPage)
**文件**: `Main/live/AudioVideoCallPage.js`

**核心功能**:
- 微信视频通话
- 语音通话
- 通话录制
- 通话历史记录

### 3. 设置页面 (Setting)
**文件**: `Main/setting/Setting.js`

**功能模块**:
- 摄像头设置
- 存储设置  
- AI 功能设置
- 固件升级
- 通用设置

### 4. 存储管理 (AllStorage)
**文件**: `Main/allVideo/AllStorage.js`

**存储类型**:
- **云存储**: 支持云端视频存储和回放
- **SD卡存储**: 本地 SD 卡录像管理
- **本地存储**: 设备本地文件管理

**功能特性**:
- 多选删除
- 批量下载
- 日期筛选
- 事件类型过滤

## AI 功能模块

### 1. 宝宝哭声检测 (BabyCrying)
**文件**: `Main/aicamera/BabyCrying.js`

**功能**: 
- 检测宝宝哭声
- 推送报警通知
- 灵敏度调节

### 2. 宠物识别 (PetIdentification)  
**文件**: `Main/aicamera/PetIdentification.js`

**功能**:
- 宠物运动检测
- 宠物行为识别
- 智能推送

### 3. 运动检测 (MotionDetectionPage)
**文件**: `Main/aicamera/MotionDetectionPage.js`

**检测类型**:
- 物体运动检测
- 人员运动检测  
- 声音检测
- 咳嗽检测

**配置选项**:
- 检测开关
- 灵敏度设置 (高/中/低)
- 检测区域设置

### 4. 手势通话 (GestureCallPage)
**文件**: `Main/aicamera/GestureCallPage.js`

**功能**:
- 手势识别
- 自动拨打电话
- 联系人管理

### 5. 整点报时 (OnTimeAlarmPage)
**文件**: `Main/aicamera/OnTimeAlarmPage.js`

**功能**:
- 整点语音提醒
- 时间段设置
- 开关控制

## 存储架构

### 云存储系统
**核心类**: `CloudEventLoader`

**功能**:
- 云端视频上传/下载
- 视频列表获取
- 缩略图生成
- VIP 权限管理

### SD卡存储系统  
**核心类**: `SdcardEventLoader`

**功能**:
- SD卡状态检测
- 本地录像管理
- 时间轴回放
- 文件下载

### 本地存储系统
**核心类**: `LocalEventLoader`

**功能**:
- 相册文件管理
- 本地文件访问
- 媒体文件播放

## 报警系统

### 报警视频播放 (AlarmVideoUI)
**文件**: `Main/alarmDetail/AlarmVideoUI.js`

**功能**:
- 报警视频播放
- 报警事件详情
- 相关视频推荐

### 报警列表 (AlarmPage)  
**文件**: `Main/alarm/AlarmPage.js`

**功能**:
- 报警事件列表
- 事件类型筛选
- 日期范围选择
- 批量操作

## 推送消息处理

### 推送类型
1. **运动检测推送** (`smart_camera_motion`)
2. **长时间无人推送** (`7.1`)  
3. **一键呼叫推送** (`oneKeyCall`)
4. **微信通话推送** (`callEventRequirePermission`)

### 处理流程
```javascript
// 推送消息解析
parsePushInfo() {
  let type = Package.entryInfo.type;
  let event = Package.entryInfo.event;
  let extra = Package.entryInfo.extra;
  
  // 根据事件类型跳转对应页面
  if (event === "smart_camera_motion") {
    // 跳转报警视频页面
  } else if (event === "7.1") {
    // 跳转报警列表页面  
  }
}
```

## 技术特性

### 1. 性能优化
- 页面懒加载 (RouteProxy)
- 图片缓存管理
- 视频流优化
- 内存管理

### 2. 多语言支持
- 国际化字符串管理
- 地区适配
- 服务器环境区分

### 3. 设备兼容性
- 多型号摄像头支持
- 固件版本适配
- 功能特性检测

### 4. 数据安全
- 加密传输
- 权限验证
- 隐私保护

## 开发规范

### 代码结构
```
Main/
├── index.js              # 入口文件
├── RouteProxy.js         # 路由代理
├── live/                 # 直播相关
├── setting/              # 设置页面
├── aicamera/            # AI功能
├── allVideo/            # 存储管理
├── alarm/               # 报警功能
├── framework/           # 框架层
├── util/                # 工具类
└── components/          # 通用组件
```

### 命名规范
- 页面组件: PascalCase (如 `LiveVideoPageV2`)
- 工具类: PascalCase (如 `CameraPlayer`)  
- 常量: UPPER_SNAKE_CASE (如 `PIID_MOVE_SWITCH`)
- 方法: camelCase (如 `_handleVideoClick`)

## 部署说明

### 构建命令
```bash
# 安装依赖
npm install

# 启动调试
npm start com.xiaomi.cardcamera.v2

# 发布项目  
npm run publish com.xiaomi.cardcamera.v2
```

### 配置文件
- `project.json`: 项目基本配置
- `package.json`: 依赖管理
- `sonar-project.properties`: 代码质量检测

## 详细页面功能说明

### 摄像头设置页面 (CameraSetting)
**功能模块**:
- **图像设置**: 分辨率、画质、水印设置
- **夜视设置**: 红外夜视、全彩夜视模式
- **图像旋转**: 画面翻转、旋转角度调整
- **WDR设置**: 宽动态范围调整
- **睡眠设置**: 定时休眠功能

### 存储设置页面 (StorageSetting)
**功能模块**:
- **SD卡管理**: 格式化、状态检测、容量显示
- **录制模式**: 连续录制、事件录制
- **存储空间**: 剩余空间显示、清理功能
- **NAS设置**: 网络存储配置

### AI设置页面 (AISetting)
**AI功能开关**:
- 人脸识别开关
- 宝宝哭声检测
- 宠物识别
- 运动检测
- 声音检测

### 人脸管理页面 (FaceManager2)
**功能特性**:
- 人脸录入和管理
- 人脸识别设置
- 陌生人检测
- 家庭成员管理
- VIP功能支持

### 报警设置相关页面

#### 监控时段设置 (SurveillancePeriodSettingV2)
- 自定义监控时间段
- 工作日/周末分别设置
- 全天监控选项

#### 通知类型设置 (NotificationTypeSetting)
- 推送消息类型选择
- 消息推送时间设置
- 免打扰时段配置

#### 分区灵敏度设置 (PartitionSensitivitySetting)
- 检测区域划分
- 不同区域灵敏度调节
- 检测类型配置

## 核心工具类详解

### CameraPlayer 类
**文件**: `Main/util/CameraPlayer.js`

**核心功能**:
- P2P连接管理
- 视频流控制
- 音频通话
- 设备状态监控

**关键方法**:
```javascript
// 连接状态管理
startConnect()
stopConnect()
isConnected()

// 视频控制
startPlay()
stopPlay()
snapshot()
startRecord()
stopRecord()

// 音频控制
startTalk()
stopTalk()
```

### CameraConfig 类
**文件**: `Main/util/CameraConfig.js`

**配置管理**:
- 服务器环境配置 (国内/国际/欧洲)
- 设备型号适配
- 功能特性开关
- VIP状态管理

**重要配置项**:
```javascript
static isInternationalServer = true;  // 国际服务器
static isEuropeServer = false;        // 欧洲服务器
static isCloudServer = false;         // 云存储支持
static isVip = false;                 // VIP状态
```

### TrackUtil 类
**文件**: `Main/util/TrackUtil.js`

**埋点统计**:
- 用户行为追踪
- 功能使用统计
- 性能数据收集
- 错误日志上报

**使用示例**:
```javascript
// 点击事件统计
TrackUtil.reportClickEvent("Camera_Setting_ClickNum");

// 结果事件统计
TrackUtil.reportResultEvent("Camera_Connect_Time", "Time", useTime);
```

### API 类
**文件**: `Main/API.js`

**网络请求封装**:
- 统一请求接口
- 参数自动组装
- 错误处理
- 日志记录

**请求方法**:
```javascript
// GET请求
API.get('/path/to/api', 'subdomain', params)

// POST请求
API.post('/path/to/api', 'subdomain', data)
```

## 存储系统详细架构

### 事件加载器接口 (EventLoaderInf)
**文件**: `Main/framework/EventLoaderInf.js`

**接口定义**:
```javascript
class EventLoaderInf {
  // 获取事件列表
  async getAllEvent(aDate, aEvent, aIsMore)

  // 获取单日事件
  async getOneDayAllEvent(aDate, aEvent, aIsMore, aOrder)

  // 获取缩略图
  getThumb(aRec)

  // 下载文件
  download(aRec, aPath, aListener)

  // 删除文件
  delete(aRecs)
}
```

### 云存储加载器 (CloudEventLoader)
**文件**: `Main/framework/CloudEventLoader.js`

**核心功能**:
- 云端视频列表获取
- M3U8视频下载
- 缩略图获取
- VIP权限验证

**下载流程**:
```javascript
download(aRec, aPath, aListener) {
  // 调用原生下载接口
  Service.miotcamera.downloadM3U8ToMP4V2(
    aRec.fileId,
    aPath,
    DldCbName,
    aRec.isAlarm,
    "H265"
  );
}
```

### SD卡文件管理器 (SdFileManager)
**文件**: `Main/sdcard/util/SdFileManager.js`

**RDT协议通信**:
- 设备文件列表获取
- 缩略图下载
- 视频文件下载
- 命令队列管理

**通信流程**:
```javascript
// 发送RDT命令
Service.miotcamera.sendRDTCommandToDevice(base64Data)

// 接收数据回调
Service.miotcamera.bindRDTDataReceiveCallback(callbackName)
```

## 页面导航系统

### StackNavigationInstance
**文件**: `Main/StackNavigationInstance.js`

**全局导航管理**:
- 页面栈管理
- 跨页面通信
- 导航状态追踪

### 页面跳转示例
```javascript
// 跳转到设置页面
this.props.navigation.navigate('Setting');

// 带参数跳转
this.props.navigation.navigate('AlarmVideoUI', {
  item: videoItem,
  cfg: playConfig
});

// 返回上一页
this.props.navigation.goBack();
```

## 国际化支持

### 多语言字符串管理
**文件**: `Main/MHLocalizableString.js`

**支持语言**:
- 简体中文
- 英文
- 其他国际化语言

**使用方式**:
```javascript
import { localStrings as LocalizedStrings } from '../MHLocalizableString';

// 获取本地化字符串
const title = LocalizedStrings['camera_setting'];
```

## 设备兼容性

### 支持的摄像头型号
- **小米摄像头**: chuangmi.camera.xxx 系列
- **AI摄像头**: 支持人脸识别、宝宝哭声等AI功能
- **PTZ摄像头**: 支持云台控制功能
- **门铃摄像头**: 支持一键呼叫功能

### 功能特性检测
```javascript
// 检测是否支持PTZ
CameraConfig.isPTZCamera(Device.model)

// 检测是否支持AI功能
VersionUtil.isAiCameraModel(Device.model)

// 检测固件版本
VersionUtil.isFirmwareSupportCloud(Device.model)
```

## 数据流架构图

```mermaid
graph LR
    A[用户操作] --> B[React Native UI]
    B --> C[MIOT SDK]
    C --> D[原生模块]
    D --> E[设备通信]

    F[云服务器] --> G[API接口]
    G --> C

    H[本地存储] --> I[SQLite/文件系统]
    I --> C

    J[设备固件] --> K[P2P连接]
    K --> D
```

## 性能优化策略

### 1. 内存管理
- 及时释放视频资源
- 图片缓存优化
- 组件生命周期管理

### 2. 网络优化
- 请求合并和缓存
- 断线重连机制
- 数据压缩传输

### 3. 渲染优化
- 虚拟列表 (FlatList)
- 图片懒加载
- 避免不必要的重渲染

## 错误处理机制

### 网络错误处理
```javascript
// API请求错误处理
API.get('/api/path')
  .then(response => {
    // 处理成功响应
  })
  .catch(error => {
    Toast.fail('网络请求失败', error);
    LogUtil.logOnAll('API Error:', error);
  });
```

### 设备连接错误处理
```javascript
// P2P连接错误处理
CameraPlayer.getInstance()
  .addConnectionListener((state) => {
    if (state.state < 1) {
      // 连接断开，尝试重连
      this.handleConnectionError();
    }
  });
```

## 安全机制

### 1. 数据加密
- 视频流加密传输
- 敏感信息本地加密存储
- API通信HTTPS加密

### 2. 权限控制
- 设备所有者权限验证
- 分享用户权限限制
- VIP功能权限检查

### 3. 隐私保护
- 用户数据匿名化
- 本地数据自动清理
- 隐私协议合规检查

## 测试策略

### 单元测试
- 工具类函数测试
- 数据处理逻辑测试
- API接口测试

### 集成测试
- 页面跳转流程测试
- 设备连接测试
- 数据同步测试

### 性能测试
- 内存泄漏检测
- 启动时间测试
- 视频播放性能测试

## 开发指南

### 环境搭建
1. **安装依赖**
   ```bash
   npm install
   cd projects/com.xiaomi.cardcamera.v2
   npm install
   ```

2. **启动调试**
   ```bash
   npm start com.xiaomi.cardcamera.v2
   ```

3. **发布构建**
   ```bash
   npm run publish com.xiaomi.cardcamera.v2
   ```

### 新功能开发流程

1. **创建新页面**
   - 在对应模块目录下创建页面文件
   - 在 `RouteProxy.js` 中添加路由配置
   - 在 `index.js` 中注册页面路由

2. **添加新的AI功能**
   - 继承 `BaseSettingPage` 基类
   - 实现设备通信接口
   - 添加用户界面组件
   - 配置权限和VIP检查

3. **扩展存储功能**
   - 实现 `EventLoaderInf` 接口
   - 添加数据加载逻辑
   - 配置缓存和下载机制

### 代码规范

1. **文件命名**
   - 页面组件: `PascalCase.js`
   - 工具类: `PascalCase.js`
   - 常量文件: `UPPER_SNAKE_CASE.js`

2. **代码风格**
   - 使用 ESLint 进行代码检查
   - 遵循 React Native 最佳实践
   - 添加必要的注释和文档

3. **性能要求**
   - 避免内存泄漏
   - 优化渲染性能
   - 合理使用缓存

## 常见问题解决

### 1. 视频播放问题
- 检查P2P连接状态
- 验证设备在线状态
- 确认网络连接质量

### 2. 存储功能异常
- 检查SD卡状态
- 验证云存储权限
- 确认VIP服务状态

### 3. AI功能不工作
- 检查设备固件版本
- 验证AI功能开关状态
- 确认设备型号支持

### 4. 推送消息处理
- 检查推送消息格式
- 验证事件类型匹配
- 确认页面跳转逻辑

## 版本更新记录

### v1.0.28 (当前版本)
- 优化AI功能性能
- 修复存储管理问题
- 增强国际化支持
- 改进用户体验

### 未来规划
- 支持更多AI功能
- 优化视频播放性能
- 增强云存储功能
- 改进用户界面设计

## 技术支持

### 文档资源
- [MIOT SDK 官方文档](https://github.com/MiEcosystem/miot-plugin-sdk/wiki)
- [React Native 官方文档](https://reactnative.dev/docs/getting-started)
- [米家插件开发指南](https://github.com/MiEcosystem/miot-plugin-sdk)

### 联系方式
- 技术支持: 提交工单到米家开发者平台
- 问题反馈: GitHub Issues
- 开发交流: 米家开发者社区

---

**文档版本**: v1.0
**最后更新**: 2024年8月
**适用版本**: CardCamera v1.0.28

*本技术文档详细介绍了 CardCamera 米家插件的架构设计、功能模块、开发规范等内容，为开发者提供全面的技术参考。如有疑问或建议，欢迎反馈。*
