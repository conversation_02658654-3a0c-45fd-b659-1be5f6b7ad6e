export default {
  setting: '設定',
  featureSetting: 'デバイス設定',
  commonSetting: '一般設定',
  name: 'デバイス名',
  deviceService: 'デバイスサービス',
  location: '位置を管理',
  memberSet: 'ボタン',
  share: 'デバイスを共有',
  btGateway: 'BLE ゲートウェイ',
  voiceAuth: '音声承認',
  ifttt: '自動化',
  productBaike: '製品情報',
  firmwareUpgrade: 'ファームウェアのアップデート',
  firmwareUpdate: 'ファームウェアのアップデート',
  more: '追加設定',
  help: 'ヘルプ',
  legalInfo: '法律情報',
  deleteDevice: 'デバイスを削除',
  autoUpgrade: 'ファームウェアを自動アップデート',
  checkUpgrade: 'ファームウェアのアップデートを確認',
  security: 'セキュリティ設定',
  networkInfo: 'ネットワーク情報',
  feedback: 'フィードバック',
  timezone: 'デバイスのタイムゾーン',
  addToDesktop: 'ホーム画面に追加',
  open: 'オン',
  close: 'オフ',
  other: 'その他',
  multipleKeyShowOnHome: 'ホームページに表示されるボタンの数: {0}',
  // 常用设备
  favoriteDevices: 'Xiaomi Homeホームページに表示する',
  favoriteCamera: 'ダッシュボードカードのサイズ',
  favoriteAddDevices: 'お気に入りに追加',
  // MHDatePicker
  cancel: 'キャンセル',
  ok: '確認',
  am: '午前',
  pm: '午後',
  numberMonth: {
    'zero': '{0}か月',
    'one': '{0}か月',
    'two': '{0}か月',
    'few': '{0}か月',
    'many': '{0}か月',
    'other': '{0}か月'
  },
  numberDay: {
    'zero': '{0}日',
    'one': '{0}日',
    'two': '{0}日',
    'few': '{0}日',
    'many': '{0}日',
    'other': '{0}日'
  },
  numberHour: {
    'zero': '{0}時間',
    'one': '{0}時間',
    'two': '{0}時間',
    'few': '{0}時間',
    'many': '{0}時間',
    'other': '{0}時間'
  },
  numberMinute: {
    'zero': '{0}分',
    'one': '{0}分',
    'two': '{0}分',
    'few': '{0}分',
    'many': '{0}分',
    'other': '{0}分'
  },
  numberSecond: {
    'zero': '{0}秒',
    'one': '{0}秒',
    'two': '{0}秒',
    'few': '{0}秒',
    'many': '{0}秒',
    'other': '{0}秒'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{0}/{1}/{2}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: '終了',
  firmwareUpgradeUpdate: '更新',
  firmwareUpgradeLook: '表示',
  firmwareUpgradeForceUpdate: '現在ご使用のファームウェアは旧バージョンのため、一部の機能が正しく作動しない場合があります。より良いエクスペリエンスのために、最新バージョンに更新してください。',
  firmwareUpgradeForceUpdating: 'デバイス更新中です。後でもう一度お試しください。',
  firmwareUpgradeNew_pre: 'アップデートが利用可能です。 ',
  firmwareUpgradeNew_sub: '今すぐアップデート？',
  handling: 'お待ちください…',
  error: 'エラーが発生しました。後でもう一度お試しください。',
  createLightGroup: 'ライトグループを作成（新規）',
  manageLightGroup: 'ライトグループを管理（新規）',
  deleteLightGroup: 'ライトのグループ化を解除',
  deleteCurtainGroup: 'デバイスのグループ化を解除',
  linkDevice: 'デバイスをリンクさせる',
  noSuppurtedLinkageDevice: '利用可能なデバイスはありません',
  noSuppurtedLinkageTip: '1. Xiaomi Homeアプリでデバイスを追加し、部屋に割り当てたことを確認してください。\\n2. Bluetoothデバイスをこのデバイスの近くに置いておくと接続しやすくなります。',
  supportedLinkageDevices: '以下のデバイスとリンクできます。',
  linkageDistanceTip: 'リンクできるようにデバイスを近づけます。',
  linkageRemoveTip: 'リンクされているBluetoothデバイスを変更するには、まずそのデバイスを削除します。',
  link: 'リンク',
  removeLink: '削除',
  linkFail: 'リンクできませんでした',
  removeLinkFail: '削除できませんでした',
  linkConfirm: '今すぐこのデバイスとリンクさせますか？',
  removeLinkConfirm: '今すぐ削除しますか？',
  linking: 'リンク中…',
  linkDeviceBracelet: 'バンドをリンク',
  scanDeviceBracelet: 'バンドをスキャン中…',
  scanDeviceBraceletTip: 'うまく接続できるように、Mi Bandをこのデバイスの近くに置き、Bluetoothがオンになっていることを確認してください。',
  scanDeviceBraceletEmptyTitle: '付近にMi Bandが見つかりませんでした',
  scanDeviceBraceletEmptyTip1: '1. バンドのBluetoothがオンになっていることを確認してください。',
  scanDeviceBraceletEmptyTip2: '2. バンドをもう一方のデバイスの近くに置きます。',
  linkedDeviceBraceletHeaderTip: '以下のバンドとリンクされています。',
  availableLinkDeviceBraceletHeaderTip: '以下のバンドとリンクできます。',
  linkedDeviceBraceletFooterTip: 'リンクされているバンドを変更するには、まずそのバンドを削除します。',
  availableLinkDeviceBraceletFooterTip: 'バンドのBluetoothがオンになっていることを確認し、もう一方のデバイスの近くに置きます。',
  pluginVersion: 'プラグインバージョン',
  helpAndFeedback: 'ヘルプとフィードバック',
  offline: 'オフライン',
  downloading: 'ダウンロード中…',
  installing: 'インストール中…',
  upgradeSuccess: '正常にアップデートされました',
  upgradeFailed: 'アップデートできませんでした。後でもう一度お試しください。',
  upgradeTimeout: '更新処理がタイムアウトしました',
  autoUpgradeInfo: '{0}の間で自動アップデートを試みます',
  today: '今日',
  tomorrow: '明日',
  currentIsLatestVersion: '現在のバージョンは最新です',
  lastestVersion: '最新バージョン: ',
  currentVersion: '現在のバージョン: ',
  fetchFailed: 'アクセスできませんでした。もう一度お試しください。',
  releaseNote: 'アップデート内容',
  releaseVersionHistory: 'ファームウェアのアップデート履歴',
  firmwareAutoUpdate: 'ファームウェアの自動アップデート',
  autoUpdateDescriptionNote: '新しいファームウェアが検出されると、デバイスは{0}の間で自動アップデートを試みます。アップデートはデバイスを使用していない時にインストールされ、アップデート処理中に音声や光による通知はありません。',
  updateNow: 'アップデート',
  requireBelMesh: 'この機能が正常に動作するには、Bluetoothメッシュ ゲートウェイが必要です。',
  createCurtainGroup: 'カーテングループを作成',
  createCurtainGroupTip: '2つのカーテンモーターを組み合わせて1つにグループ化し、両面カーテンとして制御できます。',
  act: '移動',
  create: '作成',
  chooseCurtainGroupTitle: 'カーテンモーターを選択',
  currentDevice: 'このデバイス',
  curtain: 'カーテン',
  noCurtainGroupTip: '現在グループ化できません。別のカーテンモーターを追加して、もう一度お試しください。',
  switchPlugin: '標準プラグイン',
  defaultPlugin: 'デフォルト',
  selectDefaultHP: 'デフォルト',
  stdPluginTitle: '標準',
  thirdPluginTitle: '従来型',
  stdPluginSubTitle: '追加機能で旧バージョンのページに切り替え可能です',
  stdGuideDialogTitle: '新バージョンが利用可能',
  stdGuideDialogSubTitle: 'アプリをアップデートして、さらに合理的な新体験を。',
  stdGuideDialogNote: 'アップデート後に機能が見つからない場合は、「追加機能」に移動している可能性があります。',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'ボタン',
  keyLeft: '左ボタン',
  keyMiddle: '中ボタン',
  keyRight: '右ボタン',
  keyType: 'ボタンの種類',
  keyName: '名前',
  light: 'ランプ',
  updateIcon: 'アイコンを変更',
  done: '完了',
  modifyName: '名前を編集',
  keyUpdateIconTips: 'アイコンが「{0}」に切り替わると、「{0}」をオンにするようにMi AIに指示できます。',
  nameHasChars: '名前に特殊文字は使用できません',
  nameTooLong: '名前は40文字以内にしてください',
  nameIsEmpty: '名前は入力必須です',
  nameNotSupportEmoji: '名前に絵文字は使用できません',
  // 房间
  room: '部屋',
  room_nameInputTips: '部屋名を入力',
  room_nameSuggest: 'お勧めの名前',
  room_createNew: '新しい部屋を作成',
  room_bedroom: '寝室',
  room_masterBedroom: '主寝室',
  room_secondBedroom: '副寝室',
  room_kitchen: 'キッチン',
  room_diningRoom: 'ダイニング',
  room_washroom: 'トイレ',
  room_childrensRoom: '子供部屋',
  room_office: '学習部屋',
  room_study: '書斎',
  room_balcony: 'バルコニー',
  room_studio: '工作室',
  room_bathroom: '浴室',
  room_backyard: '裏庭',
  room_unassigned: '割り当てられていません',
  no_privacy_tip_content: 'プライバシーポリシーを読み込めませんでした。ネットワーク設定を確認して再試行するか、フィードバックでこの問題を報告してください。',
  moreDeviceInfo: 'デバイスの詳細情報',
  deviceNet: 'デバイスネットワーク',
  customizeName: 'カスタム名',
  software: 'ソフトウェア',
  hardware: 'ハードウェア',
  bleMeshGateway: 'Bluetoothメッシュゲートウェイ',
  deviceDid: 'デバイスID',
  deviceSN: 'デバイスSN',
  mcuVersion: 'MCUファームウェアバージョン',
  sdkVersion: 'SDKファームウェアバージョン',
  deviceModel: 'デバイスモデル',
  deviceQR: 'デバイス QR コード',
  download: 'ダウンロード',
  saveSuccess: '保存されました',
  saveFailed: '保存できませんでした',
  clipboardy: 'コピーされました',
  connected: '接続しています',
  notConnected: '接続されていません',
  bleConnected: 'Bluetoothダイレクト接続',
  deviceOffline: 'オフライン',
  deviceConsumables: 'デバイスの付属消耗品',
  consumableStateSufficient: '十分',
  consumableStateInsufficient: '不足',
  consumableStateUnknown: 'ステータス不明',
  consumableStateDepletion: '使い切りました',
  consumableStateRemainPercent: '残り{0}%',
  consumableStateEstimatedHour: {
    'zero': '残り{0}時間',
    'one': '残り{0}時間',
    'two': '残り{0}時間',
    'few': '残り{0}時間',
    'many': '残り{0}時間',
    'other': '残り{0}時間'
  },
  consumableStateEstimatedDay: {
    'zero': '残り{0}日',
    'one': '残り{0}日',
    'two': '残り{0}日',
    'few': '残り{0}日',
    'many': '残り{0}日',
    'other': '残り{0}日'
  },
  changeIcon: 'アイコンを変更',
  deviceCall: '緊急アラート',
  cloudStorage: 'クラウドストレージの通知',
  cloudStorageVip: 'クラウドメンバーシップの状況に関する通知を受け取る',
  largeCardEvent: 'キャプチャされた最新のイベントをカードに表示する',
  // 开关智能
  switch_title_controlDevice: 'デバイスを操作',
  switch_subtitle_controlDeviceType: '各ボタンのデバイスの種類を設定',
  common_listItem_value_unset: '未設定',
  switch_title_buttonControlDevice: '制御対象デバイス (${})',
  switch_listItem_title_toWirelessSwitch: 'ワイヤレススイッチに変更',
  switch_listItem_subtile_wirelessSwitchSetting: 'この機能をオンにすると、物理ボタンでスイッチを操作できなくなります。自動化には引き続き使用可能です。',
  switch_dia_msg_wirelessSwitchSetting: 'このボタンは別のアイテム (${}) に関連付けられています。ワイヤレススイッチに切り替えてご利用ください。',
  switch_listItem_title_voiceControlLoop: 'スイッチの音声コマンド',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Mi AI でスイッチを操作する',
  switch_listItem_value_voiceControlLoopOn: 'オン',
  switch_listItem_value_voiceControlLoopOff: 'オフ',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Mi AI を使って音声コマンドでスイッチを操作します。スマートライトがスイッチに接続されている場合、電源がオフになり、接続が解除される可能性があります。',
  switch_listItem_title_operationMode: '操作モード',
  switch_listItem_title_speedMode: 'スーパースピードモード',
  switch_listItem_title_standardMode: '標準モード',
  switch_listItem_subtile_speedModeDescription: '「1回押す」の自動化しか設定する必要がない場合は、このオプションを選択します。このオプションを使用すると、自動化の反応時間が向上します。',
  switch_listItem_subtile_standardModeDescription: 'デバイスに「2回押す」または「長押し」の自動化の設定が必要な場合、このオプションを選択します。',
  switch_dia_msg_speedModeMessage: 'このデバイスにはすでに「2回押す」および「長押し」の自動化が設定されています。スーパースピードモードを選択すると、これらの自動化を使用できなくなります。このまま続行しますか？',
  switch_title_selectDeviceType: 'デバイスの種類を選択',
  switch_subtitle_selectDeviceType: '${} によって制御されるデバイスの種類を選択してください',
  switch_subtitle_liveWire_selectDeviceType: '${} によって制御されるデバイスの種類を選択します。スイッチが正常に動作することを確認するために、1 つのボタンを通常のデバイスに接続したままにしておきます。',
  switch_title_deviceType_normalDevice: '通常のデバイス (スマート機能のない照明およびランプ)',
  switch_title_deviceType_smartLight: 'スマートライト',
  switch_title_deviceType_smartSwitch: 'その他のスマートスイッチ',
  switch_title_deviceType_manualScene: 'バッチ制御',
  switch_title_deviceType_otherSmartDevice: 'その他のスマートデバイス',
  switch_value_deviceType_normalDevice: '通常のデバイス',
  switch_value_deviceType_smartLight: 'スマートライト',
  switch_value_deviceType_smartSwitch: 'その他のスマートスイッチ',
  switch_value_deviceType_manualScene: 'バッチ制御',
  switch_value_deviceType_otherSmartDevice: 'その他のスマートデバイス',
  switch_button_title_seeCreatedScene: '自動化を表示',
  switch_button_title_linkSmartLight: 'スマートライトを接続',
  switch_button_title_linkSmartSwitch: 'スマートスイッチを接続',
  switch_button_title_linkManualScene: 'バッチ制御を接続',
  switch_button_title_switchNameSetting: 'ボタン名を設定',
  switch_nav_title_buttonControlLight: '制御されたスマートライト (${})',
  switch_nav_subtitle_buttonControlLight: 'スマートライトをボタンに接続してオン/オフし、オンラインに保ちます',
  switch_header_title_selectLightOrGroup: 'スマートライトまたは照明グループを選択してください',
  switch_nav_title_buttonControlSwitch: '制御されたスマートスイッチ (${})',
  switch_nav_subtitle_buttonControlSwitch: 'スイッチを接続して、別のオン/オフ インターフェースを追加します。ボタンを押すと、選択したスイッチがオンまたはオフになります。',
  switch_header_title_selectSwitch: 'スマートスイッチを選択',
  switch_nav_title_buttonControlManualScene: '指定されたバッチ制御 (${})',
  switch_nav_subtitle_buttonControlManualScene: 'ボタンを押して実行するバッチ制御を指定します',
  switch_header_title_selectManualScene: 'バッチ制御を選択',
  common_edit: '編集',
  common_reselect: 'もう一度選択',
  common_deleted: '削除済み',
  common_delete: '削除',
  common_delete_failed: '削除できませんでした。ネットワーク設定を確認して、もう一度お試しください。',
  common_setting_failed: '設定できませんでした。デバイスがネットワークに接続されているかどうかを確認し、再試行してください。',
  common_saving: '保存しています...',
  switch_listItem_title_executionType: '実行モード',
  switch_listItem_value_executionTypeCloud: 'クラウド',
  switch_listItem_value_executionTypeLocale: 'ローカル',
  switch_dia_msg_deleteScene: 'この自動化を削除しますか？',
  switch_scene_name_toggleSwitchDevice: '${} | 1 回押す | ${} | オン/オフ | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | 1 回押す | ${} | オン/オフ | ${}',
  switch_scene_name_executeManualScene: '${} | 1 回押す | ${} | 実行 | ${}-${}',
  switch_list_device_unavailable: '表示されていないデバイスはこの機能をサポートしていません',
  switch_button_subtitle_notCurrentHome: '現在のホームにはありません',
  common_list_empty: 'ここにはまだ何もありません',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'ペアリングモード',
  switch_title_buttonControlDevice_oneGang: 'デバイスコントロール'
};