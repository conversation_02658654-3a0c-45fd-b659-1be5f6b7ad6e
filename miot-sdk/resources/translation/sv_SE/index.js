export default {
  setting: 'Inställningar',
  featureSetting: 'Enhetsinställningar',
  commonSetting: 'Allmänna inställningar',
  name: '<PERSON><PERSON><PERSON>nam<PERSON>',
  deviceService: '<PERSON>hetstjänster',
  location: '<PERSON><PERSON> platser',
  memberSet: 'Knap<PERSON>',
  share: 'Dela enhet',
  btGateway: 'BLE-nod',
  voiceAuth: 'Rösttillstånd',
  ifttt: 'Automatisering',
  productBaike: 'Produktinfo.',
  firmwareUpgrade: 'Uppdatering av firmware',
  firmwareUpdate: 'Uppdatering av firmware',
  more: 'Ytterligare inställningar',
  help: 'Hjälp',
  legalInfo: 'Juridisk information',
  deleteDevice: 'Radera enhet',
  autoUpgrade: 'Uppdatera firmware automatiskt',
  checkUpgrade: 'Sök efter firmware-uppdateringar',
  security: 'Säkerhetsinställningar',
  networkInfo: 'Nätverksinformation',
  feedback: 'Feedback',
  timezone: 'Enhe<PERSON> tidszon',
  addToDesktop: '<PERSON>ä<PERSON> till på Hemskärmen',
  open: 'P<PERSON>',
  close: 'Av',
  other: 'Övrigt',
  multipleKeyShowOnHome: 'Antalet knappar som visas på hemsidan: {0}',
  // 常用设备
  favoriteDevices: 'Visa på Xiaomi Homes hemsida',
  favoriteCamera: 'Instrumentbrädans kortstorlek',
  favoriteAddDevices: 'Lägg till i favoriter',
  // MHDatePicker
  cancel: 'Avbryt',
  ok: 'Bekräfta',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} månader',
    'one': '{0} månad',
    'two': '{0} månader',
    'few': '{0} månader',
    'many': '{0} månader',
    'other': '{0} månader'
  },
  numberDay: {
    'zero': '{0} dagar',
    'one': '{0} dag',
    'two': '{0} dagar',
    'few': '{0} dagar',
    'many': '{0} dagar',
    'other': '{0} dagar'
  },
  numberHour: {
    'zero': '{0} timmar',
    'one': '{0} timma',
    'two': '{0} timmar',
    'few': '{0} timmar',
    'many': '{0} timmar',
    'other': '{0} timmar'
  },
  numberMinute: {
    'zero': '{0} min.',
    'one': '{0} min.',
    'two': '{0} min.',
    'few': '{0} min.',
    'many': '{0} min.',
    'other': '{0} min.'
  },
  numberSecond: {
    'zero': '{0} sek.',
    'one': '{0} sek.',
    'two': '{0} sek.',
    'few': '{0} sek.',
    'many': '{0} sek.',
    'other': '{0} sek.'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Lämna',
  firmwareUpgradeUpdate: 'Uppdatera',
  firmwareUpgradeLook: 'Visa',
  firmwareUpgradeForceUpdate: 'Aktuell firmware kanske är för gammal för att köra vissa funktioner. Uppdatera till senaste versionen för bättre upplevelse.',
  firmwareUpgradeForceUpdating: 'Enheten uppdaterar. Försök igen senare.',
  firmwareUpgradeNew_pre: 'En uppdatering finns tillgänglig. ',
  firmwareUpgradeNew_sub: 'Uppdatera nu?',
  handling: 'Vänta en stund…',
  error: 'Ett fel uppstod. Försök igen senare.',
  createLightGroup: 'Skapa belysningsgrupp (ny)',
  manageLightGroup: 'Hantera belysningsgrupp (ny)',
  deleteLightGroup: 'Avgruppera belysningar',
  deleteCurtainGroup: 'Avgruppera enheter',
  linkDevice: 'Länka enheter',
  noSuppurtedLinkageDevice: 'Inga enheter tillgängliga',
  noSuppurtedLinkageTip: '1. Säkerställ att du har lagt till enheterna i Xiaomi Home-appen och tilldelat dem till rum.\\n2. Håll Bluetooth-enheter nära den här enheten för att ansluta dem.',
  supportedLinkageDevices: 'Kan länkas med följande enheter:',
  linkageDistanceTip: 'Håll enheterna nära varadra för att säkerställa att de kan länkas.',
  linkageRemoveTip: 'Ta bort enheten först, för att byta länkad Bluetooth-enhet.',
  link: 'Länk',
  removeLink: 'Ta bort',
  linkFail: 'Kunde inte länka',
  removeLinkFail: 'Kunde inte ta bort',
  linkConfirm: 'Länka med den här enheten nu?',
  removeLinkConfirm: 'Ta bort nu?',
  linking: 'Länkar…',
  linkDeviceBracelet: 'Länkband',
  scanDeviceBracelet: 'Skannar efter band…',
  scanDeviceBraceletTip: 'Håll Mi Band när den här enheten och säkerställ att dess Bluetooth är aktiverad för att kunna ansluta.',
  scanDeviceBraceletEmptyTitle: 'Kunde inte hitta Mi-band i närheten',
  scanDeviceBraceletEmptyTip1: '1. Kontrollera att bandets bluetooth är aktiverat.',
  scanDeviceBraceletEmptyTip2: '2. Håll bandet nära den andra enheten.',
  linkedDeviceBraceletHeaderTip: 'Länkad med föjande band:',
  availableLinkDeviceBraceletHeaderTip: 'Kan länkas med följande band:',
  linkedDeviceBraceletFooterTip: 'Ta bort bandet först, för att byta länkade band.',
  availableLinkDeviceBraceletFooterTip: 'Säkerställ att bandets bluetooth är aktiverat och håll det nära den andra enheten.',
  pluginVersion: 'Plugin-version',
  helpAndFeedback: 'Hjälp och Feedback',
  offline: 'Offline',
  downloading: 'Laddar ner...',
  installing: 'Installerar...',
  upgradeSuccess: 'Har nu uppdaterats',
  upgradeFailed: 'Kunde inte uppdatera. Försök igen senare.',
  upgradeTimeout: 'Uppdateringstiden gick ut',
  autoUpgradeInfo: 'Kommer att försöka att uppdatera automatiskt mellan {0}',
  today: 'Idag',
  tomorrow: 'Imorgon',
  currentIsLatestVersion: 'Aktuell version är uppdaterad',
  lastestVersion: 'Senaste versionen: ',
  currentVersion: 'Aktuell version: ',
  fetchFailed: 'Kunde inte få åtkomst. Försök igen.',
  releaseNote: 'Vad är nytt',
  releaseVersionHistory: 'Uppdateringshistorik för firmware',
  firmwareAutoUpdate: 'Automatiska uppdateringar av firmware',
  autoUpdateDescriptionNote: 'När en ny firmware upptäcks kommer enheten att försöka att uppdatera automatiskt mellan {0}. Uppdateringen kommer att installeras när du inte använder enheten och det kommer inte att finnas några ljud- eller ljusnotifikationer under uppdateringsprocessen.',
  updateNow: 'Uppdatera',
  requireBelMesh: 'Den här funktionen kräver en Bluetooth mech-nod för att fungera normalt.',
  createCurtainGroup: 'Skapa en gardingrupp',
  createCurtainGroupTip: 'Två gardinmotorer kan kombineras till en grupp som kan styras som en dubbelsidig gardin.',
  act: 'Flytta',
  create: 'Skapa',
  chooseCurtainGroupTitle: 'Välj en gardinmotor',
  currentDevice: 'Denna enhet',
  curtain: 'Gardin',
  noCurtainGroupTip: 'Kan inte gruppera nu. Lägg till en annan gardinmotor och försök igen.',
  switchPlugin: 'Standard-plugin',
  defaultPlugin: 'Standard',
  selectDefaultHP: 'Standard',
  stdPluginTitle: 'Standard',
  thirdPluginTitle: 'Traditionell',
  stdPluginSubTitle: 'Du kan byta till den äldre versionen av sidan i extra-funktionerna',
  stdGuideDialogTitle: 'Ny version tillgänglig',
  stdGuideDialogSubTitle: 'Uppdatera appen för en ny, mer strömlinjeformad upplevelse.',
  stdGuideDialogNote: 'Om du inte kan hitta en funktion efter en uppdatering kan den ha flyttats till "Ytterligare funktioner".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Byt',
  keyLeft: 'Vänster knapp',
  keyMiddle: 'Mellanknapp',
  keyRight: 'Höger knapp',
  keyType: 'Knappsort',
  keyName: 'Namn',
  light: 'Lampa',
  updateIcon: 'Ändra ikon',
  done: 'Klar',
  modifyName: 'Redigera namn',
  keyUpdateIconTips: 'När ikonen byts till "{0}" kan du fråga Mi AI om att aktivera "{0}".',
  nameHasChars: 'Namnet kan inte innehålla specialtecken',
  nameTooLong: 'Namn kan innehålla upp till 40 tecken',
  nameIsEmpty: 'Namn kan inte vara tomt',
  nameNotSupportEmoji: 'Namn kan inte inkludera emojis',
  // 房间
  room: 'Rum',
  room_nameInputTips: 'Ange ett rumsnamn',
  room_nameSuggest: 'Rekommenderat namn',
  room_createNew: 'Skapa ett nytt rum',
  room_bedroom: 'Sovrum',
  room_masterBedroom: 'Huvudsovrum',
  room_secondBedroom: 'Andra-sovrum',
  room_kitchen: 'Kök',
  room_diningRoom: 'Matsal',
  room_washroom: 'Toalett',
  room_childrensRoom: 'Barnens rum',
  room_office: 'Studio',
  room_study: 'Bibliotek',
  room_balcony: 'Balkong',
  room_studio: 'Workshop',
  room_bathroom: 'Badrum',
  room_backyard: 'Bakgård',
  room_unassigned: 'Icke tilldelade',
  no_privacy_tip_content: 'Kunde inte ladda Integritetspolicy. Kontrollera dina nätverksinställningar och försök igen, eller rapportera det här problemet via Feedback.',
  moreDeviceInfo: 'Mer enhetsinfo.',
  deviceNet: 'Enhetens nätverk',
  customizeName: 'Anpassat namn',
  software: 'Mjukvara',
  hardware: 'Hårdvara',
  bleMeshGateway: 'Bluetooth mesh-nod',
  deviceDid: 'Enhets-ID',
  deviceSN: 'Enhets SN',
  mcuVersion: 'Version av MCU-firmware',
  sdkVersion: 'Version av SDK-firmare',
  deviceModel: 'Enhetens modell',
  deviceQR: 'Enhetens QR-kod',
  download: 'Ladda ner',
  saveSuccess: 'Sparad',
  saveFailed: 'Kunde inte spara',
  clipboardy: 'Har nu kopierats',
  connected: 'Ansluten',
  notConnected: 'Inte ansluten',
  bleConnected: 'Direkt Bluetooth-anslutning',
  deviceOffline: 'Offline',
  deviceConsumables: 'Enhetens förbrukningsvaror',
  consumableStateSufficient: 'Tillräcklig',
  consumableStateInsufficient: 'Otillräckligt',
  consumableStateUnknown: 'Status okänd',
  consumableStateDepletion: 'Använda',
  consumableStateRemainPercent: '{0} % återstår',
  consumableStateEstimatedHour: {
    'zero': '{0} timmar återstår',
    'one': '{0} timma återstår',
    'two': '{0} timmar återstår',
    'few': '{0} timmar återstår',
    'many': '{0} timmar återstår',
    'other': '{0} timmar återstår'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} dagar återstår',
    'one': '{0} dag återstår',
    'two': '{0} dagar återstår',
    'few': '{0} dagar återstår',
    'many': '{0} dagar återstår',
    'other': '{0} dagar återstår'
  },
  changeIcon: 'Ändra ikon',
  deviceCall: 'Varningar vid nödsituationer',
  cloudStorage: 'Notifikationer om molnlagring',
  cloudStorageVip: 'Ta emot notifikationer om medlemsstatus för molnet',
  largeCardEvent: 'Visa senaste inspelade händelser på kortet',
  // 开关智能
  switch_title_controlDevice: 'Kontrollenheter',
  switch_subtitle_controlDeviceType: 'Ange enhetstyper för varje knapp',
  common_listItem_value_unset: 'Inte inställt',
  switch_title_buttonControlDevice: 'Styrda enheter (${})',
  switch_listItem_title_toWirelessSwitch: 'Byt till trådlös strömbrytare',
  switch_listItem_subtile_wirelessSwitchSetting: 'Fysiska knappar kommer inte att kunna kontrollera strömbrytarna när den här funktionen är aktiverad. Du kommer fortfarande att använda dem för automatiseringar.',
  switch_dia_msg_wirelessSwitchSetting: 'Den här knappen är associerad med ett annat objekt (${}). Byt till trådlös strömbrytare för att använda den.',
  switch_listItem_title_voiceControlLoop: 'Röstkommandon för strömbrytare',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Styr strömbrytare med Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'På',
  switch_listItem_value_voiceControlLoopOff: 'Avstängd',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Styr strömbrytare med röstkommandon via Mi AI. Om smart belysning är ansluten till strömbrytarna kan de stängas av och kopplas bort.',
  switch_listItem_title_operationMode: 'Driftsläge',
  switch_listItem_title_speedMode: 'Superhastighetsläge',
  switch_listItem_title_standardMode: 'Standardläge',
  switch_listItem_subtile_speedModeDescription: 'Välj det här alternativet om automatisering endast behövs för att ange "enkeltryck". Det här alternativet kommer att förbättra automatiseringens svarstid.',
  switch_listItem_subtile_standardModeDescription: 'Välj det här alternativet om enheten behöver ställas in för automatiseringar som innebär "dubbeltryckning" eller "tryck och håll inne"',
  switch_dia_msg_speedModeMessage: 'Den här enheten har redan angivna automatiseringar som innebär "dubbeltryckning" och "tryck och håll inne". Om du väljer "superhastighetsläge" kommer du inte att kunna använda de här automatiseringarna längre. Fortsätt ändå?',
  switch_title_selectDeviceType: 'Välj enhetstyp',
  switch_subtitle_selectDeviceType: 'Välj enhetstyp styrd av ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Välj enhetstyp styrd av ${}. Lämna en knapp ansluten till vanliga enheter för att säkerställa att strömbrytaren fungerar normalt.',
  switch_title_deviceType_normalDevice: 'Vanliga enheter (belysning och lampor utan smart-funktion)',
  switch_title_deviceType_smartLight: 'Smart belysning',
  switch_title_deviceType_smartSwitch: 'Andra smarta strömbrytare',
  switch_title_deviceType_manualScene: 'Batch-kontroller',
  switch_title_deviceType_otherSmartDevice: 'Andra smarta enheter',
  switch_value_deviceType_normalDevice: 'Vanliga enheter',
  switch_value_deviceType_smartLight: 'Smart belysning',
  switch_value_deviceType_smartSwitch: 'Andra smarta strömbrytare',
  switch_value_deviceType_manualScene: 'Batch-kontroller',
  switch_value_deviceType_otherSmartDevice: 'Andra smarta enheter',
  switch_button_title_seeCreatedScene: 'Visa automatiseringar',
  switch_button_title_linkSmartLight: 'Anslut smart belysning',
  switch_button_title_linkSmartSwitch: 'Anslut smart belysning',
  switch_button_title_linkManualScene: 'Anslut batch-hantering',
  switch_button_title_switchNameSetting: 'Ange knappnamn',
  switch_nav_title_buttonControlLight: 'Styrda smart-belysningar (${})',
  switch_nav_subtitle_buttonControlLight: 'Anslut smarta belysningar till en knapp för att aktivera och avaktivera dem och hålla dem anslutna till internet',
  switch_header_title_selectLightOrGroup: 'Välj en smart belysning, eller belysningsrupp',
  switch_nav_title_buttonControlSwitch: 'Styrda smart-strömbrytare (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Anslut strömbrytarna för att lägga till ett annat på/av-gränssnitt. Att trycka på knappen kommer att aktivera och avaktivera de utvalda strömbrytarna.',
  switch_header_title_selectSwitch: 'Välj smarta strömbrytare',
  switch_nav_title_buttonControlManualScene: 'Tilldelade batch-kontroller (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Tilldela batch-kontroller för att köra dem genom att trycka på knappen.',
  switch_header_title_selectManualScene: 'Välj batch-kontroller',
  common_edit: 'Redigera',
  common_reselect: 'Välj igen',
  common_deleted: 'Raderad',
  common_delete: 'Radera',
  common_delete_failed: 'Kunde inte radera. Kontrollera dina nätverksinställningar och försök igen.',
  common_setting_failed: 'Kunde inte ange. Kontrollera om enheten är ansluten till nätverket och försök igen.',
  common_saving: 'Sparar...',
  switch_listItem_title_executionType: 'Aktivt läge',
  switch_listItem_value_executionTypeCloud: 'Moln',
  switch_listItem_value_executionTypeLocale: 'Lokal',
  switch_dia_msg_deleteScene: 'Radera den här automatiseringen?',
  switch_scene_name_toggleSwitchDevice: '${} | Enkeltryck | ${} | På/Av | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Enkeltryck | ${} | På/Av | ${}',
  switch_scene_name_executeManualScene: '${} | Enkeltryck | ${} | Aktiv | ${}-${}',
  switch_list_device_unavailable: 'Enheter som inte visas, stöder inte den här funktionen',
  switch_button_subtitle_notCurrentHome: 'Inte i det aktuella hemmet',
  common_list_empty: 'Inget här än',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Parkopplingsläge',
  switch_title_buttonControlDevice_oneGang: 'Enhetskontroller'
};