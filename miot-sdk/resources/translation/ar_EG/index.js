export default {
  setting: 'الإعدادات',
  featureSetting: 'إعدادات الجهاز',
  commonSetting: 'الإعدادات العامة',
  name: 'اسم الجهاز',
  deviceService: 'خدمات الجهاز',
  location: 'إدارة المواقع',
  memberSet: 'الأزرار',
  share: 'مشاركة الجهاز',
  btGateway: 'بوابة BLE',
  voiceAuth: 'ترخيص صوتي',
  ifttt: 'التشغيل الآلي',
  productBaike: 'معلومات المنتج',
  firmwareUpgrade: 'تحديث البرامج الثابتة',
  firmwareUpdate: 'تحديث البرامج الثابتة',
  more: 'الإعدادات الإضافية',
  help: 'المساعدة',
  legalInfo: 'المعلومات القانونية',
  deleteDevice: 'إزالة الجهاز',
  autoUpgrade: 'تحديث البرامج الثابتة تلقائيًا',
  checkUpgrade: 'تحقق من تحديثات البرامج الثابتة',
  security: 'إعدادات الحماية',
  networkInfo: 'معلومات الشبكة',
  feedback: 'التعليقات',
  timezone: 'المنطقة الزمنية للجهاز',
  addToDesktop: 'إضافة إلى الشاشة الرئيسية',
  open: 'مشغل',
  close: 'متوقف',
  other: 'أخرى',
  multipleKeyShowOnHome: 'عدد الأزرار التي تظهر على الصفحة الرئيسية: {0}',
  // 常用设备
  favoriteDevices: 'العرض على الصفحة الرئيسية في Xiaomi Home',
  favoriteCamera: 'حجم بطاقة لوحة المعلومات',
  favoriteAddDevices: 'إضافة إلى المفضلة',
  // MHDatePicker
  cancel: 'إلغاء',
  ok: 'تأكيد',
  am: 'ص',
  pm: 'م',
  numberMonth: {
    'zero': '{0} شهور',
    'one': '{0} شهر',
    'two': '{0} شهور',
    'few': '{0} شهور',
    'many': '{0} شهور',
    'other': '{0} شهور'
  },
  numberDay: {
    'zero': '{0} أيام',
    'one': '{0} يوم',
    'two': '{0} أيام',
    'few': '{0} أيام',
    'many': '{0} أيام',
    'other': '{0} أيام'
  },
  numberHour: {
    'zero': '{0} ساعات',
    'one': '{0} ساعة',
    'two': '{0} ساعات',
    'few': '{0} ساعات',
    'many': '{0} ساعات',
    'other': '{0} ساعات'
  },
  numberMinute: {
    'zero': '{0} دقائق',
    'one': '{0} دقيقة',
    'two': '{0} دقائق',
    'few': '{0} دقائق',
    'many': '{0} دقائق',
    'other': '{0} دقائق'
  },
  numberSecond: {
    'zero': '{0} ثواني',
    'one': '{0} ثانية',
    'two': '{0} ثواني',
    'few': '{0} ثواني',
    'many': '{0} ثواني',
    'other': '{0} ثواني'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'خروج',
  firmwareUpgradeUpdate: 'تحديث',
  firmwareUpgradeLook: 'عرض',
  firmwareUpgradeForceUpdate: 'قد يكون البرنامج الثابت الحالي قديمًا جدًا لتشغيل بعض الميزات. قم بالتحديث إلى أحدث إصدار للحصول على تجربة أفضل.',
  firmwareUpgradeForceUpdating: 'جهازك قيد التحديث، أعد المحاولة لاحقًا.',
  firmwareUpgradeNew_pre: 'هناك تحديث متاح. ',
  firmwareUpgradeNew_sub: 'تحديث الآن؟',
  handling: 'لحظة من فضلك…',
  error: 'حدث خطأ ما، يُرجى إعادة المحاولة لاحقًا.',
  createLightGroup: 'إنشاء مجموعة الإضاءة (جديد)',
  manageLightGroup: 'أدارة مجموعة الإضاءة (جديد)',
  deleteLightGroup: 'إلغاء تجميع الإضاءة',
  deleteCurtainGroup: 'إلغاء تجميع الأجهزة',
  linkDevice: 'ربط الأجهزة',
  noSuppurtedLinkageDevice: 'لا توجد أجهزة متاحة',
  noSuppurtedLinkageTip: '1. تأكد من إضافة الأجهزة في Xiaomi Home وتخصيصها للغرف .\\n2. حافظ على قرب مسافة أجهزة البلوتوث من هذا الجهاز لتوصيلها بنجاح.',
  supportedLinkageDevices: 'يمكن ربطه بالأجهزة التالية:',
  linkageDistanceTip: 'احتفظ بالأجهزة بالقرب منك للتأكد من قدرتها على الربط.',
  linkageRemoveTip: 'لتغيير جهاز البلوتوث المرتبط، قم بحذف الجهاز أولًا.',
  link: 'رابط',
  removeLink: 'حذف',
  linkFail: 'تعذر الربط',
  removeLinkFail: 'تعذر الحذف',
  linkConfirm: 'الربط بهذا الجهاز الآن؟',
  removeLinkConfirm: 'حذف الآن؟',
  linking: 'جاري الربط…',
  linkDeviceBracelet: 'ربط السوار',
  scanDeviceBracelet: 'البحث عن سوار…',
  scanDeviceBraceletTip: 'احتفظ بسوار Mi Band بالقرب من هذا الجهاز وتأكد من تشغيل البلوتوث للاتصال بنجاح.',
  scanDeviceBraceletEmptyTitle: 'تعذر العثور على أجهزة Mi Band قريبة',
  scanDeviceBraceletEmptyTip1: '1. تأكد من تشغيل البلوتوث الخاص بالسوار.',
  scanDeviceBraceletEmptyTip2: '2. احتفظ بالسوار بالقرب من الجهاز الآخر.',
  linkedDeviceBraceletHeaderTip: 'مرتبطة بالأساور التالية:',
  availableLinkDeviceBraceletHeaderTip: 'يمكن ربطه بالأساور التالية:',
  linkedDeviceBraceletFooterTip: 'لتغيير السوار المرتبط، أزل السوار أولًا.',
  availableLinkDeviceBraceletFooterTip: 'تأكد من تشغيل البلوتوث الخاص بالسوار واحتفظ به بالقرب من الجهاز الآخر.',
  pluginVersion: 'نسخة المكوِّن الإضافي',
  helpAndFeedback: 'المساعدة والتعليقات',
  offline: 'غير متصل',
  downloading: 'جاري التحميل…',
  installing: 'جاري التثبيت…',
  upgradeSuccess: 'تم التحديث بنجاح',
  upgradeFailed: 'تعذر التحديث، حاول مرة أخرى لاحقًا.',
  upgradeTimeout: 'انتهت مهلة التحديث',
  autoUpgradeInfo: 'سنحاول التحديث تلقائيًا بين {0}',
  today: 'اليوم',
  tomorrow: 'غداً',
  currentIsLatestVersion: 'الإصدار الحالي محدث',
  lastestVersion: 'أحدث إصدار: ',
  currentVersion: 'الإصدار الحالي: ',
  fetchFailed: 'تعذر الوصول. حاول مرة أخري.',
  releaseNote: 'ما الجديد',
  releaseVersionHistory: 'سجل تحديث البرنامج الثابت',
  firmwareAutoUpdate: 'تحديثات تلقائية للبرنامج الثابت',
  autoUpdateDescriptionNote: 'بمجرد اكتشاف برنامج ثابت جديد، سيحاول الجهاز التحديث تلقائيًا بين {0}. سيتم تثبيت التحديث في حالة عدم استخدامك للجهاز ولن يكون هناك إشعارات صوتية أو ضوئية أثناء عملية التحديث.',
  updateNow: 'تحديث',
  requireBelMesh: 'تتطلب هذه الميزة بوابة شبكة بلوتوث لتعمل بشكل طبيعي.',
  createCurtainGroup: 'إنشاء مجموعة ستارة',
  createCurtainGroupTip: 'يمكن دمج محركي ستارة في مجموعة يمكن التحكم فيها كستارة مزدوجة الجوانب.',
  act: 'تحريك',
  create: 'إنشاء',
  chooseCurtainGroupTitle: 'اختر محرك ستارة',
  currentDevice: 'هذا الجهاز',
  curtain: 'ستارة',
  noCurtainGroupTip: 'لا يمكن التجميع الآن. أضف محرك ستارة آخر وحاول مرة أخرى.',
  switchPlugin: 'ملحق المكون الإضافي',
  defaultPlugin: 'افتراضي',
  selectDefaultHP: 'افتراضي',
  stdPluginTitle: 'قياسي',
  thirdPluginTitle: 'تقليدي',
  stdPluginSubTitle: 'يمكنك الانتقال إلى الإصدار القديم للصفحة من الميزات الإضافية',
  stdGuideDialogTitle: 'هناك إصدار جديد متوفر',
  stdGuideDialogSubTitle: 'تم تحديث التطبيق إلى تجربة جديدة أكثر انسيابية.',
  stdGuideDialogNote: 'إذا لم تتمكن من العثور على مِيزة بعد التحديث، فربما تم نقلها إلى "ميزات إضافية".',
  stdGuideDialogButtonOK: 'موافق',
  // 多键开关设置
  key: 'مفتاح',
  keyLeft: 'المفتاح الأيسر',
  keyMiddle: 'المفتاح الأوسط',
  keyRight: 'المفتاح الأيمن',
  keyType: 'نوع المفتاح',
  keyName: 'الاسم',
  light: 'مصباح',
  updateIcon: 'تغيير الرمز',
  done: 'تم',
  modifyName: 'تعديل الاسم',
  keyUpdateIconTips: 'عند تبديل الرمز إلى "{0}"، يمكنك أن تطلب من مساعد Mi AI الذكي تشغيل "{0}".',
  nameHasChars: 'لا يجوز أن يحتوي الاسم على أحرف خاصة',
  nameTooLong: 'يمكن أن يحتوي الاسم على حتى 40 حرفًا',
  nameIsEmpty: 'لا يُمكن أن يكون الاسم فارغًا',
  nameNotSupportEmoji: 'لا يجوز أن تتضمن الأسماء رموزًا تعبيرية',
  // 房间
  room: 'غرفة',
  room_nameInputTips: 'أدخل اسم غرفة',
  room_nameSuggest: 'الاسم الموصى به',
  room_createNew: 'إنشاء غرفة جديدة',
  room_bedroom: 'غرفة النوم',
  room_masterBedroom: 'غرفة النوم الرئيسية',
  room_secondBedroom: 'غرفة النوم الثانية',
  room_kitchen: 'المطبخ',
  room_diningRoom: 'غرفة الطعام',
  room_washroom: 'الحمام',
  room_childrensRoom: 'غرفة الأطفال',
  room_office: 'غرفة الدراسة',
  room_study: 'المكتبة',
  room_balcony: 'الشرفة',
  room_studio: 'الورشة',
  room_bathroom: 'الحمام',
  room_backyard: 'الحديقة الخلفية',
  room_unassigned: 'غير مخصص',
  no_privacy_tip_content: 'تعذر تحميل سياسة الخصوصية. تحقّق من إعدادات الشبكة ثم حاول مجددًا أو أبلغ عن هذه المشكلة عبر التعليقات.',
  moreDeviceInfo: 'مزيد من معلومات الجهاز',
  deviceNet: 'شبكة الجهاز',
  customizeName: 'اسم مخصص',
  software: 'البرامج',
  hardware: 'معدات الجهاز',
  bleMeshGateway: 'بوابة شبكة البلوتوث',
  deviceDid: 'معرف الجهاز',
  deviceSN: 'الرقم التسلسلي للجهاز',
  mcuVersion: 'إصدار البرنامج الثابت MCU',
  sdkVersion: 'إصدار البرنامج الثابت SDK',
  deviceModel: 'طراز الجهاز',
  deviceQR: 'رمز QR الخاص بالجهاز',
  download: 'تنزيل',
  saveSuccess: 'تم الحفظ بنجاح',
  saveFailed: 'تعذَّر الحفظ',
  clipboardy: 'تم النسخ بنجاح',
  connected: 'متصل',
  notConnected: 'غير متصل',
  bleConnected: 'اتصال مباشر بالبلوتوث',
  deviceOffline: 'غير متصل',
  deviceConsumables: 'مستلزمات الجهاز',
  consumableStateSufficient: 'كافية',
  consumableStateInsufficient: 'غير كافية',
  consumableStateUnknown: 'الحالة غير معروفة',
  consumableStateDepletion: 'مستخدمة',
  consumableStateRemainPercent: '{0}% متبقية',
  consumableStateEstimatedHour: {
    'zero': '{0} ساعات متبقية',
    'one': '{0} ساعة متبقية',
    'two': '{0} ساعات متبقية',
    'few': '{0} ساعات متبقية',
    'many': '{0} ساعات متبقية',
    'other': '{0} ساعات متبقية'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} أيام متبقية',
    'one': '{0} يوم متبقي',
    'two': '{0} أيام متبقية',
    'few': '{0} أيام متبقية',
    'many': '{0} أيام متبقية',
    'other': '{0} أيام متبقية'
  },
  changeIcon: 'تغيير الأيقونة',
  deviceCall: 'تنبيهات الطوارئ',
  cloudStorage: 'إشعارات التخزين السحابية',
  cloudStorageVip: 'تلقي إشعارات حول حالة عضوية السحابة',
  largeCardEvent: 'عرض آخر الأحداث المسجلة على البطاقة',
  // 开关智能
  switch_title_controlDevice: 'التحكم في الأجهزة',
  switch_subtitle_controlDeviceType: 'اضبط نوع الجهاز لكل زر',
  common_listItem_value_unset: 'غير مضبوط',
  switch_title_buttonControlDevice: 'الأجهزة التي يتم التحكم فيها (${})',
  switch_listItem_title_toWirelessSwitch: 'تبديل إلى مفتاح لاسلكي',
  switch_listItem_subtile_wirelessSwitchSetting: 'لن تتمكن الأزرار المادية من التحكم في المفاتيح عند تشغيل هذه الميزة. وسيظل بإمكانك استخدامها في عمليات التشغيل التلقائي.',
  switch_dia_msg_wirelessSwitchSetting: 'هذا الزر مرتبط بعنصر آخر (${}). لاستخدامه، قم بتغييره إلى مفتاح لاسلكي.',
  switch_listItem_title_voiceControlLoop: 'الأوامر الصوتية للمفاتيح',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'مفاتيح التحكم مع مساعد Mi AI الذكي',
  switch_listItem_value_voiceControlLoopOn: 'مشغل',
  switch_listItem_value_voiceControlLoopOff: 'متوقف',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'مفاتيح التحكم باستخدام الأوامر الصوتية عبر مساعد Mi AI الذكي. إذا كانت المصابيح الذكية متصلة بالمفتاح، فقد يتم إيقاف تشغيلها وفصلها.',
  switch_listItem_title_operationMode: 'وضع التشغيل',
  switch_listItem_title_speedMode: 'وضع السرعة الفائقة',
  switch_listItem_title_standardMode: 'الوضع القياسي',
  switch_listItem_subtile_speedModeDescription: 'حدد هذا الخيار إذا كانت عمليات التشغيل التلقائي تحتاج فقط في إعدادها إلى "ضغطة واحدة". سيؤدي هذا الخيار إلى تحسين وقت استجابة التشغيل التلقائي.',
  switch_listItem_subtile_standardModeDescription: 'حدد هذا الخيار إذا كان الجهاز يحتاج إلى إعداد عمليات التشغيل التلقائي ب "الضغط المزدوج" أو "الضغط مطولًا".',
  switch_dia_msg_speedModeMessage: 'بالنسبة لهذا الجهاز، لقد تم ضبط الأتمتة مسبقا على "النقر المزدوج" و"الضغط المستمر". إذا قمت بتحديد وضع التسريع الفائق، فلن تتمكن بعد الآن من استخدام عمليات الأتمتة هذه. هل مازلت تريد المتابعة؟',
  switch_title_selectDeviceType: 'تحديد نوع الجهاز',
  switch_subtitle_selectDeviceType: 'حدد نوع الجهاز الذي يتحكم فيه ${}',
  switch_subtitle_liveWire_selectDeviceType: 'حدد نوع الجهاز الذي يتم التحكم فيه من ${}. اترك زرًا واحدًا متصلاً بالأجهزة الشائعة لضمان تشغيل المفتاح بشكل صحيح.',
  switch_title_deviceType_normalDevice: 'الأجهزة المشتركة (أضواء ومصابيح بدون حل ذكي)',
  switch_title_deviceType_smartLight: 'أضواء ذكية',
  switch_title_deviceType_smartSwitch: 'مفاتيح ذكية أخرى',
  switch_title_deviceType_manualScene: 'التحكم في مجموعات',
  switch_title_deviceType_otherSmartDevice: 'أجهزة مشتركة أخرى',
  switch_value_deviceType_normalDevice: 'أجهزة عادية',
  switch_value_deviceType_smartLight: 'أضواء ذكية',
  switch_value_deviceType_smartSwitch: 'مفاتيح ذكية أخرى',
  switch_value_deviceType_manualScene: 'التحكم في مجموعات',
  switch_value_deviceType_otherSmartDevice: 'أجهزة مشتركة أخرى',
  switch_button_title_seeCreatedScene: 'عرض الأتمتة',
  switch_button_title_linkSmartLight: 'قم بتوصيل الأضواء الذكية',
  switch_button_title_linkSmartSwitch: 'قم بتوصيل المفتاح الذكي',
  switch_button_title_linkManualScene: 'ربط التحكم في مجموعة',
  switch_button_title_switchNameSetting: 'تعيين اسم الزر',
  switch_nav_title_buttonControlLight: 'الأضواء الذكية التي يتم التحكم فيها (${})',
  switch_nav_subtitle_buttonControlLight: 'قم بتوصيل الأضواء الذكية بزر واحد لتشغيلها وإيقافها وإبقائها متصلة بالإنترنت',
  switch_header_title_selectLightOrGroup: 'حدد ضوءًا ذكيًا أو مجموعة من الأضواء',
  switch_nav_title_buttonControlSwitch: 'المفاتيح الذكية التي يتم التحكم فيها (${})',
  switch_nav_subtitle_buttonControlSwitch: 'إرفاق مفاتيح لإضافة واجهات تشغيل/إيقاف إضافية. اضغط على الزر لتشغيل وإيقاف المفاتيح المحددة.',
  switch_header_title_selectSwitch: 'حدد المفاتيح الذكية',
  switch_nav_title_buttonControlManualScene: 'عناصر التحكم في مجموعة المعينة (${})',
  switch_nav_subtitle_buttonControlManualScene: 'قم بتعيين التحكم في مجموعات لتتمكن من التحكم فيها بضغطة زر',
  switch_header_title_selectManualScene: 'حدد التحكم في مجموعات',
  common_edit: 'تعديل',
  common_reselect: 'التحديد مرة أخرى',
  common_deleted: 'تم الحذف بنجاح',
  common_delete: 'حذف',
  common_delete_failed: 'تعذر الحذف. تحقق من إعدادات الشبكة ثم حاول مجدداً.',
  common_setting_failed: 'فشل الإعداد. تحقق مما إذا كان الجهاز متصلاً بالشبكة وحاول مرة أخرى.',
  common_saving: 'جارٍ الحفظ...',
  switch_listItem_title_executionType: 'وضع التشغيل',
  switch_listItem_value_executionTypeCloud: 'الخدمة السحابية',
  switch_listItem_value_executionTypeLocale: 'محلي',
  switch_dia_msg_deleteScene: 'حذف هذه الاتمتة؟',
  switch_scene_name_toggleSwitchDevice: '${} | ضغطة واحدة | ${} | تشغيل/إيقاف | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | ضغطة واحدة | ${} | تشغيل/إيقاف | ${}',
  switch_scene_name_executeManualScene: '${} | ضغطة واحدة | ${} | تشغيل| ${}-${}',
  switch_list_device_unavailable: 'لا تدعم الأجهزة غير المعروضة هذه الميزة',
  switch_button_subtitle_notCurrentHome: 'غير موجودة في المنزل الحالي',
  common_list_empty: 'لا شيء هنا حتى الآن',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'وضع الاقتران',
  switch_title_buttonControlDevice_oneGang: 'عناصر التحكم بالجهاز'
};