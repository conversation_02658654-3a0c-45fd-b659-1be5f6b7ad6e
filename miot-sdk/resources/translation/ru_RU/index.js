export default {
  setting: 'Настройки',
  featureSetting: 'Настройки устройства',
  commonSetting: 'Общие настройки',
  name: 'И<PERSON>я устройства',
  deviceService: 'Службы устройства',
  location: 'Управление местами',
  memberSet: 'Кнопки',
  share: 'Поделиться устройством',
  btGateway: 'Шлюз BLE',
  voiceAuth: 'Голосовая авторизация',
  ifttt: 'Автоматизация',
  productBaike: 'Информация о продукте',
  firmwareUpgrade: 'Обновление прошивки',
  firmwareUpdate: 'Обновление прошивки',
  more: 'Расширенные настройки',
  help: 'Справка',
  legalInfo: 'Юридическая информация',
  deleteDevice: 'Удалить устройство',
  autoUpgrade: 'Обновлять прошивку автоматически',
  checkUpgrade: 'Проверить наличие обновлений прошивки',
  security: 'Настройки безопасности',
  networkInfo: 'Информация о сети',
  feedback: 'Отчет',
  timezone: 'Часовой пояс устройства',
  addToDesktop: 'Добавить на рабочий стол',
  open: 'Вкл.',
  close: 'Откл.',
  other: 'Другое',
  multipleKeyShowOnHome: 'Количество кнопок, отображаемых на главной странице: {0}',
  // 常用设备
  favoriteDevices: 'Отобразить на главной странице Xiaomi Home',
  favoriteCamera: 'Размер карточки панели управления',
  favoriteAddDevices: 'Добавить в избранное',
  // MHDatePicker
  cancel: 'Отмена',
  ok: 'Подтвердить',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} месяцев',
    'one': '{0} месяц',
    'two': '{0} месяца',
    'few': '{0} месяца',
    'many': '{0} месяцев',
    'other': '{0} месяца'
  },
  numberDay: {
    'zero': '{0} дней',
    'one': '{0} день',
    'two': '{0} дня',
    'few': '{0} дня',
    'many': '{0} дней',
    'other': '{0} дня'
  },
  numberHour: {
    'zero': '{0} ч',
    'one': '{0} ч',
    'two': '{0} ч',
    'few': '{0} ч',
    'many': '{0} ч',
    'other': '{0} ч'
  },
  numberMinute: {
    'zero': '{0} мин',
    'one': '{0} мин',
    'two': '{0} мин',
    'few': '{0} мин',
    'many': '{0} мин',
    'other': '{0} мин'
  },
  numberSecond: {
    'zero': '{0} с',
    'one': '{0} с',
    'two': '{0} с',
    'few': '{0} с',
    'many': '{0} с',
    'other': '{0} с'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}.{1}.{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Выйти',
  firmwareUpgradeUpdate: 'Обновление',
  firmwareUpgradeLook: 'Просмотр',
  firmwareUpgradeForceUpdate: 'Текущая версия ПО устарела, некоторые возможности могут быть недоступны. Обновите до последней версии ПО.',
  firmwareUpgradeForceUpdating: 'Устройство обновляется. Повторите попытку позже.',
  firmwareUpgradeNew_pre: 'Доступно обновление. ',
  firmwareUpgradeNew_sub: 'Обновить сейчас?',
  handling: 'Секунду…',
  error: 'Произошла ошибка. Повторите попытку позже.',
  createLightGroup: 'Создать группу светильников (новое)',
  manageLightGroup: 'Управление группой светильников (новое)',
  deleteLightGroup: 'Разгруппировать светильники',
  deleteCurtainGroup: 'Разгруппировать устройства',
  linkDevice: 'Cвязать устройства',
  noSuppurtedLinkageDevice: 'Нет доступных устройств',
  noSuppurtedLinkageTip: '1. Убедитесь, что вы добавили устройства в приложение Xiaomi Home и закрепили их за комнатами.\\n2. Держите Bluetooth-устройства рядом с этим устройством, чтобы успешно подключить их.',
  supportedLinkageDevices: 'Может связываться со следующими устройствами:',
  linkageDistanceTip: 'Держите устройства в близко друг от друга, чтобы они смогли установить связь.',
  linkageRemoveTip: 'Чтобы изменить связанное Bluetooth-устройство, сначала удалите устройство.',
  link: 'Связать',
  removeLink: 'Удалить',
  linkFail: 'Не удалось связать',
  removeLinkFail: 'Не удалось удалить',
  linkConfirm: 'Установить связь с этим устройством?',
  removeLinkConfirm: 'Удалить сейчас?',
  linking: 'Связывание…',
  linkDeviceBracelet: 'Связать браслет',
  scanDeviceBracelet: 'Поиск браслета…',
  scanDeviceBraceletTip: 'Для успешного подключения разместите браслет Mi Band рядом с этим устройством и убедитесь, что Bluetooth браслета включен.',
  scanDeviceBraceletEmptyTitle: 'Не удалось обнаружить браслеты Mi Band поблизости',
  scanDeviceBraceletEmptyTip1: '1. Убедитесь, что Bluetooth браслета включен.',
  scanDeviceBraceletEmptyTip2: '2. Держите браслет рядом с другим устройством.',
  linkedDeviceBraceletHeaderTip: 'Связано со следующими браслетами:',
  availableLinkDeviceBraceletHeaderTip: 'Может связываться со следующими браслетами:',
  linkedDeviceBraceletFooterTip: 'Чтобы изменить связаный браслет, сначала удалите его.',
  availableLinkDeviceBraceletFooterTip: 'Убедитесь, что Bluetooth браслета включен, и разместите браслет рядом с другим устройством.',
  pluginVersion: 'Версия плагина',
  helpAndFeedback: 'Справка и отчет',
  offline: 'Не в сети',
  downloading: 'Загрузка…',
  installing: 'Установка…',
  upgradeSuccess: 'Обновление завершено',
  upgradeFailed: 'Не удалось обновить. Повторите попытку позже.',
  upgradeTimeout: 'Время ожидания обновления истекло',
  autoUpgradeInfo: 'Автоматическая попытка обновления будет выполнена между {0}',
  today: 'Сегодня',
  tomorrow: 'Завтра',
  currentIsLatestVersion: 'Обновление не требуется',
  lastestVersion: 'Последняя версия: ',
  currentVersion: 'Текущая версия: ',
  fetchFailed: 'Не удалось получить доступ. Повторите попытку.',
  releaseNote: 'Список изменений',
  releaseVersionHistory: 'Журнал обновлений прошивки',
  firmwareAutoUpdate: 'Автообновление прошивки',
  autoUpdateDescriptionNote: 'После обнаружения новой прошивки устройство попытается автоматически обновиться между {0}. Обновление устанавливается, когда вы не используете устройство, и в процессе обновления не выдается звуковых или световых уведомлений.',
  updateNow: 'Обновить',
  requireBelMesh: 'Для нормальной работы этой функции требуется Bluetooth-шлюз mesh-сети.',
  createCurtainGroup: 'Создать группу штор',
  createCurtainGroupTip: 'Два мотора штор можно объединить в группу, которой можно управлять как двусторонней шторой.',
  act: 'Переместить',
  create: 'Создать',
  chooseCurtainGroupTitle: 'Выбрать мотор шторы',
  currentDevice: 'Это устройство',
  curtain: 'Штора',
  noCurtainGroupTip: 'Сейчас не удается сгруппировать. Добавьте еще один мотор шторы и повторите попытку.',
  switchPlugin: 'Стандартный плагин',
  defaultPlugin: 'По умолчанию',
  selectDefaultHP: 'По умолчанию',
  stdPluginTitle: 'Стандартный',
  thirdPluginTitle: 'Традиционный',
  stdPluginSubTitle: 'Перейти на старую версию страницы можно в разделе «Дополнительные функции»',
  stdGuideDialogTitle: 'Доступна новая версия',
  stdGuideDialogSubTitle: 'Обновите приложение, чтобы воспользоваться новым, более удобным интерфейсом.',
  stdGuideDialogNote: 'Если вы не можете найти функцию после обновления, возможно, она была перемещена в «Дополнительные функции».',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Переключатель',
  keyLeft: 'Левый переключатель',
  keyMiddle: 'Средний переключатель',
  keyRight: 'Правый переключатель',
  keyType: 'Тип переключателя',
  keyName: 'Имя',
  light: 'Лампа',
  updateIcon: 'Изменить значок',
  done: 'Готово',
  modifyName: 'Изменить имя',
  keyUpdateIconTips: 'Когда значок меняется на «{0}», вы можете попросить Mi AI включить «{0}».',
  nameHasChars: 'Имя не может содержать специальные символы',
  nameTooLong: 'Имя может содержать до 40 символов',
  nameIsEmpty: 'Имя не может быть пустым',
  nameNotSupportEmoji: 'Имена не могут содержать эмодзи',
  // 房间
  room: 'Комната',
  room_nameInputTips: 'Введите имя комнаты',
  room_nameSuggest: 'Рекомендованное имя',
  room_createNew: 'Создать новую комнату',
  room_bedroom: 'Спальня',
  room_masterBedroom: 'Главная спальня',
  room_secondBedroom: 'Вторая спальня',
  room_kitchen: 'Кухня',
  room_diningRoom: 'Столовая',
  room_washroom: 'Туалет',
  room_childrensRoom: 'Детская',
  room_office: 'Кабинет',
  room_study: 'Библиотека',
  room_balcony: 'Балкон',
  room_studio: 'Мастерская',
  room_bathroom: 'Ванная',
  room_backyard: 'Задний двор',
  room_unassigned: 'Не назначено',
  no_privacy_tip_content: 'Не удалось загрузить Политику конфиденциальности. Проверьте настройки сети и повторите попытку или сообщите об этой проблеме с помощью функции «Отчет».',
  moreDeviceInfo: 'Дополнительная информация об устройстве',
  deviceNet: 'Сеть устройства',
  customizeName: 'Пользовательское имя',
  software: 'Программное обеспечение',
  hardware: 'Аппаратное обеспечение',
  bleMeshGateway: 'Bluetooth-шлюз мesh-сети',
  deviceDid: 'Ид. устройства',
  deviceSN: 'Серийный номер устройства',
  mcuVersion: 'Версия прошивки MCU',
  sdkVersion: 'Версия прошивки SDK',
  deviceModel: 'Модель устройства',
  deviceQR: 'QR-код устройства',
  download: 'Скачать',
  saveSuccess: 'Сохранено',
  saveFailed: 'Не удалось сохранить',
  clipboardy: 'Скопировано',
  connected: 'Подключено',
  notConnected: 'Не подключено',
  bleConnected: 'Прямое Bluetooth-соединение',
  deviceOffline: 'Не в сети',
  deviceConsumables: 'Расходные материалы для устройства',
  consumableStateSufficient: 'Достаточно',
  consumableStateInsufficient: 'Недостаточно',
  consumableStateUnknown: 'Неизвестное состояние',
  consumableStateDepletion: 'Израсходовано',
  consumableStateRemainPercent: 'Осталось {0}%',
  consumableStateEstimatedHour: {
    'zero': 'Осталось {0} часов',
    'one': 'Остался {0} час',
    'two': 'Осталось {0} часа',
    'few': 'Осталось {0} часа',
    'many': 'Осталось {0} часов',
    'other': 'Осталось {0} часа'
  },
  consumableStateEstimatedDay: {
    'zero': 'Осталось {0} дней',
    'one': 'Остался {0} день',
    'two': 'Осталось {0} дня',
    'few': 'Осталось {0} дня',
    'many': 'Осталось {0} дней',
    'other': 'Осталось {0} дня'
  },
  changeIcon: 'Изменить значок',
  deviceCall: 'Экстренные оповещения',
  cloudStorage: 'Уведомления об облачном хранилище',
  cloudStorageVip: 'Получать уведомления о статусе членства в облаке',
  largeCardEvent: 'Показывать последние зафиксированные события на карточке',
  // 开关智能
  switch_title_controlDevice: 'Управление устройствами',
  switch_subtitle_controlDeviceType: 'Выбрать тип устройства для каждой кнопки',
  common_listItem_value_unset: 'Не выбрано',
  switch_title_buttonControlDevice: 'Управляемые устройства (${})',
  switch_listItem_title_toWirelessSwitch: 'Преобразовать в беспроводной переключатель',
  switch_listItem_subtile_wirelessSwitchSetting: 'Физические кнопки не смогут управлять выключателями, если эта функция включена. Вы по-прежнему сможете использовать их для автоматизации.',
  switch_dia_msg_wirelessSwitchSetting: 'Эта кнопка связана с другим объектом (${}). Переключитесь на беспроводной переключатель, чтобы воспользоваться кнопкой.',
  switch_listItem_title_voiceControlLoop: 'Голосовые команды для переключателей',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Управляйте выключателями с помощью Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Вкл.',
  switch_listItem_value_voiceControlLoopOff: 'Откл.',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Управляйте выключателями с помощью голосовых команд через Mi AI. Если к выключателю подключены смарт-светильники, они могут быть выключены и отсоединены.',
  switch_listItem_title_operationMode: 'Режим эксплуатации',
  switch_listItem_title_speedMode: 'Суперскоростной режим',
  switch_listItem_title_standardMode: 'Стандартный режим',
  switch_listItem_subtile_speedModeDescription: 'Выберите этот вариант, если автоматизацию необходимо настроить только на «Одно нажатие». Эта опция улучшит время отклика автоматизации.',
  switch_listItem_subtile_standardModeDescription: 'Выберите этот вариант, если на устройстве необходимо настроить автоматизацию «Двойное нажатие» или «Нажмите и удерживайте»',
  switch_dia_msg_speedModeMessage: 'На этом устройстве уже настроена автоматизация «Двойное нажатие» и «Нажмите и удерживайте». Если вы выберете сверхскоростной режим, вы больше не сможете использовать эту автоматизацию. Продолжить?',
  switch_title_selectDeviceType: 'Выберите тип устройства',
  switch_subtitle_selectDeviceType: 'Выберите тип устройства под управлением ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Выберите тип устройства под управлением ${}. Оставьте одну кнопку подключенной к обычным устройствам, чтобы убедиться, что переключатель работает правильно.',
  switch_title_deviceType_normalDevice: 'Обычные устройства (светильники и лампы без смарт-функций)',
  switch_title_deviceType_smartLight: 'Умные светильники',
  switch_title_deviceType_smartSwitch: 'Другие смарт-переключатели',
  switch_title_deviceType_manualScene: 'Групповое управление',
  switch_title_deviceType_otherSmartDevice: 'Другие смарт-устройства',
  switch_value_deviceType_normalDevice: 'Обычные устройства',
  switch_value_deviceType_smartLight: 'Умные светильники',
  switch_value_deviceType_smartSwitch: 'Другие смарт-переключатели',
  switch_value_deviceType_manualScene: 'Групповое управление',
  switch_value_deviceType_otherSmartDevice: 'Другие смарт-устройства',
  switch_button_title_seeCreatedScene: 'Показать сценарии',
  switch_button_title_linkSmartLight: 'Подключить умный светильник',
  switch_button_title_linkSmartSwitch: 'Подключить смарт-переключатель',
  switch_button_title_linkManualScene: 'Подключить групповое управление',
  switch_button_title_switchNameSetting: 'Выберите имя кнопки',
  switch_nav_title_buttonControlLight: 'Управляемые умные светильники (${})',
  switch_nav_subtitle_buttonControlLight: 'Подключите умные светильники к кнопке, чтобы включать и выключать их, а также оставлять в режиме онлайн',
  switch_header_title_selectLightOrGroup: 'Выберите умный светильник или группу светильников',
  switch_nav_title_buttonControlSwitch: 'Управляемые смарт-переключатели (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Подключите переключатели, чтобы добавить еще один интерфейс включения/выключения. Нажатие кнопки включает и выключает выбранные переключатели.',
  switch_header_title_selectSwitch: 'Выберите смарт-переключатели',
  switch_nav_title_buttonControlManualScene: 'Назначенные элементы группового управления (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Назначьте элементы группового управления для их запуска при помощи нажатия кнопки',
  switch_header_title_selectManualScene: 'Выберите элементы группового управления',
  common_edit: 'Изменить',
  common_reselect: 'Повторите выбор',
  common_deleted: 'Удалено',
  common_delete: 'Удалить',
  common_delete_failed: 'Ошибка удаления. Проверьте настройки сети и повторите попытку.',
  common_setting_failed: 'Не удалось выбрать. Убедитесь, что устройство подключено к сети и повторите попытку.',
  common_saving: 'Сохранение…',
  switch_listItem_title_executionType: 'Режим запуска',
  switch_listItem_value_executionTypeCloud: 'Oблако',
  switch_listItem_value_executionTypeLocale: 'Локально',
  switch_dia_msg_deleteScene: 'Удалить этот сценарий?',
  switch_scene_name_toggleSwitchDevice: '${} | Одно нажатие | ${} | Вкл./откл. | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Одно нажатие | ${} | Вкл./откл. | ${}',
  switch_scene_name_executeManualScene: '${} | Одно нажатие | ${} | Запуск | ${}-${}',
  switch_list_device_unavailable: 'Скрытые устройства не поддерживают эту функцию',
  switch_button_subtitle_notCurrentHome: 'Не в текущем доме',
  common_list_empty: 'Здесь ничего нет',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Режим сопряжения',
  switch_title_buttonControlDevice_oneGang: 'Управление устройством'
};