export default {
  setting: '설정',
  featureSetting: '기기 설정',
  commonSetting: '일반 설정',
  name: '기기 이름',
  deviceService: '기기 서비스',
  location: '위치 관리',
  memberSet: '버튼',
  share: '기기 공유',
  btGateway: 'BLE 게이트웨이',
  voiceAuth: '음성 권한',
  ifttt: '자동화',
  productBaike: '제품 정보',
  firmwareUpgrade: '펌웨어 업데이트',
  firmwareUpdate: '펌웨어 업데이트',
  more: '추가 설정',
  help: '도움말',
  legalInfo: '법률 정보',
  deleteDevice: '기기 삭제',
  autoUpgrade: '펌웨어 자동 업데이트',
  checkUpgrade: '펌웨어 업데이트 확인',
  security: '보안 설정',
  networkInfo: '네트워크 정보',
  feedback: '피드백',
  timezone: '기기 시간대',
  addToDesktop: '홈 화면에 추가',
  open: '켜짐',
  close: '꺼짐',
  other: '기타',
  multipleKeyShowOnHome: '홈 페이지에 표시할 버튼 수: {0}',
  // 常用设备
  favoriteDevices: 'Xiaomi Home 홈페이지에 표시',
  favoriteCamera: '대시보드 카드 크기',
  favoriteAddDevices: '즐겨 찾기에 추가',
  // MHDatePicker
  cancel: '취소',
  ok: '확인',
  am: '오전',
  pm: '오후',
  numberMonth: {
    'zero': '{0}개월',
    'one': '{0}개월',
    'two': '{0}개월',
    'few': '{0}개월',
    'many': '{0}개월',
    'other': '{0}개월'
  },
  numberDay: {
    'zero': '{0}일',
    'one': '{0}일',
    'two': '{0}일',
    'few': '{0}일',
    'many': '{0}일',
    'other': '{0}일'
  },
  numberHour: {
    'zero': '{0}시간',
    'one': '{0}시간',
    'two': '{0}시간',
    'few': '{0}시간',
    'many': '{0}시간',
    'other': '{0}시간'
  },
  numberMinute: {
    'zero': '{0}분',
    'one': '{0}분',
    'two': '{0}분',
    'few': '{0}분',
    'many': '{0}분',
    'other': '{0}분'
  },
  numberSecond: {
    'zero': '{0}초',
    'one': '{0}초',
    'two': '{0}초',
    'few': '{0}초',
    'many': '{0}초',
    'other': '{0}초'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: '종료',
  firmwareUpgradeUpdate: '업데이트',
  firmwareUpgradeLook: '보기',
  firmwareUpgradeForceUpdate: '현재 펌웨어 버전이 너무 오래되었습니다. 몇몇 기능이 정상적으로 작동하지 않을 수 있습니다. 더 나은 사용을 위해 최신 버전으로 업데이트 하십시오.',
  firmwareUpgradeForceUpdating: '기기를 업데이트 중입니다. 나중에 다시 시도하세요.',
  firmwareUpgradeNew_pre: '업데이트 사용 가능. ',
  firmwareUpgradeNew_sub: '지금 업데이트할까요?',
  handling: '잠시 기다려주십시오…',
  error: '오류가 발생했습니다. 나중에 다시 시도하세요.',
  createLightGroup: '조명 그룹 생성 (신규)',
  manageLightGroup: '조명 그룹 관리 (신규)',
  deleteLightGroup: '조명 그룹 해제',
  deleteCurtainGroup: '기기 그룹 해제',
  linkDevice: '기기 링크',
  noSuppurtedLinkageDevice: '사용 가능한 기기 없음',
  noSuppurtedLinkageTip: '1. 기기를 Xiaomi Home 앱에 추가하고 방을 지정하십시오.\\n2. 블루투스 기기를 해당 기기와 가까운 곳에 두고 연결하십시오.',
  supportedLinkageDevices: '다음 기기에 연결 가능:',
  linkageDistanceTip: '기기를 근접 거리에 놓고 링크되었는지 확인하세요.',
  linkageRemoveTip: '링그된 블루투스 기기를 변경하려면 먼저 해당 기기를 제거해야 합니다.',
  link: '링크',
  removeLink: '제거',
  linkFail: '링크 실패',
  removeLinkFail: '제거 실패',
  linkConfirm: '지금 이 기기를 링크하시겠습니까?',
  removeLinkConfirm: '지금 제거하시겠습니까?',
  linking: '링크 중...',
  linkDeviceBracelet: '밴드 링크',
  scanDeviceBracelet: '밴드 스캔 중...',
  scanDeviceBraceletTip: '기기를 근접 거리에 놓고 블루투스를 켜서 성공적으로 링크하세요.',
  scanDeviceBraceletEmptyTitle: '주변의 Mi 밴드를 찾을 수 없음',
  scanDeviceBraceletEmptyTip1: '1. 밴드의 블루투스가 켜져 있는지 확인하세요.',
  scanDeviceBraceletEmptyTip2: '2. 밴드와 다른 기기의 거리를 가깝게 유지하세요.',
  linkedDeviceBraceletHeaderTip: '다음 기기에 링크됨:',
  availableLinkDeviceBraceletHeaderTip: '다음 기기에 링크 가능:',
  linkedDeviceBraceletFooterTip: '링그된 밴드를 변경하려면 먼저 해당 밴드를 제거해야 합니다.',
  availableLinkDeviceBraceletFooterTip: '밴드의 블루투스가 켜져 있는지 확인하고 다른 기기와 가까운 위치를 유지하세요.',
  pluginVersion: '플러그인 버전',
  helpAndFeedback: '도움말 & 피드백',
  offline: '오프라인',
  downloading: '다운로드 중...',
  installing: '설치 중…',
  upgradeSuccess: '업데이트 성공',
  upgradeFailed: '업데이트 실패, 나중에 다시 시도하세요.',
  upgradeTimeout: '업데이트 시간 초과',
  autoUpgradeInfo: '{0} 사이에 자동으로 업데이트 시도',
  today: '오늘',
  tomorrow: '내일',
  currentIsLatestVersion: '현재 버전은 최신입니다',
  lastestVersion: '최신 버전: ',
  currentVersion: '현재 버전: ',
  fetchFailed: '액세스 실패, 다시 시도하세요.',
  releaseNote: '새 소식',
  releaseVersionHistory: '펌웨어 업데이트 기록',
  firmwareAutoUpdate: '자동 펌웨어 업데이트',
  autoUpdateDescriptionNote: '새 펌웨어가 감지되면 기기는 {0} 사이에 자동 업데이트를 시도합니다. 업데이트는 기기를 사용하지 않을 때 설치되며 업데이트 중에는 오디오 또는 표시등 알림을 하지 않습니다.',
  updateNow: '업데이트',
  requireBelMesh: '이 앱을 정상적으로 작동하려면 블루투스 메쉬 게이트웨이가 정상 작동해야 합니다.',
  createCurtainGroup: '커튼 그룹 생성',
  createCurtainGroupTip: '두 커튼의 모터를 하나의 그룹으로 결합하면 양면 커튼으로 제어할 수 있습니다.',
  act: '이동',
  create: '생성',
  chooseCurtainGroupTitle: '커튼 모터 선택',
  currentDevice: '이 기기',
  curtain: '커튼',
  noCurtainGroupTip: '지금은 그룹으로 결합할 수 있습니다. 다른 커튼 모터를 선택하고 다시 시도하세요.',
  switchPlugin: '표준 플러그인',
  defaultPlugin: '기본 설정',
  selectDefaultHP: '기본 설정',
  stdPluginTitle: '표준',
  thirdPluginTitle: '기존',
  stdPluginSubTitle: '추가 기능에서 페이지의 이전 버전으로 전환할 수 있습니다',
  stdGuideDialogTitle: '신규 버전 사용 가능',
  stdGuideDialogSubTitle: '새롭고 더욱 원활한 경험을 위해 앱을 업데이트하세요.',
  stdGuideDialogNote: '업데이트 후에도 이 기능을 찾을 수 없으면 "추가 기능"으로 이전되었을 수 있습니다.',
  stdGuideDialogButtonOK: '확인',
  // 多键开关设置
  key: '스위치',
  keyLeft: '왼쪽 스위치',
  keyMiddle: '중간 스위치',
  keyRight: '오른쪽 스위치',
  keyType: '스위치 유형',
  keyName: '이름',
  light: '램프',
  updateIcon: '아이콘 변경',
  done: '완료',
  modifyName: '이름 수정',
  keyUpdateIconTips: '"{0}" 아이콘으로 변경하면 Mi AI에게 "{0}" 켜기를 요청할 수 있습니다.',
  nameHasChars: '이름에 특수기호를 포함할 수 없습니다.',
  nameTooLong: '이름은 최대 40자리까지 가능합니다.',
  nameIsEmpty: '이름은 비워둘 수 없습니다.',
  nameNotSupportEmoji: '이름에는 이모티콘을 사용할 수 없습니다',
  // 房间
  room: '방',
  room_nameInputTips: '방 이름을 입력합니다.',
  room_nameSuggest: '추천 이름',
  room_createNew: '새 방 생성',
  room_bedroom: '침실',
  room_masterBedroom: '메인 침실',
  room_secondBedroom: '두 번째 침실',
  room_kitchen: '주방',
  room_diningRoom: '다이닝룸',
  room_washroom: '화장실',
  room_childrensRoom: '아이 방',
  room_office: '사무실',
  room_study: '서재',
  room_balcony: '발코니',
  room_studio: '작업실',
  room_bathroom: '욕실',
  room_backyard: '뒤뜰',
  room_unassigned: '미배정',
  no_privacy_tip_content: '개인 정보 보호 정책을 로드할 수 없습니다. 네트워크 설정을 확인하고 다시 시도하거나 피드백을 통해 이 문제를 보고하세요.',
  moreDeviceInfo: '추가 기기 정보',
  deviceNet: '기기 네트워크',
  customizeName: '사용자지정 이름',
  software: '소프트웨어',
  hardware: '하드웨어',
  bleMeshGateway: '블루투스 메쉬 게이트웨이',
  deviceDid: '기기 ID',
  deviceSN: '기기 SN',
  mcuVersion: 'MCU 펌웨어 버전',
  sdkVersion: 'SDK 펌웨어 버전',
  deviceModel: '기기 모델',
  deviceQR: '기기 QR 코드',
  download: '다운로드',
  saveSuccess: '저장 성공',
  saveFailed: '저장하지 못했습니다',
  clipboardy: '복사 성공',
  connected: '연결됨',
  notConnected: '연결되지 않음',
  bleConnected: '블루투스 직접 연결',
  deviceOffline: '오프라인',
  deviceConsumables: '기기 소모품',
  consumableStateSufficient: '충분',
  consumableStateInsufficient: '부족',
  consumableStateUnknown: '상태 알 수 없음',
  consumableStateDepletion: '소진됨',
  consumableStateRemainPercent: '{0}%% 남음',
  consumableStateEstimatedHour: {
    'zero': '{0}시간 남음',
    'one': '{0}시간 남음',
    'two': '{0}시간 남음',
    'few': '{0}시간 남음',
    'many': '{0}시간 남음',
    'other': '{0}시간 남음'
  },
  consumableStateEstimatedDay: {
    'zero': '{0}일 남음',
    'one': '{0}일 남음',
    'two': '{0}일 남음',
    'few': '{0}일 남음',
    'many': '{0}일 남음',
    'other': '{0}일 남음'
  },
  changeIcon: '아이콘 변경',
  deviceCall: '긴급 경보',
  cloudStorage: '클라우드 저장소 알림',
  cloudStorageVip: '클라우드 멤버십 상태에 대한 알림 받기',
  largeCardEvent: '카드에서 캡처한 최신 이벤트 표시',
  // 开关智能
  switch_title_controlDevice: '기기 제어',
  switch_subtitle_controlDeviceType: '각 버튼의 기기 타입 설정',
  common_listItem_value_unset: '설정되지 않음',
  switch_title_buttonControlDevice: '제어되는 기기 (${})',
  switch_listItem_title_toWirelessSwitch: '무선 스위치로 변경',
  switch_listItem_subtile_wirelessSwitchSetting: '이 기능이 켜져 있으면 물리적 버튼으로 스위치를 제어할 수 없습니다. 이 경우 물리적 버튼은 자동화하는 데 사용할 수 있습니다.',
  switch_dia_msg_wirelessSwitchSetting: '이 버튼은 다른 항목 (${})과 연관되어 있습니다. 무선 스위치로 변경하여 사용하십시오.',
  switch_listItem_title_voiceControlLoop: '스위치용 음성 명령',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Mi AI로 스위치 제어하기',
  switch_listItem_value_voiceControlLoopOn: '켜짐',
  switch_listItem_value_voiceControlLoopOff: '꺼짐',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Mi AI를 통해 음성 명령으로 스위치를 제어합니다. 스마트 조명이 스위치에 연결되어 있으면 전원을 끄고 연결을 끊을 수 있습니다.',
  switch_listItem_title_operationMode: '작동 모드',
  switch_listItem_title_speedMode: '슈퍼 스피드 모드',
  switch_listItem_title_standardMode: '표준 모드',
  switch_listItem_subtile_speedModeDescription: '"한 번 누르기"에 대해서만 자동화를 설정하려는 경우 이 옵션을 선택하십시오. 이 옵션을 사용하면 자동화 응답 시간이 향상됩니다.',
  switch_listItem_subtile_standardModeDescription: '"두 번 누르기" 또는 "길게 누르기" 자동화를 기기에 설정하려는 경우 이 옵션을 선택하십시오.',
  switch_dia_msg_speedModeMessage: '이 기기에 이미 "두 번 누르기" 및 "길게 누르기" 자동화가 설정되어 있습니다. 슈퍼 스피드 모드를 선택하면 이러한 자동화를 더 이상 사용할 수 없습니다. 그래도 계속하시겠습니까?',
  switch_title_selectDeviceType: '기기 타입 선택',
  switch_subtitle_selectDeviceType: '${}(으)로 제어할 기기 타입 선택',
  switch_subtitle_liveWire_selectDeviceType: '${}(으)로 제어할 기기 타입을 선택하십시오. 스위치가 정상적으로 작동하는지 확인하기 위해 일반 기기에 하나의 버튼을 연결한 상태로 두십시오.',
  switch_title_deviceType_normalDevice: '일반 기기 (조명 및 램프 등의 스마트 기능이 없는 기기)',
  switch_title_deviceType_smartLight: '스마트 조명',
  switch_title_deviceType_smartSwitch: '기타 스마트 스위치',
  switch_title_deviceType_manualScene: '일괄 제어',
  switch_title_deviceType_otherSmartDevice: '기타 스마트 기기',
  switch_value_deviceType_normalDevice: '일반 기기',
  switch_value_deviceType_smartLight: '스마트 조명',
  switch_value_deviceType_smartSwitch: '기타 스마트 스위치',
  switch_value_deviceType_manualScene: '일괄 제어',
  switch_value_deviceType_otherSmartDevice: '기타 스마트 기기',
  switch_button_title_seeCreatedScene: '자동화 보기',
  switch_button_title_linkSmartLight: '스마트 조명 연결',
  switch_button_title_linkSmartSwitch: '스마트 스위치 연결',
  switch_button_title_linkManualScene: '일괄 제어 연결',
  switch_button_title_switchNameSetting: '버튼 이름 설정',
  switch_nav_title_buttonControlLight: '제어된 스마트 조명 (${})',
  switch_nav_subtitle_buttonControlLight: '스마트 조명을 버튼에 연결하여 켜고 끄는 동시에 온라인 상태로 유지',
  switch_header_title_selectLightOrGroup: '스마트 조명 또는 조명 그룹 선택',
  switch_nav_title_buttonControlSwitch: '제어된 스마트 스위치 (${})',
  switch_nav_subtitle_buttonControlSwitch: '다른 켜기/끄기 인터페이스를 추가하려면 스위치를 연결하십시오. 버튼을 누르면 선택한 스위치가 켜지거나 꺼집니다.',
  switch_header_title_selectSwitch: '스마트 스위치 선택',
  switch_nav_title_buttonControlManualScene: '할당된 일괄 제어 (${})',
  switch_nav_subtitle_buttonControlManualScene: '버튼을 눌러서 일괄 제어를 실행하도록 할당',
  switch_header_title_selectManualScene: '일괄 제어 선택',
  common_edit: '편집',
  common_reselect: '다시 선택',
  common_deleted: '삭제됨',
  common_delete: '삭제',
  common_delete_failed: '삭제할 수 없습니다. 네트워크 설정을 확인하시고 다시 시도하십시오.',
  common_setting_failed: '설정할 수 없습니다. 기기가 네트워크에 연결되어 있는지 확인하시고 다시 시도하십시오.',
  common_saving: '저장 중…',
  switch_listItem_title_executionType: '운행 모드',
  switch_listItem_value_executionTypeCloud: '클라우드',
  switch_listItem_value_executionTypeLocale: '로컬',
  switch_dia_msg_deleteScene: '이 자동화를 삭제하시겠습니까?',
  switch_scene_name_toggleSwitchDevice: '${} | 한 번 누르기 | ${} | 켜기/끄기 | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | 한 번 누르기 | ${} | 켜기/끄기 | ${}',
  switch_scene_name_executeManualScene: '${} | 한 번 누르기 | ${} | 실행 | ${}-${}',
  switch_list_device_unavailable: '표시되지 않은 기기는 이 기능을 지원하지 않습니다',
  switch_button_subtitle_notCurrentHome: '현재 홈에 없음',
  common_list_empty: '아직 아무것도 없습니다',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: '페어링 모드',
  switch_title_buttonControlDevice_oneGang: '기기 제어'
};