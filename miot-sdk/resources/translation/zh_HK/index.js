export default {
  setting: '設定',
  featureSetting: '功能設定',
  commonSetting: '一般設定',
  name: '裝置名稱',
  deviceService: '裝置服務',
  location: '位置管理',
  memberSet: '按鍵設定',
  share: '裝置共享',
  btGateway: '藍牙閘道',
  voiceAuth: '語音授權',
  ifttt: '智能場景',
  productBaike: '產品百科',
  firmwareUpgrade: '韌體更新',
  firmwareUpdate: '韌體更新',
  more: '更多設定',
  help: '使用説明',
  legalInfo: '法律資訊',
  deleteDevice: '刪除裝置',
  autoUpgrade: '硬件自動升級',
  checkUpgrade: '韌體更新',
  security: '安全設定',
  networkInfo: '網絡資訊',
  feedback: '反應問題',
  timezone: '裝置時區',
  addToDesktop: '增加捷徑到桌面',
  open: '開',
  close: '關',
  other: '其它',
  multipleKeyShowOnHome: '在首頁顯示為{0}個按鍵',
  // 常用设备
  favoriteDevices: '米家首頁顯示',
  favoriteCamera: '首頁卡片大小',
  favoriteAddDevices: '設為首頁常用裝置',
  // MHDatePicker
  cancel: '取消',
  ok: '確定',
  am: '上午',
  pm: '下午',
  numberMonth: {
    'zero': '{0}個月',
    'one': '{0}個月',
    'two': '{0}個月',
    'few': '{0}個月',
    'many': '{0}個月',
    'other': '{0}個月'
  },
  numberDay: {
    'zero': '{0}天',
    'one': '{0}天',
    'two': '{0}天',
    'few': '{0}天',
    'many': '{0}天',
    'other': '{0}天'
  },
  numberHour: {
    'zero': '{0}小時',
    'one': '{0}小時',
    'two': '{0}小時',
    'few': '{0}小時',
    'many': '{0}小時',
    'other': '{0}小時'
  },
  numberMinute: {
    'zero': '{0}分鐘',
    'one': '{0}分鐘',
    'two': '{0}分鐘',
    'few': '{0}分鐘',
    'many': '{0}分鐘',
    'other': '{0}分鐘'
  },
  numberSecond: {
    'zero': '{0}秒鐘',
    'one': '{0}秒鐘',
    'two': '{0}秒鐘',
    'few': '{0}秒鐘',
    'many': '{0}秒鐘',
    'other': '{0}秒鐘'
  },
  months: '個月',
  // 复数
  days: '天',
  // 复数
  hours: '小時',
  // 复数
  minutes: '分鐘',
  // 复数
  seconds: '秒鐘',
  // 复数
  month: '個月',
  // 单数
  day: '天',
  // 单数
  hour: '小時',
  // 单数
  minute: '分鐘',
  // 单数
  second: '秒鐘',
  // 单数
  yearUnit: '年',
  // 单数
  monthUnit: '月',
  // 单数
  dayUnit: '日',
  // 单数
  hourUnit: '時',
  // 单数
  minuteUnit: '分',
  // 单数
  secondUnit: '秒',
  // 单数
  dateSubTitle: '{0}年{1}月{2}日',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{0} {1}:{2}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: '退出',
  firmwareUpgradeUpdate: '升級',
  firmwareUpgradeLook: '去看看',
  firmwareUpgradeForceUpdate: '由於您當前的用戶端版本過低，一些功能可能無法正常使用。 請升級最新版本，以體驗更豐富的功能',
  firmwareUpgradeForceUpdating: '您的裝置正在升級，請稍後，以體驗更豐富的功能',
  firmwareUpgradeNew_pre: '檢測到裝置有最新韌體版本',
  firmwareUpgradeNew_sub: '，是否升級？',
  handling: '操作中...',
  error: '處理失敗，請稍後再試',
  createLightGroup: '建立燈組（新）',
  manageLightGroup: '燈組管理（新）',
  deleteLightGroup: '解散燈組',
  deleteCurtainGroup: '解散組',
  linkDevice: '連接裝置',
  noSuppurtedLinkageDevice: '當前無可連接裝置',
  noSuppurtedLinkageTip: '1.請確保在米家APP中已新增需要連接的裝置，並按要求分配到對應房間下；\n2.請將需要連接的藍牙裝置與本裝置保持在一定範圍內，否則將無法進行連接。',
  supportedLinkageDevices: '可連接如下裝置',
  linkageDistanceTip: '請將需要連接的藍牙裝置與本裝置保持在一定範圍內，否則將無法進行連接',
  linkageRemoveTip: '如需更換連接的藍牙裝置，請先解除連接',
  link: '綁定',
  removeLink: '解除綁定',
  linkFail: '綁定失敗',
  removeLinkFail: '解除綁定失敗',
  linkConfirm: '確認綁定該裝置？',
  removeLinkConfirm: '確認解除綁定？',
  linking: '正在綁定...',
  linkDeviceBracelet: '綁定手環',
  scanDeviceBracelet: '掃描手環中...',
  scanDeviceBraceletTip: '請將小米手環與本裝置保持在一定的範圍內，並確保手環藍芽廣播已開啟',
  scanDeviceBraceletEmptyTitle: '附近未發現可連接的小米手環',
  scanDeviceBraceletEmptyTip1: '1.請確認小米手環已開啟藍牙廣播',
  scanDeviceBraceletEmptyTip2: '2.請確認小米手環在本裝置附近',
  linkedDeviceBraceletHeaderTip: '已綁定如下手環',
  availableLinkDeviceBraceletHeaderTip: '已關聯如下手環',
  linkedDeviceBraceletFooterTip: '如需更換關聯的手環，請先解除關聯。',
  availableLinkDeviceBraceletFooterTip: '請將小米手環與本裝置保持在一定的範圍內並確保手環藍牙廣播已開啟',
  pluginVersion: '外掛程式版本',
  helpAndFeedback: '幫助與反饋',
  offline: '離線',
  downloading: '正在下載...',
  installing: '正在安裝...',
  upgradeSuccess: '已成功更新',
  upgradeFailed: '更新失敗, 請稍後再試',
  upgradeTimeout: '更新逾時',
  autoUpgradeInfo: '將在{0}嘗試自動更新',
  today: '今天',
  tomorrow: '明天',
  currentIsLatestVersion: '當前已是最新版本',
  lastestVersion: '最新版本：',
  currentVersion: '當前版本：',
  fetchFailed: '取得失敗，請稍候',
  releaseNote: '更新日誌',
  releaseVersionHistory: '韌體版本記錄',
  firmwareAutoUpdate: '韌體自動更新',
  autoUpdateDescriptionNote: '檢測到新韌體後，裝置將在{0}嘗試自動更新。裝置必須處於空閒狀態以完成更新。更新過程無聲音提示和燈光打擾。',
  updateNow: '即刻更新',
  requireBelMesh: '該功能需要搭配藍牙Mesh網關使用',
  createCurtainGroup: '建立窗簾伴侶組',
  createCurtainGroupTip: '將兩個窗簾伴侶組成一個窗簾伴侶組使用，組合後可以作為雙開簾呈現和控制。',
  act: '動一下',
  create: '建立',
  chooseCurtainGroupTitle: '請選擇窗簾伴侶',
  currentDevice: '本裝置',
  curtain: '窗簾',
  noCurtainGroupTip: '暫無可成組的裝置，請再增加一個窗簾伴侶後再試',
  switchPlugin: '標準外掛程式',
  defaultPlugin: '預設樣式',
  selectDefaultHP: '預設樣式',
  stdPluginTitle: '標準樣式',
  thirdPluginTitle: '傳統樣式',
  stdPluginSubTitle: '點擊更多功能，支援打開傳統樣式頁面',
  stdGuideDialogTitle: '全新升級',
  stdGuideDialogSubTitle: '軟體介面已全新升級，簡潔暢快搶先體驗！',
  stdGuideDialogNote: '如需查找升級前功能介面可點擊底部“更多功能”入口',
  stdGuideDialogButtonOK: '我知道了',
  // 多键开关设置
  key: '按鍵',
  keyLeft: '左鍵',
  keyMiddle: '中鍵',
  keyRight: '右鍵',
  keyType: '按鍵類型',
  keyName: '名稱',
  light: '燈',
  updateIcon: '更換圖示',
  done: '完成',
  modifyName: '修改名稱',
  keyUpdateIconTips: '將按鍵圖示設為“{0}”後，可對小愛同學說“打開{0}”',
  nameHasChars: '名稱不能包含特殊符號',
  nameTooLong: '名稱不能超過40個字符',
  nameIsEmpty: '名稱不能空白',
  nameNotSupportEmoji: '名稱不支援emoji表情',
  // 房间
  room: '房間',
  room_nameInputTips: '請輸入房間名稱',
  room_nameSuggest: '推薦房間名稱',
  room_createNew: '建立新房間',
  room_bedroom: '臥室',
  room_masterBedroom: '主臥',
  room_secondBedroom: '次臥',
  room_kitchen: '廚房',
  room_diningRoom: '餐廳',
  room_washroom: '洗手間',
  room_childrensRoom: '兒童房',
  room_office: '辦公室',
  room_study: '書房',
  room_balcony: '陽台',
  room_studio: '工作室',
  room_bathroom: '浴室',
  room_backyard: '後院',
  room_unassigned: '未分配房間',
  no_privacy_tip_content: '無法取得私隱，請檢查手機網路或在米家APP中回饋問題。',
  moreDeviceInfo: '更多裝置資訊',
  deviceNet: '裝置網絡',
  customizeName: '自訂名稱',
  software: '軟件',
  hardware: '硬體',
  bleMeshGateway: '藍牙mesh閘道',
  deviceDid: '裝置ID',
  deviceSN: '裝置SN',
  mcuVersion: 'MCU硬件版本',
  sdkVersion: 'SDK硬件版本',
  deviceModel: '裝置型號',
  deviceQR: '裝置QR 碼',
  download: '下載',
  saveSuccess: '儲存成功',
  saveFailed: '儲存失敗',
  clipboardy: '已複製到剪貼簿',
  connected: '已連接',
  notConnected: '未連接',
  bleConnected: '藍牙直連',
  deviceOffline: '裝置離線',
  deviceConsumables: '裝置耗材',
  consumableStateSufficient: '充足',
  consumableStateInsufficient: '不足',
  consumableStateUnknown: '狀態未知',
  consumableStateDepletion: '已耗盡',
  consumableStateRemainPercent: '剩餘 {0}%',
  consumableStateEstimatedHour: {
    'zero': '預計還可以使用{0}小時',
    'one': '預計還可以使用{0}小時',
    'two': '預計還可以使用{0}小時',
    'few': '預計還可以使用{0}小時',
    'many': '預計還可以使用{0}小時',
    'other': '預計還可以使用{0}小時'
  },
  consumableStateEstimatedDay: {
    'zero': '預計還可以使用{0}天',
    'one': '預計還可以使用{0}天',
    'two': '預計還可以使用{0}天',
    'few': '預計還可以使用{0}天',
    'many': '預計還可以使用{0}天',
    'other': '預計還可以使用{0}天'
  },
  changeIcon: '更換圖示',
  deviceCall: '緊急事件電話呼叫',
  cloudStorage: '雲端儲存服務提示',
  cloudStorageVip: '雲端儲存會員狀態提示',
  largeCardEvent: '大卡顯示最新看家事件',
  // 开关智能
  switch_title_controlDevice: '控制裝置',
  switch_subtitle_controlDeviceType: '請確認每個按鍵控制的設別類型',
  common_listItem_value_unset: '未設定',
  switch_title_buttonControlDevice: '${}控制裝置',
  switch_listItem_title_toWirelessSwitch: '轉無線開關',
  switch_listItem_subtile_wirelessSwitchSetting: '開啟後實體按鍵不再控制開關的通斷，僅用於觸發自動化',
  switch_dia_msg_wirelessSwitchSetting: '按鍵關聯了${}，需轉無線開關使用',
  switch_listItem_title_voiceControlLoop: '語音控制開關',
  switch_listItem_title_xiaoAiVoiceControlLoop: '小愛語音控制開關',
  switch_listItem_value_voiceControlLoopOn: '開啟',
  switch_listItem_value_voiceControlLoopOff: '關閉',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: '開啟後，小愛同學可以控制開關， 若開關連接智能燈，可能造成智能燈斷電離線',
  switch_listItem_title_operationMode: '操作模式',
  switch_listItem_title_speedMode: '疾速模式',
  switch_listItem_title_standardMode: '標準模式',
  switch_listItem_subtile_speedModeDescription: '若該裝置只需設定「按一下」的自動化，推薦選擇此項，裝置將快速回應按一下操作，提升自動化回應速度',
  switch_listItem_subtile_standardModeDescription: '若該裝置需要設定「按兩下」或「長按」的自動化，請選擇此項',
  switch_dia_msg_speedModeMessage: '當前裝置設定了「按兩下」或「長按」的自動化，在極速模式下，相關智能場景將無法回應',
  switch_title_selectDeviceType: '選擇裝置類型',
  switch_subtitle_selectDeviceType: '請選擇${}控制的裝置類型',
  switch_subtitle_liveWire_selectDeviceType: '請選擇${}控制的裝置類型。為保障單火開關正常運作，建議保留一個按鍵連接普通裝置',
  switch_title_deviceType_normalDevice: '普通裝置（普通燈等非智慧裝置）',
  switch_title_deviceType_smartLight: '智慧燈',
  switch_title_deviceType_smartSwitch: '其他智慧開關（開關互控）',
  switch_title_deviceType_manualScene: '執行批量控制',
  switch_title_deviceType_otherSmartDevice: '其他智慧裝置',
  switch_value_deviceType_normalDevice: '普通裝置',
  switch_value_deviceType_smartLight: '智慧燈',
  switch_value_deviceType_smartSwitch: '其他智慧開關',
  switch_value_deviceType_manualScene: '批量控制',
  switch_value_deviceType_otherSmartDevice: '其他智慧裝置',
  switch_button_title_seeCreatedScene: '查看已建立的自動化',
  switch_button_title_linkSmartLight: '快速關聯智慧燈',
  switch_button_title_linkSmartSwitch: '快速關聯智慧開關',
  switch_button_title_linkManualScene: '快速關聯批量控制',
  switch_button_title_switchNameSetting: '設定按鍵名稱',
  switch_nav_title_buttonControlLight: '${}控制智慧燈',
  switch_nav_subtitle_buttonControlLight: '將按鍵和智慧燈關聯後，按一下實體按鍵會開啟/關閉智慧燈，且可讓智慧燈保持在線',
  switch_header_title_selectLightOrGroup: '選擇智慧燈或燈組',
  switch_nav_title_buttonControlSwitch: '${}控制智慧開關',
  switch_nav_subtitle_buttonControlSwitch: '將閒置按鍵關聯其他智慧開關，實現開關雙控。點選實體按鍵可以開啟/關閉所選的開關',
  switch_header_title_selectSwitch: '選擇智慧開關',
  switch_nav_title_buttonControlManualScene: '${}執行批量控制',
  switch_nav_subtitle_buttonControlManualScene: '將閒置按鍵關聯批量控制後。按一下實體按鍵會執行所選的批量控制',
  switch_header_title_selectManualScene: '選擇批量控制',
  common_edit: '編輯',
  common_reselect: '重新選擇',
  common_deleted: '已刪除',
  common_delete: '刪除',
  common_delete_failed: '刪除失敗，請檢查網路後重試',
  common_setting_failed: '設定失敗，請確認裝置網路後重試',
  common_saving: '儲存中，請稍候...',
  switch_listItem_title_executionType: '執行方式',
  switch_listItem_value_executionTypeCloud: '雲端',
  switch_listItem_value_executionTypeLocale: '本機',
  switch_dia_msg_deleteScene: '刪除自動化？',
  switch_scene_name_toggleSwitchDevice: '${} 點一下${}——開/關 ${}- ${}',
  switch_scene_name_toggleLightDevice: '${} 點一下${}——開/關 ${}',
  switch_scene_name_executeManualScene: '${} 點一下${}——執行 ${}',
  switch_list_device_unavailable: '未顯示的裝置暫不支援此功能',
  switch_button_subtitle_notCurrentHome: '不在當前家庭',
  common_list_empty: '暫無',
  switch_dia_msg_repeatScene: '目前按鍵已建立了點選自動化，綁定控制裝置後，自動化可能會重複執行，是否需刪除控制裝置的綁定？',
  common_loading: '載入中，請稍候...',
  pairMode: '配對模式',
  switch_title_buttonControlDevice_oneGang: '裝置控制'
};