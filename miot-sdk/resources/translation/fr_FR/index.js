export default {
  setting: 'Paramètres',
  featureSetting: 'Réglages des appareils',
  commonSetting: 'Paramètres généraux',
  name: 'Nom de l\'appareil',
  deviceService: 'Services de l\'appareil',
  location: 'Gérer les emplacements',
  memberSet: 'Boutons',
  share: 'Partager l\'appareil',
  btGateway: 'Passerelle BLE',
  voiceAuth: 'Autorisation vocale',
  ifttt: 'Automatisation',
  productBaike: 'Informations produit',
  firmwareUpgrade: 'Mise à jour du micrologiciel',
  firmwareUpdate: 'Mise à jour du micrologiciel',
  more: 'Paramètres supplémentaires',
  help: 'Aide',
  legalInfo: 'Informations légales',
  deleteDevice: 'Supprimer l\'appareil',
  autoUpgrade: 'Mettre à jour le micrologiciel automatiquement',
  checkUpgrade: 'Vérifier les mises à jour du micrologiciel',
  security: 'Paramètres de sécurité',
  networkInfo: 'Informations réseau',
  feedback: 'Commentaires',
  timezone: 'Fuseau horaire de l\'appareil',
  addToDesktop: 'Ajouter à l\'écran d\'accueil',
  open: 'Activé',
  close: 'Désactivé',
  other: 'Autre',
  multipleKeyShowOnHome: 'Le nombre de boutons affichés sur la page d\'accueil : {0}',
  // 常用设备
  favoriteDevices: 'Afficher sur la page d\'accueil Xiaomi Home',
  favoriteCamera: 'Taille de la carte du tableau de bord',
  favoriteAddDevices: 'Ajouter aux Favoris',
  // MHDatePicker
  cancel: 'Annuler',
  ok: 'Confirmer',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} mois',
    'one': '{0} mois',
    'two': '{0} mois',
    'few': '{0} mois',
    'many': '{0} mois',
    'other': '{0} mois'
  },
  numberDay: {
    'zero': '{0} jour',
    'one': '{0} jour',
    'two': '{0} jours',
    'few': '{0} jours',
    'many': '{0} jours',
    'other': '{0} jours'
  },
  numberHour: {
    'zero': '{0} h',
    'one': '{0} h',
    'two': '{0} h',
    'few': '{0} h',
    'many': '{0} h',
    'other': '{0} h'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} s',
    'one': '{0} s',
    'two': '{0} s',
    'few': '{0} s',
    'many': '{0} s',
    'other': '{0} s'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Quitter',
  firmwareUpgradeUpdate: 'Mettre à jour',
  firmwareUpgradeLook: 'Afficher',
  firmwareUpgradeForceUpdate: 'La version actuelle du micrologiciel est trop ancienne. Certaines fonctionnalités peuvent ne pas fonctionner correctement. Mettez à jour la dernière version pour une meilleure expérience',
  firmwareUpgradeForceUpdating: 'Votre appareil est en cours de mise à jour. Réessayez plus tard.',
  firmwareUpgradeNew_pre: 'Une mise à jour est disponible. ',
  firmwareUpgradeNew_sub: 'Mettre à jour maintenant ?',
  handling: 'Patientez un instant…',
  error: 'Une erreur s\'est produite. Réessayez plus tard.',
  createLightGroup: 'Créer un groupe de lumière (nouveau)',
  manageLightGroup: 'Gérer un groupe de lumière (nouveau)',
  deleteLightGroup: 'Dissocier les lumières',
  deleteCurtainGroup: 'Dissocier les appareils',
  linkDevice: 'Relier les appareils',
  noSuppurtedLinkageDevice: 'Aucun appareil disponible',
  noSuppurtedLinkageTip: '1. Assurez-vous d\'avoir ajouté les appareils dans l\'application Xiaomi Home et de les avoir assignés à des pièces.\\n. Gardez les appareils Bluetooth à proximité de cet appareil pour les connecter correctement.',
  supportedLinkageDevices: 'Peut être relié aux appareils suivants :',
  linkageDistanceTip: 'Gardez les appareils à proximité les uns des autres pour vous assurer qu\'ils sont capables de se connecter.',
  linkageRemoveTip: 'Pour changer l\'appareil Bluetooth lié, supprimez d\'abord l\'appareil.',
  link: 'Lien',
  removeLink: 'Supprimer',
  linkFail: 'Impossible de relier',
  removeLinkFail: 'Impossible de supprimer',
  linkConfirm: 'Relier avec cet appareil maintenant ?',
  removeLinkConfirm: 'Supprimer maintenant ?',
  linking: 'Liaison en cours...',
  linkDeviceBracelet: 'Bande de liaison',
  scanDeviceBracelet: 'Recherche de bandes...',
  scanDeviceBraceletTip: 'Maintenez la bande Xiaomi à proximité de l\'appareil et assurez-vous que le Bluetooth est activé pour que la connexion soit réussie.',
  scanDeviceBraceletEmptyTitle: 'Impossible de trouver de bande Xiaomi à proximité',
  scanDeviceBraceletEmptyTip1: '1. Assurez-vous que la bande Bluetooth est activée.',
  scanDeviceBraceletEmptyTip2: '2. Maintenez la bande près de l\'autre appareil.',
  linkedDeviceBraceletHeaderTip: 'Lié aux bandes suivantes :',
  availableLinkDeviceBraceletHeaderTip: 'Peut être relié aux appareils suivants :',
  linkedDeviceBraceletFooterTip: 'Pour changer la bande reliée, il faut d\'abord supprimer la bande.',
  availableLinkDeviceBraceletFooterTip: 'Assurez-vous que la fonction Bluetooth du bracelet est activée et maintenez-le à proximité de l\'autre appareil.',
  pluginVersion: 'Version plugin',
  helpAndFeedback: 'Aide et commentaires',
  offline: 'Hors ligne',
  downloading: 'Téléchargement en cours…',
  installing: 'Installation en cours…',
  upgradeSuccess: 'Mis à jour avec succès',
  upgradeFailed: 'Mise à jour impossible. Réessayer plus tard.',
  upgradeTimeout: 'La mise à jour a expiré',
  autoUpgradeInfo: 'La mise à jour se fera automatiquement entre {0}',
  today: 'Aujourd’hui',
  tomorrow: 'Demain',
  currentIsLatestVersion: 'La version actuelle est à jour',
  lastestVersion: 'Dernière version : ',
  currentVersion: 'Version actuelle : ',
  fetchFailed: 'Accès impossible. Réessayer.',
  releaseNote: 'Les nouveautés',
  releaseVersionHistory: 'Historique de mise à jour du micrologiciel',
  firmwareAutoUpdate: 'Mises à jour automatiques du micrologiciel',
  autoUpdateDescriptionNote: 'Dès qu\'un nouveau micrologiciel est détecté, l\'appareil tente de se mettre à jour automatiquement entre {0}. La mise à jour sera installée lorsque vous n\'utiliserez pas l\'appareil et il n\'y aura pas de notification sonore ou lumineuse pendant le processus de mise à jour.',
  updateNow: 'Mise à jour',
  requireBelMesh: 'Cette fonction nécessite une passerelle maillée Bluetooth pour fonctionner normalement.',
  createCurtainGroup: 'Créer un groupe rideau',
  createCurtainGroupTip: 'Deux moteurs de rideau peuvent être combinés en un groupe qui peut être contrôlé comme un rideau double face.',
  act: 'Déplacer',
  create: 'Créer',
  chooseCurtainGroupTitle: 'Choisir un moteur de rideau',
  currentDevice: 'Cet appareil',
  curtain: 'Rideau',
  noCurtainGroupTip: 'Il n\'est pas possible de grouper maintenant. Ajoutez un autre moteur de rideau et réessayez.',
  switchPlugin: 'Plugin standard',
  defaultPlugin: 'Par défaut',
  selectDefaultHP: 'Par défaut',
  stdPluginTitle: 'Standard',
  thirdPluginTitle: 'Traditionnel',
  stdPluginSubTitle: 'Vous pouvez revenir à l\'ancienne version de la page dans les fonctionnalités supplémentaires',
  stdGuideDialogTitle: 'Nouvelle version disponible',
  stdGuideDialogSubTitle: 'Mise à jour de l\'application pour une nouvelle expérience plus rationalisée.',
  stdGuideDialogNote: 'Si vous ne retrouvez pas une fonctionnalité après une mise à jour, il se peut qu\'elle ait été déplacée dans « Fonctionnalités supplémentaires ».',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Bouton',
  keyLeft: 'Bouton de gauche',
  keyMiddle: 'Bouton du milieu',
  keyRight: 'Bouton de droite',
  keyType: 'Nom du bouton',
  keyName: 'Nom',
  light: 'Lampe',
  updateIcon: 'Modifier l\'icône',
  done: 'OK',
  modifyName: 'Modifier le nom',
  keyUpdateIconTips: 'Lorsque l\'icône est sur « {0} », vous pouvez demander à Mi AI d\'activer « {0} »',
  nameHasChars: 'Le nom ne peut pas contenir de caractères spéciaux',
  nameTooLong: 'Le nom peut comporter jusqu\'à 40 caractères',
  nameIsEmpty: 'Le nom ne peut être vide',
  nameNotSupportEmoji: 'Les noms ne peuvent pas comporter d\'émoticône',
  // 房间
  room: 'Pièce',
  room_nameInputTips: 'Saisir le nom d\'une pièce',
  room_nameSuggest: 'Nom recommandé',
  room_createNew: 'Créer une nouvelle pièce',
  room_bedroom: 'Chambre',
  room_masterBedroom: 'Chambre parentale',
  room_secondBedroom: 'Deuxième chambre',
  room_kitchen: 'Cuisine',
  room_diningRoom: 'Salle à manger',
  room_washroom: 'Toilettes',
  room_childrensRoom: 'Chambre d’enfant',
  room_office: 'Bureau',
  room_study: 'Bibliothèque',
  room_balcony: 'Balcon',
  room_studio: 'Atelier',
  room_bathroom: 'Salle de bains',
  room_backyard: 'Jardin',
  room_unassigned: 'Non affecté',
  no_privacy_tip_content: 'Impossible de charger la politique de confidentialité. Vérifiez vos paramètres réseau et réessayez ou signalez ce problème via Feedback.',
  moreDeviceInfo: 'Plus d\'informations sur l\'appareil',
  deviceNet: 'Appareil réseau',
  customizeName: 'Personnaliser le nom',
  software: 'Logiciel',
  hardware: 'Matériel',
  bleMeshGateway: 'Passerelle Bluetooth Mesh',
  deviceDid: 'Identifiant de l\'appareil',
  deviceSN: 'Numéro de série de l\'appareil ',
  mcuVersion: 'Version du micrologicel MCU',
  sdkVersion: 'Version du micrologicel SDK',
  deviceModel: 'Modèle de l\'appareil',
  deviceQR: 'Code QR de l\'appareil',
  download: 'Télécharger',
  saveSuccess: 'Sauvegardé avec succès',
  saveFailed: 'Impossible de sauvegarder',
  clipboardy: 'Copié avec succès',
  connected: 'Connecté',
  notConnected: 'Non connecté',
  bleConnected: 'Connexion Bluetooth directe',
  deviceOffline: 'Hors ligne',
  deviceConsumables: 'Approvisionnement de l\'appareil',
  consumableStateSufficient: 'Suffisant',
  consumableStateInsufficient: 'Insuffisant',
  consumableStateUnknown: 'État inconnu',
  consumableStateDepletion: 'Épuisé',
  consumableStateRemainPercent: '{0}% restant',
  consumableStateEstimatedHour: {
    'zero': '{0} h restante',
    'one': '{0} h restante',
    'two': '{0} h restantes',
    'few': '{0} h restantes',
    'many': '{0} h restantes',
    'other': '{0} h restantes'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} jour restant',
    'one': '{0} jour restant',
    'two': '{0} jours restants',
    'few': '{0} jours restants',
    'many': '{0} jours restants',
    'other': '{0} jours restants'
  },
  changeIcon: 'Modifier l\'icône',
  deviceCall: 'Alertes d\'urgence',
  cloudStorage: 'Notifications de stockage dans le cloud',
  cloudStorageVip: 'Recevoir des notifications sur le statut de l\'abonnement au cloud',
  largeCardEvent: 'Afficher les derniers événements capturés sur la carte',
  // 开关智能
  switch_title_controlDevice: 'Contrôler des appareils',
  switch_subtitle_controlDeviceType: 'Définir les types d\'appareil pour chaque bouton',
  common_listItem_value_unset: 'Non défini',
  switch_title_buttonControlDevice: 'Appareils contrôlés (${})',
  switch_listItem_title_toWirelessSwitch: 'Transformer en interrupteur sans fil',
  switch_listItem_subtile_wirelessSwitchSetting: 'Les boutons physiques ne pourront pas contrôler les interrupteurs lorsque cette fonctionnalité est activée. Vous pourrez toujours les utiliser pour les automatismes.',
  switch_dia_msg_wirelessSwitchSetting: 'Ce bouton est associé à un autre élément (${}). Passez à l\'interrupteur sans fil pour l’utiliser.',
  switch_listItem_title_voiceControlLoop: 'Commandes vocales pour interrupteurs',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Contrôle des interrupteurs avec Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Activé',
  switch_listItem_value_voiceControlLoopOff: 'Désactivé',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Contrôlez les interrupteurs à l\'aide de commandes vocales via Mi AI. Si des lampes intelligentes sont connectées à l\'interrupteur, elles peuvent être éteintes et déconnectées.',
  switch_listItem_title_operationMode: 'Mode de fonctionnement',
  switch_listItem_title_speedMode: 'Super mode vitesse',
  switch_listItem_title_standardMode: 'Mode standard',
  switch_listItem_subtile_speedModeDescription: 'Sélectionnez cette option si les automatismes ne doivent être configurés que pour une « simple pression ». Cette option améliorera le temps de réponse de l\'automate.',
  switch_listItem_subtile_standardModeDescription: 'Sélectionnez cette option si l\'appareil doit établir des automatismes de « double pression » ou de « pression et maintien ».',
  switch_dia_msg_speedModeMessage: 'Cet appareil est déjà équipé des automatismes « double pression » et « pression et maintien ». Si vous sélectionnez le mode Super vitesse, vous ne pourrez plus utiliser ces automatismes. Continuer quand même ?',
  switch_title_selectDeviceType: 'Sélectionner le type d\'appareil',
  switch_subtitle_selectDeviceType: 'Sélectionnez le type d\'appareil contrôlé par ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Sélectionnez le type d’appareil contrôlé par ${}. Laissez un bouton connecté aux appareils réguliers pour vous assurer que l\'interrupteur fonctionne normalement.',
  switch_title_deviceType_normalDevice: 'Appareils réguliers (lumières et lampes sans fonctionnalité intelligente)',
  switch_title_deviceType_smartLight: 'Lumières intelligentes',
  switch_title_deviceType_smartSwitch: 'Autre interrupteur intelligent',
  switch_title_deviceType_manualScene: 'Contrôle par lot',
  switch_title_deviceType_otherSmartDevice: 'Autre appareils connectés',
  switch_value_deviceType_normalDevice: 'Appareils réguliers',
  switch_value_deviceType_smartLight: 'Lumières intelligentes',
  switch_value_deviceType_smartSwitch: 'Autre interrupteur intelligent',
  switch_value_deviceType_manualScene: 'Contrôle par lot',
  switch_value_deviceType_otherSmartDevice: 'Autre appareils connectés',
  switch_button_title_seeCreatedScene: 'Afficher les automatisations',
  switch_button_title_linkSmartLight: 'Connecter la lumière intelligente',
  switch_button_title_linkSmartSwitch: 'Connecter l\'interrupteur intelligent',
  switch_button_title_linkManualScene: 'Connecter le contrôle par lots',
  switch_button_title_switchNameSetting: 'Définir le nom du bouton',
  switch_nav_title_buttonControlLight: 'Lumières intelligentes contrôlées (${})',
  switch_nav_subtitle_buttonControlLight: 'Connectez des lumières intelligentes à un bouton pour les allumer et les éteindre et les garder en ligne',
  switch_header_title_selectLightOrGroup: 'Sélectionner la lumière intelligente ou le groupe de lumière',
  switch_nav_title_buttonControlSwitch: 'Interrupteurs intelligents contrôlés (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Connectez les interrupteurs pour ajouter une autre interface allumer/éteindre. Appuyez sur le bouton pour activer et désactiver les interrupteurs sélectionnés.',
  switch_header_title_selectSwitch: 'Sélectionner les interrupteurs intelligents',
  switch_nav_title_buttonControlManualScene: 'Contrôles par lots attribués (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Assigner des contrôles de lot pour les exécuter en appuyant sur le bouton',
  switch_header_title_selectManualScene: 'Sélectionner les contrôles par lot',
  common_edit: 'Modifier',
  common_reselect: 'Sélectionner à nouveau',
  common_deleted: 'Supprimé',
  common_delete: 'Supprimer',
  common_delete_failed: 'Suppression impossible. Vérifiez vos paramètres réseau et réessayez.',
  common_setting_failed: 'Impossible de définir. Vérifiez si l’appareil est connecté au réseau et réessayez.',
  common_saving: 'Enregistrement…',
  switch_listItem_title_executionType: 'Mode d\'exécution',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Local',
  switch_dia_msg_deleteScene: 'Supprimer cette automatisation ?',
  switch_scene_name_toggleSwitchDevice: '${} | Appui simple | ${} | Allumé/éteins| ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Appui simple | ${} | Allumé/éteins| ${}',
  switch_scene_name_executeManualScene: '${} | Appui simple | ${} | Exécution | ${}-${}',
  switch_list_device_unavailable: 'Les appareils non affichés ne prennent pas en charge cette fonctionnalité.',
  switch_button_subtitle_notCurrentHome: 'Pas dans le domicile actuel',
  common_list_empty: 'Rien pour l\'instant',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Mode d\'appariement',
  switch_title_buttonControlDevice_oneGang: 'Contrôles de l\'appareil'
};