export default {
  setting: '<PERSON><PERSON><PERSON>',
  featureSetting: '<PERSON><PERSON><PERSON> dispozitiv',
  commonSetting: '<PERSON><PERSON><PERSON> generale',
  name: 'Nume dispozitiv',
  deviceService: 'Servicii dispozitiv',
  location: 'Gestionare locații',
  memberSet: 'Butoane',
  share: 'Partajează dispozitiv',
  btGateway: 'Gateway BLE',
  voiceAuth: 'Autorizare vocală',
  ifttt: 'Automatizare',
  productBaike: 'Informații despre produs',
  firmwareUpgrade: 'Actualizare firmware',
  firmwareUpdate: 'Actualizare firmware',
  more: 'Setări adiționale',
  help: 'Ajutor',
  legalInfo: 'Informații legale',
  deleteDevice: 'Șterge dispozitivul',
  autoUpgrade: 'Actualizează automat firmware-ul',
  checkUpgrade: 'Caută actualizări de firmware',
  security: 'Setări de securitate',
  networkInfo: 'Informații despre rețea',
  feedback: 'Feedback',
  timezone: 'Fusul orar al dispozitivului',
  addToDesktop: '<PERSON>ug<PERSON> pe ecranul principal',
  open: 'Pornit',
  close: 'Oprit',
  other: 'Altele',
  multipleKeyShowOnHome: 'Numărul de butoane afișate pe pagina de pornire: {0}',
  // 常用设备
  favoriteDevices: 'Afișați pe pagina de pornire Xiaomi Home',
  favoriteCamera: 'Dimensiunea cardului panoului de bord',
  favoriteAddDevices: 'Adaugă la favorite',
  // MHDatePicker
  cancel: 'Anulează',
  ok: 'Confirmă',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} luni',
    'one': '{0} luni',
    'two': '{0} luni',
    'few': '{0} luni',
    'many': '{0} luni',
    'other': '{0} luni'
  },
  numberDay: {
    'zero': '{0} zile',
    'one': '{0} zile',
    'two': '{0} zile',
    'few': '{0} zile',
    'many': '{0} zile',
    'other': '{0} zile'
  },
  numberHour: {
    'zero': '{0} h',
    'one': '{0} h',
    'two': '{0} h',
    'few': '{0} h',
    'many': '{0} h',
    'other': '{0} h'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} sec',
    'one': '{0} sec',
    'two': '{0} sec',
    'few': '{0} sec',
    'many': '{0} sec',
    'other': '{0} sec'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}.{1}.{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Ieșire',
  firmwareUpgradeUpdate: 'Actualizează',
  firmwareUpgradeLook: 'Vizualizează',
  firmwareUpgradeForceUpdate: 'Firmware-ul actual poate fi prea vechi pentru a rula unele funcții. Actualizează la cea mai recentă versiune pentru o experiență mai bună.',
  firmwareUpgradeForceUpdating: 'Dispozitivul se actualizează. Încearcă din nou mai târziu.',
  firmwareUpgradeNew_pre: 'Este disponibilă o actualizare. ',
  firmwareUpgradeNew_sub: 'Actualizează acum?',
  handling: 'Doar o secundă…',
  error: 'A apărut o eroare. Încearcă din nou mai târziu.',
  createLightGroup: 'Creează un grup de lumină (nou)',
  manageLightGroup: 'Gestionează grupul de lumină (nou)',
  deleteLightGroup: 'Degrupează luminile',
  deleteCurtainGroup: 'Degrupează dispozitivele',
  linkDevice: 'Asociază dispozitivele',
  noSuppurtedLinkageDevice: 'Niciun dispozitiv disponibil',
  noSuppurtedLinkageTip: '1. Asigurați-vă de faptul că ați adăugat dispozitivele în aplicația Xiaomi Home și le-ați atribuit camerelor.\\n2. Țineți dispozitivele Bluetooth aproape de acest dispozitiv pentru a reuși să le conectați.',
  supportedLinkageDevices: 'Poate fi asociat cu următoarele dispozitive:',
  linkageDistanceTip: 'Păstrează dispozitivele în imediata apropiere pentru a te asigura că se pot asocia.',
  linkageRemoveTip: 'Pentru a schimba dispozitivul Bluetooth asociat, mai întâi elimină primul dispozitiv.',
  link: 'Asociere',
  removeLink: 'Elimină',
  linkFail: 'Nu s-a putut asocia',
  removeLinkFail: 'Nu s-a putut elimina',
  linkConfirm: 'Asociezi acum cu acest dispozitiv?',
  removeLinkConfirm: 'Elimini acum?',
  linking: 'Se asociază...',
  linkDeviceBracelet: 'Asociază brățara',
  scanDeviceBracelet: 'Se scanează brățara...',
  scanDeviceBraceletTip: 'Ține Mi Band aproape de acest dispozitiv și asigură-te că are Bluetooth pornit pentru a reuși să se conecteze.',
  scanDeviceBraceletEmptyTitle: 'Nu s-au găsit dispozitive Mi Band în apropiere',
  scanDeviceBraceletEmptyTip1: '1. Asigură-te că brățara are Bluetooth pornit.',
  scanDeviceBraceletEmptyTip2: '2. Ține brățara aproape de celălalt dispozitiv.',
  linkedDeviceBraceletHeaderTip: 'Asociat cu următoarele brățări:',
  availableLinkDeviceBraceletHeaderTip: 'Poate fi asociat cu următoarele brățări:',
  linkedDeviceBraceletFooterTip: 'Pentru a schimba brățara asociată, elimină mai întâi prima brățară.',
  availableLinkDeviceBraceletFooterTip: 'Asigură-te că brățara are Bluetooth pornit și ține-o aproape de celălalt dispozitiv.',
  pluginVersion: 'Versiune plugin',
  helpAndFeedback: 'Ajutor și feedback',
  offline: 'Offline',
  downloading: 'Se descarcă...',
  installing: 'Se instalează…',
  upgradeSuccess: 'Actualizat cu succes',
  upgradeFailed: 'Nu s-a putut actualiza. Încearcă din nou mai târziu.',
  upgradeTimeout: 'Actualizarea a expirat',
  autoUpgradeInfo: 'Va încerca să se actualizeze automat între {0}',
  today: 'Azi',
  tomorrow: 'Mâine',
  currentIsLatestVersion: 'Versiunea actuală este la zi',
  lastestVersion: 'Cea mai recentă versiune: ',
  currentVersion: 'Versiunea actuală: ',
  fetchFailed: 'Nu s-a putut accesa. Încearcă din nou.',
  releaseNote: 'Ce este nou',
  releaseVersionHistory: 'Istoricul actualizărilor de firmware',
  firmwareAutoUpdate: 'Actualizări automate de firmware',
  autoUpdateDescriptionNote: 'După ce se detectează un firmware nou, dispozitivul va încerca să se actualizeze automat între {0}. Actualizarea va fi instalată când nu folosiți dispozitivul și nu vor exista notificări audio sau luminoase în timpul procesului de actualizare.',
  updateNow: 'Actualizează',
  requireBelMesh: 'Pentru a funcționa normal, această caracteristică necesită un gateway Bluetooth mesh.',
  createCurtainGroup: 'Creează un grup de draperii',
  createCurtainGroupTip: 'Două motoare de draperii pot fi combinate într-un grup care poate fi controlat ca o draperie dublă.',
  act: 'Mută',
  create: 'Creează',
  chooseCurtainGroupTitle: 'Selectează un motor de draperie',
  currentDevice: 'Acest dispozitiv',
  curtain: 'Draperie',
  noCurtainGroupTip: 'Acum nu se poate grupa. Adaugă un alt motor de draperie și încearcă din nou.',
  switchPlugin: 'Plugin standard',
  defaultPlugin: 'Implicit',
  selectDefaultHP: 'Implicit',
  stdPluginTitle: 'Standard',
  thirdPluginTitle: 'Tradițional',
  stdPluginSubTitle: 'Poți comuta la versiunea veche a paginii în caracteristicile suplimentare',
  stdGuideDialogTitle: 'Noua versiune este disponibilă',
  stdGuideDialogSubTitle: 'Actualizează aplicația pentru o experiență nouă, mai simplificată.',
  stdGuideDialogNote: 'Dacă nu găsești o caracteristică după o actualizare, este posibil să fi fost mutată la „Caracteristici suplimentare”.',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Comută',
  keyLeft: 'Comutator stânga',
  keyMiddle: 'Comutator mijloc',
  keyRight: 'Comutator dreapta',
  keyType: 'Tip comutator',
  keyName: 'Nume',
  light: 'Lampă',
  updateIcon: 'Schimbă pictograma',
  done: 'Gata',
  modifyName: 'Editează numele',
  keyUpdateIconTips: 'Când pictograma este comutată pe "{0}", poți cere ca Mi IA să pornească "{0}".',
  nameHasChars: 'Numele nu poate conține caractere speciale',
  nameTooLong: 'Numele poate conține până la 40 caractere',
  nameIsEmpty: 'Numele nu poate fi necompletat',
  nameNotSupportEmoji: 'Numele nu poate include emoji',
  // 房间
  room: 'Cameră',
  room_nameInputTips: 'Introdu un nume de cameră',
  room_nameSuggest: 'Nume recomandat',
  room_createNew: 'Crează o cameră nouă',
  room_bedroom: 'Dormitor',
  room_masterBedroom: 'Dormitor matrimonial',
  room_secondBedroom: 'Al doilea dormitor',
  room_kitchen: 'Bucătărie',
  room_diningRoom: 'Sufragerie',
  room_washroom: 'Toaletă',
  room_childrensRoom: 'Camera copiilor',
  room_office: 'Cameră de studiu',
  room_study: 'Bibliotecă',
  room_balcony: 'Balcon',
  room_studio: 'Atelier',
  room_bathroom: 'Baie',
  room_backyard: 'Curtea din spate',
  room_unassigned: 'Nealocat',
  no_privacy_tip_content: 'Politica de confidențialitate nu a putut fi încărcată. Verifică setările de rețea și încearcă din nou sau raportează această problemă prin Feedback.',
  moreDeviceInfo: 'Mai multe informații despre dispozitiv',
  deviceNet: 'Rețea dispozitive',
  customizeName: 'Nume personalizat',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Gateway Bluetooth mesh',
  deviceDid: 'ID dispozitiv',
  deviceSN: 'Număr serie dispozitiv',
  mcuVersion: 'Vesiune firmware MCU',
  sdkVersion: 'Vesiune firmware SDK',
  deviceModel: 'Modelul dispozitivului',
  deviceQR: 'Codul QR al dispozitivului',
  download: 'Descărcați',
  saveSuccess: 'Salvat cu succes',
  saveFailed: 'Nu s-a putut salva',
  clipboardy: 'Copiat cu succes',
  connected: 'Conectat',
  notConnected: 'Neconectat',
  bleConnected: 'Conexiune Bluetooth directă',
  deviceOffline: 'Offline',
  deviceConsumables: 'Consumabile pentru dispozitive',
  consumableStateSufficient: 'Suficient',
  consumableStateInsufficient: 'Insuficient',
  consumableStateUnknown: 'Stare necunoscută',
  consumableStateDepletion: 'Consumat',
  consumableStateRemainPercent: '{0}% rămas',
  consumableStateEstimatedHour: {
    'zero': '{0} h rămase',
    'one': '{0} h rămase',
    'two': '{0} h rămase',
    'few': '{0} h rămase',
    'many': '{0} h rămase',
    'other': '{0} h rămase'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} zile rămase',
    'one': '{0} zile rămase',
    'two': '{0} zile rămase',
    'few': '{0} zile rămase',
    'many': '{0} zile rămase',
    'other': '{0} zile rămase'
  },
  changeIcon: 'Schimbă pictograma',
  deviceCall: 'Alerte de urgență',
  cloudStorage: 'Notificări de stocare în cloud',
  cloudStorageVip: 'Primiți notificări despre starea abonamentului cloud',
  largeCardEvent: 'Afișați cele mai recente evenimente capturate pe card',
  // 开关智能
  switch_title_controlDevice: 'Controlează dispozitive',
  switch_subtitle_controlDeviceType: 'Setați tipurile de dispozitive pentru fiecare buton',
  common_listItem_value_unset: 'Nu este setat',
  switch_title_buttonControlDevice: 'Dispozitive controlate (${})',
  switch_listItem_title_toWirelessSwitch: 'Schimbă la comutator wireless',
  switch_listItem_subtile_wirelessSwitchSetting: 'Atunci când această funcție este activată, nu veți putea controla întrerupătoarele folosind butoanele fizice. Totuși, le veți putea folosi pentru automatizări.',
  switch_dia_msg_wirelessSwitchSetting: 'Acest buton este asociat cu un alt articol (${}). Treceți la comutatorul wireless pentru a-l folosi.',
  switch_listItem_title_voiceControlLoop: 'Comenzi vocale pentru întrerupătoare',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Controlați întrerupătoarele cu ajutorul Mi IA',
  switch_listItem_value_voiceControlLoopOn: 'Pornit',
  switch_listItem_value_voiceControlLoopOff: 'Oprit',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Controlați întrerupătoarele cu ajutorul comenzilor vocale prin Mi IA. Dacă luminile inteligente sunt conectate la întrerupător, acestea pot fi oprite și deconectate.',
  switch_listItem_title_operationMode: 'Mod de operare',
  switch_listItem_title_speedMode: 'Mod de super viteză',
  switch_listItem_title_standardMode: 'Mod standard',
  switch_listItem_subtile_speedModeDescription: 'Selectați această opțiune dacă automatizările trebuie setate doar pentru o "singură apăsare". Această opțiune va optimiza automatizarea timpului de răspuns.',
  switch_listItem_subtile_standardModeDescription: 'Selectați această opțiune dacă dispozitivul trebuie să seteze automatizările de "apăsare dublă" sau "țineți apăsat"',
  switch_dia_msg_speedModeMessage: 'Acest dispozitiv are setate deja automatizările "apăsare dublă" și "țineți apăsat". Dacă selectați Modul de super viteză, nu veți mai putea folosi aceste automatizări. Vreți să continuați oricum?',
  switch_title_selectDeviceType: 'Selectează tipul dispozitivului',
  switch_subtitle_selectDeviceType: 'Selectați tipul de dispozitiv controlat de ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Selectați tipul de dispozitiv controlat de ${}. Lăsați un buton conectat la dispozitivele obișnuite pentru a vă asigura că comutatorul funcționează normal.',
  switch_title_deviceType_normalDevice: 'Dispozitive obișnuite (lumini și lămpi fără funcționalitate inteligentă)',
  switch_title_deviceType_smartLight: 'Lumini inteligente',
  switch_title_deviceType_smartSwitch: 'Alte întrerupătoare inteligente',
  switch_title_deviceType_manualScene: 'Controale de grup',
  switch_title_deviceType_otherSmartDevice: 'Alte dispozitive inteligente',
  switch_value_deviceType_normalDevice: 'Dispozitive obișnuite',
  switch_value_deviceType_smartLight: 'Lumini inteligente',
  switch_value_deviceType_smartSwitch: 'Alte întrerupătoare inteligente',
  switch_value_deviceType_manualScene: 'Controale de grup',
  switch_value_deviceType_otherSmartDevice: 'Alte dispozitive inteligente',
  switch_button_title_seeCreatedScene: 'Vezi automatizările',
  switch_button_title_linkSmartLight: 'Conectează o lumină inteligentă',
  switch_button_title_linkSmartSwitch: 'Conectează un întrerupător inteligent',
  switch_button_title_linkManualScene: 'Conectează un control de grup',
  switch_button_title_switchNameSetting: 'Setați numele butonului',
  switch_nav_title_buttonControlLight: 'Lumini inteligente controlate (${})',
  switch_nav_subtitle_buttonControlLight: 'Conectați luminile inteligente la un buton pentru a le porni și opri și pentru a le menține online',
  switch_header_title_selectLightOrGroup: 'Selectați lumină inteligentă sau grup de lumini',
  switch_nav_title_buttonControlSwitch: 'Întrerupătoare inteligente controlate (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Conectați întrerupătoarele pentru a adăuga o altă interfață pornit/oprit. Apăsarea butonului va porni și opri întrerupătoarele selectate.',
  switch_header_title_selectSwitch: 'Selectați întrerupătoarele inteligente',
  switch_nav_title_buttonControlManualScene: 'Controalele de grup atribuite (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Atribuiți controale de grup pentru a le rula apăsând butonul',
  switch_header_title_selectManualScene: 'Selectați controalele de grup',
  common_edit: 'Editează',
  common_reselect: 'Selectează din nou',
  common_deleted: 'Șters',
  common_delete: 'Șterge',
  common_delete_failed: 'Nu s-a putut șterge. Verificați setările rețelei și încercați din nou.',
  common_setting_failed: 'Nu s-a putut seta. Verificați dacă dispozitivul este conectat la rețea și încercați din nou.',
  common_saving: 'Se salvează…',
  switch_listItem_title_executionType: 'Modul de rulare',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Local',
  switch_dia_msg_deleteScene: 'Ștergeți această automatizare?',
  switch_scene_name_toggleSwitchDevice: '${} | O singură apăsare | ${} | Pornit/Oprit | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | O singură apăsare | ${} | Pornit/Oprit | ${}',
  switch_scene_name_executeManualScene: '${} | O singură apăsare | ${} | Rulează | ${}-${}',
  switch_list_device_unavailable: 'Dispozitivele care nu sunt afișate nu acceptă această funcție',
  switch_button_subtitle_notCurrentHome: 'Nu există în casa curentă',
  common_list_empty: 'Deocamdată nu există nimic aici',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Mod de asociere',
  switch_title_buttonControlDevice_oneGang: 'Comenzile dispozitivului'
};