export default {
  setting: 'การตั้งค่า',
  featureSetting: 'การตั้งค่าอุปกรณ์',
  commonSetting: 'การตั้งค่าทั่วไป',
  name: 'ชื่ออุปกรณ์',
  deviceService: 'บริการอุปกรณ์',
  location: 'จัดการตำแหน่งที่ตั้ง',
  memberSet: 'ปุ่ม',
  share: 'แชร์อุปกรณ์',
  btGateway: 'เกตเวย์ BLE',
  voiceAuth: 'การอนุญาตด้วยเสียง',
  ifttt: 'ระบบอัตโนมัติ',
  productBaike: 'ข้อมูลผลิตภัณฑ์',
  firmwareUpgrade: 'อัปเดตเฟิร์มแวร์',
  firmwareUpdate: 'อัปเดตเฟิร์มแวร์',
  more: 'การตั้งค่าเพิ่มเติม',
  help: 'ความช่วยเหลือ',
  legalInfo: 'ข้อมูลทางกฎหมาย',
  deleteDevice: 'ลบอุปกรณ์ออก',
  autoUpgrade: 'อัปเดตเฟิร์มแวร์โดยอัตโนมัติ',
  checkUpgrade: 'ตรวจสอบหาการอัปเดตเฟิร์มแวร์',
  security: 'การตั้งค่าความปลอดภัย',
  networkInfo: 'ข้อมูลเครือข่าย',
  feedback: 'ข้อเสนอแนะ',
  timezone: 'โซนเวลาของอุปกรณ์',
  addToDesktop: 'เพิ่มไปยังหน้าหลัก',
  open: 'เปิด',
  close: 'ปิด',
  other: 'อื่นๆ',
  multipleKeyShowOnHome: 'จำนวนปุ่มที่แสดงในหน้าหลัก: {0}',
  // 常用设备
  favoriteDevices: 'แสดงบนหน้าหลักของ Xiaomi Home',
  favoriteCamera: 'ขนาดการ์ดแดชบอร์ด',
  favoriteAddDevices: 'เพิ่มในรายการโปรด',
  // MHDatePicker
  cancel: 'ยกเลิก ',
  ok: 'ยืนยัน',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} เดือน',
    'one': '{0} เดือน',
    'two': '{0} เดือน',
    'few': '{0} เดือน',
    'many': '{0} เดือน',
    'other': '{0} เดือน'
  },
  numberDay: {
    'zero': '{0} วัน',
    'one': '{0} วัน',
    'two': '{0} วัน',
    'few': '{0} วัน',
    'many': '{0} วัน',
    'other': '{0} วัน'
  },
  numberHour: {
    'zero': '{0} ชม.',
    'one': '{0} ชม.',
    'two': '{0} ชม.',
    'few': '{0} ชม.',
    'many': '{0} ชม.',
    'other': '{0} ชม.'
  },
  numberMinute: {
    'zero': '{0} นาที',
    'one': '{0} นาที',
    'two': '{0} นาที',
    'few': '{0} นาที',
    'many': '{0} นาที',
    'other': '{0} นาที'
  },
  numberSecond: {
    'zero': '{0} วิ.',
    'one': '{0} วิ.',
    'two': '{0} วิ.',
    'few': '{0} วิ.',
    'many': '{0} วิ.',
    'other': '{0} วิ.'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'ออก',
  firmwareUpgradeUpdate: 'อัปเดต',
  firmwareUpgradeLook: 'ดู',
  firmwareUpgradeForceUpdate: 'เวอร์ชั่นเฟิร์มแวร์ปัจจุบันเก่าไป ฟีเจอร์บางอย่างอาจทำงานไม่ถูกต้อง อัปเดตเป็นเวอร์ชั่นล่าสุดเพื่อประสบการณ์การใช้งานที่ดีขึ้น',
  firmwareUpgradeForceUpdating: 'กำลังอัปเดตอุปกรณ์ ลองอีกครั้งในภายหลัง',
  firmwareUpgradeNew_pre: 'มีการอัปเดต ',
  firmwareUpgradeNew_sub: 'อัปเดตตอนนี้หรือไม่?',
  handling: 'รอสักครู่…',
  error: 'เกิดข้อผิดพลาด ลองอีกครั้งในภายหลัง',
  createLightGroup: 'สร้างกลุ่มไฟ (ใหม่)',
  manageLightGroup: 'จัดการกลุ่มไฟ (ใหม่)',
  deleteLightGroup: 'ยกเลิกการจับกลุ่มไฟ',
  deleteCurtainGroup: 'ยกเลิกการจับกลุ่มอุปกรณ์',
  linkDevice: 'เชื่อมต่ออุปกรณ์',
  noSuppurtedLinkageDevice: 'ไม่มีอุปกรณ์ที่พร้อมใช้งาน',
  noSuppurtedLinkageTip: '1. ตรวจสอบให้แน่ใจว่าคุณได้เพิ่มอุปกรณ์ในแอป Xiaomi Home และกำหนดไปยังห้องต่าง ๆ แล้ว\\n2. วางอุปกรณ์บลูทูธไว้ใกล้กับอุปกรณ์นี้เพื่อให้เชื่อมต่อได้สำเร็จ',
  supportedLinkageDevices: 'สามารถเชื่อมต่อกับอุปกรณ์ดังต่อไปนี้:',
  linkageDistanceTip: 'วางอุปกรณ์ไว้ใกล้กันเพื่อให้แน่ใจว่าสามารถเชื่อมต่อได้',
  linkageRemoveTip: 'หากต้องการเปลี่ยนอุปกรณ์บลูทูธที่เชื่อมต่อ ให้ถอดอุปกรณ์ออกก่อน',
  link: 'ลิงก์',
  removeLink: 'ลบ',
  linkFail: 'ไม่สามารถเชื่อมต่อ',
  removeLinkFail: 'ไม่สามารถลบ',
  linkConfirm: 'เชื่อมต่อกับอุปกรณ์นี้ในตอนนี้?',
  removeLinkConfirm: 'นำออกตอนนี้?',
  linking: 'กำลังเชื่อมต่อ…',
  linkDeviceBracelet: 'เชื่อมต่อแบนด์',
  scanDeviceBracelet: 'กำลังสแกนหาแบนด์…',
  scanDeviceBraceletTip: 'วาง Mi Band ใกล้กับอุปกรณ์นี้และตรวจสอบให้แน่ใจว่าบลูทูธเปิดอยู่เพื่อเชื่อมต่อสำเร็จ',
  scanDeviceBraceletEmptyTitle: 'ไม่สามารถค้นหา Mi Bands ที่อยู่ใกล้เคียง',
  scanDeviceBraceletEmptyTip1: '1. ตรวจสอบว่าบลูทูธของแบนด์เปิดอยู่',
  scanDeviceBraceletEmptyTip2: '2. วางแบนด์ไว้ใกล้กับอุปกรณ์อื่น ๆ',
  linkedDeviceBraceletHeaderTip: 'เชื่อมต่อกับแบนด์ต่อไปนี้:',
  availableLinkDeviceBraceletHeaderTip: 'สามารถเชื่อมต่อกับแบนด์ต่อไปนี้:',
  linkedDeviceBraceletFooterTip: 'หากต้องการเปลี่ยนแบนด์ที่เชื่อมต่อ ให้ถอดแบนด์ออกก่อน',
  availableLinkDeviceBraceletFooterTip: 'ตรวจสอบให้แน่ใจว่าบลูทูธของแบนด์นั้นเปิดอยู่ และวางไว้ใกล้กับอุปกรณ์อื่น',
  pluginVersion: 'เวอร์ชันปลั๊กอิน',
  helpAndFeedback: 'ความช่วยเหลือและข้อเสนอแนะ',
  offline: 'ออฟไลน์',
  downloading: 'กำลังดาวน์โหลด...',
  installing: 'กำลังติดตั้ง…',
  upgradeSuccess: 'อัปเดตเรียบร้อยแล้ว',
  upgradeFailed: 'ไม่สามารถอัปเดตได้ ลองอีกครั้งภายหลัง',
  upgradeTimeout: 'การอัปเดตหมดเวลา',
  autoUpgradeInfo: 'จะพยายามอัปเดตโดยอัตโนมัติระหว่าง {0}',
  today: 'วันนี้',
  tomorrow: 'พรุ่งนี้',
  currentIsLatestVersion: 'เวอร์ชั่นปัจจุบันเป็นเวอร์ชั่นล่าสุดแล้ว',
  lastestVersion: 'เวอร์ชั่นล่าสุด: ',
  currentVersion: 'เวอร์ชั่นปัจจุบัน: ',
  fetchFailed: 'ไม่สามารถเข้าถึง ลองอีกครั้ง',
  releaseNote: 'มีอะไรใหม่',
  releaseVersionHistory: 'ประวัติการอัปเดตเฟิร์มแวร์',
  firmwareAutoUpdate: 'การอัปเดตเฟิร์มแวร์อัตโนมัติ',
  autoUpdateDescriptionNote: 'เมื่อตรวจพบเฟิร์มแวร์ใหม่ อุปกรณ์จะพยายามอัปเดตโดยอัตโนมัติระหว่าง {0} การอัปเดตจะถูกติดตั้งเมื่อคุณไม่ได้ใช้งานอุปกรณ์ และจะไม่มีการแจ้งเตือนด้วยเสียงหรือแสงระหว่างกระบวนการอัปเดต',
  updateNow: 'อัปเดต',
  requireBelMesh: 'ฟีเจอร์นี้ต้องใช้เกตเวย์บลูทูธเมชเพื่อให้ให้ทำงานได้ตามปกติ',
  createCurtainGroup: 'สร้างกลุ่มผ้าม่าน',
  createCurtainGroupTip: 'มอเตอร์ม่านสองตัวสามารถรวมกันเป็นกลุ่มที่สามารถควบคุมเป็นม่านสองด้านได้',
  act: 'ย้าย',
  create: 'สร้าง',
  chooseCurtainGroupTitle: 'เลือกมอเตอร์ม่าน',
  currentDevice: 'อุปกรณ์นี้',
  curtain: 'ผ้าม่าน',
  noCurtainGroupTip: 'ไม่สามารถจัดกลุ่มได้ในขณะนี้ เพิ่มมอเตอร์ม่านอีกตัวแล้วลองอีกครั้ง',
  switchPlugin: 'ปลั๊กอินมาตรฐาน',
  defaultPlugin: 'ค่าเริ่มต้น',
  selectDefaultHP: 'ค่าเริ่มต้น',
  stdPluginTitle: 'มาตรฐาน',
  thirdPluginTitle: 'ดั้งเดิม',
  stdPluginSubTitle: 'คุณสามารถสลับไปยังหน้าเวอร์ชันเก่าได้ในคุณสมบัติเพิ่มเติม',
  stdGuideDialogTitle: 'เวอร์ชันใหม่ที่พร้อมใช้งาน',
  stdGuideDialogSubTitle: 'อัปเดตแอปเพื่อรับประสบการณ์ใหม่ที่คล่องตัวยิ่งขึ้น',
  stdGuideDialogNote: 'หากคุณไม่พบคุณสมบัติหลังการอัปเดต มันอาจถูกย้ายไปที่ "คุณสมบัติเพิ่มเติม"',
  stdGuideDialogButtonOK: 'ตกลง',
  // 多键开关设置
  key: 'สลับ',
  keyLeft: 'สวิตช์ด้านซ้าย',
  keyMiddle: 'สวิตช์กลาง',
  keyRight: 'สวิตช์ด้านขวา',
  keyType: 'ประเภทสวิตช์',
  keyName: 'ชื่อ',
  light: 'โคมไฟ',
  updateIcon: 'เปลี่ยนไอคอน',
  done: 'เสร็จสิ้น',
  modifyName: 'แก้ไขชื่อ',
  keyUpdateIconTips: 'เมื่อไอคอนเปลี่ยนเป็น "{0}" คุณสามารถขอให้ AI เปิด "{0}"',
  nameHasChars: 'ชื่อไม่สามารถมีอักขระพิเศษ',
  nameTooLong: 'ชื่อมีอักขระได้สูงสุด 40 ตัว',
  nameIsEmpty: 'ชื่อต้องไม่เว้นว่าง',
  nameNotSupportEmoji: 'ชื่อต้องไม่มีอีโมจิ',
  // 房间
  room: 'ห้อง',
  room_nameInputTips: 'ป้อนชื่อห้อง',
  room_nameSuggest: 'ชื่อที่แนะนำ',
  room_createNew: 'สร้างห้องใหม่',
  room_bedroom: 'ห้องนอน',
  room_masterBedroom: 'ห้องนอนใหญ่',
  room_secondBedroom: 'ห้องนอนรอง',
  room_kitchen: 'ห้องครัว',
  room_diningRoom: 'ห้องอาหาร',
  room_washroom: 'ห้องน้ำ',
  room_childrensRoom: 'ห้องเด็ก',
  room_office: 'ห้องเรียน',
  room_study: 'ห้องสมุด',
  room_balcony: 'ระเบียง',
  room_studio: 'ห้องทำงาน',
  room_bathroom: 'ห้องอาบน้ำ',
  room_backyard: 'สนามหลังบ้าน',
  room_unassigned: 'ไม่ได้กำหนด',
  no_privacy_tip_content: 'ไม่สามารถโหลดนโยบายความเป็นส่วนตัว ตรวจสอบการตั้งค่าเครือข่ายของคุณแล้วลองอีกครั้งหรือรายงานปัญหานี้ผ่านทางข้อเสนอแนะ',
  moreDeviceInfo: 'ข้อมูลอุปกรณ์เพิ่มเติม',
  deviceNet: 'เครือข่ายอุปกรณ์',
  customizeName: 'ชื่อที่กำหนดเอง',
  software: 'ซอฟต์แวร์',
  hardware: 'ฮาร์ดแวร์',
  bleMeshGateway: 'เกตเวย์บลูทูธเมช',
  deviceDid: 'ID อุปกรณ์',
  deviceSN: 'SN อุปกรณ์',
  mcuVersion: 'เวอร์ชั่นเฟิร์มแวร์ MCU',
  sdkVersion: 'เวอร์ชั่นเฟิร์มแวร์ SDK',
  deviceModel: 'รุ่นอุปกรณ์',
  deviceQR: 'รหัส QR ของอุปกรณ์',
  download: 'ดาวน์โหลด',
  saveSuccess: 'บันทึกเรียบร้อยแล้ว',
  saveFailed: 'ไม่สามารถบันทึกได้',
  clipboardy: 'คัดลอกเรียบร้อยแล้ว',
  connected: 'เชื่อมต่อแล้ว',
  notConnected: 'ยังไม่ได้เชื่อมต่อ',
  bleConnected: 'การเชื่อมต่อบลูทูธโดยตรง',
  deviceOffline: 'ออฟไลน์',
  deviceConsumables: 'วัสดุอุปกรณ์',
  consumableStateSufficient: 'เพียงพอ',
  consumableStateInsufficient: 'ไม่เพียงพอ',
  consumableStateUnknown: 'ไม่ทราบสถานะ',
  consumableStateDepletion: 'หมดเกลี้ยง',
  consumableStateRemainPercent: 'คงเหลือ {0}%',
  consumableStateEstimatedHour: {
    'zero': 'เหลืออีก {0} ชม.',
    'one': 'คงเหลือ {0} ชม.',
    'two': 'เหลืออีก {0} ชม.',
    'few': 'เหลืออีก {0} ชม.',
    'many': 'เหลืออีก {0} ชม.',
    'other': 'เหลืออีก {0} ชม.'
  },
  consumableStateEstimatedDay: {
    'zero': 'เหลืออีก {0} วัน',
    'one': 'ใช้ได้อีก {0} วัน',
    'two': 'เหลืออีก {0} วัน',
    'few': 'เหลืออีก {0} วัน',
    'many': 'เหลืออีก {0} วัน',
    'other': 'เหลืออีก {0} วัน'
  },
  changeIcon: 'เปลี่ยนไอคอน',
  deviceCall: 'แจ้งเตือนฉุกเฉิน',
  cloudStorage: 'การแจ้งเตือนในการบริการจัดเก็บบนคลาวด์',
  cloudStorageVip: 'รับการแจ้งเตือนเกี่ยวกับสถานะการเป็นสมาชิกคลาวด์',
  largeCardEvent: 'แสดงเหตุการณ์ที่บันทึกไว้ล่าสุดบนการ์ด',
  // 开关智能
  switch_title_controlDevice: 'ควบคุมอุปกรณ์',
  switch_subtitle_controlDeviceType: 'ตั้งค่าประเภทอุปกรณ์สำหรับแต่ละปุ่ม',
  common_listItem_value_unset: 'ไม่ได้ตั้งค่า',
  switch_title_buttonControlDevice: 'อุปกรณ์ที่ควบคุม (${})',
  switch_listItem_title_toWirelessSwitch: 'เปลี่ยนเป็นสวิตช์ไร้สาย',
  switch_listItem_subtile_wirelessSwitchSetting: 'ปุ่มทางกายภาพจะไม่สามารถควบคุมสวิตช์ได้เมื่อคุณสมบัตินี้เปิดอยู่ คุณยังคงสามารถใช้ปุ่มดังกล่าวสำหรับระบบอัตโนมัติได้',
  switch_dia_msg_wirelessSwitchSetting: 'ปุ่มนี้เชื่อมโยงกับรายการอื่น (${}) เปลี่ยนเป็นสวิตช์ไร้สายเพื่อใช้งาน',
  switch_listItem_title_voiceControlLoop: 'คำสั่งเสียงสำหรับสวิตช์',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'ควบคุมสวิตช์ด้วย Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'เปิด',
  switch_listItem_value_voiceControlLoopOff: 'ปิด',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'ควบคุมสวิตช์ด้วยคำสั่งเสียงผ่าน Mi AI หากหลอดไฟอัจฉริยะเชื่อมต่อกับสวิตช์ หลอดไฟอาจปิดอยู่และตัดการเชื่อมต่อ',
  switch_listItem_title_operationMode: 'โหมดปฏิบัติการ',
  switch_listItem_title_speedMode: 'โหมดความเร็วสูง',
  switch_listItem_title_standardMode: 'โหมดมาตรฐาน',
  switch_listItem_subtile_speedModeDescription: 'เลือกตัวเลือกนี้หากระบบอัตโนมัติจำเป็นต้องตั้งค่าสำหรับ "กดครั้งเดียว" เท่านั้น ตัวเลือกนี้จะปรับปรุงเวลาตอบสนองของระบบอัตโนมัติ',
  switch_listItem_subtile_standardModeDescription: 'เลือกตัวเลือกนี้ หากอุปกรณ์ต้องตั้งค่าระบบอัตโนมัติแบบ "กดสองครั้ง" หรือ "กดค้าง"',
  switch_dia_msg_speedModeMessage: 'อุปกรณ์นี้มีการตั้งค่าระบบอัตโนมัติ "กดสองครั้ง" และ "กดค้าง" ไว้แล้ว หากคุณเลือกโหมดความเร็วสุดยอด คุณจะไม่สามารถใช้ระบบอัตโนมัตินี้ได้อีกต่อไป ดำเนินการต่อหรือไม่?',
  switch_title_selectDeviceType: 'เลือกประเภทอุปกรณ์',
  switch_subtitle_selectDeviceType: 'เลือกประเภทอุปกรณ์ที่ควบคุมโดย ${}',
  switch_subtitle_liveWire_selectDeviceType: 'เลือกประเภทอุปกรณ์ที่ควบคุมโดย ${} ปล่อยให้ปุ่มเดียวเชื่อมต่อกับอุปกรณ์ทั่วไปเพื่อให้แน่ใจว่าสวิตช์ทำงานได้ตามปกติ',
  switch_title_deviceType_normalDevice: 'อุปกรณ์ทั่วไป (ไฟและโคมไฟที่ไม่มีฟังก์ชันอัจฉริยะ)',
  switch_title_deviceType_smartLight: 'ไฟอัจฉริยะ',
  switch_title_deviceType_smartSwitch: 'สวิตช์อัจฉริยะอื่นๆ',
  switch_title_deviceType_manualScene: 'การควบคุมแบบกลุ่ม',
  switch_title_deviceType_otherSmartDevice: 'อุปกรณ์อัจฉริยะอื่นๆ',
  switch_value_deviceType_normalDevice: 'อุปกรณ์ปกติ',
  switch_value_deviceType_smartLight: 'ไฟอัจฉริยะ',
  switch_value_deviceType_smartSwitch: 'สวิตช์อัจฉริยะอื่นๆ',
  switch_value_deviceType_manualScene: 'การควบคุมแบบกลุ่ม',
  switch_value_deviceType_otherSmartDevice: 'อุปกรณ์อัจฉริยะอื่นๆ',
  switch_button_title_seeCreatedScene: 'ดูระบบอัตโนมัติ',
  switch_button_title_linkSmartLight: 'เชื่อมต่อไฟอัจฉริยะ',
  switch_button_title_linkSmartSwitch: 'เชื่อมต่อสวิตช์อัจฉริยะ',
  switch_button_title_linkManualScene: 'เชื่อมต่อการควบคุมแบทช์',
  switch_button_title_switchNameSetting: 'ตั้งชื่อปุ่ม',
  switch_nav_title_buttonControlLight: 'ไฟอัจฉริยะที่ควบคุม (${})',
  switch_nav_subtitle_buttonControlLight: 'เชื่อมต่อไฟอัจฉริยะเข้ากับปุ่มเพื่อเปิดและปิดและออนไลน์อยู่เสมอ',
  switch_header_title_selectLightOrGroup: 'เลือกไฟอัจฉริยะหรือกลุ่มไฟ',
  switch_nav_title_buttonControlSwitch: 'สวิตช์อัจฉริยะที่ควบคุม (${})',
  switch_nav_subtitle_buttonControlSwitch: 'เชื่อมต่อสวิตช์เพื่อเพิ่มอินเทอร์เฟซเปิด/ปิดอื่น การกดปุ่มจะเป็นการเปิดและปิดสวิตช์ที่เลือก',
  switch_header_title_selectSwitch: 'เลือกสวิตช์อัจฉริยะ',
  switch_nav_title_buttonControlManualScene: 'การควบคุมแบตช์ที่ได้รับมอบหมาย (${})',
  switch_nav_subtitle_buttonControlManualScene: 'กำหนดชุดควบคุมเพื่อเรียกใช้โดยกดปุ่ม',
  switch_header_title_selectManualScene: 'เลือกการควบคุมแบทช์',
  common_edit: 'แก้ไข',
  common_reselect: 'เลือกอีกครั้ง',
  common_deleted: 'ลบเรียบร้อยแล้ว',
  common_delete: 'ลบ',
  common_delete_failed: 'ไม่สามารถลบได้ ตรวจสอบการตั้งค่าเครือข่ายของคุณแล้วลองอีกครั้ง',
  common_setting_failed: 'ไม่สามารถตั้งค่าได้ ตรวจสอบว่าอุปกรณ์เชื่อมต่อกับเครือข่ายแล้วลองอีกครั้ง',
  common_saving: 'กำลังบันทึก...',
  switch_listItem_title_executionType: 'กำลังเรียกใช้โหมด',
  switch_listItem_value_executionTypeCloud: 'คลาวด์',
  switch_listItem_value_executionTypeLocale: 'ในเครื่อง',
  switch_dia_msg_deleteScene: 'ลบการทำงานอัตโนมัตินี้ใช่ไหม?',
  switch_scene_name_toggleSwitchDevice: '${} | กดครั้งเดียว | ${} | เปิด/ปิด | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | กดครั้งเดียว | ${} | เปิด/ปิด | ${}',
  switch_scene_name_executeManualScene: '${} | กดครั้งเดียว | ${} | ทำงาน | ${}-${}',
  switch_list_device_unavailable: 'อุปกรณ์ที่ไม่แสดงไม่รองรับคุณสมบัตินี้',
  switch_button_subtitle_notCurrentHome: 'ไม่ได้อยู่ในบ้านปัจจุบัน',
  common_list_empty: 'ยังไม่มีอะไรในนี้',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'โหมดการจับคู่',
  switch_title_buttonControlDevice_oneGang: 'การควบคุมอุปกรณ์'
};