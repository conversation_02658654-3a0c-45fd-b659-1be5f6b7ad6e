export default {
  setting: 'Defini<PERSON>õ<PERSON>',
  featureSetting: 'Definições do dispositivo',
  commonSetting: 'Definições gerais',
  name: 'Nome do dispositivo',
  deviceService: 'Serviços do dispositivo',
  location: 'Gerir localizações',
  memberSet: 'Botõ<PERSON>',
  share: 'Partilhar dispositivo',
  btGateway: 'Gateway BLE',
  voiceAuth: 'Autorização de voz',
  ifttt: 'Automação',
  productBaike: 'Informação do produto',
  firmwareUpgrade: 'Atualização de \'firmware\'',
  firmwareUpdate: 'Atualização de \'firmware\'',
  more: 'Definições adicionais',
  help: 'Ajuda',
  legalInfo: 'Informações legais',
  deleteDevice: 'Eliminar o dispositivo',
  autoUpgrade: 'Atualizar \'firmware\' automaticamente',
  checkUpgrade: 'Verificar se há atualizações de \'firmware\'',
  security: 'Definições de segurança',
  networkInfo: 'Informações da rede',
  feedback: 'Comentários',
  timezone: 'Fuso horário do dispositivo',
  addToDesktop: 'Adicionar ao Ecrã inicial',
  open: 'Ligado',
  close: 'Desligado',
  other: 'Outro',
  multipleKeyShowOnHome: 'O número de botões mostrados na página inicial: {0}',
  // 常用设备
  favoriteDevices: 'Mostrar na página inicial da Xiaomi Home',
  favoriteCamera: 'Tamanho dos cartões do painel',
  favoriteAddDevices: 'Adicionar aos favoritos',
  // MHDatePicker
  cancel: 'Cancelar',
  ok: 'Confirmar',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} meses',
    'one': '{0} mês',
    'two': '{0} meses',
    'few': '{0} meses',
    'many': '{0} meses',
    'other': '{0} meses'
  },
  numberDay: {
    'zero': '{0} dias',
    'one': '{0} dia',
    'two': '{0} dias',
    'few': '{0} dias',
    'many': '{0} dias',
    'other': '{0} dias'
  },
  numberHour: {
    'zero': '{0} h',
    'one': '{0} h',
    'two': '{0} h',
    'few': '{0} h',
    'many': '{0} h',
    'other': '{0} h'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} s',
    'one': '{0} s',
    'two': '{0} s',
    'few': '{0} s',
    'many': '{0} s',
    'other': '{0} s'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Sair',
  firmwareUpgradeUpdate: 'Atualizar',
  firmwareUpgradeLook: 'Ver',
  firmwareUpgradeForceUpdate: 'O firmware atual pode ser muito antigo para executar algumas funcionalidades. Atualize para a versão mais recente para obter uma melhor experiência.',
  firmwareUpgradeForceUpdating: 'O dispositivo está a atualizar. Tente novamente mais tarde.',
  firmwareUpgradeNew_pre: 'Uma atualização está disponível. ',
  firmwareUpgradeNew_sub: 'Atualizar agora?',
  handling: 'Um momento…',
  error: 'Ocorreu um erro. Tente novamente mais tarde.',
  createLightGroup: 'Criar grupo de luzes (novo)',
  manageLightGroup: 'Gerir grupo de luzes (novo)',
  deleteLightGroup: 'Desagrupar luzes',
  deleteCurtainGroup: 'Desagrupar dispositivos',
  linkDevice: 'Associar dispositivos',
  noSuppurtedLinkageDevice: 'Nenhum dispositivo disponível',
  noSuppurtedLinkageTip: '1. Verifique se adicionou os dispositivos na aplicação Xiaomi Home e os atribuiu às divisões.\\n2. Mantenha os dispositivos Bluetooth próximos deste dispositivo para conectá-los com sucesso.',
  supportedLinkageDevices: 'Pode ser associado com os seguintes dispositivos:',
  linkageDistanceTip: 'Mantenha os dispositivos próximos para garantir que eles possam ser associados.',
  linkageRemoveTip: 'Para alterar o dispositivo Bluetooth vinculado, remova o dispositivo primeiro.',
  link: 'Associar',
  removeLink: 'Remover',
  linkFail: 'Não foi possível associar',
  removeLinkFail: 'Não foi possível remover',
  linkConfirm: 'Associar com este dispositivo agora?',
  removeLinkConfirm: 'Remover agora?',
  linking: 'A associar…',
  linkDeviceBracelet: 'Associar pulseira',
  scanDeviceBracelet: 'A pesquisar por pulseira…',
  scanDeviceBraceletTip: 'Mantenha a pulseira Mi Band próxima deste dispositivo e verifique se o Bluetooth está ligado para conectar com sucesso.',
  scanDeviceBraceletEmptyTitle: 'Não foi possível encontrar Mi Bands por perto',
  scanDeviceBraceletEmptyTip1: '1. Certifique-se de que o Bluetooth da pulseira está ligado.',
  scanDeviceBraceletEmptyTip2: '2. Mantenha a pulseira próxima do outro dispositivo.',
  linkedDeviceBraceletHeaderTip: 'Associado às seguintes pulseiras:',
  availableLinkDeviceBraceletHeaderTip: 'Pode ser associado com as seguintes pulseiras:',
  linkedDeviceBraceletFooterTip: 'Para alterar a pulseira associada, remova a pulseira primeiro.',
  availableLinkDeviceBraceletFooterTip: 'Certifique-se de que o Bluetooth da pulseira está ligado e mantenha-a próximo do outro dispositivo.',
  pluginVersion: 'Versão do \'plug-in\'',
  helpAndFeedback: 'Ajuda e Comentários',
  offline: 'Desligado',
  downloading: 'A transferir…',
  installing: 'A instalar…',
  upgradeSuccess: 'Atualizado com sucesso',
  upgradeFailed: 'Não foi possível atualizar. Tente novamente mais tarde.',
  upgradeTimeout: 'A atualização expirou',
  autoUpgradeInfo: 'Tentará atualizar automaticamente entre {0}',
  today: 'Hoje',
  tomorrow: 'Amanhã',
  currentIsLatestVersion: 'A versão atual está atualizada',
  lastestVersion: 'Última versão: ',
  currentVersion: 'Versão atual: ',
  fetchFailed: 'Não foi possível aceder. Tente novamente.',
  releaseNote: 'O que há de novo',
  releaseVersionHistory: 'Histórico de atualização do \'firmware\'',
  firmwareAutoUpdate: 'Atualizações automáticas do \'firmware\'',
  autoUpdateDescriptionNote: 'Assim que um novo \'firmware\' for detetado, o dispositivo tentará atualizar automaticamente entre {0}. A atualização será instalada quando não estiver a usar o dispositivo e não haverá notificações de áudio ou luz durante o processo de atualização.',
  updateNow: 'Atualização',
  requireBelMesh: 'Esta funcionalidade requer um \'gateway\' de malha Bluetooth para funcionar normalmente.',
  createCurtainGroup: 'Criar um grupo de cortinas',
  createCurtainGroupTip: 'Dois motores de cortinas podem ser combinados num grupo que pode ser controlado como uma cortina de dupla face.',
  act: 'Mover',
  create: 'Criar',
  chooseCurtainGroupTitle: 'Selecionar um motor de cortina',
  currentDevice: 'Este dispositivo',
  curtain: 'Cortinas',
  noCurtainGroupTip: 'Não é possível agrupar agora. Adicione outro motor de cortinas e tente novamente.',
  switchPlugin: '\'Plug-in\' padrão',
  defaultPlugin: 'Predefinido',
  selectDefaultHP: 'Predefinido',
  stdPluginTitle: 'Padrão',
  thirdPluginTitle: 'Tradicional',
  stdPluginSubTitle: 'Pode mudar para a versão antiga da página nas funcionalidades adicionais',
  stdGuideDialogTitle: 'Nova versão disponível',
  stdGuideDialogSubTitle: 'Atualize a aplicação para uma experiência nova e mais simplificada.',
  stdGuideDialogNote: 'Se não conseguir encontrar uma funcionalidade após uma atualização, a mesma pode ter sido movida para as "Funcionalidades adicionais".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Interruptor',
  keyLeft: 'Interruptor esquerdo',
  keyMiddle: 'Interruptor do meio',
  keyRight: 'Interruptor direito',
  keyType: 'Tipo de interruptor',
  keyName: 'Nome',
  light: 'Iluminação',
  updateIcon: 'Alterar ícone',
  done: 'Concluído',
  modifyName: 'Editar nome',
  keyUpdateIconTips: 'Quando o ícone mudar para "{0}", pode pedir ao Mi AI para ligar "{0}".',
  nameHasChars: 'O nome não pode conter caracteres especiais',
  nameTooLong: 'O nome pode ter até 40 carateres',
  nameIsEmpty: 'O nome não pode estar em branco',
  nameNotSupportEmoji: 'O nome não pode incluir \'emoji\'',
  // 房间
  room: 'Divisão',
  room_nameInputTips: 'Introduzir um nome de uma divisão',
  room_nameSuggest: 'Nome recomendado',
  room_createNew: 'Criar uma nova divisão',
  room_bedroom: 'Quarto',
  room_masterBedroom: 'Quarto principal',
  room_secondBedroom: 'Segundo quarto',
  room_kitchen: 'Cozinha',
  room_diningRoom: 'Sala de jantar',
  room_washroom: 'Casa de banho',
  room_childrensRoom: 'Quarto das crianças',
  room_office: 'Escritório',
  room_study: 'Biblioteca',
  room_balcony: 'Varanda',
  room_studio: 'Oficina',
  room_bathroom: 'Casa de banho',
  room_backyard: 'Quintal',
  room_unassigned: 'Não atribuídos',
  no_privacy_tip_content: 'Não foi possível carregar a Política de Privacidade. Verifique as suas configurações de rede e tente novamente ou relate o problema através de Comentários.',
  moreDeviceInfo: 'Mais informação do dispositivo',
  deviceNet: 'Rede do dispositivo',
  customizeName: 'Nome personalizado',
  software: '\'Software\'',
  hardware: '\'Hardware\'',
  bleMeshGateway: '\'Gateway\' de malha Bluetooth',
  deviceDid: 'ID do Dispositivo',
  deviceSN: 'SN do dispositivo',
  mcuVersion: 'Versão do \'firmware\' MCU',
  sdkVersion: 'Versão do \'firmware\' do SDK',
  deviceModel: 'Modelo do dispositivo',
  deviceQR: 'Código QR do dispositivo',
  download: 'Descarregar',
  saveSuccess: 'Guardado com sucesso',
  saveFailed: 'Não foi possível guardar',
  clipboardy: 'Copiado com sucesso',
  connected: 'Ligado',
  notConnected: 'Desligado',
  bleConnected: 'Conexão Bluetooth direta',
  deviceOffline: 'Desligado',
  deviceConsumables: 'Consumíveis do dispositivo',
  consumableStateSufficient: 'Suficiente',
  consumableStateInsufficient: 'Insuficiente',
  consumableStateUnknown: 'Estado desconhecido',
  consumableStateDepletion: 'Usado',
  consumableStateRemainPercent: '{0}% restante',
  consumableStateEstimatedHour: {
    'zero': '{0} h restantes',
    'one': '{0} h restante',
    'two': '{0} h restantes',
    'few': '{0} h restantes',
    'many': '{0} h restantes',
    'other': '{0} h restantes'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} dias restantes',
    'one': '{0} dia restante',
    'two': '{0} dias restantes',
    'few': '{0} dias restantes',
    'many': '{0} dias restantes',
    'other': '{0} dias restantes'
  },
  changeIcon: 'Alterar ícone',
  deviceCall: 'Alertas de emergência',
  cloudStorage: 'Notificações de armazenamento na nuvem',
  cloudStorageVip: 'Receba notificações sobre o estado de associação na nuvem',
  largeCardEvent: 'Mostrar eventos capturados recentemente no cartão',
  // 开关智能
  switch_title_controlDevice: 'Controlar dispositivos',
  switch_subtitle_controlDeviceType: 'Defina tipos de dispositivos para cada botão',
  common_listItem_value_unset: 'Não definido',
  switch_title_buttonControlDevice: 'Dispositivos controlados (${})',
  switch_listItem_title_toWirelessSwitch: 'Mudar para interruptor sem fio',
  switch_listItem_subtile_wirelessSwitchSetting: 'Os botões físicos não poderão controlar os interruptores quando esta funcionalidade estiver ligada. Ainda assim poderá usá-los para automações.',
  switch_dia_msg_wirelessSwitchSetting: 'Este botão está associado a outro item (${}). Altere-o para um interruptor sem fio para utilizá-lo.',
  switch_listItem_title_voiceControlLoop: 'Comandos de voz para interruptores',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Controle os interruptores com a Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Ligado',
  switch_listItem_value_voiceControlLoopOff: 'Desligado',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Controle interruptores com comandos de voz via Mi AI. Se luzes inteligentes estiverem conectadas ao interruptor, elas poderão ser desligadas e desconectadas.',
  switch_listItem_title_operationMode: 'Modo de operação',
  switch_listItem_title_speedMode: 'Modo de alta velocidade',
  switch_listItem_title_standardMode: 'Modo predefinido',
  switch_listItem_subtile_speedModeDescription: 'Selecione esta opção se as automações só precisarem de ser configuradas para "toque único". Esta opção melhorará o tempo de resposta da automação.',
  switch_listItem_subtile_standardModeDescription: 'Selecione esta opção se o dispositivo precisar de configurar automações de "toque duplo" ou "manter pressionado"',
  switch_dia_msg_speedModeMessage: 'Este dispositivo já possui automações de “toque duplo” e “manter pressionado” configuradas. Se selecionar o modo Super velocidade, não poderá mais usar estas automações. Continuar na mesma?',
  switch_title_selectDeviceType: 'Selecione o tipo do dispositivo',
  switch_subtitle_selectDeviceType: 'Selecione o tipo do dispositivo controlado por ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Selecione o tipo do dispositivo controlado por ${}. Deixe um botão ligado a dispositivos regulares para garantir que o interruptor funcione normalmente.',
  switch_title_deviceType_normalDevice: 'Dispositivos regulares (luzes e candeeiros sem funcionalidade inteligente)',
  switch_title_deviceType_smartLight: 'Luzes inteligentes',
  switch_title_deviceType_smartSwitch: 'Outros interruptores inteligentes',
  switch_title_deviceType_manualScene: 'Controlos em lote',
  switch_title_deviceType_otherSmartDevice: 'Outros dispositivos inteligentes',
  switch_value_deviceType_normalDevice: 'Dispositivos regulares',
  switch_value_deviceType_smartLight: 'Luzes inteligentes',
  switch_value_deviceType_smartSwitch: 'Outros interruptores inteligentes',
  switch_value_deviceType_manualScene: 'Controlos em lote',
  switch_value_deviceType_otherSmartDevice: 'Outros dispositivos inteligentes',
  switch_button_title_seeCreatedScene: 'Ver automações',
  switch_button_title_linkSmartLight: 'Ligar luz inteligente',
  switch_button_title_linkSmartSwitch: 'Ligar interruptor inteligente',
  switch_button_title_linkManualScene: 'Ligar controlo em lote',
  switch_button_title_switchNameSetting: 'Definir nome do botão',
  switch_nav_title_buttonControlLight: 'Luzes inteligentes controladas (${})',
  switch_nav_subtitle_buttonControlLight: 'Ligue luzes inteligentes a um botão para ligá-los ou desligá-los e mantê-los online',
  switch_header_title_selectLightOrGroup: 'Selecione uma luz inteligente ou grupo de luzes',
  switch_nav_title_buttonControlSwitch: 'Interruptores inteligentes controlados (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Ligue interruptores para adicionar outra interface liga/desliga. Pressionar o botão ligará e desligará os interruptores selecionados.',
  switch_header_title_selectSwitch: 'Selecionar interruptores inteligentes',
  switch_nav_title_buttonControlManualScene: 'Controlos em lote atribuídos (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Atribuir controlos em lote para executá-los ao pressionar o botão',
  switch_header_title_selectManualScene: 'Selecionar controlos em lote',
  common_edit: 'Editar',
  common_reselect: 'Selecionar novamente',
  common_deleted: 'Eliminado',
  common_delete: 'Eliminar',
  common_delete_failed: 'Não foi possível eliminar. Verifique as suas definições de rede e tente novamente.',
  common_setting_failed: 'Não foi possível definir. Verifique se o dispositivo está ligado à rede e tente novamente.',
  common_saving: 'A guardar…',
  switch_listItem_title_executionType: 'Modo de execução',
  switch_listItem_value_executionTypeCloud: 'Nuvem',
  switch_listItem_value_executionTypeLocale: 'Local',
  switch_dia_msg_deleteScene: 'Eliminar esta automação?',
  switch_scene_name_toggleSwitchDevice: '${} | Pressionamento único | ${} | Ligado/Desligado | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Pressionamento único | ${} | Ligado/Desligado | ${}',
  switch_scene_name_executeManualScene: '${} | Pressionamento único | ${} | A executar | ${}-${}',
  switch_list_device_unavailable: 'Os dispositivos não mostrados não são compatíveis com esta funcionalidade',
  switch_button_subtitle_notCurrentHome: 'Não está na casa atual',
  common_list_empty: 'Nada aqui ainda',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Modo de Emparelhamento',
  switch_title_buttonControlDevice_oneGang: 'Controlos de dispositivos'
};