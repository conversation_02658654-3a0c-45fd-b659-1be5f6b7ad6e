export default {
  setting: 'Settings',
  featureSetting: 'Device settings',
  commonSetting: 'General settings',
  name: 'Device name',
  deviceService: 'Device services',
  location: 'Manage locations',
  memberSet: 'Buttons',
  share: 'Share device',
  btGateway: 'BLE gateway',
  voiceAuth: 'Voice authorization',
  ifttt: 'Automation',
  productBaike: 'Product info',
  firmwareUpgrade: 'Firmware update',
  firmwareUpdate: 'Firmware update',
  more: 'Additional settings',
  help: 'Help',
  legalInfo: 'Legal information',
  deleteDevice: 'Delete device',
  autoUpgrade: 'Update firmware automatically',
  checkUpgrade: 'Check for firmware updates',
  security: 'Security settings',
  networkInfo: 'Network info',
  feedback: 'Feedback',
  timezone: 'Device time zone',
  addToDesktop: 'Add to Home screen',
  open: 'On',
  close: 'Off',
  other: 'Other',
  multipleKeyShowOnHome: 'The number of buttons shown on the home page: {0}',
  // 常用设备
  favoriteDevices: 'Display on Xiaomi Home home page',
  favoriteCamera: 'Dashboard card size',
  favoriteAddDevices: 'Add to favorites',
  // MHDatePicker
  cancel: 'Cancel',
  ok: 'Confirm',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} months',
    'one': '{0} month',
    'two': '{0} months',
    'few': '{0} months',
    'many': '{0} months',
    'other': '{0} months'
  },
  numberDay: {
    'zero': '{0} days',
    'one': '{0} day',
    'two': '{0} days',
    'few': '{0} days',
    'many': '{0} days',
    'other': '{0} days'
  },
  numberHour: {
    'zero': '{0} hrs',
    'one': '{0} hr',
    'two': '{0} hrs',
    'few': '{0} hrs',
    'many': '{0} hrs',
    'other': '{0} hrs'
  },
  numberMinute: {
    'zero': '{0} mins',
    'one': '{0} min',
    'two': '{0} mins',
    'few': '{0} mins',
    'many': '{0} mins',
    'other': '{0} mins'
  },
  numberSecond: {
    'zero': '{0} secs',
    'one': '{0} sec',
    'two': '{0} secs',
    'few': '{0} secs',
    'many': '{0} secs',
    'other': '{0} secs'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Exit',
  firmwareUpgradeUpdate: 'Update',
  firmwareUpgradeLook: 'View',
  firmwareUpgradeForceUpdate: 'Current firmware may be too old to run some features. Update to the latest version for better experience.',
  firmwareUpgradeForceUpdating: 'Device is updating. Try again later.',
  firmwareUpgradeNew_pre: 'An update is available. ',
  firmwareUpgradeNew_sub: 'Update now?',
  handling: 'Just a sec…',
  error: 'An error occurred. Try again later.',
  createLightGroup: 'Create light group (new)',
  manageLightGroup: 'Manage light group (new)',
  deleteLightGroup: 'Ungroup lights',
  deleteCurtainGroup: 'Ungroup devices',
  linkDevice: 'Link devices',
  noSuppurtedLinkageDevice: 'No devices available',
  noSuppurtedLinkageTip: '1. Make sure you\'ve added the devices in the Xiaomi Home app and assigned them to rooms.\\n2. Keep Bluetooth devices close to this device to connect them successfully.',
  supportedLinkageDevices: 'Can be linked with the following devices:',
  linkageDistanceTip: 'Keep the devices in close proximity to make sure they\'re able to link.',
  linkageRemoveTip: 'To change the linked Bluetooth device, remove the device first.',
  link: 'Link',
  removeLink: 'Remove',
  linkFail: 'Couldn\'t link',
  removeLinkFail: 'Couldn\'t remove',
  linkConfirm: 'Link with this device now?',
  removeLinkConfirm: 'Remove now?',
  linking: 'Linking…',
  linkDeviceBracelet: 'Link band',
  scanDeviceBracelet: 'Scanning for band…',
  scanDeviceBraceletTip: 'Keep Mi Band close to this device and make sure its Bluetooth is on to connect successfully.',
  scanDeviceBraceletEmptyTitle: 'Couldn\'t find Mi Bands nearby',
  scanDeviceBraceletEmptyTip1: '1. Make sure the band\'s Bluetooth is turned on.',
  scanDeviceBraceletEmptyTip2: '2. Keep the band close to the other device.',
  linkedDeviceBraceletHeaderTip: 'Linked with the following bands:',
  availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands:',
  linkedDeviceBraceletFooterTip: 'To change the linked band, remove the band first.',
  availableLinkDeviceBraceletFooterTip: 'Make sure the band\'s Bluetooth is turned on, and keep it close to the other device.',
  pluginVersion: 'Plugin version',
  helpAndFeedback: 'Help & Feedback',
  offline: 'Offline',
  downloading: 'Downloading…',
  installing: 'Installing…',
  upgradeSuccess: 'Updated successfully',
  upgradeFailed: 'Couldn\'t update. Try again later.',
  upgradeTimeout: 'Update timed out',
  autoUpgradeInfo: 'Will try to update automatically between {0}',
  today: 'Today',
  tomorrow: 'Tomorrow',
  currentIsLatestVersion: 'Current version is up to date',
  lastestVersion: 'Latest version: ',
  currentVersion: 'Current version: ',
  fetchFailed: 'Couldn\'t access. Try again.',
  releaseNote: 'What\'s new',
  releaseVersionHistory: 'Firmware update history',
  firmwareAutoUpdate: 'Automatic firmware updates',
  autoUpdateDescriptionNote: 'Once a new firmware is detected, the device will try to update automatically between {0}. The update will be installed when you aren\'t using the device and there will be no audio or light notifications during the update process.',
  updateNow: 'Update',
  requireBelMesh: 'This feature requires a Bluetooth mesh gateway to work normally.',
  createCurtainGroup: 'Create a curtain group',
  createCurtainGroupTip: 'Two curtain motors can be combined into a group that can be controlled as a double-sided curtain.',
  act: 'Move',
  create: 'Create',
  chooseCurtainGroupTitle: 'Select a curtain motor',
  currentDevice: 'This device',
  curtain: 'Curtain',
  noCurtainGroupTip: 'Can\'t group now. Add another curtain motor and try again.',
  switchPlugin: 'Standard plugin',
  defaultPlugin: 'Default',
  selectDefaultHP: 'Default',
  stdPluginTitle: 'Standard',
  thirdPluginTitle: 'Traditional',
  stdPluginSubTitle: 'You can switch to the old version of the page in the additional features',
  stdGuideDialogTitle: 'New version available',
  stdGuideDialogSubTitle: 'Update the app for a new, more streamlined experience.',
  stdGuideDialogNote: 'If you can\'t find a feature after an update, it might have been moved to "Additional features".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Switch',
  keyLeft: 'Left switch',
  keyMiddle: 'Middle switch',
  keyRight: 'Right switch',
  keyType: 'Switch type',
  keyName: 'Name',
  light: 'Lamp',
  updateIcon: 'Change icon',
  done: 'Done',
  modifyName: 'Edit name',
  keyUpdateIconTips: 'When the icon is switched to "{0}", you can ask Mi AI to turn on "{0}".',
  nameHasChars: 'Name can\'t contain special characters',
  nameTooLong: 'Name can contain up to 40 characters',
  nameIsEmpty: 'Name can\'t be empty',
  nameNotSupportEmoji: 'Names can\'t include emoji',
  // 房间
  room: 'Room',
  room_nameInputTips: 'Enter a room name',
  room_nameSuggest: 'Recommended name',
  room_createNew: 'Create a new room',
  room_bedroom: 'Bedroom',
  room_masterBedroom: 'Master bedroom',
  room_secondBedroom: 'Second bedroom',
  room_kitchen: 'Kitchen',
  room_diningRoom: 'Dining room',
  room_washroom: 'Toilet',
  room_childrensRoom: 'Kids\' room',
  room_office: 'Study',
  room_study: 'Library',
  room_balcony: 'Balcony',
  room_studio: 'Workshop',
  room_bathroom: 'Bathroom',
  room_backyard: 'Backyard',
  room_unassigned: 'Unassigned',
  no_privacy_tip_content: 'Couldn\'t load Privacy Policy. Check your network settings and try again or report this issue via Feedback.',
  moreDeviceInfo: 'More device info',
  deviceNet: 'Device network',
  customizeName: 'Custom name',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Bluetooth mesh gateway',
  deviceDid: 'Device ID',
  deviceSN: 'Device SN',
  mcuVersion: 'MCU firmware version',
  sdkVersion: 'SDK firmware version',
  deviceModel: 'Device model',
  deviceQR: 'Device QR code',
  download: 'Download',
  saveSuccess: 'Saved successfully',
  saveFailed: 'Couldn\'t save',
  clipboardy: 'Copied successfully',
  connected: 'Connected',
  notConnected: 'Not connected',
  bleConnected: 'Direct Bluetooth connection',
  deviceOffline: 'Offline',
  deviceConsumables: 'Device supplies',
  consumableStateSufficient: 'Sufficient',
  consumableStateInsufficient: 'Insufficient',
  consumableStateUnknown: 'Status unknown',
  consumableStateDepletion: 'Used up',
  consumableStateRemainPercent: '{0}% remaining',
  consumableStateEstimatedHour: {
    'zero': '{0} hrs remaining',
    'one': '{0} hr remaining',
    'two': '{0} hrs remaining',
    'few': '{0} hrs remaining',
    'many': '{0} hrs remaining',
    'other': '{0} hrs remaining'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} days remaining',
    'one': '{0} day remaining',
    'two': '{0} days remaining',
    'few': '{0} days remaining',
    'many': '{0} days remaining',
    'other': '{0} days remaining'
  },
  changeIcon: 'Change icon',
  deviceCall: 'Emergency alerts',
  cloudStorage: 'Cloud storage notifications',
  cloudStorageVip: 'Receive notifications about cloud membership status',
  largeCardEvent: 'Show latest captured events on the card',
  // 开关智能
  switch_title_controlDevice: 'Control devices',
  switch_subtitle_controlDeviceType: 'Set device types for each button',
  common_listItem_value_unset: 'Not set',
  switch_title_buttonControlDevice: 'Controlled devices (${})',
  switch_listItem_title_toWirelessSwitch: 'Change to wireless switch',
  switch_listItem_subtile_wirelessSwitchSetting: 'Physical buttons won\'t be able to control switches when this feature is on. You\'ll still be able to use them for automations.',
  switch_dia_msg_wirelessSwitchSetting: 'This button is associated with another item (${}). Change to wireless switch to use it.',
  switch_listItem_title_voiceControlLoop: 'Voice commands for switches',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Control switches with Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'On',
  switch_listItem_value_voiceControlLoopOff: 'Off',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Control switches with voice commands via Mi AI. If smart lights are connected to the switch, they might be powered off and disconnected.',
  switch_listItem_title_operationMode: 'Operation mode',
  switch_listItem_title_speedMode: 'Super speed mode',
  switch_listItem_title_standardMode: 'Standard mode',
  switch_listItem_subtile_speedModeDescription: 'Select this option if automations only need to be set up for "single press". This option will improve automation response time.',
  switch_listItem_subtile_standardModeDescription: 'Select this option if device needs to set up "double press" or "press and hold" automations',
  switch_dia_msg_speedModeMessage: 'This device already has "double press" and "press and hold" automations set up. If you select Super speed mode, you won\'t be able to use these automations anymore. Continue anyway?',
  switch_title_selectDeviceType: 'Select device type',
  switch_subtitle_selectDeviceType: 'Select device type controlled by ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Select device type controlled by ${}. Leave one button connected to regular devices to make sure the switch works normally.',
  switch_title_deviceType_normalDevice: 'Regular devices (lights and lamps without smart functionality)',
  switch_title_deviceType_smartLight: 'Smart lights',
  switch_title_deviceType_smartSwitch: 'Other smart switches',
  switch_title_deviceType_manualScene: 'Batch controls',
  switch_title_deviceType_otherSmartDevice: 'Other smart devices',
  switch_value_deviceType_normalDevice: 'Regular devices',
  switch_value_deviceType_smartLight: 'Smart lights',
  switch_value_deviceType_smartSwitch: 'Other smart switches',
  switch_value_deviceType_manualScene: 'Batch controls',
  switch_value_deviceType_otherSmartDevice: 'Other smart devices',
  switch_button_title_seeCreatedScene: 'View automations',
  switch_button_title_linkSmartLight: 'Connect smart light',
  switch_button_title_linkSmartSwitch: 'Connect smart switch',
  switch_button_title_linkManualScene: 'Connect batch control',
  switch_button_title_switchNameSetting: 'Set button name',
  switch_nav_title_buttonControlLight: 'Controlled smart lights (${})',
  switch_nav_subtitle_buttonControlLight: 'Connect smart lights to a button to turn them on and off and keep them online',
  switch_header_title_selectLightOrGroup: 'Select smart light or light group',
  switch_nav_title_buttonControlSwitch: 'Controlled smart switches (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Connect switches to add another on/off interface. Pressing the button will turn selected switches on and off.',
  switch_header_title_selectSwitch: 'Select smart switches',
  switch_nav_title_buttonControlManualScene: 'Assigned batch controls (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Assign batch controls to run them by pressing the button',
  switch_header_title_selectManualScene: 'Select batch controls',
  common_edit: 'Edit',
  common_reselect: 'Select again',
  common_deleted: 'Deleted',
  common_delete: 'Delete',
  common_delete_failed: 'Couldn\'t delete. Check your network settings and try again.',
  common_setting_failed: 'Couldn\'t set. Check whether the device is connected to the network and try again.',
  common_saving: 'Saving…',
  switch_listItem_title_executionType: 'Running mode',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Local',
  switch_dia_msg_deleteScene: 'Delete this automation?',
  switch_scene_name_toggleSwitchDevice: '${} | Single press | ${} | On/Off | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Single press | ${} | On/Off | ${}',
  switch_scene_name_executeManualScene: '${} | Single press | ${} | Run | ${}-${}',
  switch_list_device_unavailable: 'Devices not shown don\'t support this feature',
  switch_button_subtitle_notCurrentHome: 'Not in the current home',
  common_list_empty: 'Nothing here yet',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Pairing mode',
  switch_title_buttonControlDevice_oneGang: 'Device controls'
};