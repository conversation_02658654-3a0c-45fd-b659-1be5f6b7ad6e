export default {
  setting: 'Cài đặt',
  featureSetting: 'Cài đặt thiết bị',
  commonSetting: '<PERSON>ài đặt chung',
  name: 'Tên thiết bị',
  deviceService: '<PERSON>ị<PERSON> vụ của thiết bị',
  location: 'Quản lý các vị trí',
  memberSet: 'Nút',
  share: 'Chia sẻ thiết bị',
  btGateway: 'Cổng BLE',
  voiceAuth: 'Cấp quyền bằng giọng nói',
  ifttt: 'Tự động hóa',
  productBaike: 'Thông tin sản phẩm',
  firmwareUpgrade: 'Cập nhật firmware',
  firmwareUpdate: 'Cập nhật firmware',
  more: 'Cài đặt khác',
  help: 'Trợ giúp',
  legalInfo: 'Thông tin pháp lý',
  deleteDevice: 'Xóa thiết bị',
  autoUpgrade: 'Cập nhật firmware tự động',
  checkUpgrade: '<PERSON><PERSON><PERSON> tra cập nhật firmware',
  security: '<PERSON>ài đặt bảo mật',
  networkInfo: 'Thông tin mạng',
  feedback: '<PERSON>ản hồi',
  timezone: 'Múi giờ thiết bị',
  addToDesktop: 'Thêm vào Màn hình chính',
  open: 'Bật',
  close: 'Tắt',
  other: 'Khác',
  multipleKeyShowOnHome: 'Số lượng nút hiển thị trên trang chủ: {0}',
  // 常用设备
  favoriteDevices: 'Hiển thị trên trang chủ Xiaomi Home',
  favoriteCamera: 'Kích thước thẻ bảng điều khiển',
  favoriteAddDevices: 'Thêm vào mục yêu thích',
  // MHDatePicker
  cancel: 'Hủy',
  ok: 'Xác nhận',
  am: 'SA',
  pm: 'CH',
  numberMonth: {
    'zero': '{0} tháng',
    'one': '{0} tháng',
    'two': '{0} tháng',
    'few': '{0} tháng',
    'many': '{0} tháng',
    'other': '{0} tháng'
  },
  numberDay: {
    'zero': '{0} ngày',
    'one': '{0} ngày',
    'two': '{0} ngày',
    'few': '{0} ngày',
    'many': '{0} ngày',
    'other': '{0} ngày'
  },
  numberHour: {
    'zero': '{0} giờ',
    'one': '{0} giờ',
    'two': '{0} giờ',
    'few': '{0} giờ',
    'many': '{0} giờ',
    'other': '{0} giờ'
  },
  numberMinute: {
    'zero': '{0} phút',
    'one': '{0} phút',
    'two': '{0} phút',
    'few': '{0} phút',
    'many': '{0} phút',
    'other': '{0} phút'
  },
  numberSecond: {
    'zero': '{0} giây',
    'one': '{0} giây',
    'two': '{0} giây',
    'few': '{0} giây',
    'many': '{0} giây',
    'other': '{0} giây'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}-{1}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Thoát',
  firmwareUpgradeUpdate: 'Cập nhật',
  firmwareUpgradeLook: 'Xem',
  firmwareUpgradeForceUpdate: 'Phiên bản chương trình cơ sở hiện tại quá cũ, một số tính năng có thể không hoạt động bình thường. Cập nhật lên phiên bản mới nhất để có trải nghiệm tốt hơn.',
  firmwareUpgradeForceUpdating: 'Thiết bị đang cập nhật. Hãy thử lại sau.',
  firmwareUpgradeNew_pre: 'Tìm thấy cập nhật mới. ',
  firmwareUpgradeNew_sub: 'Cập nhật ngay?',
  handling: 'Đợi một chút…',
  error: 'Đã xảy ra lỗi. Hãy thử lại sau.',
  createLightGroup: 'Tạo nhóm đèn (mới)',
  manageLightGroup: 'Quản lý nhóm đèn (mới)',
  deleteLightGroup: 'Bỏ nhóm đèn',
  deleteCurtainGroup: 'Bỏ nhóm thiết bị',
  linkDevice: 'Liên kết thiết bị',
  noSuppurtedLinkageDevice: 'Không có thiết bị nào',
  noSuppurtedLinkageTip: '1. Đảm bảo bạn đã thêm các thiết bị vào ứng dụng Xiaomi Home và chỉ định chúng cho các phòng.\\n2. Giữ các thiết bị Bluetooth gần thiết bị này để kết nối chúng thành công.',
  supportedLinkageDevices: 'Có thể liên kết với các thiết bị sau:',
  linkageDistanceTip: 'Giữ các thiết bị ở gần nhau để đảm bảo chúng có thể liên kết.',
  linkageRemoveTip: 'Để thay đổi thiết bị Bluetooth được liên kết, trước tiên hãy xóa thiết bị.',
  link: 'Liên kết',
  removeLink: 'Xóa',
  linkFail: 'Không thể liên kết',
  removeLinkFail: 'Không thể xóa',
  linkConfirm: 'Liên kết với thiết bị này ngay bây giờ?',
  removeLinkConfirm: 'Xóa ngay bây giờ?',
  linking: 'Đang liên kết...',
  linkDeviceBracelet: 'Liên kết vòng đeo tay',
  scanDeviceBracelet: 'Đang quét vòng đeo tay...',
  scanDeviceBraceletTip: 'Giữ Mi Band gần thiết bị này và nhớ bật Bluetooth của thiết bị để kết nối thành công.',
  scanDeviceBraceletEmptyTitle: 'Không tìm thấy thiết bị Mi Band ở gần đây',
  scanDeviceBraceletEmptyTip1: '1. Nhớ bật Bluetooth của vòng đeo tay.',
  scanDeviceBraceletEmptyTip2: '2. Giữ vòng đeo tay gần thiết bị khác.',
  linkedDeviceBraceletHeaderTip: 'Đã liên kết với các vòng đeo tay sau:',
  availableLinkDeviceBraceletHeaderTip: 'Có thể liên kết với các vòng đeo tay sau:',
  linkedDeviceBraceletFooterTip: 'Để thay đổi vòng đeo tay được liên kết, trước tiên hãy xóa vòng đeo tay đó.',
  availableLinkDeviceBraceletFooterTip: 'Nhớ bật Bluetooth của vòng đeo tay và giữ vòng đeo tay gần thiết bị khác.',
  pluginVersion: 'Phiên bản trình cắm',
  helpAndFeedback: 'Trợ giúp và Phản hồi',
  offline: 'Ngoại tuyến',
  downloading: 'Đang tải xuống...',
  installing: 'Đang cài đặt…',
  upgradeSuccess: 'Đã cập nhật thành công',
  upgradeFailed: 'Không thể cập nhật. Hãy thử lại sau.',
  upgradeTimeout: 'Đã hết thời gian chờ cập nhật',
  autoUpgradeInfo: 'Sẽ cố gắng cập nhật tự động trong khoảng thời gian từ {0}',
  today: 'Hôm nay',
  tomorrow: 'Ngày mai',
  currentIsLatestVersion: 'Phiên bản hiện tại đã là phiên bản cập nhật',
  lastestVersion: 'Phiên bản mới nhất: ',
  currentVersion: 'Phiên bản hiện tại: ',
  fetchFailed: 'Không thể truy cập. Hãy thử lại.',
  releaseNote: 'Tóm tắt cập nhật',
  releaseVersionHistory: 'Lịch sử cập nhật chương trình cơ sở',
  firmwareAutoUpdate: 'Tự động cập nhật chương trình cơ sở',
  autoUpdateDescriptionNote: 'Sau khi phát hiện thấy chương trình cơ sở mới, thiết bị sẽ cố gắng cập nhật tự động trong khoảng thời gian từ {0}. Thiết bị sẽ cài đặt bản cập nhật khi bạn không sử dụng thiết bị và sẽ không có thông báo bằng âm thanh hoặc ánh sáng trong quá trình cập nhật.',
  updateNow: 'Cập nhật',
  requireBelMesh: 'Tính năng này yêu cầu cổng Bluetooth mesh để hoạt động bình thường.',
  createCurtainGroup: 'Tạo nhóm rèm',
  createCurtainGroupTip: 'Bạn có thể kết hợp hai động cơ rèm thành một nhóm để có thể điều khiển như rèm hai mặt.',
  act: 'Di chuyển',
  create: 'Tạo',
  chooseCurtainGroupTitle: 'Chọn động cơ rèm',
  currentDevice: 'Thiết bị này',
  curtain: 'Rèm cửa',
  noCurtainGroupTip: 'Không thể nhóm ngay lúc này. Hãy thêm một động cơ rèm khác và thử lại.',
  switchPlugin: 'Trình cắm tiêu chuẩn',
  defaultPlugin: 'Mặc định',
  selectDefaultHP: 'Mặc định',
  stdPluginTitle: 'Tiêu chuẩn',
  thirdPluginTitle: 'Truyền thống',
  stdPluginSubTitle: 'Bạn có thể chuyển sang phiên bản cũ của trang này trong mục các tính năng bổ sung',
  stdGuideDialogTitle: 'Có phiên bản mới',
  stdGuideDialogSubTitle: 'Cập nhật ứng dụng để có trải nghiệm mới mẻ, nâng cấp hơn.',
  stdGuideDialogNote: 'Nếu bạn không tìm được tính năng sau khi cập nhật thì tính năng đó có thể đã được chuyển sang mục "Các tính năng bổ sung".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Công tắc',
  keyLeft: 'Công tắc bên trái',
  keyMiddle: 'Công tắc ở giữa',
  keyRight: 'Công tắc bên phải',
  keyType: 'Loại công tắc',
  keyName: 'Tên',
  light: 'Đèn',
  updateIcon: 'Thay đổi biểu tượng',
  done: 'Xong',
  modifyName: 'Chỉnh sửa tên',
  keyUpdateIconTips: 'Khi biểu tượng được chuyển sang "{0}", bạn có thể yêu cầu Mi AI bật "{0}".',
  nameHasChars: 'Tên không được chứa các ký tự đặc biệt',
  nameTooLong: 'Tên có thể chứa tối đa 40 ký tự',
  nameIsEmpty: 'Không được để trống tên',
  nameNotSupportEmoji: 'Tên không được chứa biểu tượng cảm xúc',
  // 房间
  room: 'Phòng',
  room_nameInputTips: 'Nhập tên phòng',
  room_nameSuggest: 'Tên được đề xuất',
  room_createNew: 'Tạo phòng mới',
  room_bedroom: 'Phòng ngủ',
  room_masterBedroom: 'Phòng ngủ chính',
  room_secondBedroom: 'Phòng ngủ thứ hai',
  room_kitchen: 'Nhà bếp',
  room_diningRoom: 'Phòng ăn',
  room_washroom: 'Nhà vệ sinh',
  room_childrensRoom: 'Phòng trẻ em',
  room_office: 'Phòng học',
  room_study: 'Phòng đọc sách',
  room_balcony: 'Ban công',
  room_studio: 'Phòng làm việc',
  room_bathroom: 'Phòng tắm',
  room_backyard: 'Sân sau',
  room_unassigned: 'Chưa được chỉ định',
  no_privacy_tip_content: 'Không thể tải Chính sách Riêng tư. Hãy kiểm tra cài đặt mạng của bạn và thử lại hoặc báo cáo sự cố này qua mục Phản hồi.',
  moreDeviceInfo: 'Thêm thông tin về thiết bị',
  deviceNet: 'Mạng của thiết bị',
  customizeName: 'Tên tùy chỉnh',
  software: 'Phần mềm',
  hardware: 'Phần cứng',
  bleMeshGateway: 'Cổng Bluetooth Mesh',
  deviceDid: 'ID thiết bị',
  deviceSN: 'Số sê-ri thiết bị',
  mcuVersion: 'Phiên bản chương trình cơ sở MCU',
  sdkVersion: 'Phiên bản chương trình cơ sở SDK',
  deviceModel: 'Mẫu thiết bị',
  deviceQR: 'Mã QR của thiết bị',
  download: 'Tải xuống',
  saveSuccess: 'Đã lưu thành công',
  saveFailed: 'Không thể lưu',
  clipboardy: 'Đã sao chép thành công',
  connected: 'Đã kết nối',
  notConnected: 'Chưa kết nối',
  bleConnected: 'Kết nối Bluetooth trực tiếp',
  deviceOffline: 'Ngoại tuyến',
  deviceConsumables: 'Vật tư thiết bị',
  consumableStateSufficient: 'Đủ',
  consumableStateInsufficient: 'Không đủ',
  consumableStateUnknown: 'Trạng thái không xác định',
  consumableStateDepletion: 'Sử dụng hết',
  consumableStateRemainPercent: 'Còn lại {0}%',
  consumableStateEstimatedHour: {
    'zero': 'Còn lại {0} giờ',
    'one': 'Còn lại {0} giờ',
    'two': 'Còn lại {0} giờ',
    'few': 'Còn lại {0} giờ',
    'many': 'Còn lại {0} giờ',
    'other': 'Còn lại {0} giờ'
  },
  consumableStateEstimatedDay: {
    'zero': 'Còn lại {0} ngày',
    'one': 'Còn lại {0} ngày',
    'two': 'Còn lại {0} ngày',
    'few': 'Còn lại {0} ngày',
    'many': 'Còn lại {0} ngày',
    'other': 'Còn lại {0} ngày'
  },
  changeIcon: 'Thay đổi biểu tượng',
  deviceCall: 'Cảnh báo khẩn cấp',
  cloudStorage: 'Thông báo dịch vụ lưu trữ đám mây',
  cloudStorageVip: 'Nhận thông báo về trạng thái thành viên dịch vụ đám mây',
  largeCardEvent: 'Hiển thị các sự kiện mới nhất đã được ghi lại trên thẻ.',
  // 开关智能
  switch_title_controlDevice: 'Điều khiển thiết bị',
  switch_subtitle_controlDeviceType: 'Đặt loại thiết bị cho từng nút',
  common_listItem_value_unset: 'Chưa đặt',
  switch_title_buttonControlDevice: 'Thiết bị được điều khiển (${})',
  switch_listItem_title_toWirelessSwitch: 'Đổi sang công tắc không dây',
  switch_listItem_subtile_wirelessSwitchSetting: 'Nút vật lý sẽ không thể điều khiển công tắc khi tính năng này được bật. Bạn vẫn có thể sử dụng chúng cho các tình huống tự động.',
  switch_dia_msg_wirelessSwitchSetting: 'Nút này được liên kết với một mục khác (${}). Đổi sang công tắc không dây để sử dụng.',
  switch_listItem_title_voiceControlLoop: 'Lệnh thoại dành cho công tắc',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Điều khiển công tắc bằng Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Bật',
  switch_listItem_value_voiceControlLoopOff: 'Tắt',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Điều khiển công tắc bằng lệnh thoại thông qua Mi AI. Nếu đèn thông minh được kết nối với công tắc, chúng có thể bị tắt và ngắt kết nối.',
  switch_listItem_title_operationMode: 'Chế độ vận hành',
  switch_listItem_title_speedMode: 'Chế độ siêu tốc',
  switch_listItem_title_standardMode: 'Chế độ tiêu chuẩn',
  switch_listItem_subtile_speedModeDescription: 'Chọn tùy chọn này nếu tự động chỉ cần thiết lập cho "bấm một lần". Tùy chọn này sẽ cải thiện thời gian phản hồi tự động.',
  switch_listItem_subtile_standardModeDescription: 'Chọn tùy chọn này nếu thiết bị cần thiết lập tự động "bấm hai lần" hoặc "bấm và giữ"',
  switch_dia_msg_speedModeMessage: 'Thiết bị này đã thiết lập tự động "bấm hai lần" và "bấm và giữ". Nếu bạn chọn Chế độ siêu tốc, bạn sẽ không thể sử dụng các tự động này. Vẫn tiếp tục?',
  switch_title_selectDeviceType: 'Chọn loại thiết bị',
  switch_subtitle_selectDeviceType: 'Chọn loại thiết bị được điều khiển bởi ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Chọn loại thiết bị do ${} điều khiển. Để lại một nút kết nối với các thiết bị thông thường để đảm bảo công tắc hoạt động bình thường.',
  switch_title_deviceType_normalDevice: 'Thiết bị thông thường (đèn và đèn bàn không có chức năng thông minh)',
  switch_title_deviceType_smartLight: 'Đèn thông minh',
  switch_title_deviceType_smartSwitch: 'Công tắc thông minh khác',
  switch_title_deviceType_manualScene: 'Điều khiển hàng loạt',
  switch_title_deviceType_otherSmartDevice: 'Thiết bị thông minh khác',
  switch_value_deviceType_normalDevice: 'Thiết bị thông thường',
  switch_value_deviceType_smartLight: 'Đèn thông minh',
  switch_value_deviceType_smartSwitch: 'Công tắc thông minh khác',
  switch_value_deviceType_manualScene: 'Điều khiển hàng loạt',
  switch_value_deviceType_otherSmartDevice: 'Thiết bị thông minh khác',
  switch_button_title_seeCreatedScene: 'Xem tự động hóa',
  switch_button_title_linkSmartLight: 'Kết nối đèn thông minh',
  switch_button_title_linkSmartSwitch: 'Kết nối công tắc thông minh',
  switch_button_title_linkManualScene: 'Kết nối điều khiển hàng loạt',
  switch_button_title_switchNameSetting: 'Đặt tên nút',
  switch_nav_title_buttonControlLight: 'Đèn thông minh được điều khiển (${})',
  switch_nav_subtitle_buttonControlLight: 'Kết nối đèn thông minh với nút nhấn để bật, tắt và giữ chúng trực tuyến',
  switch_header_title_selectLightOrGroup: 'Chọn đèn thông minh hoặc nhóm đèn',
  switch_nav_title_buttonControlSwitch: 'Công tắc thông minh được điều khiển (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Kết nối các công tắc để thêm giao diện bật/tắt khác. Bấm nút sẽ bật và tắt các công tắc đã chọn.',
  switch_header_title_selectSwitch: 'Chọn công tắc thông minh',
  switch_nav_title_buttonControlManualScene: 'Điều khiển hàng loạt được chỉ định (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Chỉ định điều khiển hàng loạt để chạy bằng cách bấm nút',
  switch_header_title_selectManualScene: 'Chọn điều khiển hàng loạt',
  common_edit: 'Sửa',
  common_reselect: 'Chọn lại',
  common_deleted: 'Đã xóa',
  common_delete: 'Xóa',
  common_delete_failed: 'Không thể xóa. Kiểm tra cài đặt mạng của bạn và thử lại.',
  common_setting_failed: 'Không thể đặt. Kiểm tra xem thiết bị đã được kết nối với mạng chưa và thử lại.',
  common_saving: 'Đang lưu…',
  switch_listItem_title_executionType: 'Chế độ chạy',
  switch_listItem_value_executionTypeCloud: 'Đám mây',
  switch_listItem_value_executionTypeLocale: 'Cục bộ',
  switch_dia_msg_deleteScene: 'Xóa tự động hóa này?',
  switch_scene_name_toggleSwitchDevice: '${} | Bấm một lần | ${} | Bật/Tắt | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Bấm một lần | ${} | Bật/Tắt | ${}',
  switch_scene_name_executeManualScene: '${} | Bấm một lần | ${} | Chạy | ${}-${}',
  switch_list_device_unavailable: 'Các thiết bị không được hiển thị đều không hỗ trợ tính năng này',
  switch_button_subtitle_notCurrentHome: 'Không có trong nhà hiện tại',
  common_list_empty: 'Không có gì ở đây',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Chế độ Ghép nối',
  switch_title_buttonControlDevice_oneGang: 'Điều khiển thiết bị'
};