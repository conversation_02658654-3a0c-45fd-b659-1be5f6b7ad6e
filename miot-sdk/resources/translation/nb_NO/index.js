export default {
  setting: 'Innstillinger',
  featureSetting: 'Enhetsinnstillinger',
  commonSetting: '<PERSON>relle innstillinger',
  name: '<PERSON><PERSON><PERSON>navn',
  deviceService: 'Enhetstjenester',
  location: 'Administrer steder',
  memberSet: 'Knapper',
  share: 'Del enhet',
  btGateway: 'BLE-gateway',
  voiceAuth: 'Stemmeautorisering',
  ifttt: 'Automatisering',
  productBaike: 'Produktinformasjon',
  firmwareUpgrade: 'Fastvareoppdatering',
  firmwareUpdate: 'Fastvareoppdatering',
  more: 'Ytterligere innstillinger',
  help: 'Hjelp',
  legalInfo: 'Juridisk informasjon',
  deleteDevice: 'Slett enhet',
  autoUpgrade: 'Oppdater fastvare automatisk',
  checkUpgrade: 'Se etter fastvareoppdateringer',
  security: 'Sikkerhetsinnstillinger',
  networkInfo: 'Nettverksinformasjon',
  feedback: 'Tilbakemelding',
  timezone: 'Enhe<PERSON> tidssone',
  addToDesktop: 'Legg til startskjerm',
  open: 'P<PERSON>',
  close: 'Av',
  other: 'Annet',
  multipleKeyShowOnHome: 'Antal<PERSON> knapper som vises på startsiden: {0}',
  // 常用设备
  favoriteDevices: 'Vis på Xiaomi Home-startsiden',
  favoriteCamera: 'Kontrollpanelets kortstørrelse',
  favoriteAddDevices: 'Legg til favoritter',
  // MHDatePicker
  cancel: 'Avbryt',
  ok: 'Bekreft',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} måned',
    'one': '{0} måned',
    'two': '{0} måneder',
    'few': '{0} måneder',
    'many': '{0} måneder',
    'other': '{0} måneder'
  },
  numberDay: {
    'zero': '{0} dager',
    'one': '{0} dag',
    'two': '{0} dager',
    'few': '{0} dager',
    'many': '{0} dager',
    'other': '{0} dager'
  },
  numberHour: {
    'zero': '{0} t',
    'one': '{0} t',
    'two': '{0} t',
    'few': '{0} t',
    'many': '{0} t',
    'other': '{0} t'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} sek',
    'one': '{0} sek',
    'two': '{0} sek',
    'few': '{0} sek',
    'many': '{0} sek',
    'other': '{0} sek'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Avslutt',
  firmwareUpgradeUpdate: 'Oppdater',
  firmwareUpgradeLook: 'Vis',
  firmwareUpgradeForceUpdate: 'Den aktuelle fastvaren er kanskje for gammel til å kjøre enkelte funksjoner. Oppdater til den nyeste versjonen for en bedre opplevelse.',
  firmwareUpgradeForceUpdating: 'Enheten oppdateres. Prøv igjen senere.',
  firmwareUpgradeNew_pre: 'En oppdatering er tilgjengelig. ',
  firmwareUpgradeNew_sub: 'Oppdater nå?',
  handling: 'Et øyeblikk …',
  error: 'Det oppsto en feil. Prøv igjen senere.',
  createLightGroup: 'Opprett lysgruppe (ny)',
  manageLightGroup: 'Administrer lysgruppe (ny)',
  deleteLightGroup: 'Opphev gruppering av lamper',
  deleteCurtainGroup: 'Opphev gruppering av enheter',
  linkDevice: 'Koble sammen enheter',
  noSuppurtedLinkageDevice: 'Ingen enheter tilgjengelig',
  noSuppurtedLinkageTip: '1. Sjekk at du har lagt til enhetene i Xiaomi Home-appen og tilordnet dem til rom.\\n2. Hold Bluetooth-enheter i nærheten av denne enheten for å koble dem til.',
  supportedLinkageDevices: 'Kan kobles sammen med følgende enheter:',
  linkageDistanceTip: 'Hold enhetene i nærheten av hverandre for å sikre at de kan kobles sammen.',
  linkageRemoveTip: 'For å endre den tilkoblede enheten må enheten fjernes først.',
  link: 'Koble sammen',
  removeLink: 'Fjern',
  linkFail: 'Kunne ikke koble sammen',
  removeLinkFail: 'Kunne ikke fjerne',
  linkConfirm: 'Koble sammen med denne enheten nå?',
  removeLinkConfirm: 'Fjerne nå?',
  linking: 'Kobler sammen …',
  linkDeviceBracelet: 'Koble sammen bånd',
  scanDeviceBracelet: 'Skanner etter bånd …',
  scanDeviceBraceletTip: 'Hold Mi Band i nærheten av denne enheten og kontroller at Bluetooth er aktivert for å koble til.',
  scanDeviceBraceletEmptyTitle: 'Kunne ikke finne Mi Band i nærheten',
  scanDeviceBraceletEmptyTip1: '1. Kontroller at båndets Bluetooth er aktivert.',
  scanDeviceBraceletEmptyTip2: '2. Hold båndet i nærheten av den andre enheten.',
  linkedDeviceBraceletHeaderTip: 'Koblet sammen med følgende bånd:',
  availableLinkDeviceBraceletHeaderTip: 'Kan kobles sammen med følgende bånd:',
  linkedDeviceBraceletFooterTip: 'For å bytte sammenkoblet bånd må båndet fjernes først.',
  availableLinkDeviceBraceletFooterTip: 'Kontroller at båndet har Bluetooth aktivert og hold det i nærheten av den andre enheten.',
  pluginVersion: 'Plugin-versjon',
  helpAndFeedback: 'Hjelp og tilbakemelding',
  offline: 'Frakoblet',
  downloading: 'Laster ned …',
  installing: 'Installerer …',
  upgradeSuccess: 'Oppdatert',
  upgradeFailed: 'Kunne ikke oppdatere. Prøv igjen senere.',
  upgradeTimeout: 'Oppdateringen ble tidsavbrutt',
  autoUpgradeInfo: 'Prøver å oppdatere automatisk mellom {0}',
  today: 'I dag',
  tomorrow: 'I morgen',
  currentIsLatestVersion: 'Gjeldende versjon er oppdatert',
  lastestVersion: 'Nyeste versjon: ',
  currentVersion: 'Gjeldende versjon: ',
  fetchFailed: 'Fikk ikke tilgang. Prøv igjen.',
  releaseNote: 'Dette er nytt',
  releaseVersionHistory: 'Fastvareoppdateringshistorikk',
  firmwareAutoUpdate: 'Automatiske fastvareoppdateringer',
  autoUpdateDescriptionNote: 'Når en ny fastvare er oppdaget, vil enheten prøve å oppdatere automatisk mellom {0}. Oppdateringen installeres når du ikke bruker enheten, og det vises ikke lyd- eller lysvarsler under oppdateringsprosessen.',
  updateNow: 'Oppdater',
  requireBelMesh: 'Denne funksjonen krever en Bluetooth Mesh-gateway',
  createCurtainGroup: 'Opprett en gardingruppe',
  createCurtainGroupTip: 'To gardinmotorer kan kombineres til én gruppe som kan kontrolleres som en dobbeltsidig gardin.',
  act: 'Flytt',
  create: 'Opprett',
  chooseCurtainGroupTitle: 'Velg en gardinmotor',
  currentDevice: 'Denne enheten',
  curtain: 'Gardin',
  noCurtainGroupTip: 'Kan ikke gruppere nå. Legg til en annen gardinmotor og prøv igjen.',
  switchPlugin: 'Standard plugin',
  defaultPlugin: 'Standard',
  selectDefaultHP: 'Standard',
  stdPluginTitle: 'Standard',
  thirdPluginTitle: 'Tradisjonell',
  stdPluginSubTitle: 'Du kan bytte til den gamle versjonen av siden i ytterligere funksjoner',
  stdGuideDialogTitle: 'Ny versjon tilgjengelig',
  stdGuideDialogSubTitle: 'Oppdater appen for en ny og mer strømlinjeformet opplevelse.',
  stdGuideDialogNote: 'Hvis du ikke finner en funksjon etter oppdatering, kan det hende den er flyttet til "Ytterligere funksjoner".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Bryter',
  keyLeft: 'Venstre bryter',
  keyMiddle: 'Midtre bryter',
  keyRight: 'Høyre bryter',
  keyType: 'Brytertype',
  keyName: 'Navn',
  light: 'Lampe',
  updateIcon: 'Endre ikon',
  done: 'Ferdig',
  modifyName: 'Rediger navn',
  keyUpdateIconTips: 'Når ikonet er byttet til "{0}", kan du be Mi AI om å slå på "{0}".',
  nameHasChars: 'Navnet kan ikke inneholde spesialtegn',
  nameTooLong: 'Navnet kan inneholde opptil 40 tegn',
  nameIsEmpty: 'Navnet kan ikke være tomt',
  nameNotSupportEmoji: 'Navnet kan ikke inneholde emojier',
  // 房间
  room: 'Rom',
  room_nameInputTips: 'Angi et romnavn',
  room_nameSuggest: 'Anbefalt navn',
  room_createNew: 'Opprett et nytt rom',
  room_bedroom: 'Soverom',
  room_masterBedroom: 'Hovedsoverom',
  room_secondBedroom: 'Andre soverom',
  room_kitchen: 'Kjøkken',
  room_diningRoom: 'Spisestue',
  room_washroom: 'Toalett',
  room_childrensRoom: 'Barnerom',
  room_office: 'Bibliotek',
  room_study: 'Bibliotek',
  room_balcony: 'Altan',
  room_studio: 'Verksted',
  room_bathroom: 'Bad',
  room_backyard: 'Bakgård',
  room_unassigned: 'Ikke tilordnet',
  no_privacy_tip_content: 'Kunne ikke laste inn personvernerklæringen. Sjekk nettverksinnstillingene og prøv igjen, eller meld fra om dette problemet via Tilbakemelding.',
  moreDeviceInfo: 'Ytterligere enhetsinformasjon',
  deviceNet: 'Enhetens nettverk',
  customizeName: 'Egendefinert navn',
  software: 'Programvare',
  hardware: 'Maskinvare',
  bleMeshGateway: 'Bluetooth Mesh-gateway',
  deviceDid: 'Enhets-ID',
  deviceSN: 'Enhets-SN',
  mcuVersion: 'MCU-fastvareversjon',
  sdkVersion: 'SDK-fastvareversjon',
  deviceModel: 'Enhetsmodell',
  deviceQR: 'Enhetens QR-kode',
  download: 'Last ned',
  saveSuccess: 'Lagret',
  saveFailed: 'Kunne ikke lagre',
  clipboardy: 'Kopiert',
  connected: 'Tilkoblet',
  notConnected: 'Ikke tilkoblet',
  bleConnected: 'Direkte Bluetooth-tilkobling',
  deviceOffline: 'Frakoblet',
  deviceConsumables: 'Forbruksvarer for enheten',
  consumableStateSufficient: 'Tilstrekkelig',
  consumableStateInsufficient: 'Utilstrekkelig',
  consumableStateUnknown: 'Status ukjent',
  consumableStateDepletion: 'Oppbrukt',
  consumableStateRemainPercent: '{0}% gjenstår',
  consumableStateEstimatedHour: {
    'zero': '{0} t gjenstår',
    'one': '{0} t gjenstår',
    'two': '{0} t gjenstår',
    'few': '{0} t gjenstår',
    'many': '{0} t gjenstår',
    'other': '{0} t gjenstår'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} dager gjenstår',
    'one': '{0} dag gjenstår',
    'two': '{0} dager gjenstår',
    'few': '{0} dager gjenstår',
    'many': '{0} dager gjenstår',
    'other': '{0} dager gjenstår'
  },
  changeIcon: 'Endre ikon',
  deviceCall: 'Nødvarsler',
  cloudStorage: 'Nettskylagringsvarsler',
  cloudStorageVip: 'Motta varsler om nettskymedlemskapets status',
  largeCardEvent: 'Vis nyeste hendelsesopptak på kortet',
  // 开关智能
  switch_title_controlDevice: 'Kontrollenheter',
  switch_subtitle_controlDeviceType: 'Angi typer enhet for hver knapp',
  common_listItem_value_unset: 'Ikke angitt',
  switch_title_buttonControlDevice: 'Kontrollerte enheter (${})',
  switch_listItem_title_toWirelessSwitch: 'Endre til trådløs bryter',
  switch_listItem_subtile_wirelessSwitchSetting: 'Fysiske knapper vil ikke kunne kontrollere brytere når denne funksjonen er aktivert. Du vil fortsatt kunne bruke dem til automatiseringer.',
  switch_dia_msg_wirelessSwitchSetting: 'Denne knappen er tilknyttet et annet element (${}). Bytt til trådløs bryter for å bruke den.',
  switch_listItem_title_voiceControlLoop: 'Stemmekommandoer for brytere',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Kontroller brytere med Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'På',
  switch_listItem_value_voiceControlLoopOff: 'Av',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Kontroller brytere med stemmekommandoer via Mi AI. Hvis smartlys er koblet til bryteren, kan det hende de slås av og frakobles.',
  switch_listItem_title_operationMode: 'Operasjonsmodus',
  switch_listItem_title_speedMode: 'Superhastighetsmodus',
  switch_listItem_title_standardMode: 'Standard modus',
  switch_listItem_subtile_speedModeDescription: 'Velg dette hvis automatisseringer bare trenger å konfigureres for "enkelttrykk". Dette alternativet forbedrer automatiseringsresponstiden.',
  switch_listItem_subtile_standardModeDescription: 'Velg dette alternativet hvis enheten må konfigureres for automatiseringene "dobbelttrykk" eller "trykk og hold inne"',
  switch_dia_msg_speedModeMessage: 'Denne enheten har allerede automatiseringene "dobbelttrykk" og "trykk og hold" konfigurert. Hvis du velger superhastighetsmodus, vil du ikke lenger kunne bruke disse automatiseringene. Fortsette likevel?',
  switch_title_selectDeviceType: 'Velg enhetstype',
  switch_subtitle_selectDeviceType: 'Velg enhetstype kontrollert av ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Velg enhetstype kontrollert av ${}. La én knapp være knyttet til vanlige enheter for å sikre at enheten fungerer som normalt.',
  switch_title_deviceType_normalDevice: 'Vanlige enheter (lys og lamper uten smartfunksjoner)',
  switch_title_deviceType_smartLight: 'Smarte lys',
  switch_title_deviceType_smartSwitch: 'Andre smartbrytere',
  switch_title_deviceType_manualScene: 'Samlingskontroller',
  switch_title_deviceType_otherSmartDevice: 'Andre smartenheter',
  switch_value_deviceType_normalDevice: 'Vanlige enheter',
  switch_value_deviceType_smartLight: 'Smarte lys',
  switch_value_deviceType_smartSwitch: 'Andre smartbrytere',
  switch_value_deviceType_manualScene: 'Samlingskontroller',
  switch_value_deviceType_otherSmartDevice: 'Andre smartenheter',
  switch_button_title_seeCreatedScene: 'Vis automatiseringer',
  switch_button_title_linkSmartLight: 'Koble til smartlys',
  switch_button_title_linkSmartSwitch: 'Koble til smartbryter',
  switch_button_title_linkManualScene: 'Koble til samlingskontroll',
  switch_button_title_switchNameSetting: 'Angi knappens navn',
  switch_nav_title_buttonControlLight: 'Kontrollerte smarte lys (${})',
  switch_nav_subtitle_buttonControlLight: 'Koble smartlys til en knapp for å slå dem på og av og holde dem online',
  switch_header_title_selectLightOrGroup: 'Velg smart lys eller lysgruppe',
  switch_nav_title_buttonControlSwitch: 'Kontrollerte smartbrytere (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Koble til brytere for å legge til et annet på/av-grensesnitt. Når du trykker på knappen, slås de valgte bryterne på og av.',
  switch_header_title_selectSwitch: 'Velg smartbrytere',
  switch_nav_title_buttonControlManualScene: 'Tilordnede samlingskontroller (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Tilordne samlingskontroller for å kjøre dem ved å trykke på knappen',
  switch_header_title_selectManualScene: 'Velg samlingskontroller',
  common_edit: 'Rediger',
  common_reselect: 'Velg på nytt',
  common_deleted: 'Slettet',
  common_delete: 'Slett',
  common_delete_failed: 'Kunne ikke slette. Sjekk nettverksinnstillingene dine og prøv igjen.',
  common_setting_failed: 'Kunne ikke angi. Sjekk om enheten er koblet til nettverket og prøv igjen.',
  common_saving: 'Lagrer…',
  switch_listItem_title_executionType: 'Kjøremodus',
  switch_listItem_value_executionTypeCloud: 'Nettsky',
  switch_listItem_value_executionTypeLocale: 'Lokal',
  switch_dia_msg_deleteScene: 'Slette denne automatiseringen?',
  switch_scene_name_toggleSwitchDevice: '${} | Enkelttrykk | ${} | På/av | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Enkelttrykk | ${} | På/av | ${}',
  switch_scene_name_executeManualScene: '${} | Enkelttrykk | ${} | Kjør | ${}-${}',
  switch_list_device_unavailable: 'Enheter som ikke vises, støtter ikke denne funksjonen',
  switch_button_subtitle_notCurrentHome: 'Ikke i det aktuelle hjemmet',
  common_list_empty: 'Ingenting her enda',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Sammenkoblingsmodus',
  switch_title_buttonControlDevice_oneGang: 'Enhetskontroller'
};