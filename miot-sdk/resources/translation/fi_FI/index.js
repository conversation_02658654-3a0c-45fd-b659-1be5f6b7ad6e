export default {
  setting: 'Asetukset',
  featureSetting: 'Laitteen asetukset',
  commonSetting: '<PERSON><PERSON><PERSON><PERSON> asetukset',
  name: 'Laitteen nimi',
  deviceService: 'Laitteen palvelut',
  location: '<PERSON>inn<PERSON> sijainteja',
  memberSet: 'Painikke<PERSON>',
  share: 'Jaa laite',
  btGateway: 'BLE-yhdyskäytävä',
  voiceAuth: '<PERSON>änivaltuutus',
  ifttt: 'Automaatio',
  productBaike: 'Tuotetiedot',
  firmwareUpgrade: 'Laiteohjelmiston päivitys',
  firmwareUpdate: 'Laiteohjelmiston päivitys',
  more: 'Lisäasetukset',
  help: 'Ohje',
  legalInfo: 'O<PERSON><PERSON><PERSON><PERSON> tiedot',
  deleteDevice: 'Poista laite',
  autoUpgrade: 'Päivitä laiteohjelmisto automaattisesti',
  checkUpgrade: 'Tarkista laiteohjelmistopäivitykset',
  security: 'Tietoturva-asetukset',
  networkInfo: '<PERSON>er<PERSON> tiedot',
  feedback: 'Palaute',
  timezone: 'Laitteen aikavyöhyke',
  addToDesktop: 'Lis<PERSON><PERSON> aloitusnäytölle',
  open: 'P<PERSON><PERSON><PERSON><PERSON>',
  close: '<PERSON><PERSON> päältä',
  other: 'Muu',
  multipleKeyShowOnHome: 'Aloitussivulla näkyvien painikkeiden määrä: {0}',
  // 常用设备
  favoriteDevices: 'Näytä Xiaomi Home -sovelluksen aloitussivulla',
  favoriteCamera: 'Kojelaudan kortin koko',
  favoriteAddDevices: 'Lisää suosikkeihin',
  // MHDatePicker
  cancel: 'Peruuta',
  ok: 'Vahvista',
  am: 'ap.',
  pm: 'ip.',
  numberMonth: {
    'zero': '{0} kuukautta',
    'one': '{0} kuukausi',
    'two': '{0} kuukautta',
    'few': '{0} kuukautta',
    'many': '{0} kuukautta',
    'other': '{0} kuukautta'
  },
  numberDay: {
    'zero': '{0} päivää',
    'one': '{0} päivä',
    'two': '{0} päivää',
    'few': '{0} päivää',
    'many': '{0} päivää',
    'other': '{0} päivää'
  },
  numberHour: {
    'zero': '{0} h',
    'one': '{0} h',
    'two': '{0} h',
    'few': '{0} h',
    'many': '{0} h',
    'other': '{0} h'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} s',
    'one': '{0} s',
    'two': '{0} s',
    'few': '{0} s',
    'many': '{0} s',
    'other': '{0} s'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}.{1}.{0}',
  // 2019年06月03日
  time24SubTitle: '{0}.{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Poistu',
  firmwareUpgradeUpdate: 'Päivitä',
  firmwareUpgradeLook: 'Näytä',
  firmwareUpgradeForceUpdate: 'Nykyinen laiteohjelmisto saattaa olla liian vanha joidenkin ominaisuuksien käyttämiseen. Päivitä uusimpaan versioon paremman käyttökokemuksen saamiseksi.',
  firmwareUpgradeForceUpdating: 'Laitetta päivitetään. Yritä myöhemmin uudelleen.',
  firmwareUpgradeNew_pre: 'Päivitys on saatavilla. ',
  firmwareUpgradeNew_sub: 'Päivitä nyt?',
  handling: 'Hetki vain…',
  error: 'Tapahtui virhe. Yritä myöhemmin uudelleen.',
  createLightGroup: 'Luo valoryhmä (uusi)',
  manageLightGroup: 'Hallinnoi valoryhmää (uusi)',
  deleteLightGroup: 'Pura valoryhmä',
  deleteCurtainGroup: 'Pura laiteryhmä',
  linkDevice: 'Linkitä laitteet',
  noSuppurtedLinkageDevice: 'Laitteita ei ole saatavilla',
  noSuppurtedLinkageTip: '1. Varmista, että olet lisännyt laitteet Xiaomi Home -sovellukseen ja määrittänyt ne huoneisiin.\\n2. Pidä Bluetooth-laitteet tämän laitteen lähellä, jotta ne voidaan yhdistää onnistuneesti.',
  supportedLinkageDevices: 'Voidaan yhdistää seuraaviin laitteisiin:',
  linkageDistanceTip: 'Pidä laitteet läheisellä etäisyydellä varmistaaksesi, että ne pystyvät yhdistymään toisiinsa.',
  linkageRemoveTip: 'Jos haluat vaihtaa yhdistettyä Bluetooth-laitetta, poista laite ensin.',
  link: 'Yhdistä',
  removeLink: 'Poista',
  linkFail: 'Ei voitu yhdistää',
  removeLinkFail: 'Ei voitu poistaa',
  linkConfirm: 'Yhdistä tähän laitteeseen nyt?',
  removeLinkConfirm: 'Poista nyt?',
  linking: 'Yhdistetään…',
  linkDeviceBracelet: 'Yhdistä ranneke',
  scanDeviceBracelet: 'Haetaan ranneketta…',
  scanDeviceBraceletTip: 'Pidä Mi Band tämän laitteen lähellä ja varmista, että sen Bluetooth on päällä, jotta yhteys onnistuu.',
  scanDeviceBraceletEmptyTitle: 'Mi Bandeja ei löytynyt lähistöltä',
  scanDeviceBraceletEmptyTip1: '1. Varmista, että rannekkeen Bluetooth on päällä.',
  scanDeviceBraceletEmptyTip2: '2. Pidä ranneke lähellä toista laitetta.',
  linkedDeviceBraceletHeaderTip: 'Yhdistetty seuraaviin rannekkeisiin:',
  availableLinkDeviceBraceletHeaderTip: 'Voidaan yhdistää seuraaviin rannekkeisiin:',
  linkedDeviceBraceletFooterTip: 'Jos haluat vaihtaa yhdistettyä ranneketta, poista ranneke ensin.',
  availableLinkDeviceBraceletFooterTip: 'Varmista, että rannekkeen Bluetooth on päällä, ja pidä se lähellä toista laitetta.',
  pluginVersion: 'Laajennusversio',
  helpAndFeedback: 'Ohje & Palaute',
  offline: 'Offline',
  downloading: 'Ladataan...',
  installing: 'Asennetaan…',
  upgradeSuccess: 'Päivitetty onnistuneesti',
  upgradeFailed: 'Ei voitu päivittää. Yritä myöhemmin uudelleen.',
  upgradeTimeout: 'Päivitys aikakatkaistu',
  autoUpgradeInfo: 'Yritetään päivittää automaattisesti välillä {0}',
  today: 'Tänään',
  tomorrow: 'Huomenna',
  currentIsLatestVersion: 'Nykyinen versio on ajan tasalla',
  lastestVersion: 'Viimeisin versio: ',
  currentVersion: 'Nykyinen versio: ',
  fetchFailed: 'Ei pääsyä. Yritä uudelleen.',
  releaseNote: 'Uutta',
  releaseVersionHistory: 'Laiteohjelmiston päivityshistoria',
  firmwareAutoUpdate: 'Automaattiset laiteohjelmistopäivitykset',
  autoUpdateDescriptionNote: 'Kun uusi laiteohjelmisto havaitaan, laite yrittää päivittää automaattisesti välillä {0}. Päivitys asennetaan, kun et käytä laitetta, eikä päivitysprosessin aikana tule ääni- tai valoilmoituksia.',
  updateNow: 'Päivitä',
  requireBelMesh: 'Tämä ominaisuus vaatii Bluetooth mesh -yhdyskäytävän toimiakseen normaalisti.',
  createCurtainGroup: 'Luo verhoryhmä',
  createCurtainGroupTip: 'Kaksi verhomoottoria voidaan yhdistää ryhmäksi, jota voidaan ohjata kaksipuolisena verhona.',
  act: 'Siirrä',
  create: 'Luo',
  chooseCurtainGroupTitle: 'Valitse verhomoottori',
  currentDevice: 'Tämä laite',
  curtain: 'Verho',
  noCurtainGroupTip: 'Ei voida ryhmittää nyt. Lisää toinen verhomoottori ja yritä uudelleen.',
  switchPlugin: 'Vakiolaajennus',
  defaultPlugin: 'Oletus',
  selectDefaultHP: 'Oletus',
  stdPluginTitle: 'Vakio',
  thirdPluginTitle: 'Perinteinen',
  stdPluginSubTitle: 'Voit vaihtaa sivun vanhaan versioon lisäominaisuuksista',
  stdGuideDialogTitle: 'Uusi versio saatavilla',
  stdGuideDialogSubTitle: 'Päivitä sovellus saadaksesi uuden, virtaviivaisemman käyttökokemuksen.',
  stdGuideDialogNote: 'Jos et löydä ominaisuutta päivityksen jälkeen, se on saatettu siirtää "Lisäominaisuudet" -kohtaan.',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Katkaisin',
  keyLeft: 'Vasen katkaisin',
  keyMiddle: 'Keskimmäinen katkaisin',
  keyRight: 'Oikea katkaisin',
  keyType: 'Katkaisimen tyyppi',
  keyName: 'Nimi',
  light: 'Lamppu',
  updateIcon: 'Muokkaa kuvaketta',
  done: 'Valmis',
  modifyName: 'Muokkaa nimeä',
  keyUpdateIconTips: 'Kun kuvake on vaihdettu arvoon "{0}", voit pyytää Mi AI:ta laittamaan "{0}" päälle.',
  nameHasChars: 'Nimi ei voi sisältää erikoismerkkejä',
  nameTooLong: 'Nimessä voi olla enintään 40 merkkiä',
  nameIsEmpty: 'Nimi ei voi olla tyhjä',
  nameNotSupportEmoji: 'Nimet eivät voi sisältää hymiöitä',
  // 房间
  room: 'Huone',
  room_nameInputTips: 'Syötä huoneen nimi',
  room_nameSuggest: 'Suositeltu nimi',
  room_createNew: 'Luo uusi huone',
  room_bedroom: 'Makuuhuone',
  room_masterBedroom: 'Päämakuuhuone',
  room_secondBedroom: 'Toinen makuuhuone',
  room_kitchen: 'Keittiö',
  room_diningRoom: 'Ruokailuhuone',
  room_washroom: 'Vessa',
  room_childrensRoom: 'Lastenhuone',
  room_office: 'Työhuone',
  room_study: 'Kirjasto',
  room_balcony: 'Parveke',
  room_studio: 'Työpaja',
  room_bathroom: 'Kylpyhuone',
  room_backyard: 'Takapiha',
  room_unassigned: 'Ei määritetty',
  no_privacy_tip_content: 'Tietosuojakäytäntöä ei voitu ladata. Tarkista verkkoasetuksesi ja yritä uudelleen tai ilmoita tästä ongelmasta Palautteen kautta.',
  moreDeviceInfo: 'Lisää laitetietoja',
  deviceNet: 'Laitteen verkko',
  customizeName: 'Mukautettu nimi',
  software: 'Ohjelmisto',
  hardware: 'Laitteisto',
  bleMeshGateway: 'Bluetooth mesh -yhdyskäytävä',
  deviceDid: 'Laitteen tunnus',
  deviceSN: 'Laitteen sarjanumero',
  mcuVersion: 'MCU-laiteohjelmistoversio',
  sdkVersion: 'SDK-laiteohjelmistoversio',
  deviceModel: 'Laitteen malli',
  deviceQR: 'Laitteen QR-koodi',
  download: 'Lataa',
  saveSuccess: 'Tallennettu onnistuneesti',
  saveFailed: 'Ei voitu tallentaa',
  clipboardy: 'Kopioitu onnistuneesti',
  connected: 'Yhdistetty',
  notConnected: 'Ei yhdistetty',
  bleConnected: 'Suora Bluetooth-yhteys',
  deviceOffline: 'Offline',
  deviceConsumables: 'Laitteen tarvikkeet',
  consumableStateSufficient: 'Riittävä',
  consumableStateInsufficient: 'Riittämätön',
  consumableStateUnknown: 'Tila tuntematon',
  consumableStateDepletion: 'Käytetty',
  consumableStateRemainPercent: '{0} % jäljellä',
  consumableStateEstimatedHour: {
    'zero': '{0} h jäljellä',
    'one': '{0} h jäljellä',
    'two': '{0} h jäljellä',
    'few': '{0} h jäljellä',
    'many': '{0} h jäljellä',
    'other': '{0} h jäljellä'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} päivää jäljellä',
    'one': '{0} päivä jäljellä',
    'two': '{0} päivää jäljellä',
    'few': '{0} päivää jäljellä',
    'many': '{0} päivää jäljellä',
    'other': '{0} päivää jäljellä'
  },
  changeIcon: 'Muokkaa kuvaketta',
  deviceCall: 'Hätätiedotteet',
  cloudStorage: 'Cloud-tallennustilan ilmoitukset',
  cloudStorageVip: 'Saa ilmoituksia pilven jäsenyyden tilasta',
  largeCardEvent: 'Näytä viimeisimmät kortille tallennetut tapahtumat',
  // 开关智能
  switch_title_controlDevice: 'Hallitse laitteita',
  switch_subtitle_controlDeviceType: 'Aseta laitetyypit jokaiselle painikkeelle',
  common_listItem_value_unset: 'Ei ole asetettu',
  switch_title_buttonControlDevice: 'Hallitut laitteet (${})',
  switch_listItem_title_toWirelessSwitch: 'Vaihda langattomaan katkaisimeen',
  switch_listItem_subtile_wirelessSwitchSetting: 'Fyysiset painikkeet eivät voi ohjata kytkimiä, kun tämä ominaisuus on käytössä. Voit silti käyttää niitä automaatioissa.',
  switch_dia_msg_wirelessSwitchSetting: 'Tämä painike on liitetty toiseen kohteeseen (${}). Vaihda langattomaan kytkimeen käyttääksesi sitä.',
  switch_listItem_title_voiceControlLoop: 'Kytkimien äänikomennot',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Mi AI:n ohjauskytkimet',
  switch_listItem_value_voiceControlLoopOn: 'Päällä',
  switch_listItem_value_voiceControlLoopOff: 'Pois',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Ohjauskytkimet äänikomennoilla Mi AI:n kautta. Jos älyvalot on yhdistetty kytkimeen, ne saattavat sammua ja katkaista yhteytensä.',
  switch_listItem_title_operationMode: 'Toimintatila',
  switch_listItem_title_speedMode: 'Supernopeustila',
  switch_listItem_title_standardMode: 'Vakiotila',
  switch_listItem_subtile_speedModeDescription: 'Valitse tämä vaihtoehto, jos automaatioita tarvitsee määrittää vain "kertapainalluksella". Tämä vaihtoehto parantaa automaation vasteaikaa.',
  switch_listItem_subtile_standardModeDescription: 'Valitse tämä vaihtoehto, jos laitteelle on määritettävä "tuplapainallus"- tai "pidä painettuna" -automaatio',
  switch_dia_msg_speedModeMessage: 'Tässä laitteessa on jo "tuplapainallus" ja "pidä painettuna" -automaatioasetukset määritettynä. Jos valitset supernopeustilan, et voi enää käyttää näitä automaatioita. Jatka silti?',
  switch_title_selectDeviceType: 'Valitse laitteen tyyppi',
  switch_subtitle_selectDeviceType: 'Valitse ${} ohjaama laitetyyppi',
  switch_subtitle_liveWire_selectDeviceType: 'Valitse ${} hallitsema laitetyyppi. Jätä yksi painike kytkettynä tavallisiin laitteisiin varmistaaksesi, että kytkin toimii normaalisti.',
  switch_title_deviceType_normalDevice: 'Tavalliset laitteet (valot ja lamput ilman älykkäitä toimintoja)',
  switch_title_deviceType_smartLight: 'Älyvalot',
  switch_title_deviceType_smartSwitch: 'Muut älykytkimet',
  switch_title_deviceType_manualScene: 'Eräohjaimet',
  switch_title_deviceType_otherSmartDevice: 'Muut älylaitteet',
  switch_value_deviceType_normalDevice: 'Tavalliset laitteet',
  switch_value_deviceType_smartLight: 'Älyvalot',
  switch_value_deviceType_smartSwitch: 'Muut älykytkimet',
  switch_value_deviceType_manualScene: 'Eräohjaimet',
  switch_value_deviceType_otherSmartDevice: 'Muut älylaitteet',
  switch_button_title_seeCreatedScene: 'Näytä automaatiot',
  switch_button_title_linkSmartLight: 'Yhdistä älyvalo',
  switch_button_title_linkSmartSwitch: 'Yhdistä älykytkin',
  switch_button_title_linkManualScene: 'Yhdistä eräohjain',
  switch_button_title_switchNameSetting: 'Aseta painikkeen nimi',
  switch_nav_title_buttonControlLight: 'Ohjatut älyvalot (${})',
  switch_nav_subtitle_buttonControlLight: 'Yhdistä älyvalot painikkeeseen sytyttääksesi ja sammuttaaksesi ne ja pitääksesi ne verkossa',
  switch_header_title_selectLightOrGroup: 'Valitse älyvalo tai valoryhmä',
  switch_nav_title_buttonControlSwitch: 'Ohjatut älykytkimet (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Yhdistä kytkimet lisätäksesi toisen päälle/pois liitännän. Painikkeen painaminen kytkee valitut kytkimet päälle ja pois päältä.',
  switch_header_title_selectSwitch: 'Valitse älykytkimet',
  switch_nav_title_buttonControlManualScene: 'Määritetyt eräohjaimet (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Määritä eräohjaimet suorittamaan ne painamalla painiketta',
  switch_header_title_selectManualScene: 'Valitse eräohjaimet',
  common_edit: 'Muokkaa',
  common_reselect: 'Valitse uudelleen',
  common_deleted: 'Poistettu',
  common_delete: 'Poista',
  common_delete_failed: 'Ei voitu poistaa. Tarkista verkkoasetuksesi ja yritä uudelleen.',
  common_setting_failed: 'Ei voitu asettaa. Tarkista, onko laite kytketty verkkoon, ja yritä uudelleen.',
  common_saving: 'Tallennetaan…',
  switch_listItem_title_executionType: 'Suoritustila',
  switch_listItem_value_executionTypeCloud: 'Pilvi',
  switch_listItem_value_executionTypeLocale: 'Paikallinen',
  switch_dia_msg_deleteScene: 'Poistetaanko tämä automaatio?',
  switch_scene_name_toggleSwitchDevice: '${} | Yksi painallus | ${} | Päällä/Pois | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Yksi painallus | ${} | Päällä/Pois | ${}',
  switch_scene_name_executeManualScene: '${} | Yksi painallus | ${} | Suorita | ${}-${}',
  switch_list_device_unavailable: 'Laitteet, jotka eivät ole näkyvissä, eivät tue tätä ominaisuutta',
  switch_button_subtitle_notCurrentHome: 'Ei nykyisessä kodissa',
  common_list_empty: 'Täällä ei ole vielä mitään',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Pariliitostila',
  switch_title_buttonControlDevice_oneGang: 'Laiteohjaimet'
};