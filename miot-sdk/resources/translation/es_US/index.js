export default {
  setting: 'Ajustes',
  featureSetting: 'Ajustes del dispositivo',
  commonSetting: 'Ajustes generales',
  name: 'Nombre del dispositivo',
  deviceService: 'Servicios del dispositivo',
  location: 'Administrar ubicaciones',
  memberSet: 'Botones',
  share: 'Compartir dispositivo',
  btGateway: 'Puerta de enlace BLE',
  voiceAuth: 'Autorización por voz',
  ifttt: 'Automatización',
  productBaike: 'Información del producto',
  firmwareUpgrade: 'Actualización de firmware',
  firmwareUpdate: 'Actualización de firmware',
  more: 'Ajustes adicionales',
  help: 'Ayuda',
  legalInfo: 'Información legal',
  deleteDevice: 'Eliminar dispositivo',
  autoUpgrade: 'Actualizar el firmware automáticamente',
  checkUpgrade: 'Buscar actualizaciones de firmware',
  security: 'Ajustes de seguridad',
  networkInfo: 'Información de red',
  feedback: 'Comentarios',
  timezone: 'Zona horaria del dispositivo',
  addToDesktop: 'Añadir a la pantalla principal',
  open: 'Activado',
  close: 'Desactivado',
  other: 'Otro',
  multipleKeyShowOnHome: 'El número de botones que se mostrarán en la página principal: {0}',
  // 常用设备
  favoriteDevices: 'Mostrar en la página principal de Xiaomi Home',
  favoriteCamera: 'Tamaño de tarjetas en el panel',
  favoriteAddDevices: 'Agregar a favoritos',
  // MHDatePicker
  cancel: 'Cancelar',
  ok: 'Confirmar',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} meses',
    'one': '{0} mes',
    'two': '{0} meses',
    'few': '{0} meses',
    'many': '{0} meses',
    'other': '{0} meses'
  },
  numberDay: {
    'zero': '{0} días',
    'one': '{0} día',
    'two': '{0} días',
    'few': '{0} días',
    'many': '{0} días',
    'other': '{0} días'
  },
  numberHour: {
    'zero': '{0} hrs',
    'one': '{0} hr',
    'two': '{0} hrs',
    'few': '{0} hrs',
    'many': '{0} hrs',
    'other': '{0} hrs'
  },
  numberMinute: {
    'zero': '{0} mins',
    'one': '{0} min',
    'two': '{0} mins',
    'few': '{0} mins',
    'many': '{0} mins',
    'other': '{0} mins'
  },
  numberSecond: {
    'zero': '{0} segs',
    'one': '{0} seg',
    'two': '{0} segs',
    'few': '{0} segs',
    'many': '{0} segs',
    'other': '{0} segs'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Salir',
  firmwareUpgradeUpdate: 'Actualizar',
  firmwareUpgradeLook: 'Ver',
  firmwareUpgradeForceUpdate: 'Es probable que el firmware actual esté demasiado desactualizado como para ejecutar algunas características. Actualice a la versión más reciente para tener una mejor experiencia.',
  firmwareUpgradeForceUpdating: 'El dispositivo se está actualizando. Intente de nuevo más tarde.',
  firmwareUpgradeNew_pre: 'Hay una actualización disponible. ',
  firmwareUpgradeNew_sub: '¿Actualizar ahora?',
  handling: 'Un momento...',
  error: 'Ha ocurrido un error. Intente de nuevo más tarde.',
  createLightGroup: 'Crear grupo de luces (nuevo)',
  manageLightGroup: 'Gestionar grupo de luces (nuevo)',
  deleteLightGroup: 'Desagrupar luces',
  deleteCurtainGroup: 'Desagrupar dispositivos',
  linkDevice: 'Vincular dispositivos',
  noSuppurtedLinkageDevice: 'No hay dispositivos disponibles',
  noSuppurtedLinkageTip: '1. Verifique haber agregado dispositivos en la aplicación Xiaomi Home y asignarlos a habitaciones.\\n2. Mantenga los dispositivos bluetooth cerca de este dispositivo para conectarlos correctamente.',
  supportedLinkageDevices: 'Puede vincularse con los siguientes dispositivos:',
  linkageDistanceTip: 'Mantenga los dispositivos cerca para que se puedan vincular.',
  linkageRemoveTip: 'para cambiar el dispositivo bluetooth vinculado, elimínelo primero.',
  link: 'Vincular',
  removeLink: 'Eliminar',
  linkFail: 'No fue posible vincular',
  removeLinkFail: 'No se pudo eleminar',
  linkConfirm: '¿Vincular este dispositivo ahora?',
  removeLinkConfirm: '¿Eliminar ahora?',
  linking: 'Vinculando...',
  linkDeviceBracelet: 'Vincular banda',
  scanDeviceBracelet: 'Escaneando bandas...',
  scanDeviceBraceletTip: 'Mantener Mi Band cerca de este dispositivo para que su bluetooth se pueda conectar correctamente.',
  scanDeviceBraceletEmptyTitle: 'No se encontraron Mi Band cerca',
  scanDeviceBraceletEmptyTip1: '1. Verifique que el bluetooth de la banda esté encendido.',
  scanDeviceBraceletEmptyTip2: '2. Mantenga la banda cerca del otro dispositivo.',
  linkedDeviceBraceletHeaderTip: 'Vinculado con las siguientes bandas:',
  availableLinkDeviceBraceletHeaderTip: 'Puede vincularse con las siguientes bandas:',
  linkedDeviceBraceletFooterTip: 'Para cambiar las bandas vinculadas, elimínela primero.',
  availableLinkDeviceBraceletFooterTip: 'Verifique que el bluetooth de la banda esté encendido y manténgalo cerca del otro dispositivo.',
  pluginVersion: 'Versión del plugin',
  helpAndFeedback: 'Ayuda y comentarios',
  offline: 'Desconectado',
  downloading: 'Descargando…',
  installing: 'Instalando...',
  upgradeSuccess: 'Actualizado con éxito',
  upgradeFailed: 'No se pudo actualizar. Intente de nuevo más tarde.',
  upgradeTimeout: 'Se agotó el tiempo de actualización',
  autoUpgradeInfo: 'Se intentará actualizar automáticamente entre las {0}',
  today: 'Hoy',
  tomorrow: 'Mañana',
  currentIsLatestVersion: 'La versión actual está actualizada',
  lastestVersion: 'Versión más reciente: ',
  currentVersion: 'Versión actual: ',
  fetchFailed: 'No se pudo acceder. Vuelva a intentarlo.',
  releaseNote: 'Qué hay de nuevo',
  releaseVersionHistory: 'Historial de actualizaciones del firmware',
  firmwareAutoUpdate: 'Actualizaciones automáticas de firmware',
  autoUpdateDescriptionNote: 'Una vez detectado un nuevo firmware, el dispositivo intentará actualizarse automáticamente entre las {0}. La actualización se instalará cuando no esté utilizando el dispositivo y no habrá notificaciones sonoras ni luminosas durante el proceso de actualización.',
  updateNow: 'Actualizar',
  requireBelMesh: 'Esta función requiere un gateway de malla Bluetooth para funcionar con normalidad.',
  createCurtainGroup: 'Crear un grupo de cortinas',
  createCurtainGroupTip: 'Se pueden combinar dos motores de cortinas en un grupo para controlarse como cortinas de dos lados.',
  act: 'Mover',
  create: 'Crear',
  chooseCurtainGroupTitle: 'Seleccionar un motor de cortina',
  currentDevice: 'Este dispositivo',
  curtain: 'Cortina',
  noCurtainGroupTip: 'No se puede agrupar ahora. Añada otro motor de cortina e intente de nuevo.',
  switchPlugin: 'Plugin estándar',
  defaultPlugin: 'Predeterminado',
  selectDefaultHP: 'Predeterminado',
  stdPluginTitle: 'Estándar',
  thirdPluginTitle: 'Tradicional',
  stdPluginSubTitle: 'Puede cambiar a la versión antigua en la página de funciones adicionales',
  stdGuideDialogTitle: 'Nueva versión disponible',
  stdGuideDialogSubTitle: 'Actualice la aplicación para tener una experiencia nueva y más fluida.',
  stdGuideDialogNote: 'Si no puede encontrar una función después de actualizar, puede haberse movido a "funciones adicionales".',
  stdGuideDialogButtonOK: 'Aceptar',
  // 多键开关设置
  key: 'Interruptor',
  keyLeft: 'Interruptor izquierdo',
  keyMiddle: 'Interruptor central',
  keyRight: 'Interruptor derecho',
  keyType: 'Tipo de interruptor',
  keyName: 'Nombre',
  light: 'Lámpara',
  updateIcon: 'Cambiar ícono',
  done: 'Listo',
  modifyName: 'Editar nombre',
  keyUpdateIconTips: 'Cuando el ícono está en "{0}", puede pedirle a Mi IA que encienda "{0}".',
  nameHasChars: 'El nombre no puede contener caracteres especiales',
  nameTooLong: 'El nombre puede contener hasta 40 caracteres',
  nameIsEmpty: 'El nombre no puede estar en blanco',
  nameNotSupportEmoji: 'Los nombres no pueden incluir emoji',
  // 房间
  room: 'Habitación',
  room_nameInputTips: 'Ingresar un nombre de habitación',
  room_nameSuggest: 'Nombre recomendado',
  room_createNew: 'Crear habitación nueva',
  room_bedroom: 'Recámara',
  room_masterBedroom: 'Recámara principal',
  room_secondBedroom: 'Segunda recámara',
  room_kitchen: 'Cocina',
  room_diningRoom: 'Comedor',
  room_washroom: 'Baño',
  room_childrensRoom: 'Habitación de los niños',
  room_office: 'Oficina',
  room_study: 'Biblioteca',
  room_balcony: 'Balcón',
  room_studio: 'Taller',
  room_bathroom: 'Baño',
  room_backyard: 'Patio trasero',
  room_unassigned: 'Sin asignar',
  no_privacy_tip_content: 'No se pudo cargar la Política de Privacidad. Verifique la configuración de su red y vuelva a intentarlo o reporte este problema a través de un Comentario.',
  moreDeviceInfo: 'Más información del dispositivo',
  deviceNet: 'Red del dispositivo',
  customizeName: 'Nombre personalizado',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Puerta de enlace Bluetooth Mesh',
  deviceDid: 'ID del dispositivo',
  deviceSN: 'Número de serie del dispositivo',
  mcuVersion: 'Versión MCU del firmware',
  sdkVersion: 'Versión SDK del firmware',
  deviceModel: 'Modelo del dispositivo',
  deviceQR: 'Código QR del dispositivo',
  download: 'Descargar',
  saveSuccess: 'Se guardó correctamente',
  saveFailed: 'No se pudo guardar',
  clipboardy: 'Se copió correctamente',
  connected: 'Conectado',
  notConnected: 'No conectado',
  bleConnected: 'Conexión bluetooth directa',
  deviceOffline: 'Sin conexión',
  deviceConsumables: 'Suministros del dispositivo',
  consumableStateSufficient: 'Suficiente',
  consumableStateInsufficient: 'Insuficiente',
  consumableStateUnknown: 'Estado desconocido',
  consumableStateDepletion: 'Gastado',
  consumableStateRemainPercent: 'Resta el {0} %',
  consumableStateEstimatedHour: {
    'zero': '{0} hrs restantes',
    'one': '{0} hr restante',
    'two': '{0} hrs restantes',
    'few': '{0} hrs restantes',
    'many': '{0} hrs restantes',
    'other': '{0} hrs restantes'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} días restantes',
    'one': '{0} día restante',
    'two': '{0} días restantes',
    'few': '{0} días restantes',
    'many': '{0} días restantes',
    'other': '{0} días restantes'
  },
  changeIcon: 'Cambiar ícono',
  deviceCall: 'Alertas de emergencia',
  cloudStorage: 'Notificaciones de almacenamiento en la Nube',
  cloudStorageVip: 'Recibir notificaciones sobre el estado de la membresía en la Nube',
  largeCardEvent: 'Mostrar los últimos eventos capturados en la tarjeta',
  // 开关智能
  switch_title_controlDevice: 'Controlar dispositivos',
  switch_subtitle_controlDeviceType: 'Establecer tipos de dispositivos para cada botón',
  common_listItem_value_unset: 'No establecido',
  switch_title_buttonControlDevice: 'Dispositivos controlados (${})',
  switch_listItem_title_toWirelessSwitch: 'Cambiar a interruptor inalámbrico',
  switch_listItem_subtile_wirelessSwitchSetting: 'Los botones físicos no podrán controlar los interruptores cuando se active esta función. De todos modos, aún podrá utilizarlos para las automatizaciones.',
  switch_dia_msg_wirelessSwitchSetting: 'Este botón está asociado con otro elemento (${}). Cambie al interruptor inalámbrico para usarlo.',
  switch_listItem_title_voiceControlLoop: 'Comandos de voz para interruptores',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Controlar los interruptores mediante Mi IA',
  switch_listItem_value_voiceControlLoopOn: 'Activado',
  switch_listItem_value_voiceControlLoopOff: 'Desactivado',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Controle los interruptores por comandos de voz mediante Mi IA. Si las luces inteligentes están conectadas al interruptor, puede que se apaguen y desconecten.',
  switch_listItem_title_operationMode: 'Modo de funcionamiento',
  switch_listItem_title_speedMode: 'Modo de supervelocidad',
  switch_listItem_title_standardMode: 'Modo estándar',
  switch_listItem_subtile_speedModeDescription: 'Seleccione esta opción si las automatizaciones solamente deben establecerse para "pulsar una vez". Esto mejorará el tiempo de respuesta de las automatizaciones .',
  switch_listItem_subtile_standardModeDescription: 'Elija esta opción si el dispositivo debe establecer automatizaciones de "pulsar dos veces" o "mantener presionado"',
  switch_dia_msg_speedModeMessage: 'Este dispositivo ya cuenta con una configuración de automatizaciones de "pulsar dos veces" y "mantener presionado". Si selecciona el Súper modo de velocidad, ya no podrá utilizar estas automatizaciones. ¿Continuar de todos modos?',
  switch_title_selectDeviceType: 'Seleccionar tipo de dispositivo',
  switch_subtitle_selectDeviceType: 'Seleccione el tipo de dispositivo controlado por ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Seleccione el tipo de dispositivo controlado por ${}. Deje un botón conectado a los dispositivos normales para asegurarse de que el interruptor funcione con normalidad.',
  switch_title_deviceType_normalDevice: 'Dispositivos normales (luces y lámparas sin funcionalidad inteligente)',
  switch_title_deviceType_smartLight: 'Luces inteligentes',
  switch_title_deviceType_smartSwitch: 'Otros interruptores inteligentes',
  switch_title_deviceType_manualScene: 'Controles de grupos',
  switch_title_deviceType_otherSmartDevice: 'Otros dispositivos inteligentes',
  switch_value_deviceType_normalDevice: 'Dispositivos normales',
  switch_value_deviceType_smartLight: 'Luces inteligentes',
  switch_value_deviceType_smartSwitch: 'Otros interruptores inteligentes',
  switch_value_deviceType_manualScene: 'Controles de grupos',
  switch_value_deviceType_otherSmartDevice: 'Otros dispositivos inteligentes',
  switch_button_title_seeCreatedScene: 'Ver automatizaciones',
  switch_button_title_linkSmartLight: 'Conectar luz inteligente',
  switch_button_title_linkSmartSwitch: 'Conectar interruptores inteligentes',
  switch_button_title_linkManualScene: 'Conectar control de grupo',
  switch_button_title_switchNameSetting: 'Establecer nombre del botón',
  switch_nav_title_buttonControlLight: 'Luces inteligentes controladas (${})',
  switch_nav_subtitle_buttonControlLight: 'Conecte luces inteligentes a un botón para encenderlas y apagarlas y mantenerlas online',
  switch_header_title_selectLightOrGroup: 'Seleccione una luz inteligente o un grupo de luces',
  switch_nav_title_buttonControlSwitch: 'Interruptores inteligentes controlados (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Conecte interruptores para añadir otra interfaz de encendido/apagado. Al pulsar el botón se encenderán y apagarán los interruptores seleccionados.',
  switch_header_title_selectSwitch: 'Seleccionar interruptores inteligentes',
  switch_nav_title_buttonControlManualScene: 'Controles de grupos asignados (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Asigne controles por grupos para ejecutarlos pulsando el botón',
  switch_header_title_selectManualScene: 'Seleccionar controles de grupos',
  common_edit: 'Editar',
  common_reselect: 'Seleccionar de nuevo',
  common_deleted: 'Eliminado',
  common_delete: 'Eliminar',
  common_delete_failed: 'No se pudo eliminar. Compruebe sus ajustes de red y vuelva a intentarlo.',
  common_setting_failed: 'No se pudo establecer. Compruebe si el dispositivo está conectado a la red e inténtelo de nuevo.',
  common_saving: 'Guardando…',
  switch_listItem_title_executionType: 'Modo de ejecución',
  switch_listItem_value_executionTypeCloud: 'Nube',
  switch_listItem_value_executionTypeLocale: 'Local',
  switch_dia_msg_deleteScene: '¿Eliminar esta automatización?',
  switch_scene_name_toggleSwitchDevice: '${} | Un toque | ${} | Activado/Desactivado | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Un toque | ${} | Activado/Desactivado | ${}',
  switch_scene_name_executeManualScene: '${} | Un toque | ${} | Ejecutándose | ${}-${}',
  switch_list_device_unavailable: 'Los dispositivos que no se muestran no soportan esta función',
  switch_button_subtitle_notCurrentHome: 'No se encuentra en el hogar actual',
  common_list_empty: 'Aún no hay nada aquí',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Modo de vinculación',
  switch_title_buttonControlDevice_oneGang: 'Controles del dispositivo'
};