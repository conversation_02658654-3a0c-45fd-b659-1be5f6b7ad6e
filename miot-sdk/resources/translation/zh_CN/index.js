export default {
  setting: '设置',
  featureSetting: '功能设置',
  commonSetting: '通用设置',
  name: '设备名称',
  deviceService: '设备服务',
  location: '位置管理',
  memberSet: '按键设置',
  share: '设备共享',
  btGateway: '蓝牙网关',
  voiceAuth: '语音授权',
  ifttt: '智能场景',
  productBaike: '产品百科',
  firmwareUpgrade: '固件升级',
  firmwareUpdate: '固件更新',
  more: '更多设置',
  help: '使用帮助',
  legalInfo: '法律信息',
  deleteDevice: '删除设备',
  autoUpgrade: '固件自动升级',
  checkUpgrade: '检查固件升级',
  security: '安全设置',
  networkInfo: '网络信息',
  feedback: '反馈问题',
  timezone: '设备时区',
  addToDesktop: '添加桌面快捷方式',
  open: "开",
  close: "关",
  other: '其他',
  multipleKeyShowOnHome: "在首页展示为{0}个按键",
  // 常用设备
  favoriteDevices: "米家首页显示",
  favoriteCamera: "首页卡片大小",
  favoriteAddDevices: "设为首页常用设备",
  // MHDatePicker
  cancel: '取消',
  ok: '确定',
  am: '上午',
  pm: '下午',
  numberMonth: {
    'zero': '{0}个月',
    'one': '{0}个月',
    'two': '{0}个月',
    'few': '{0}个月',
    'many': '{0}个月',
    'other': '{0}个月'
  },
  numberDay: {
    'zero': '{0}天',
    'one': '{0}天',
    'two': '{0}天',
    'few': '{0}天',
    'many': '{0}天',
    'other': '{0}天'
  },
  numberHour: {
    'zero': '{0}小时',
    'one': '{0}小时',
    'two': '{0}小时',
    'few': '{0}小时',
    'many': '{0}小时',
    'other': '{0}小时'
  },
  numberMinute: {
    'zero': '{0}分钟',
    'one': '{0}分钟',
    'two': '{0}分钟',
    'few': '{0}分钟',
    'many': '{0}分钟',
    'other': '{0}分钟'
  },
  numberSecond: {
    'zero': '{0}秒钟',
    'one': '{0}秒钟',
    'two': '{0}秒钟',
    'few': '{0}秒钟',
    'many': '{0}秒钟',
    'other': '{0}秒钟'
  },
  months: '个月', // 复数
  days: '天', // 复数
  hours: '小时', // 复数
  minutes: '分钟', // 复数
  seconds: '秒钟', // 复数
  month: '个月', // 单数
  day: '天', // 单数
  hour: '小时', // 单数
  minute: '分钟', // 单数
  second: '秒钟', // 单数
  yearUnit: '年', // 单数
  monthUnit: '月', // 单数
  dayUnit: '日', // 单数
  hourUnit: '时', // 单数
  minuteUnit: '分', // 单数
  secondUnit: '秒', // 单数
  dateSubTitle: '{0}年{1}月{2}日', // 2019年06月03日
  time24SubTitle: '{0}:{1}', // 11:43
  time12SubTitle: '{0} {1}:{2}', // 上午 11:43
  singleSubTitle: '{0} {1}', // 5 小时
  // 升级相关
  firmwareUpgradeExit: '退出',
  firmwareUpgradeUpdate: '升级',
  firmwareUpgradeLook: '去看看',
  firmwareUpgradeForceUpdate: '由于您当前的设备固件版本过低，一些功能可能无法正常使用。请升级至最新版本，以体验更丰富的功能',
  firmwareUpgradeForceUpdating: '您的设备正在升级，请稍后，以体验更丰富的功能',
  firmwareUpgradeNew_pre: '检测到设备有最新固件版本',
  firmwareUpgradeNew_sub: '，是否升级',
  handling: '操作中...',
  error: '处理失败，请稍后再试',
  createLightGroup: '创建灯组（新）',
  manageLightGroup: '灯组管理（新）',
  deleteLightGroup: '解散灯组',
  deleteCurtainGroup: '解散组',
  linkDevice: '关联设备',
  noSuppurtedLinkageDevice: '当前无可关联设备',
  noSuppurtedLinkageTip: '1.请确保在米家APP中已添加需要关联的设备，并按要求分配到对应房间下；\n2.请将需要关联的蓝牙设备与本设备保持在一定范围内，否则将无法建立关联。',
  supportedLinkageDevices: '可关联如下设备',
  linkageDistanceTip: '请将需要关联的蓝牙设备与本设备保持在一定范围内，否则将无法建立关联',
  linkageRemoveTip: '如需更换关联的蓝牙设备，请先解除关联',
  link: '关联',
  removeLink: '解除关联',
  linkFail: '关联失败',
  removeLinkFail: '解除关联失败',
  linkConfirm: '确认关联该设备？',
  removeLinkConfirm: '确认解除关联？',
  linking: '正在关联...',
  linkDeviceBracelet: '关联手环',
  scanDeviceBracelet: '扫描手环中...',
  scanDeviceBraceletTip: '请将小米手环与本设备保持在一定的范围内, 并确保手环蓝牙广播已开启',
  scanDeviceBraceletEmptyTitle: '附近未发现可关联的小米手环',
  scanDeviceBraceletEmptyTip1: '1.请确认小米手环已开启蓝牙广播',
  scanDeviceBraceletEmptyTip2: '2.请确认小米手环在本设备附近',
  linkedDeviceBraceletHeaderTip: '已关联如下手环',
  availableLinkDeviceBraceletHeaderTip: '可关联如下手环',
  linkedDeviceBraceletFooterTip: '如需更换关联的手环，请先解除关联。',
  availableLinkDeviceBraceletFooterTip: '请将小米手环与本设备保持在一定的范围内并确保手环蓝牙广播已开启',
  pluginVersion: '插件版本',
  helpAndFeedback: '帮助与反馈',
  offline: '离线',
  downloading: '正在下载...',
  installing: '正在安装...',
  upgradeSuccess: '更新成功',
  upgradeFailed: '更新失败, 请稍后再试',
  upgradeTimeout: '更新超时',
  autoUpgradeInfo: '将在{0}尝试自动更新',
  today: '今天',
  tomorrow: '明天',
  currentIsLatestVersion: '当前已是最新版本',
  lastestVersion: '最新版本：',
  currentVersion: '当前版本：',
  fetchFailed: '获取失败，请稍后',
  releaseNote: '更新日志',
  releaseVersionHistory: '固件版本记录',
  firmwareAutoUpdate: '固件自动更新',
  autoUpdateDescriptionNote: '检测到新固件后，设备将在{0}尝试自动更新。设备必须处于空闲状态以完成更新。更新过程无声音提示和灯光打扰。',
  updateNow: '立即更新',
  requireBelMesh: '该功能需要搭配蓝牙Mesh网关使用',
  createCurtainGroup: '创建窗帘伴侣组',
  createCurtainGroupTip: '将两个窗帘伴侣组成一个窗帘伴侣组使用，组合后可以作为双开帘呈现和控制。',
  act: '动一下',
  create: '创建',
  chooseCurtainGroupTitle: '请选择窗帘伴侣',
  currentDevice: '本设备',
  curtain: '窗帘',
  noCurtainGroupTip: '暂无可成组的设备，请再添加一个窗帘伴侣后再试',
  switchPlugin: '标准插件',
  defaultPlugin: '默认样式',
  selectDefaultHP: '默认样式',
  stdPluginTitle: '标准样式',
  thirdPluginTitle: '传统样式',
  stdPluginSubTitle: '点击更多功能，支持打开传统样式页面',
  stdGuideDialogTitle: '全新升级',
  stdGuideDialogSubTitle: '软件界面已全新升级，简洁畅快抢先体验 ！',
  stdGuideDialogNote: '如需查找升级前功能界面可点击底部“更多功能”入口',
  stdGuideDialogButtonOK: '知道了',
  // 多键开关设置
  key: '按键',
  keyLeft: '左键',
  keyMiddle: '中键',
  keyRight: '右键',
  keyType: '按键类型',
  keyName: '名称',
  light: '灯',
  updateIcon: '更换图标',
  done: '完成',
  modifyName: '修改名称',
  keyUpdateIconTips: '将按键图标设为“{0}”后，可对小爱同学说“打开{0}”',
  nameHasChars: '名称不能包含特殊符号',
  nameTooLong: '名称不能超过40个字符',
  nameIsEmpty: '名称不能为空',
  nameNotSupportEmoji: '名称不支持emoji表情',
  // 房间
  room: '房间',
  room_nameInputTips: '请输入房间名',
  room_nameSuggest: '推荐房间名称',
  room_createNew: '创建新房间',
  room_bedroom: '卧室',
  room_masterBedroom: '主卧',
  room_secondBedroom: '次卧',
  room_kitchen: '厨房',
  room_diningRoom: '餐厅',
  room_washroom: '卫生间',
  room_childrensRoom: '儿童房',
  room_office: '办公室',
  room_study: '书房',
  room_balcony: '阳台',
  room_studio: '工作室',
  room_bathroom: '浴室',
  room_backyard: '后院',
  room_unassigned: '未分配房间',
  no_privacy_tip_content: '无法获取隐私，请检查手机网络或在米家APP中反馈问题。',
  moreDeviceInfo: '更多设备信息',
  deviceNet: '设备网络',
  customizeName: '自定义名称',
  software: '软件',
  hardware: '硬件',
  bleMeshGateway: '蓝牙mesh网关',
  deviceDid: '设备ID',
  deviceSN: '设备SN',
  mcuVersion: 'MCU固件版本',
  sdkVersion: 'SDK固件版本',
  deviceModel: '设备型号',
  deviceQR: '设备二维码',
  download: '下载',
  saveSuccess: '保存成功',
  saveFailed: '保存失败',
  clipboardy: '已复制到剪贴板',
  connected: '已连接',
  notConnected: '未连接',
  bleConnected: '蓝牙直连',
  deviceOffline: '设备离线',
  deviceConsumables: '设备耗材',
  consumableStateSufficient: '充足',
  consumableStateInsufficient: '不足',
  consumableStateUnknown: '状态未知',
  consumableStateDepletion: '已耗尽',
  consumableStateRemainPercent: '剩余{0}%',
  consumableStateEstimatedHour: {
    'zero': '预计还可以使用{0}小时',
    'one': '预计还可以使用{0}小时',
    'two': '预计还可以使用{0}小时',
    'few': '预计还可以使用{0}小时',
    'many': '预计还可以使用{0}小时',
    'other': '预计还可以使用{0}小时'
  },
  consumableStateEstimatedDay: {
    'zero': '预计还可以使用{0}天',
    'one': '预计还可以使用{0}天',
    'two': '预计还可以使用{0}天',
    'few': '预计还可以使用{0}天',
    'many': '预计还可以使用{0}天',
    'other': '预计还可以使用{0}天'
  },
  changeIcon: '更换图标',
  deviceCall: '紧急事件电话呼叫',
  cloudStorage: "云存储服务提醒",
  cloudStorageVip: "云存储会员状态提醒",
  largeCardEvent: "大卡展示最新看家事件",
  // 开关智能
  switch_title_controlDevice: "控制设备",
  switch_subtitle_controlDeviceType: "请确认每个按键控制的设别类型",
  common_listItem_value_unset: "未设置",
  switch_title_buttonControlDevice: "${}控制设备",
  switch_listItem_title_toWirelessSwitch: "转无线开关",
  switch_listItem_subtile_wirelessSwitchSetting: "开启后实体按键不再控制开关的通断，仅用于触发自动化",
  switch_dia_msg_wirelessSwitchSetting: "按键关联了${}，需转无线开关使用",
  switch_listItem_title_voiceControlLoop: "语音控制开关",
  switch_listItem_title_xiaoAiVoiceControlLoop: "小爱语音控制开关",
  switch_listItem_value_voiceControlLoopOn: "开启",
  switch_listItem_value_voiceControlLoopOff: "关闭",
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: "开启后，小爱同学可以控制开关， 若开关上连接智能灯，可能造成智能灯断电离线",
  switch_listItem_title_operationMode: "操作模式",
  switch_listItem_title_speedMode: "疾速模式",
  switch_listItem_title_standardMode: "标准模式",
  switch_listItem_subtile_speedModeDescription: "若该设备只需设置「单击」的自动化，推荐选择此项，设备将快速响应单击操作，提升自动化响应速度",
  switch_listItem_subtile_standardModeDescription: "若该设备需要设置「双击」或「长按」的自动化，请选择此项",
  switch_dia_msg_speedModeMessage: "当前设备设置了「双击」或「长按」的自动化，疾速模式下，相关智能场景将无法响应",
  switch_title_selectDeviceType: "选择设备类型",
  switch_subtitle_selectDeviceType: "请选择${}控制的设备类型",
  switch_subtitle_liveWire_selectDeviceType: "请选择${}控制的设备类型。为保障单火开关正常工作，建议保留一个按键连接普通设备",
  switch_title_deviceType_normalDevice: "普通设备（普通灯等非智能设备）",
  switch_title_deviceType_smartLight: "智能灯",
  switch_title_deviceType_smartSwitch: "其它智能开关（开关互控）",
  switch_title_deviceType_manualScene: "执行批量控制",
  switch_title_deviceType_otherSmartDevice: "其它智能设备",
  switch_value_deviceType_normalDevice: "普通设备",
  switch_value_deviceType_smartLight: "智能灯",
  switch_value_deviceType_smartSwitch: "其他智能开关",
  switch_value_deviceType_manualScene: "批量控制",
  switch_value_deviceType_otherSmartDevice: "其它智能设备",
  switch_button_title_seeCreatedScene: "查看已创建的自动化",
  switch_button_title_linkSmartLight: "快捷关联智能灯",
  switch_button_title_linkSmartSwitch: "快捷关联智能开关",
  switch_button_title_linkManualScene: "快捷关联批量控制",
  switch_button_title_switchNameSetting: "设置按键名称",
  switch_nav_title_buttonControlLight: "${}控制智能灯",
  switch_nav_subtitle_buttonControlLight: "将按键和智能灯关联后，单击物理按键会开启/关闭智能灯，且可以让智能灯保持在线",
  switch_header_title_selectLightOrGroup: "选择智能灯或灯组",
  switch_nav_title_buttonControlSwitch: "${}控制智能开关",
  switch_nav_subtitle_buttonControlSwitch: "将闲置按键关联其他智能开关，实现开关双控。单击物理按键可以开启/关闭所选的开关",
  switch_header_title_selectSwitch: "选择智能开关",
  switch_nav_title_buttonControlManualScene: "${}执行批量控制",
  switch_nav_subtitle_buttonControlManualScene: "将闲置按键关联批量控制后。单击物理按键会执行所选的批量控制",
  switch_header_title_selectManualScene: "选择批量控制",
  common_edit: "编辑",
  common_reselect: "重新选择",
  common_deleted: "已删除",
  common_delete: "删除",
  common_delete_failed: "删除失败，请检查网络后重试",
  common_setting_failed: '设置失败，请检查设备网络后重试',
  common_saving: '保存中，请稍后...',
  switch_listItem_title_executionType: "执行方式",
  switch_listItem_value_executionTypeCloud: "云端",
  switch_listItem_value_executionTypeLocale: "本地",
  switch_dia_msg_deleteScene: "删除自动化？",
  switch_scene_name_toggleSwitchDevice: "${} 单击${}——开/关 ${}- ${}",
  switch_scene_name_toggleLightDevice: "${} 单击${}——开/关 ${}",
  switch_scene_name_executeManualScene: "${} 单击${}——执行 ${}",
  switch_list_device_unavailable: "未显示的设备暂不支持此功能",
  switch_button_subtitle_notCurrentHome: "不在当前家庭",
  common_list_empty: "暂无",
  switch_dia_msg_repeatScene: "当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？",
  common_loading: '加载中，请稍后...',
  pairMode: "配对模式",
  switch_title_buttonControlDevice_oneGang: "设备控制"
};