export default {
  setting: 'Налаштування',
  featureSetting: 'Налаштування пристрою',
  commonSetting: 'Загальні налаштування',
  name: 'Назва пристрою',
  deviceService: 'Сервіси пристрою',
  location: 'Керувати місцезнаходженнями',
  memberSet: 'Кнопки',
  share: 'Поділитися пристроєм',
  btGateway: 'Шлюз BLE',
  voiceAuth: 'Голосова авторизація',
  ifttt: 'Автоматизація',
  productBaike: 'Інформація про виріб',
  firmwareUpgrade: 'Оновлення прошивки',
  firmwareUpdate: 'Оновлення прошивки',
  more: 'Додаткові налаштування',
  help: 'Довідка',
  legalInfo: 'Юридична інформація',
  deleteDevice: 'Видалити пристрій',
  autoUpgrade: 'Автоматичне оновлення прошивки',
  checkUpgrade: 'Перевірити наявність оновлень прошивки',
  security: 'Налаштування безпеки',
  networkInfo: 'Інформація про мережу',
  feedback: 'Звіт',
  timezone: 'Часовий пояс пристрою',
  addToDesktop: 'Додати на робочий стіл',
  open: 'Увімк.',
  close: 'Вимк.',
  other: 'Інше',
  multipleKeyShowOnHome: 'Кількість кнопок на робочому столі: {0}',
  // 常用设备
  favoriteDevices: 'Відображати на головній сторінці Xiaomi Home',
  favoriteCamera: 'Розмір картки панелі управління',
  favoriteAddDevices: 'Додати до обраних',
  // MHDatePicker
  cancel: 'Скасувати',
  ok: 'Підтвердити',
  am: 'ДП',
  pm: 'ПП',
  numberMonth: {
    'zero': '{0} місяців',
    'one': '{0} місяць',
    'two': '{0} місяці',
    'few': '{0} місяці',
    'many': '{0} місяців',
    'other': '{0} місяців'
  },
  numberDay: {
    'zero': '{0} днів',
    'one': '{0} день',
    'two': '{0} дні',
    'few': '{0} дні',
    'many': '{0} днів',
    'other': '{0} днів'
  },
  numberHour: {
    'zero': '{0} годин',
    'one': '{0} година',
    'two': '{0} години',
    'few': '{0} години',
    'many': '{0} годин',
    'other': '{0} годин'
  },
  numberMinute: {
    'zero': '{0} хвилин',
    'one': '{0} хвилина',
    'two': '{0} хвилини',
    'few': '{0} хвилини',
    'many': '{0} хвилин',
    'other': '{0} хвилин'
  },
  numberSecond: {
    'zero': '{0} секунд',
    'one': '{0} секунда',
    'two': '{0} секунди',
    'few': '{0} секунди',
    'many': '{0} секунд',
    'other': '{0} секунд'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Вийти',
  firmwareUpgradeUpdate: 'Оновити',
  firmwareUpgradeLook: 'Перегляд',
  firmwareUpgradeForceUpdate: 'Поточна прошивка може бути застарою для запуску деяких функцій. Щоб покращити роботу, оновіть до останньої версії.',
  firmwareUpgradeForceUpdating: 'Пристрій оновлюється. Повторіть спробу пізніше.',
  firmwareUpgradeNew_pre: 'Доступне оновлення. ',
  firmwareUpgradeNew_sub: 'Оновити зараз?',
  handling: 'Секунду…',
  error: 'Сталася помилка. Повторіть спробу пізніше.',
  createLightGroup: 'Створити групу освітлення (нова)',
  manageLightGroup: 'Керувати групою освітлення (нова)',
  deleteLightGroup: 'Розгрупувати освітлення',
  deleteCurtainGroup: 'Розгрупувати пристрої',
  linkDevice: 'Привʼязати пристрої',
  noSuppurtedLinkageDevice: 'Немає доступних пристроїв',
  noSuppurtedLinkageTip: '1. Переконайтеся, що ви додали пристрої у програмі Xiaomi Home та привʼязали їх до кімнат.\\n2. Щоб під\'єднати пристрої Bluetooth, тримайте їх поблизу цього пристрою.',
  supportedLinkageDevices: 'Можна підʼєднати до наступних пристроїв:',
  linkageDistanceTip: 'Тримайте пристрої поблизу, щоб забезпечити надійне підʼєднання.',
  linkageRemoveTip: 'Щоб змінити підʼєднаний пристрій Bluetooth, слід спочатку видалити поточний пристрій.',
  link: 'Підʼєднати',
  removeLink: 'Видалити',
  linkFail: 'Не вдалося підʼєднати',
  removeLinkFail: 'Не вдалося видалити',
  linkConfirm: 'Підʼєднатися до цього пристрою зараз?',
  removeLinkConfirm: 'Видалити зараз?',
  linking: 'Підʼєднання…',
  linkDeviceBracelet: 'Підʼєднати браслет',
  scanDeviceBracelet: 'Сканування шлюзу…',
  scanDeviceBraceletTip: 'Щоб підʼєднатися, тримайте Mi Band близько до пристрою та увімкніть Bluetooth.',
  scanDeviceBraceletEmptyTitle: 'Не вдалося знайти пристрої Mi Band поблизу',
  scanDeviceBraceletEmptyTip1: '1. Переконайтеся, що Bluetooth браслета увімкнено.',
  scanDeviceBraceletEmptyTip2: '2. Тримайте браслет близько до пристрою.',
  linkedDeviceBraceletHeaderTip: 'Підʼєднано до наступних браслетів:',
  availableLinkDeviceBraceletHeaderTip: 'Можна підʼєднати до наступних браслетів:',
  linkedDeviceBraceletFooterTip: 'Щоб змінити підʼєднаний браслет, слід спочатку видалити поточний браслет.',
  availableLinkDeviceBraceletFooterTip: 'Увімкніть Bleutooth на браслеті та тримайте його поблизу пристрою.',
  pluginVersion: 'Версії плагінів',
  helpAndFeedback: 'Допомога та зворотній зв\'язок',
  offline: 'Офлайн',
  downloading: 'Завантаження...',
  installing: 'Встановлення…',
  upgradeSuccess: 'Оновлено успішно',
  upgradeFailed: 'Не вдалося оновити. Повторіть спробу пізніше.',
  upgradeTimeout: 'Час очікування оновлення минув',
  autoUpgradeInfo: 'Повторна спроба автоматичного оновлення через {0}',
  today: 'Сьогодні',
  tomorrow: 'Завтра',
  currentIsLatestVersion: 'Оновлення не потрібно',
  lastestVersion: 'Остання версія: ',
  currentVersion: 'Поточна версія: ',
  fetchFailed: 'Не вдалося отримати доступ. Повторіть спробу.',
  releaseNote: 'Список змін',
  releaseVersionHistory: 'Історія оновлення прошивки',
  firmwareAutoUpdate: 'Автоматичне оновлення прошивки',
  autoUpdateDescriptionNote: 'Щойно буде виявлена нова прошивка, пристрій спробує встановити оновлення автоматично протягом {0}. Оновлення буде встановлено тоді, коли ви не користуватиметеся пристроєм, і під час процесу не буде звукових або світлових сповіщень.',
  updateNow: 'Оновити',
  requireBelMesh: 'Для нормальної роботи цієї функцій потрібен мережевий шлюз Bluetooth.',
  createCurtainGroup: 'Створити групу завіс',
  createCurtainGroupTip: 'Два двигуни завіс можна обʼєднати в групу, якою можна керувати як двосторонніми завісами.',
  act: 'Перемістити',
  create: 'Створити',
  chooseCurtainGroupTitle: 'Оберіть мотор завіси',
  currentDevice: 'Цей пристрій',
  curtain: 'Завіса',
  noCurtainGroupTip: 'Не вдалося згрупувати. Додайте інший мотор завіси та повторіть спробу.',
  switchPlugin: 'Стандартний плагін',
  defaultPlugin: 'За замовчуванням',
  selectDefaultHP: 'За замовчуванням',
  stdPluginTitle: 'Стандартний',
  thirdPluginTitle: 'Традиційний',
  stdPluginSubTitle: 'Перейти на стару версію сторінки можна у додаткових функціях',
  stdGuideDialogTitle: 'Доступна нова версія',
  stdGuideDialogSubTitle: 'Оновіть програму, щоб скористатися новим, більш зручним інтерфейсом.',
  stdGuideDialogNote: 'Якщо вам не вдається знайти функцію після оновлення, можливо її було переміщено у розділ «Додаткові функції».',
  stdGuideDialogButtonOK: 'ОК',
  // 多键开关设置
  key: 'Перемикнутися',
  keyLeft: 'Лівий перемикач',
  keyMiddle: 'Середній перемикач',
  keyRight: 'Правий перемикач',
  keyType: 'Тип перемикача',
  keyName: 'Назва',
  light: 'Лампа',
  updateIcon: 'Змінити іконку',
  done: 'Готово',
  modifyName: 'Змінити назву',
  keyUpdateIconTips: 'Коли іконка увімкнена на "{0}", ви можете попросити Mi AI увімкнути "{0}".',
  nameHasChars: 'Назва не може містити спеціальних символів',
  nameTooLong: 'Назва може містити до 40 символів',
  nameIsEmpty: 'Поле назви не може бути порожнім',
  nameNotSupportEmoji: 'Назва не може містити emoji',
  // 房间
  room: 'Кімната',
  room_nameInputTips: 'Введіть назву кімнати',
  room_nameSuggest: 'Рекомендована назва',
  room_createNew: 'Створити нову кімнату',
  room_bedroom: 'Спальня',
  room_masterBedroom: 'Головна спальня',
  room_secondBedroom: 'Друга спальня',
  room_kitchen: 'Кухня',
  room_diningRoom: 'Їдальня',
  room_washroom: 'Вбиральня',
  room_childrensRoom: 'Дитяча кімната',
  room_office: 'Кабінет',
  room_study: 'Бібліотека',
  room_balcony: 'Балкон',
  room_studio: 'Майстерня',
  room_bathroom: 'Ванна кімната',
  room_backyard: 'Задній двір',
  room_unassigned: 'Непризначений',
  no_privacy_tip_content: 'Не вдалося завантажити Політику конфіденційності. Перевірте налаштування мережі та повторіть спробу, або повідомте про цю проблему через Зворотний зв\'язок.',
  moreDeviceInfo: 'Більше інформації про пристрій',
  deviceNet: 'Мережа пристрою',
  customizeName: 'Користувацька назва',
  software: 'Програмне забезпечення',
  hardware: 'Апаратне забезпечення',
  bleMeshGateway: 'Шлюз Bluetooth Mesh',
  deviceDid: 'Ідентифікатор пристрою',
  deviceSN: 'Серійний номер пристрою',
  mcuVersion: 'Версія прошивки MCU',
  sdkVersion: 'Версія прошивки SDK',
  deviceModel: 'Модель пристрою',
  deviceQR: 'QR-код пристрою',
  download: 'Завантажити',
  saveSuccess: 'Успішно збережено',
  saveFailed: 'Не вдалося зберегти',
  clipboardy: 'Успішно скопійовано',
  connected: 'Підключено',
  notConnected: 'Не підключено',
  bleConnected: 'Пряме підключення по Bluetooth',
  deviceOffline: 'Офлайн',
  deviceConsumables: 'Витратні матеріали пристрою',
  consumableStateSufficient: 'Достатньо',
  consumableStateInsufficient: 'Недостатньо',
  consumableStateUnknown: 'Невідомий статус',
  consumableStateDepletion: 'Використано',
  consumableStateRemainPercent: 'Залишилося {0}%',
  consumableStateEstimatedHour: {
    'zero': 'Залишилося {0} годин',
    'one': 'Залишилася {0} година',
    'two': 'Залишилося {0} години',
    'few': 'Залишилося {0} години',
    'many': 'Залишилося {0} годин',
    'other': 'Залишилося {0} годин'
  },
  consumableStateEstimatedDay: {
    'zero': 'Залишилося {0} днів',
    'one': 'Залишився {0} день',
    'two': 'Залишилося {0} дні',
    'few': 'Залишилося {0} дні',
    'many': 'Залишилося {0} днів',
    'other': 'Залишилося {0} днів'
  },
  changeIcon: 'Змінити іконку',
  deviceCall: 'Екстрені сповіщення',
  cloudStorage: 'Сповіщення хмарного сховища',
  cloudStorageVip: 'Отримувати сповіщення про стан членства в хмарі',
  largeCardEvent: 'Показувати останні зафіксовані події на картці',
  // 开关智能
  switch_title_controlDevice: 'Керування пристроями',
  switch_subtitle_controlDeviceType: 'Встановіть типи пристроїв для кожної кнопки',
  common_listItem_value_unset: 'Не встановлено',
  switch_title_buttonControlDevice: 'Керовані пристрої (${})',
  switch_listItem_title_toWirelessSwitch: 'Змініть на бездротовий перемикач',
  switch_listItem_subtile_wirelessSwitchSetting: 'Якщо цю функцію увімкнено, фізичні кнопки не зможуть керувати перемикачами. Ви все ще зможете використовувати їх для автоматизації.',
  switch_dia_msg_wirelessSwitchSetting: 'Цю кнопку прив\'язано до іншого об\'єкта (${}). Перейдіть на бездротовий перемикач для її використання.',
  switch_listItem_title_voiceControlLoop: 'Голосові команди для перемикачів',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Керуйте перемикачами за допомогою Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Увімк.',
  switch_listItem_value_voiceControlLoopOff: 'Вимк.',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Керуйте перемикачами за допомогою голосових команд через Mi AI. Якщо до перемикача під’єднано розумні світильники, вони можуть бути вимкнені та відключені.',
  switch_listItem_title_operationMode: 'Режим роботи',
  switch_listItem_title_speedMode: 'Супершвидкісний режим',
  switch_listItem_title_standardMode: 'Стандартний режим',
  switch_listItem_subtile_speedModeDescription: 'Виберіть цю опцію, якщо автоматизацію потрібно налаштувати лише для "одне натиснення". Ця опція покращить час реакції автоматизації.',
  switch_listItem_subtile_standardModeDescription: 'Виберіть цей параметр, якщо пристрою потрібно налаштувати автоматику "подвійне натиснення" та "натисніть та утримуйте".',
  switch_dia_msg_speedModeMessage: 'У цьому пристрої вже налаштовано автоматику "подвійне натиснення" та "натисніть та утримуйте". Якщо ви виберете супершвидкісний режим, ви більше не зможете використовувати цю автоматизацію. Продовжити?',
  switch_title_selectDeviceType: 'Вибрати тип пристрою',
  switch_subtitle_selectDeviceType: 'Виберіть тип пристрою, який управляється ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Виберіть тип пристрою, який управляється ${}. Залиште одну кнопку підключеною до простих (не смарт) пристроїв, щоб переконатися, що перемикач працює нормально.',
  switch_title_deviceType_normalDevice: 'Звичайні пристрої (світильники та лампочки без смарт-функцій)',
  switch_title_deviceType_smartLight: 'Смарт-лампи',
  switch_title_deviceType_smartSwitch: 'Інші смарт-перемикачі',
  switch_title_deviceType_manualScene: 'Групове керування',
  switch_title_deviceType_otherSmartDevice: 'Інші смарт-пристрої',
  switch_value_deviceType_normalDevice: 'Звичайні пристрої',
  switch_value_deviceType_smartLight: 'Смарт-лампи',
  switch_value_deviceType_smartSwitch: 'Інші смарт-перемикачі',
  switch_value_deviceType_manualScene: 'Групове керування',
  switch_value_deviceType_otherSmartDevice: 'Інші смарт-пристрої',
  switch_button_title_seeCreatedScene: 'Показати автоматизацію',
  switch_button_title_linkSmartLight: 'Підключити смарт-лампу',
  switch_button_title_linkSmartSwitch: 'Підключити смарт-перемикач',
  switch_button_title_linkManualScene: 'Підключити пакетний контроль',
  switch_button_title_switchNameSetting: 'Встановити назву кнопки',
  switch_nav_title_buttonControlLight: 'Керовані смарт-лампи (${})',
  switch_nav_subtitle_buttonControlLight: 'Підключіть смарт-лампи до кнопки, щоб увімкнути та вимкнути їх, а також, щоб тримати їх онлайн',
  switch_header_title_selectLightOrGroup: 'Виберіть смарт-лампу або групу ламп',
  switch_nav_title_buttonControlSwitch: 'Керовані смарт-перемикачі (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Підключіть перемикачі, щоб додати ще один інтерфейс увімкнення/ вимкнення. Натискання кнопки призведе до увімкнення та вимкнення вибраних перемикачів.',
  switch_header_title_selectSwitch: 'Виберіть смарт-перемикачі',
  switch_nav_title_buttonControlManualScene: 'Призначені елементи групового керування (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Призначте елементи групового керування для їх запуску за допомогою натискання кнопки',
  switch_header_title_selectManualScene: 'Виберіть елементи групового керування',
  common_edit: 'Змінити',
  common_reselect: 'Вибрати ще раз',
  common_deleted: 'Видалено',
  common_delete: 'Видалити',
  common_delete_failed: 'Не вдалося видалити. Перевірте налаштування мережі та повторіть спробу.',
  common_setting_failed: 'Не вдалося встановити. Перевірте чи підключено пристрій до мережі та повторіть спробу.',
  common_saving: 'Збереження...',
  switch_listItem_title_executionType: 'Режим запуску',
  switch_listItem_value_executionTypeCloud: 'Хмара',
  switch_listItem_value_executionTypeLocale: 'На пристрої',
  switch_dia_msg_deleteScene: 'Видалити цю автоматизацію?',
  switch_scene_name_toggleSwitchDevice: '${} | Одне натиснення | ${} | Увімк./Вимк. | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Одне натиснення | ${} | Увімк./Вимк. | ${}',
  switch_scene_name_executeManualScene: '${} | Одне натиснення | ${} | Запуск | ${}-${}',
  switch_list_device_unavailable: 'Не показані пристрої не підтримують цю функцію',
  switch_button_subtitle_notCurrentHome: 'Не в поточному домі',
  common_list_empty: 'Тут ще нічого немає',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Режим сполучення',
  switch_title_buttonControlDevice_oneGang: 'Управління пристроєм'
};