export default {
  setting: 'Configurações',
  featureSetting: 'Configurações do dispositivo',
  commonSetting: 'Configurações gerais',
  name: 'Nome do dispositivo',
  deviceService: 'Serviços do dispositivo',
  location: 'Gerenciar localizações',
  memberSet: 'Botõ<PERSON>',
  share: 'Compartilhar dispositivo',
  btGateway: 'Gateway BLE',
  voiceAuth: 'Autorização de voz',
  ifttt: 'Automação',
  productBaike: 'Informações do produto',
  firmwareUpgrade: 'Atualização de firmware',
  firmwareUpdate: 'Atualização de firmware',
  more: 'Configurações adicionais',
  help: 'Ajuda',
  legalInfo: 'Informações legais',
  deleteDevice: 'Excluir dispositivo',
  autoUpgrade: 'Atualizar firmware automaticamente',
  checkUpgrade: 'Verificar se há atualizações de firmware',
  security: 'Configurações de segurança',
  networkInfo: 'Informações da rede',
  feedback: 'Feedback',
  timezone: 'Fuso horário do dispositivo',
  addToDesktop: 'Adicionar à Tela Inicial',
  open: 'Ativado',
  close: 'Desativado',
  other: 'Outros',
  multipleKeyShowOnHome: 'Número de botões mostrados na página inicial: {0}',
  // 常用设备
  favoriteDevices: 'Exibir na página inicial do Xiaomi Home',
  favoriteCamera: 'Tamanho dos cartões do painel',
  favoriteAddDevices: 'Adicionar aos favoritos',
  // MHDatePicker
  cancel: 'Cancelar',
  ok: 'Confirmar',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} meses',
    'one': '{0} mês',
    'two': '{0} meses',
    'few': '{0} meses',
    'many': '{0} meses',
    'other': '{0} meses'
  },
  numberDay: {
    'zero': '{0} dias',
    'one': '{0} dia',
    'two': '{0} dias',
    'few': '{0} dias',
    'many': '{0} dias',
    'other': '{0} dias'
  },
  numberHour: {
    'zero': '{0} h',
    'one': '{0} h',
    'two': '{0} h',
    'few': '{0} h',
    'many': '{0} h',
    'other': '{0} h'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} s',
    'one': '{0} s',
    'two': '{0} s',
    'few': '{0} s',
    'many': '{0} s',
    'other': '{0} s'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}/{1}/{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Sair',
  firmwareUpgradeUpdate: 'Atualizar agora',
  firmwareUpgradeLook: 'Visualizar',
  firmwareUpgradeForceUpdate: 'O firmware atual pode ser muito antigo para executar alguns recursos. Atualize para a versão mais recente para obter uma melhor experiência.',
  firmwareUpgradeForceUpdating: 'O dispositivo está sendo atualizado. Tente novamente mais tarde.',
  firmwareUpgradeNew_pre: 'Uma atualização está disponível. ',
  firmwareUpgradeNew_sub: 'Atualizar agora?',
  handling: 'Aguarde…',
  error: 'Ocorreu um erro. Tente novamente mais tarde.',
  createLightGroup: 'Criar grupo de luzes (novo)',
  manageLightGroup: 'Gerenciar grupo de luzes (novo)',
  deleteLightGroup: 'Desagrupar luzes',
  deleteCurtainGroup: 'Desagrupar dispositivos',
  linkDevice: 'Conectar dispositivos',
  noSuppurtedLinkageDevice: 'Não há dispositivos disponíveis',
  noSuppurtedLinkageTip: '1. Certifique-se de ter adicionado os dispositivos no app Xiaomi Home e de tê-los atribuído a cômodos.\\n2. Mantenha os dispositivos Bluetooth próximos deste dispositivo para conectá-los com sucesso.',
  supportedLinkageDevices: 'Pode ser conectado aos seguintes dispositivos:',
  linkageDistanceTip: 'Mantenha os dispositivos nas proximidades para garantir que possam ser conectados.',
  linkageRemoveTip: 'Para mudar o dispositivo Bluetooth conectado, remova o dispositivo antes.',
  link: 'Conectar',
  removeLink: 'Remover',
  linkFail: 'Não foi possível conectar',
  removeLinkFail: 'Não foi possível remover',
  linkConfirm: 'Conectar com esse dispositivo agora?',
  removeLinkConfirm: 'Remover agora?',
  linking: 'Conectando...',
  linkDeviceBracelet: 'Conectar pulseira',
  scanDeviceBracelet: 'Procurando pulseira...',
  scanDeviceBraceletTip: 'Mantenha o Mi Band próximo deste dispositivo e certifique-se de que o Bluetooth esteja ativado para conectar com sucesso.',
  scanDeviceBraceletEmptyTitle: 'Não foi possível encontrar nenhum Mi Band por perto',
  scanDeviceBraceletEmptyTip1: '1. Certifique-se de que o Bluetooth da pulseira esteja ativado.',
  scanDeviceBraceletEmptyTip2: '2. Mantenha a pulseira próxima do outro dispositivo.',
  linkedDeviceBraceletHeaderTip: 'Conectado com as seguintes pulseiras:',
  availableLinkDeviceBraceletHeaderTip: 'Pode ser conectado com as seguintes pulseiras:',
  linkedDeviceBraceletFooterTip: 'Para alterar a pulseira conectada, remova a pulseira antes.',
  availableLinkDeviceBraceletFooterTip: 'Certifique-se de que o Bluetooth da pulseira está ativado e mantenha-a próxima do outro dispositivo.',
  pluginVersion: 'Versão do plugin',
  helpAndFeedback: 'Ajuda e Feedback',
  offline: 'Offline',
  downloading: 'Baixando…',
  installing: 'Instalando…',
  upgradeSuccess: 'Atualizado com sucesso',
  upgradeFailed: 'Não foi possível atualizar. Tente novamente mais tarde.',
  upgradeTimeout: 'Tempo limite atingido para a atualização',
  autoUpgradeInfo: 'Tentaremos atualizar automaticamente entre {0}',
  today: 'Hoje',
  tomorrow: 'Amanhã',
  currentIsLatestVersion: 'A versão atual está atualizada',
  lastestVersion: 'Última versão: ',
  currentVersion: 'Versão atual: ',
  fetchFailed: 'Não foi possível acessar. Tente novamente.',
  releaseNote: 'O que há de novo',
  releaseVersionHistory: 'Histórico de atualizações do firmware',
  firmwareAutoUpdate: 'Atualizações automáticas do firmware',
  autoUpdateDescriptionNote: 'Quando um novo firmware for detectado, o dispositivo tentará atualizar-se automaticamente entre {0}. A atualização será instalada quando você não estiver usando o dispositivo e não haverá notificações sonoras nem luminosas durante o processo de atualização.',
  updateNow: 'Atualizar',
  requireBelMesh: 'Esse recurso exige um gateway Bluetooth Mesh para funcionar normalmente.',
  createCurtainGroup: 'Crie um grupo de cortinas',
  createCurtainGroupTip: 'Dois motores de cortinas podem ser combinados em um grupo que pode ser controlado como uma cortina dupla face.',
  act: 'Mover',
  create: 'Criar',
  chooseCurtainGroupTitle: 'Selecione um motor de cortina',
  currentDevice: 'Este dispositivo',
  curtain: 'Cortina',
  noCurtainGroupTip: 'Não é possível agrupar agora. Adicione outro motor de cortina e tente novamente.',
  switchPlugin: 'Plugin padrão',
  defaultPlugin: 'Padrão',
  selectDefaultHP: 'Padrão',
  stdPluginTitle: 'Convencional',
  thirdPluginTitle: 'Tradicional',
  stdPluginSubTitle: 'Você pode mudar para a versão antiga da página nos recursos adicionais',
  stdGuideDialogTitle: 'Nova versão disponível',
  stdGuideDialogSubTitle: 'Atualize o app para ter uma experiência nova e mais eficiente.',
  stdGuideDialogNote: 'Se não conseguir encontrar um recurso após uma atualização, é porque ele pode ter sido transferido para "Recursos adicionais".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Controle',
  keyLeft: 'Interruptor esquerdo',
  keyMiddle: 'Interruptor central',
  keyRight: 'Interruptor direito',
  keyType: 'Tipo de interruptor',
  keyName: 'Nome',
  light: 'Lâmpada',
  updateIcon: 'Alterar ícone',
  done: 'Concluído',
  modifyName: 'Editar nome',
  keyUpdateIconTips: 'Quando o ícone mudar para "{0}", você pode pedir ao Mi AI para ativar "{0}".',
  nameHasChars: 'O nome não pode ter caracteres especiais',
  nameTooLong: 'O nome pode ter até 40 caracteres',
  nameIsEmpty: 'O nome não pode ficar em branco',
  nameNotSupportEmoji: 'Nomes não podem conter emojis',
  // 房间
  room: 'Cômodo',
  room_nameInputTips: 'Insira o nome do cômodo',
  room_nameSuggest: 'Nome recomendado',
  room_createNew: 'Crie um cômodo novo',
  room_bedroom: 'Quarto',
  room_masterBedroom: 'Suíte máster',
  room_secondBedroom: 'Quarto secundário',
  room_kitchen: 'Cozinha',
  room_diningRoom: 'Sala de jantar',
  room_washroom: 'Lavabo',
  room_childrensRoom: 'Quarto das crianças',
  room_office: 'Escritório',
  room_study: 'Biblioteca',
  room_balcony: 'Sacada',
  room_studio: 'Oficina',
  room_bathroom: 'Banheiro',
  room_backyard: 'Quintal',
  room_unassigned: 'Sem atribuição',
  no_privacy_tip_content: 'Não foi possível carregar a Política de Privacidade. Verifique suas configurações de rede e tente novamente ou comunique esse problema pelo Feedback.',
  moreDeviceInfo: 'Mais informações do dispositivo',
  deviceNet: 'Rede do dispositivo',
  customizeName: 'Nome personalizado',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Gateway Bluetooth Mesh',
  deviceDid: 'ID do dispositivo',
  deviceSN: 'Número de série do dispositivo',
  mcuVersion: 'Versão do firmware da MCU',
  sdkVersion: 'Versão do firmware do SDK',
  deviceModel: 'Modelo do dispositivo',
  deviceQR: 'QR code do dispositivo',
  download: 'Baixar',
  saveSuccess: 'Salvo com sucesso',
  saveFailed: 'Não foi possível salvar',
  clipboardy: 'Copiado com sucesso',
  connected: 'Conectado',
  notConnected: 'Não conectado',
  bleConnected: 'Conexão Bluetooth direta',
  deviceOffline: 'Offline',
  deviceConsumables: 'Suprimentos para dispositivos',
  consumableStateSufficient: 'Suficiente',
  consumableStateInsufficient: 'Insuficiente',
  consumableStateUnknown: 'Status desconhecido',
  consumableStateDepletion: 'Usado',
  consumableStateRemainPercent: '{0}% restante',
  consumableStateEstimatedHour: {
    'zero': '{0} h restantes',
    'one': '{0} h restante',
    'two': '{0} h restantes',
    'few': '{0} h restantes',
    'many': '{0} h restantes',
    'other': '{0} h restantes'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} dias restantes',
    'one': '{0} dia restante',
    'two': '{0} dias restantes',
    'few': '{0} dias restantes',
    'many': '{0} dias restantes',
    'other': '{0} dias restantes'
  },
  changeIcon: 'Alterar ícone',
  deviceCall: 'Alertas de emergência',
  cloudStorage: 'Notificações de armazenamento em nuvem',
  cloudStorageVip: 'Receba notificações sobre o status da assinatura em nuvem',
  largeCardEvent: 'Mostrar eventos capturados recentemente no cartão',
  // 开关智能
  switch_title_controlDevice: 'Controlar dispositivos',
  switch_subtitle_controlDeviceType: 'Defina tipos de dispositivos para cada botão',
  common_listItem_value_unset: 'Não definido',
  switch_title_buttonControlDevice: 'Dispositivos controlados (${})',
  switch_listItem_title_toWirelessSwitch: 'Alterar para interruptor sem fio',
  switch_listItem_subtile_wirelessSwitchSetting: 'Os botões físicos não poderão controlar os interruptores quando esse recurso estiver ativado. Você ainda poderá usá-los para automações.',
  switch_dia_msg_wirelessSwitchSetting: 'Este botão está associado a outro item (${}). Altere-o para um interruptor sem fio para utilizá-lo.',
  switch_listItem_title_voiceControlLoop: 'Comandos de voz para interruptores',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Controle os interruptores com com o Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Ativado',
  switch_listItem_value_voiceControlLoopOff: 'Desativado',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Controle os interruptores com comandos de voz usando o Mi AI. Se as lâmpadas inteligentes estiverem conectadas ao interruptor, elas poderão ser desligadas e desconectadas.',
  switch_listItem_title_operationMode: 'Modo de operação',
  switch_listItem_title_speedMode: 'Modo de alta velocidade',
  switch_listItem_title_standardMode: 'Modo padrão',
  switch_listItem_subtile_speedModeDescription: 'Selecione esta opção se as automações só precisarem ser configuradas para "pressionamento único". Esta opção melhorará o tempo de resposta da automação.',
  switch_listItem_subtile_standardModeDescription: 'Selecione esta opção se o dispositivo precisar ser configurado para as automações de "pressionar duas vezes" ou "pressionar e segurar"',
  switch_dia_msg_speedModeMessage: 'Este dispositivo já tem automações de "pressionar duas vezes" e "pressionar e segurar" configuradas. Se você selecionar o modo super-rápido, não conseguirá mais usar estas automações. Continuar mesmo assim?',
  switch_title_selectDeviceType: 'Selecione o tipo do dispositivo',
  switch_subtitle_selectDeviceType: 'Selecione o tipo do dispositivo controlado por ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Selecione o tipo do dispositivo controlado por ${}. Deixe um botão conectado a dispositivos regulares para garantir que o interruptor funcione normalmente.',
  switch_title_deviceType_normalDevice: 'Dispositivos regulares (luzes e abajures sem funcionalidade inteligente)',
  switch_title_deviceType_smartLight: 'Luzes inteligentes',
  switch_title_deviceType_smartSwitch: 'Outros interruptores inteligentes',
  switch_title_deviceType_manualScene: 'Controles em lote',
  switch_title_deviceType_otherSmartDevice: 'Outros dispositivos inteligentes',
  switch_value_deviceType_normalDevice: 'Dispositivos regulares',
  switch_value_deviceType_smartLight: 'Luzes inteligentes',
  switch_value_deviceType_smartSwitch: 'Outros interruptores inteligentes',
  switch_value_deviceType_manualScene: 'Controles em lote',
  switch_value_deviceType_otherSmartDevice: 'Outros dispositivos inteligentes',
  switch_button_title_seeCreatedScene: 'Ver automações',
  switch_button_title_linkSmartLight: 'Conectar luz inteligente',
  switch_button_title_linkSmartSwitch: 'Conectar interruptor inteligente',
  switch_button_title_linkManualScene: 'Conectar controle em lote',
  switch_button_title_switchNameSetting: 'Definir nome do botão',
  switch_nav_title_buttonControlLight: 'Luzes inteligentes controladas (${})',
  switch_nav_subtitle_buttonControlLight: 'Conecte luzes inteligentes a um botão para ligá-los ou desligá-los e mantê-los online',
  switch_header_title_selectLightOrGroup: 'Selecione uma luz inteligente ou grupo de luzes',
  switch_nav_title_buttonControlSwitch: 'Interruptores inteligentes controlados (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Conecte interruptores para adicionar outra interface liga/desliga. Pressionar o botão ligará e desligará os interruptores selecionados.',
  switch_header_title_selectSwitch: 'Selecionar interruptores inteligentes',
  switch_nav_title_buttonControlManualScene: 'Controles em lote atribuídos (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Atribuir controles em lote para executá-los ao pressionar o botão',
  switch_header_title_selectManualScene: 'Selecionar controles em lote',
  common_edit: 'Editar',
  common_reselect: 'Selecionar novamente',
  common_deleted: 'Excluído',
  common_delete: 'Excluir',
  common_delete_failed: 'Não foi possível excluir. Verifique suas configurações de rede e tente novamente.',
  common_setting_failed: 'Não foi possível definir. Verifique se o dispositivo está conectado à rede e tente novamente.',
  common_saving: 'Salvando…',
  switch_listItem_title_executionType: 'Modo de execução',
  switch_listItem_value_executionTypeCloud: 'Nuvem',
  switch_listItem_value_executionTypeLocale: 'Local',
  switch_dia_msg_deleteScene: 'Excluir esta automação?',
  switch_scene_name_toggleSwitchDevice: '${} | Pressionamento único | ${} | Ligado/Desligado | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Pressionamento único | ${} | Ligado/Desligado | ${}',
  switch_scene_name_executeManualScene: '${} | Pressionamento único | ${} | Executando | ${}-${}',
  switch_list_device_unavailable: 'Dispositivos não exibidos não são compatíveis com este recurso',
  switch_button_subtitle_notCurrentHome: 'Não está na casa atual',
  common_list_empty: 'Nada aqui por enquanto',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Modo de emparelhamento',
  switch_title_buttonControlDevice_oneGang: 'Controles do dispositivo'
};