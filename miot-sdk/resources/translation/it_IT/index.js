export default {
  setting: 'Impostazioni',
  featureSetting: 'Impostazioni dispositivo',
  commonSetting: 'Impostazioni generali',
  name: 'Nome dispositivo',
  deviceService: 'Servizi del dispositivo',
  location: 'Gestisci posizioni',
  memberSet: 'Tasti',
  share: 'Condividi dispositivo',
  btGateway: 'Gateway BLE',
  voiceAuth: 'Autorizzazione vocale',
  ifttt: 'Automazione',
  productBaike: 'Informazioni prodotto',
  firmwareUpgrade: 'Aggiornamento firmware',
  firmwareUpdate: 'Aggiornamento firmware',
  more: 'Impostazioni aggiuntive',
  help: 'Guida',
  legalInfo: 'Informazioni legali',
  deleteDevice: 'Rimuovi dispositivo',
  autoUpgrade: 'Aggiornamento automatico firmware',
  checkUpgrade: 'Controlla gli aggiornamenti del firmware',
  security: 'Impostazioni di sicurezza',
  networkInfo: 'Informazioni rete',
  feedback: 'Feedback',
  timezone: 'Fuso orario del dispositivo',
  addToDesktop: 'Aggiungi alla Home',
  open: 'Attivo',
  close: 'Disattivato',
  other: 'Altro',
  multipleKeyShowOnHome: 'Numero di pulsanti visualizzati nella pagina iniziale: {0}',
  // 常用设备
  favoriteDevices: 'Visualizza sulla pagina iniziale di Xiaomi Home',
  favoriteCamera: 'Dimensione schede dashboard',
  favoriteAddDevices: 'Aggiungi ai preferiti',
  // MHDatePicker
  cancel: 'Annulla',
  ok: 'Conferma',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} mesi',
    'one': '{0} mese',
    'two': '{0} mesi',
    'few': '{0} mesi',
    'many': '{0} mesi',
    'other': '{0} mesi'
  },
  numberDay: {
    'zero': '{0} giorni',
    'one': '{0} giorno',
    'two': '{0} giorni',
    'few': '{0} giorni',
    'many': '{0} giorni',
    'other': '{0} giorni'
  },
  numberHour: {
    'zero': '{0} ore',
    'one': '{0} ora',
    'two': '{0} ore',
    'few': '{0} ore',
    'many': '{0} ore',
    'other': '{0} ore'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} sec',
    'one': '{0} sec',
    'two': '{0} sec',
    'few': '{0} sec',
    'many': '{0} sec',
    'other': '{0} sec'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}/{1}/{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Esci',
  firmwareUpgradeUpdate: 'Aggiorna',
  firmwareUpgradeLook: 'Visualizza',
  firmwareUpgradeForceUpdate: 'La versione firmware in uso è obsoleta. Alcune funzioni potrebbero non funzionare correttamente. Aggiorna all\'ultima versione per una migliore esperienza.',
  firmwareUpgradeForceUpdating: 'Il dispositivo è in fase di aggiornamento. Riprova più tardi.',
  firmwareUpgradeNew_pre: 'Aggiornamento disponibile. ',
  firmwareUpgradeNew_sub: 'Aggiornare?',
  handling: 'Solo un secondo…',
  error: 'Si è verificato un errore. Riprova più tardi.',
  createLightGroup: 'Crea un gruppo di luci (nuovo)',
  manageLightGroup: 'Gestisci un gruppo di luci (nuovo)',
  deleteLightGroup: 'Separa le luci',
  deleteCurtainGroup: 'Separa i dispositivi',
  linkDevice: 'Connetti i dispositivi',
  noSuppurtedLinkageDevice: 'Nessun dispositivo disponibile',
  noSuppurtedLinkageTip: '1. Assicurati di aver aggiunto i dispositivi nell\'app Xiaomi Home e di averli assegnati alle stanze.\\n2. Tieni i dispositivi Bluetooth vicini a questo dispositivo per connetterli correttamente.',
  supportedLinkageDevices: 'Può essere connesso ai seguenti dispositivi:',
  linkageDistanceTip: 'Tieni i dispositivi vicini per assicurarti che siano in grado di connettersi.',
  linkageRemoveTip: 'Per modificare il dispositivo Bluetooth connesso, rimuovi prima il dispositivo.',
  link: 'Collegamento',
  removeLink: 'Rimuovi',
  linkFail: 'Impossibile connettere',
  removeLinkFail: 'Impossibile rimuovere',
  linkConfirm: 'Connettere a questo dispositivo?',
  removeLinkConfirm: 'Rimuovere?',
  linking: 'Connessione…',
  linkDeviceBracelet: 'Connetti il bracciale',
  scanDeviceBracelet: 'Ricerca bracciale in corso…',
  scanDeviceBraceletTip: 'Avvicina Mi Band al dispositivo e assicurati che il suo Bluetooth sia attivo per connetterlo correttamente.',
  scanDeviceBraceletEmptyTitle: 'Impossibile trovare Mi Band nelle vicinanze',
  scanDeviceBraceletEmptyTip1: '1. Assicurati che il Bluetooth del bracciale sia attivo.',
  scanDeviceBraceletEmptyTip2: '2. Avvicina il bracciale all\'altro dispositivo.',
  linkedDeviceBraceletHeaderTip: 'Connesso a questi bracciali:',
  availableLinkDeviceBraceletHeaderTip: 'Può essere connesso ai seguenti bracciali:',
  linkedDeviceBraceletFooterTip: 'Per cambiare il bracciale connesso, devi prima rimuoverlo.',
  availableLinkDeviceBraceletFooterTip: 'Assicurati che il Bluetooth del bracciale sia attivo e tienilo vicino all\'altro dispositivo.',
  pluginVersion: 'Versione plug-in',
  helpAndFeedback: 'Guida e feedback',
  offline: 'Offline',
  downloading: 'Download in corso…',
  installing: 'Installazione…',
  upgradeSuccess: 'Aggiornamento completato',
  upgradeFailed: 'Impossibile aggiornare. Riprova più tardi.',
  upgradeTimeout: 'Timeout aggiornamento',
  autoUpgradeInfo: 'Tentativo di aggiornamento automatico del firmware tra le {0}',
  today: 'Oggi',
  tomorrow: 'Domani',
  currentIsLatestVersion: 'La versione in uso è aggiornata',
  lastestVersion: 'Versione più recente: ',
  currentVersion: 'Versione attuale: ',
  fetchFailed: 'Impossibile accedere. Riprova.',
  releaseNote: 'Novità',
  releaseVersionHistory: 'Cronologia degli aggiornamenti del firmware',
  firmwareAutoUpdate: 'Aggiornamenti firmware automatici',
  autoUpdateDescriptionNote: 'Una volta rilevato un nuovo firmware, il dispositivo proverà ad aggiornarsi automaticamente tra le {0}. L\'aggiornamento verrà installato quando non si utilizza il dispositivo e non ci saranno notifiche audio o luminose durante il processo di aggiornamento.',
  updateNow: 'Aggiorna',
  requireBelMesh: 'Questa funzione richiede un gateway mesh Bluetooth per funzionare correttamente.',
  createCurtainGroup: 'Crea un gruppo di tende',
  createCurtainGroupTip: 'Due motori per tende possono essere combinati in un gruppo che può essere controllato come una tenda unica.',
  act: 'Sposta',
  create: 'Crea',
  chooseCurtainGroupTitle: 'Seleziona un motore per tende',
  currentDevice: 'Questo dispositivo',
  curtain: 'Tenda',
  noCurtainGroupTip: 'Impossibile raggruppare. Aggiungi un altro motore per tende e riprova.',
  switchPlugin: 'Plug-in standard',
  defaultPlugin: 'Predefinita',
  selectDefaultHP: 'Predefinita',
  stdPluginTitle: 'Standard',
  thirdPluginTitle: 'Tradizionale',
  stdPluginSubTitle: 'Puoi passare alla vecchia versione della pagina nelle funzionalità aggiuntive',
  stdGuideDialogTitle: 'Versione disponibile',
  stdGuideDialogSubTitle: 'Aggiorna l\'app per un\'esperienza nuova e semplificata.',
  stdGuideDialogNote: 'Se non riesci a trovare una funzionalità dopo un aggiornamento, potrebbe essere stata spostata in "Funzionalità aggiuntive".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Interruttore',
  keyLeft: 'Interruttore sinistro',
  keyMiddle: 'Interruttore centrale',
  keyRight: 'Interruttore destro',
  keyType: 'Tipo di interruttore',
  keyName: 'Nome',
  light: 'Lampada',
  updateIcon: 'Cambia l\'icona',
  done: 'Fatto',
  modifyName: 'Modifica il nome',
  keyUpdateIconTips: 'Quando l\'icona è impostata su "{0}", puoi chiedere a Mi AI di attivare "{0}".',
  nameHasChars: 'Il nome non può contenere caratteri speciali',
  nameTooLong: 'Il nome può contenere fino a 40 caratteri',
  nameIsEmpty: 'Il nome non può essere vuoto',
  nameNotSupportEmoji: 'I nomi non possono includere emoji',
  // 房间
  room: 'Stanza',
  room_nameInputTips: 'Inserisci un nome per la stanza',
  room_nameSuggest: 'Nome consigliato',
  room_createNew: 'Crea una nuova stanza',
  room_bedroom: 'Camera da letto',
  room_masterBedroom: 'Camera principale',
  room_secondBedroom: 'Camera secondaria',
  room_kitchen: 'Cucina',
  room_diningRoom: 'Sala da pranzo',
  room_washroom: 'Bagno di servizio',
  room_childrensRoom: 'Cameretta',
  room_office: 'Studio',
  room_study: 'Biblioteca',
  room_balcony: 'Terrazza',
  room_studio: 'Laboratorio',
  room_bathroom: 'Bagno principale',
  room_backyard: 'Cortile',
  room_unassigned: 'Non assegnati',
  no_privacy_tip_content: 'Impossibile caricare l\'informativa sulla privacy. Controlla le impostazioni di rete e riprova, oppure segnala questo problema tramite Feedback.',
  moreDeviceInfo: 'Ulteriori informazioni sul dispositivo',
  deviceNet: 'Rete dispositivo',
  customizeName: 'Nome personalizzato',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Gateway di rete Bluetooth',
  deviceDid: 'ID dispositivo',
  deviceSN: 'N. di serie',
  mcuVersion: 'Versione firmware MCU',
  sdkVersion: 'Versione firmware SDK',
  deviceModel: 'Modello dispositivo',
  deviceQR: 'Codice QR dispositivo',
  download: 'Scarica',
  saveSuccess: 'Salvato correttamente',
  saveFailed: 'Impossibile salvare',
  clipboardy: 'Copiato correttamente',
  connected: 'Connesso',
  notConnected: 'Non connesso',
  bleConnected: 'Connessione Bluetooth diretta',
  deviceOffline: 'Offline',
  deviceConsumables: 'Materiali di consumo',
  consumableStateSufficient: 'Sufficienti',
  consumableStateInsufficient: 'Insufficienti',
  consumableStateUnknown: 'Stato sconosciuto',
  consumableStateDepletion: 'Esaurito',
  consumableStateRemainPercent: '{0}%% rimanente',
  consumableStateEstimatedHour: {
    'zero': '{0} ore rimanenti',
    'one': '{0} ora rimanente',
    'two': '{0} ore rimanenti',
    'few': '{0} ore rimanenti',
    'many': '{0} ore rimanenti',
    'other': '{0} ore rimanenti'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} giorni rimanenti',
    'one': '{0} giorno rimanente',
    'two': '{0} giorni rimanenti',
    'few': '{0} giorni rimanenti',
    'many': '{0} giorni rimanenti',
    'other': '{0} giorni rimanenti'
  },
  changeIcon: 'Cambia l\'icona',
  deviceCall: 'Avvisi di emergenza',
  cloudStorage: 'Notifiche di archiviazione cloud',
  cloudStorageVip: 'Ricevi notifiche sullo stato dell\'abbonamento cloud',
  largeCardEvent: 'Mostra gli ultimi eventi acquisiti sulla scheda',
  // 开关智能
  switch_title_controlDevice: 'Controlla i dispositivi',
  switch_subtitle_controlDeviceType: 'Imposta i tipi di dispositivo per ciascun tasto',
  common_listItem_value_unset: 'Non impostato',
  switch_title_buttonControlDevice: 'Dispositivi controllati (${})',
  switch_listItem_title_toWirelessSwitch: 'Cambia in interruttore wireless',
  switch_listItem_subtile_wirelessSwitchSetting: 'I tasti fisici non potranno controllare gli interruttori quando questa funzionalità è attiva. Potrai comunque utilizzarli per le automazioni.',
  switch_dia_msg_wirelessSwitchSetting: 'Questo tasto è associato ad un altro elemento (${}). Cambia in interruttore wireless per utilizzarlo.',
  switch_listItem_title_voiceControlLoop: 'Comandi vocali per gli interruttori',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Controlla gli interruttori tramite Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Attivo',
  switch_listItem_value_voiceControlLoopOff: 'Disattivato',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Controlla gli interruttori con comandi vocali tramite Mi AI. Se le lampadine intelligenti sono collegate all\'interruttore, potrebbero essere spente e disconnesse.',
  switch_listItem_title_operationMode: 'Modalità di utilizzo',
  switch_listItem_title_speedMode: 'Modalità Super veloce',
  switch_listItem_title_standardMode: 'Modalità Standard',
  switch_listItem_subtile_speedModeDescription: 'Seleziona questa opzione se le automazioni devono essere impostate solo per la "Pressione singola". Questa opzione migliorerà il tempo di risposta dell\'automazione.',
  switch_listItem_subtile_standardModeDescription: 'Seleziona questa opzione se il dispositivo deve impostare le automazioni "Premi due volte" o "Tieni premuto".',
  switch_dia_msg_speedModeMessage: 'Questo dispositivo ha già impostato le automazioni "Premi due volte" e "Tieni premuto". Se selezioni la modalità Super veloce, non potrai più utilizzare queste automazioni. Continuare comunque?',
  switch_title_selectDeviceType: 'Seleziona il tipo di dispositivo',
  switch_subtitle_selectDeviceType: 'Seleziona il tipo di dispositivo controllato da ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Seleziona il tipo di dispositivo controllato da ${}. Lascia un tasto collegato a dispositivi normali per assicurarti che l\'interruttore funzioni correttamente.',
  switch_title_deviceType_normalDevice: 'Dispositivi normali (luci e lampade senza funzionalità smart)',
  switch_title_deviceType_smartLight: 'Luci smart',
  switch_title_deviceType_smartSwitch: 'Altri interruttori smart',
  switch_title_deviceType_manualScene: 'Scene',
  switch_title_deviceType_otherSmartDevice: 'Altri dispositivi smart',
  switch_value_deviceType_normalDevice: 'Dispositivi normali',
  switch_value_deviceType_smartLight: 'Luci smart',
  switch_value_deviceType_smartSwitch: 'Altri interruttori smart',
  switch_value_deviceType_manualScene: 'Scene',
  switch_value_deviceType_otherSmartDevice: 'Altri dispositivi smart',
  switch_button_title_seeCreatedScene: 'Visualizza le automazioni',
  switch_button_title_linkSmartLight: 'Collega la luce smart',
  switch_button_title_linkSmartSwitch: 'Collega l\'interruttore smart',
  switch_button_title_linkManualScene: 'Collega la scena',
  switch_button_title_switchNameSetting: 'Imposta il nome del tasto',
  switch_nav_title_buttonControlLight: 'Luci smart controllate (${})',
  switch_nav_subtitle_buttonControlLight: 'Collega le luci smart a un tasto per accenderle, spegnerle e mantenerle online',
  switch_header_title_selectLightOrGroup: 'Seleziona la luce o il gruppo di luci smart',
  switch_nav_title_buttonControlSwitch: 'Interruttori smart controllati (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Collega gli interruttori per aggiungere un\'altra interfaccia di accensione/spegnimento. Premendo il tasto, gli interruttori selezionati si accenderanno e spegneranno.',
  switch_header_title_selectSwitch: 'Seleziona gli interruttori smart',
  switch_nav_title_buttonControlManualScene: 'Scene assegnate (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Assegna le scene per eseguirle premendo il tasto',
  switch_header_title_selectManualScene: 'Seleziona la scena',
  common_edit: 'Modifica',
  common_reselect: 'Seleziona di nuovo',
  common_deleted: 'Eliminato',
  common_delete: 'Elimina',
  common_delete_failed: 'Impossibile eliminare. Controlla le impostazioni di rete e riprova.',
  common_setting_failed: 'Impossibile impostare. Controlla se il dispositivo è connesso alla rete e riprova.',
  common_saving: 'Salvataggio…',
  switch_listItem_title_executionType: 'Modalità di esecuzione',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Memoria interna',
  switch_dia_msg_deleteScene: 'Eliminare questa automazione?',
  switch_scene_name_toggleSwitchDevice: '${} | Pressione singola | ${} | Acceso/Spento| ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Pressione singola | ${} | Acceso/Spento| ${}',
  switch_scene_name_executeManualScene: '${} | Pressione singola | ${} | Esegui | ${}-${}',
  switch_list_device_unavailable: 'I dispositivi non mostrati non supportano questa funzionalità',
  switch_button_subtitle_notCurrentHome: 'Non nella casa corrente',
  common_list_empty: 'Niente da visualizzare',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Modalità di associazione',
  switch_title_buttonControlDevice_oneGang: 'Comandi dispositivo'
};