export default {
  setting: 'Ajustes',
  featureSetting: 'Ajustes del dispositivo',
  commonSetting: 'Ajustes generales',
  name: 'Nombre del dispositivo',
  deviceService: 'Servicios del dispositivo',
  location: 'Gestionar ubicaciones',
  memberSet: 'Botones',
  share: 'Compartir dispositivo',
  btGateway: 'Puerta de enlace BLE',
  voiceAuth: 'Autorización de voz',
  ifttt: 'Automatización',
  productBaike: 'Información del producto',
  firmwareUpgrade: 'Actualización de firmware',
  firmwareUpdate: 'Actualización de firmware',
  more: 'Ajustes adicionales',
  help: 'Ayuda',
  legalInfo: 'Información legal',
  deleteDevice: 'Eliminar dispositivo',
  autoUpgrade: 'Actualizar firmware automáticamente',
  checkUpgrade: 'Comprobar actualizaciones de firmware',
  security: 'Ajustes de seguridad',
  networkInfo: 'Información de red',
  feedback: 'Comentarios',
  timezone: 'Zona horaria del dispositivo',
  addToDesktop: 'Añadir a la pantalla de inicio',
  open: 'Activado',
  close: 'Desactivado',
  other: 'Otros',
  multipleKeyShowOnHome: 'El número de botones que se muestran en la página de inicio: {0}',
  // 常用设备
  favoriteDevices: 'Mostrar en la página de inicio de Xiaomi Home',
  favoriteCamera: 'Tamaño de tarjetas en el panel',
  favoriteAddDevices: 'Añadir a favoritos',
  // MHDatePicker
  cancel: 'Cancelar',
  ok: 'Confirmar',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} meses',
    'one': '{0} mes',
    'two': '{0} meses',
    'few': '{0} meses',
    'many': '{0} meses',
    'other': '{0} meses'
  },
  numberDay: {
    'zero': '{0} días',
    'one': '{0} día',
    'two': '{0} días',
    'few': '{0} días',
    'many': '{0} días',
    'other': '{0} días'
  },
  numberHour: {
    'zero': '{0} h',
    'one': '{0} h',
    'two': '{0} h',
    'few': '{0} h',
    'many': '{0} h',
    'other': '{0} h'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} s',
    'one': '{0} s',
    'two': '{0} s',
    'few': '{0} s',
    'many': '{0} s',
    'other': '{0} s'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Salir',
  firmwareUpgradeUpdate: 'Actualizar',
  firmwareUpgradeLook: 'Ver',
  firmwareUpgradeForceUpdate: 'La versión de firmware actual es demasiado antigua, puede que algunas características no funcionen correctamente. Actualiza a la última versión para una mejor experiencia.',
  firmwareUpgradeForceUpdating: 'El dispositivo se está actualizando. Vuelve a intentarlo más tarde.',
  firmwareUpgradeNew_pre: 'Hay una actualización disponible. ',
  firmwareUpgradeNew_sub: '¿Actualizar ahora?',
  handling: 'Espera un momento',
  error: 'Ha ocurrido un error. Vuelve a intentarlo más tarde.',
  createLightGroup: 'Crear grupo de luces (nuevo)',
  manageLightGroup: 'Gestionar grupo de luces (nuevo)',
  deleteLightGroup: 'Desagrupar luces',
  deleteCurtainGroup: 'Desagrupar dispositivos',
  linkDevice: 'Vincular dispositivos',
  noSuppurtedLinkageDevice: 'No hay dispositivos disponibles',
  noSuppurtedLinkageTip: '1. Asegúrate de que has añadido los dispositivos en la aplicación Xiaomi Home y de asignarlos a habitaciones.\\n2. Mantén los dispositivos bluetooth cerca de este dispositivo para conectarlos correctamente.',
  supportedLinkageDevices: 'Puede vincularse con los siguientes dispositivos:',
  linkageDistanceTip: 'Mantén los dispositivos cerca para que se puedan vincular.',
  linkageRemoveTip: 'Para cambiar el dispositivo bluetooth vinculado, elimina antes el dispositivo.',
  link: 'Vincular',
  removeLink: 'Eliminar',
  linkFail: 'No se ha podido vincular',
  removeLinkFail: 'No se ha podido eliminar',
  linkConfirm: '¿Vincular con este dispositivo ahora?',
  removeLinkConfirm: '¿Eliminar ahora?',
  linking: 'Vinculando',
  linkDeviceBracelet: 'Vincular pulsera',
  scanDeviceBracelet: 'Escaneando pulsera',
  scanDeviceBraceletTip: 'Mantener Mi Band cerca de este dispositivo para que su bluetooth se pueda conectar correctamente.',
  scanDeviceBraceletEmptyTitle: 'No se encontraron Mi Band cerca',
  scanDeviceBraceletEmptyTip1: '1. Asegúrate de que el bluetooth de la pulsera esté activado.',
  scanDeviceBraceletEmptyTip2: '2. Mantén la pulsera cerca del otro dispositivo.',
  linkedDeviceBraceletHeaderTip: 'Vinculado con las siguientes pulseras:',
  availableLinkDeviceBraceletHeaderTip: 'Puede vincularse con las siguientes pulseras:',
  linkedDeviceBraceletFooterTip: 'Para cambiar la pulsera vinculada, elimina primero la pulsera.',
  availableLinkDeviceBraceletFooterTip: 'Asegúrate de que el bluetooth de la pulsera esté activado y mantenla cerca del otro dispositivo.',
  pluginVersion: 'Versión del plugin',
  helpAndFeedback: 'Ayuda y comentarios',
  offline: 'Desconectado',
  downloading: 'Descargando',
  installing: 'Instalando',
  upgradeSuccess: 'Actualizado con éxito',
  upgradeFailed: 'No se ha podido actualizar. Vuelve a intentarlo más tarde.',
  upgradeTimeout: 'Se ha agotado el tiempo de actualización',
  autoUpgradeInfo: 'Se intentará actualizar automáticamente entre las {0}',
  today: 'Hoy',
  tomorrow: 'Mañana',
  currentIsLatestVersion: 'Esta es la versión más reciente',
  lastestVersion: 'Versión más reciente:',
  currentVersion: 'Versión actual:',
  fetchFailed: 'No se ha podido acceder. Vuelve a intentarlo.',
  releaseNote: 'Novedades',
  releaseVersionHistory: 'Historial de actualizaciones del firmware',
  firmwareAutoUpdate: 'Actualizaciones automáticas del firmware',
  autoUpdateDescriptionNote: 'Una vez detectado un nuevo firmware, el dispositivo intentará actualizarse automáticamente entre las {0}. La actualización se instalará cuando no estés utilizando el dispositivo y no habrá notificaciones sonoras ni luminosas durante el proceso de actualización.',
  updateNow: 'Actualizar',
  requireBelMesh: 'Esta función requiere una puerta de enlace bluetooth de malla para funcionar normalmente.',
  createCurtainGroup: 'Crear un grupo de cortinas',
  createCurtainGroupTip: 'Se pueden combinar dos motores de cortinas en un grupo para controlarse como cortinas de doble cara.',
  act: 'Mover',
  create: 'Crear',
  chooseCurtainGroupTitle: 'Seleccionar un motor de cortina',
  currentDevice: 'Este dispositivo',
  curtain: 'Cortina',
  noCurtainGroupTip: 'No se puede agrupar ahora. Añade otro motor de cortina y vuelve a intentarlo.',
  switchPlugin: 'Plugin estándar',
  defaultPlugin: 'Predeterminado',
  selectDefaultHP: 'Predeterminado',
  stdPluginTitle: 'Estándar',
  thirdPluginTitle: 'Tradicional',
  stdPluginSubTitle: 'Puedes cambiar a la versión antigua en la página de funciones adicionales',
  stdGuideDialogTitle: 'Nueva versión disponible',
  stdGuideDialogSubTitle: 'Actualiza la aplicación para tener una experiencia nueva y más fluida.',
  stdGuideDialogNote: 'Si no encuentras una función después de actualizar, puede haberse movido a "Funciones adicionales".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Interruptor',
  keyLeft: 'Interruptor izquierdo',
  keyMiddle: 'Interruptor central',
  keyRight: 'Interruptor derecho',
  keyType: 'Tipo de interruptor',
  keyName: 'Nombre',
  light: 'Lámpara',
  updateIcon: 'Cambiar icono',
  done: 'Listo',
  modifyName: 'Editar nombre',
  keyUpdateIconTips: 'Cuando el icono está en "{0}", puedes pedirle a Mi IA que active "{0}".',
  nameHasChars: 'El nombre no puede contener caracteres especiales',
  nameTooLong: 'El nombre puede contener hasta 40 caracteres',
  nameIsEmpty: 'El nombre no puede estar en blanco',
  nameNotSupportEmoji: 'Los nombres no pueden incluir emojis',
  // 房间
  room: 'Habitación',
  room_nameInputTips: 'Introducir un nombre de habitación',
  room_nameSuggest: 'Nombre recomendado',
  room_createNew: 'Crear una nueva habitación',
  room_bedroom: 'Dormitorio',
  room_masterBedroom: 'Dormitorio principal',
  room_secondBedroom: 'Segundo dormitorio',
  room_kitchen: 'Cocina',
  room_diningRoom: 'Comedor',
  room_washroom: 'Aseo',
  room_childrensRoom: 'Cuarto infantil',
  room_office: 'Despacho',
  room_study: 'Biblioteca',
  room_balcony: 'Balcón',
  room_studio: 'Taller',
  room_bathroom: 'Baño',
  room_backyard: 'Patio trasero',
  room_unassigned: 'Sin asignar',
  no_privacy_tip_content: 'No se ha podido cargar la Política de Privacidad. Comprueba tus ajustes de red y vuelve a intentarlo o informa de este problema a través de un comentario.',
  moreDeviceInfo: 'Más información del dispositivo',
  deviceNet: 'Red del dispositivo',
  customizeName: 'Nombre personalizado',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Puerta de enlace bluetooth de malla',
  deviceDid: 'ID del dispositivo',
  deviceSN: 'Número de serie del dispositivo',
  mcuVersion: 'Versión del firmware de la MCU',
  sdkVersion: 'Versión del firmware del SDK',
  deviceModel: 'Modelo del dispositivo',
  deviceQR: 'Código QR del dispositivo',
  download: 'Descargar',
  saveSuccess: 'Guardado correctamente',
  saveFailed: 'No se ha podido guardar',
  clipboardy: 'Copiado correctamente',
  connected: 'Conectado',
  notConnected: 'No conectado',
  bleConnected: 'Conexión bluetooth directa',
  deviceOffline: 'Desconectado',
  deviceConsumables: 'Suministros del dispositivo',
  consumableStateSufficient: 'Suficiente',
  consumableStateInsufficient: 'Insuficiente',
  consumableStateUnknown: 'Estado desconocido',
  consumableStateDepletion: 'Gastado',
  consumableStateRemainPercent: '{0} % restante',
  consumableStateEstimatedHour: {
    'zero': '{0} h restantes',
    'one': '{0} h restante',
    'two': '{0} h restantes',
    'few': '{0} h restantes',
    'many': '{0} h restantes',
    'other': '{0} h restantes'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} días restantes',
    'one': '{0} día restante',
    'two': '{0} días restantes',
    'few': '{0} días restantes',
    'many': '{0} días restantes',
    'other': '{0} días restantes'
  },
  changeIcon: 'Cambiar icono',
  deviceCall: 'Alertas de emergencia',
  cloudStorage: 'Notificaciones del almacenamiento de la nube',
  cloudStorageVip: 'Recibir notificaciones sobre el estado de la suscripción a la nube',
  largeCardEvent: 'Mostrar los últimos eventos resgistrados en la tarjeta',
  // 开关智能
  switch_title_controlDevice: 'Controlar dispositivos',
  switch_subtitle_controlDeviceType: 'Establecer tipos de dispositivos para cada botón',
  common_listItem_value_unset: 'No establecido',
  switch_title_buttonControlDevice: 'Dispositivos controlados (${})',
  switch_listItem_title_toWirelessSwitch: 'Cambiar a interruptor inalámbrico',
  switch_listItem_subtile_wirelessSwitchSetting: 'Los botones físicos no podrán controlar los interruptores cuando esta función esté activada. De todos modos, aún podrás utilizarlos para las automatizaciones.',
  switch_dia_msg_wirelessSwitchSetting: 'Este botón está asociado con otro elemento (${}). Cambia al interruptor inalámbrico para usarlo.',
  switch_listItem_title_voiceControlLoop: 'Comandos de voz para interruptores',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Controlar los interruptores mediante Mi IA',
  switch_listItem_value_voiceControlLoopOn: 'Activado',
  switch_listItem_value_voiceControlLoopOff: 'Desactivado',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Controla los interruptores por comandos de voz mediante Mi IA. Si las luces inteligentes están conectadas al interruptor, es posible que se apaguen y desconecten.',
  switch_listItem_title_operationMode: 'Modo de funcionamiento',
  switch_listItem_title_speedMode: 'Modo de supervelocidad',
  switch_listItem_title_standardMode: 'Modo estándar',
  switch_listItem_subtile_speedModeDescription: 'Selecciona esta opción si las automatizaciones solo deben establecerse para "pulsar una vez". Esta opción mejorará el tiempo de respuesta de la automatización.',
  switch_listItem_subtile_standardModeDescription: 'Selecciona esta opción si el dispositivo debe establecer automatizaciones de "pulsar dos veces" o "mantener pulsado"',
  switch_dia_msg_speedModeMessage: 'Este dispositivo ya cuenta con una configuración de automatizaciones de "pulsar dos veces" y "mantener pulsado". Si seleccionas el Modo de supervelocidad, ya no podrás utilizar estas automatizaciones. ¿Continuar de todos modos?',
  switch_title_selectDeviceType: 'Seleccionar tipo de dispositivo',
  switch_subtitle_selectDeviceType: 'Selecciona el tipo de dispositivo controlado por ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Selecciona el tipo de dispositivo controlado por ${}. Deja un botón conectado a los dispositivos normales para asegurarte de que el interruptor funcione con normalidad.',
  switch_title_deviceType_normalDevice: 'Dispositivos normales (luces y lámparas sin funcionalidad inteligente)',
  switch_title_deviceType_smartLight: 'Luces inteligentes',
  switch_title_deviceType_smartSwitch: 'Otros interruptores inteligentes',
  switch_title_deviceType_manualScene: 'Controles de grupos',
  switch_title_deviceType_otherSmartDevice: 'Otros dispositivos inteligentes',
  switch_value_deviceType_normalDevice: 'Dispositivos normales',
  switch_value_deviceType_smartLight: 'Luces inteligentes',
  switch_value_deviceType_smartSwitch: 'Otros interruptores inteligentes',
  switch_value_deviceType_manualScene: 'Controles de grupos',
  switch_value_deviceType_otherSmartDevice: 'Otros dispositivos inteligentes',
  switch_button_title_seeCreatedScene: 'Ver automatizaciones',
  switch_button_title_linkSmartLight: 'Conectar luz inteligente',
  switch_button_title_linkSmartSwitch: 'Conectar interruptores inteligentes',
  switch_button_title_linkManualScene: 'Conectar control de grupo',
  switch_button_title_switchNameSetting: 'Establecer nombre del botón',
  switch_nav_title_buttonControlLight: 'Luces inteligentes controladas (${})',
  switch_nav_subtitle_buttonControlLight: 'Conecta luces inteligentes a un botón para encenderlas y apagarlas y mantenerlas online',
  switch_header_title_selectLightOrGroup: 'Selecciona una luz inteligente o un grupo de luces',
  switch_nav_title_buttonControlSwitch: 'Interruptores inteligentes controlados (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Conecta interruptores para añadir otra interfaz de encendido/apagado. Al pulsar el botón se encenderán y apagarán los interruptores seleccionados.',
  switch_header_title_selectSwitch: 'Seleccionar interruptores inteligentes',
  switch_nav_title_buttonControlManualScene: 'Controles de grupos asignados (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Asigna controles por grupos para ejecutarlos pulsando el botón',
  switch_header_title_selectManualScene: 'Seleccionar controles de grupos',
  common_edit: 'Editar',
  common_reselect: 'Seleccionar de nuevo',
  common_deleted: 'Eliminado',
  common_delete: 'Eliminar',
  common_delete_failed: 'No se pudo eliminar. Comprueba tus ajustes de red y vuelve a intentarlo.',
  common_setting_failed: 'No se pudo establecer. Comprueba si el dispositivo está conectado a la red e inténtalo de nuevo.',
  common_saving: 'Guardando…',
  switch_listItem_title_executionType: 'Modo de ejecución',
  switch_listItem_value_executionTypeCloud: 'Nube',
  switch_listItem_value_executionTypeLocale: 'Local',
  switch_dia_msg_deleteScene: '¿Eliminar esta automatización?',
  switch_scene_name_toggleSwitchDevice: '${} | Un toque | ${} | Activado/Desactivado | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Un toque | ${} | Activado/Desactivado | ${}',
  switch_scene_name_executeManualScene: '${} | Un toque | ${} | Ejecutándose | ${}-${}',
  switch_list_device_unavailable: 'Los dispositivos que no se muestran no admiten esta función',
  switch_button_subtitle_notCurrentHome: 'No se encuentra en el hogar actual',
  common_list_empty: 'No hay nada todavía',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Modo de emparejamiento',
  switch_title_buttonControlDevice_oneGang: 'Controles del dispositivo'
};