export default {
  setting: 'Setelan',
  featureSetting: 'Pengaturan perangkat',
  commonSetting: '<PERSON>elan umum',
  name: '<PERSON><PERSON> perangkat',
  deviceService: 'Layanan perangkat',
  location: 'Kelola lokasi',
  memberSet: 'Tombol',
  share: 'Bagikan perangkat',
  btGateway: 'Gerbang BLE',
  voiceAuth: 'Otor<PERSON>si suara',
  ifttt: 'Otomati<PERSON><PERSON>',
  productBaike: 'Info produk',
  firmwareUpgrade: 'Pembaruan firmware',
  firmwareUpdate: 'Pembaruan firmware',
  more: 'Setelan tambahan',
  help: 'Bantuan',
  legalInfo: 'Informasi hukum',
  deleteDevice: 'Hapus perangkat',
  autoUpgrade: 'Otomatis perbarui firmware',
  checkUpgrade: 'Periksa pembaruan firmware',
  security: 'Pengaturan keamanan',
  networkInfo: 'Info jaringan',
  feedback: 'Masukan',
  timezone: 'Zona waktu perangkat',
  addToDesktop: 'Tambahkan ke layar Beranda',
  open: 'Hidup',
  close: '<PERSON>i',
  other: 'Lainnya',
  multipleKeyShowOnHome: 'Jumlah tombol yang ditampilkan di layar beranda: {0}',
  // 常用设备
  favoriteDevices: 'Tampilan di halaman beranda Xiaomi Home',
  favoriteCamera: 'Ukuran kartu dasbor',
  favoriteAddDevices: 'Tambahkan ke favorit',
  // MHDatePicker
  cancel: 'Batalkan',
  ok: 'Konfirmasi',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} bulan',
    'one': '{0} bulan',
    'two': '{0} bulan',
    'few': '{0} bulan',
    'many': '{0} bulan',
    'other': '{0} bulan'
  },
  numberDay: {
    'zero': '{0} hari',
    'one': '{0} hari',
    'two': '{0} hari',
    'few': '{0} hari',
    'many': '{0} hari',
    'other': '{0} hari'
  },
  numberHour: {
    'zero': '{0} jam',
    'one': '{0} jam',
    'two': '{0} jam',
    'few': '{0} jam',
    'many': '{0} jam',
    'other': '{0} jam'
  },
  numberMinute: {
    'zero': '{0} mnt',
    'one': '{0} mnt',
    'two': '{0} mnt',
    'few': '{0} mnt',
    'many': '{0} mnt',
    'other': '{0} mnt'
  },
  numberSecond: {
    'zero': '{0} dtk',
    'one': '{0} dtk',
    'two': '{0} dtk',
    'few': '{0} dtk',
    'many': '{0} dtk',
    'other': '{0} dtk'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Keluar',
  firmwareUpgradeUpdate: 'Perbarui',
  firmwareUpgradeLook: 'Lihat',
  firmwareUpgradeForceUpdate: 'Versi firmware saat ini sudah terlalu lama. Beberapa fitur mungkin tidak berfungsi dengan benar. Perbarui ke versi terbaru untuk menikmati pengalaman yang lebih baik.',
  firmwareUpgradeForceUpdating: 'Perangkat sedang diperbarui. Coba lagi nanti.',
  firmwareUpgradeNew_pre: 'Pembaruan tersedia. ',
  firmwareUpgradeNew_sub: 'Perbarui?',
  handling: 'Tunggu sebentar…',
  error: 'Terjadi kesalahan. Coba lagi nanti.',
  createLightGroup: 'Buat grup lampu (baru)',
  manageLightGroup: 'Kelola grup lampu (baru)',
  deleteLightGroup: 'Batalkan pengelompokan lampu',
  deleteCurtainGroup: 'Bubarkan perangkat',
  linkDevice: 'Hubungkan perangkat',
  noSuppurtedLinkageDevice: 'Tidak ada perangkat tersedia',
  noSuppurtedLinkageTip: '1. Pastikan Anda telah menambahkan perangkat di aplikasi Xiaomi Home dan menetapkannya ke ruangan.\\n2. Dekatkan perangkat Bluetooth dengan perangkat ini untuk menghubungkannya.',
  supportedLinkageDevices: 'Dapat dihubungkan dengan perangkat berikut ini:',
  linkageDistanceTip: 'Pastikan perangkat berada dalam jarak dekat agar penautan berhasil.',
  linkageRemoveTip: 'Untuk mengubah perangkat Bluetooth yang terhubung, copot perangkat terlebih dahulu.',
  link: 'Tautkan',
  removeLink: 'Copot',
  linkFail: 'Tak dapat terhubung',
  removeLinkFail: 'Tak dapat dicopot',
  linkConfirm: 'Hubungkan dengan perangkat ini sekarang?',
  removeLinkConfirm: 'Copot sekarang?',
  linking: 'Menghubungkan...',
  linkDeviceBracelet: 'Hubungkan band',
  scanDeviceBracelet: 'Memindai band...',
  scanDeviceBraceletTip: 'Dekatkan Mi Band dengan perangkat ini dan pastikan Bluetooth aktif agar dapat terhubung.',
  scanDeviceBraceletEmptyTitle: 'Tak dapat menemukan Mi Band di sekitar',
  scanDeviceBraceletEmptyTip1: '1. Pastikan Bluetooth band sudah aktif.',
  scanDeviceBraceletEmptyTip2: '2. Dekatkan band dengan perangkat lain.',
  linkedDeviceBraceletHeaderTip: 'Terhubung dengan band berikut ini:',
  availableLinkDeviceBraceletHeaderTip: 'Dapat dihubungkan dengan band berikut ini:',
  linkedDeviceBraceletFooterTip: 'Untuk mengubah band yang terhubung, copot band terlebih dahulu.',
  availableLinkDeviceBraceletFooterTip: 'Pastikan Bluetooth band aktif, dan dekatkan dengan perangkat lainnya.',
  pluginVersion: 'Versi plugin',
  helpAndFeedback: 'Bantuan & Umpan Balik',
  offline: 'Offline',
  downloading: 'Mengunduh...',
  installing: 'Memasang...',
  upgradeSuccess: 'Berhasil memperbarui',
  upgradeFailed: 'Tidak dapat memperbarui. Coba lagi nanti.',
  upgradeTimeout: 'Waktu pembaruan habis',
  autoUpgradeInfo: 'Akan coba memperbarui secara otomatis antara {0}',
  today: 'Hari ini',
  tomorrow: 'Besok',
  currentIsLatestVersion: 'Versi saat ini sudah yang terbaru',
  lastestVersion: 'Versi terbaru: ',
  currentVersion: 'Versi saat ini: ',
  fetchFailed: 'Tak dapat diakses. Coba lagi.',
  releaseNote: 'Apa yang baru',
  releaseVersionHistory: 'Riwayat pembaruan firmware',
  firmwareAutoUpdate: 'Pembaruan firmware otomatis',
  autoUpdateDescriptionNote: 'Saat firmware baru dideteksi, perangkat akan mencoba memperbarui secara otomatis di antara {0}. Pembaruan akan dipasang saat perangkat tidak sedang digunakan dan takkan ada pemberitahuan suara maupun lampu selama proses pembaruan.',
  updateNow: 'Perbarui',
  requireBelMesh: 'Fitur ini membutuhkan mesh gateway Bluetooth agar dapat berfungsi selayaknya.',
  createCurtainGroup: 'Buat grup gorden',
  createCurtainGroupTip: 'Dua motor tirai dapat dikelompokkan ke dalam satu grup yang dapat dikontrol sebagai tirai bersisi ganda.',
  act: 'Pindahkan',
  create: 'Buat',
  chooseCurtainGroupTitle: 'Pilih motor tirai',
  currentDevice: 'Perangkat ini',
  curtain: 'Tirai',
  noCurtainGroupTip: 'Tak dapat dikelompokkan sekarang. Tambahkan motor tirai lain dan coba lagi.',
  switchPlugin: 'Plugin standar',
  defaultPlugin: 'Default',
  selectDefaultHP: 'Default',
  stdPluginTitle: 'Standar',
  thirdPluginTitle: 'Tradisional',
  stdPluginSubTitle: 'Anda dapat beralih ke versi layar yang lama pada fitur tambahan',
  stdGuideDialogTitle: 'Versi baru tersedia',
  stdGuideDialogSubTitle: 'Perbarui aplikasi untuk pengalaman baru dan lebih disempurnakan.',
  stdGuideDialogNote: 'Jika Anda tak dapat menemukan sebuah fitur setelah pembaruan, fitur tersebut mungkin telah dipindahkan ke "Fitur Tambahan".',
  stdGuideDialogButtonOK: 'Oke',
  // 多键开关设置
  key: 'Tombol',
  keyLeft: 'Tombol kiri',
  keyMiddle: 'Tombol tengah',
  keyRight: 'Tombol kanan',
  keyType: 'Ganti tipe',
  keyName: 'Nama',
  light: 'Lampu',
  updateIcon: 'Ubah ikon',
  done: 'Selesai',
  modifyName: 'Edit nama',
  keyUpdateIconTips: 'Saat ikon diganti ke "{0}", Anda dapat meminta Mi AI untuk mengaktifkan "{0}".',
  nameHasChars: 'Nama tidak boleh berisi karakter khusus',
  nameTooLong: 'Nama dapat memuat hingga 40 karakter',
  nameIsEmpty: 'Nama tidak boleh kosong',
  nameNotSupportEmoji: 'Nama tidak boleh menggunakan emoji',
  // 房间
  room: 'Ruangan',
  room_nameInputTips: 'Masukkan nama ruangan',
  room_nameSuggest: 'Saran nama',
  room_createNew: 'Buat ruangan baru',
  room_bedroom: 'Kamar tidur',
  room_masterBedroom: 'Kamar tidur utama',
  room_secondBedroom: 'Kamar tidur kedua',
  room_kitchen: 'Dapur',
  room_diningRoom: 'Ruang makan',
  room_washroom: 'Toilet',
  room_childrensRoom: 'Kamar anak',
  room_office: 'Ruang belajar',
  room_study: 'Perpustakaan',
  room_balcony: 'Balkon',
  room_studio: 'Ruang kerja',
  room_bathroom: 'Kamar mandi',
  room_backyard: 'Halaman belakang',
  room_unassigned: 'Belum ditetapkan',
  no_privacy_tip_content: 'Tak dapat memuat Kebijakan Privasi. Periksa pengaturan jaringan dan coba lagi, atau laporkan masalah ini melalui Umpan Balik.',
  moreDeviceInfo: 'Info perangkat lainnya',
  deviceNet: 'Jaringan perangkat',
  customizeName: 'Nama kustom',
  software: 'Perangkat lunak',
  hardware: 'Perangkat keras',
  bleMeshGateway: 'Gateway Bluetooth Mesh',
  deviceDid: 'ID Perangkat',
  deviceSN: 'Nomor Seri Perangkat',
  mcuVersion: 'Versi firmware MCU',
  sdkVersion: 'Versi firmware SDK',
  deviceModel: 'Model perangkat',
  deviceQR: 'Kode QR perangkat',
  download: 'Unduh',
  saveSuccess: 'Berhasil disimpan',
  saveFailed: 'Tidak dapat disimpan',
  clipboardy: 'Berhasil disalin',
  connected: 'Terhubung',
  notConnected: 'Tidak terhubung',
  bleConnected: 'Koneksi langsung Bluetooth',
  deviceOffline: 'Offline',
  deviceConsumables: 'Pasokan perangkat',
  consumableStateSufficient: 'Memadai',
  consumableStateInsufficient: 'Tidak memadai',
  consumableStateUnknown: 'Status tidak diketahui',
  consumableStateDepletion: 'Terpakai',
  consumableStateRemainPercent: '{0}% tersisa',
  consumableStateEstimatedHour: {
    'zero': '{0} jam tersisa',
    'one': '{0} jam tersisa',
    'two': '{0} jam tersisa',
    'few': '{0} jam tersisa',
    'many': '{0} jam tersisa',
    'other': '{0} jam tersisa'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} hari tersisa',
    'one': '{0} hari tersisa',
    'two': '{0} hari tersisa',
    'few': '{0} hari tersisa',
    'many': '{0} hari tersisa',
    'other': '{0} hari tersisa'
  },
  changeIcon: 'Ganti ikon',
  deviceCall: 'Peringatan darurat',
  cloudStorage: 'Notifikasi penyimpanan cloud',
  cloudStorageVip: 'Dapatkan notifikasi seputar status keanggotaan cloud',
  largeCardEvent: 'Tampilkan peristiwa terbaru yang direkam pada kartu',
  // 开关智能
  switch_title_controlDevice: 'Kontrol perangkat',
  switch_subtitle_controlDeviceType: 'Atur jenis perangkat untuk setiap tombol',
  common_listItem_value_unset: 'Belum diatur',
  switch_title_buttonControlDevice: 'Perangkat yang dikontrol (${})',
  switch_listItem_title_toWirelessSwitch: 'Ubah menjadi sakelar nirkabel',
  switch_listItem_subtile_wirelessSwitchSetting: 'Tombol fisik tidak akan dapat mengontrol sakelar saat fitur ini hidup. Anda masih dapat menggunakannya untuk otomatisasi.',
  switch_dia_msg_wirelessSwitchSetting: 'Tombol ini dikaitkan dengan item lain (${}). Ganti ke sakelar nirkabel untuk menggunakannya.',
  switch_listItem_title_voiceControlLoop: 'Perintah suara untuk sakelar',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Kontrol sakelar dengan Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Hidup',
  switch_listItem_value_voiceControlLoopOff: 'Mati',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Kontrol sakelar dengan perintah suara melalui Mi AI. Jika lampu cerdas terhubung ke sakelar, maka lampu tersebut bisa mati dan hubungan akan terputus.',
  switch_listItem_title_operationMode: 'Mode Pengoperasian',
  switch_listItem_title_speedMode: 'Mode Kecepatan Super',
  switch_listItem_title_standardMode: 'Mode Standar',
  switch_listItem_subtile_speedModeDescription: 'Pilih opsi ini jika otomatisasi hanya perlu diatur untuk "tekan 1x". Opsi ini akan meningkatkan waktu respons otomatisasi.',
  switch_listItem_subtile_standardModeDescription: 'Pilih opsi ini jika perangkat perlu menyetel otomatisasi "tekan 2x" atau "tekan lama"',
  switch_dia_msg_speedModeMessage: 'Otomatisasi "tekan 2x" dan "tekan lama" perangkat ini telah disetel. Jika memilih mode kecepatan Super, Anda tidak akan dapat menggunakan otomatisasi ini. Tetap lanjutkan?',
  switch_title_selectDeviceType: 'Pilih tipe perangkat',
  switch_subtitle_selectDeviceType: 'Pilih tipe perangkat yang dikontrol oleh ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Pilih tipe perangkat yang dikontrol oleh ${}. Biarkan satu tombol terhubung ke perangkat biasa untuk memastikan sakelar berfungsi normal.',
  switch_title_deviceType_normalDevice: 'Perangkat biasa (lampu tanpa fungsi cerdas)',
  switch_title_deviceType_smartLight: 'Lampu cerdas',
  switch_title_deviceType_smartSwitch: 'Sakelar cerdas lainnya',
  switch_title_deviceType_manualScene: 'Kontrol batch',
  switch_title_deviceType_otherSmartDevice: 'Perangkat cerdas lainnya',
  switch_value_deviceType_normalDevice: 'Perangkat regular',
  switch_value_deviceType_smartLight: 'Lampu cerdas',
  switch_value_deviceType_smartSwitch: 'Sakelar cerdas lainnya',
  switch_value_deviceType_manualScene: 'Kontrol batch',
  switch_value_deviceType_otherSmartDevice: 'Perangkat cerdas lainnya',
  switch_button_title_seeCreatedScene: 'Lihat otomatisasi',
  switch_button_title_linkSmartLight: 'Hubungkan lampu cerdas',
  switch_button_title_linkSmartSwitch: 'Hubungkan saklar cerdas',
  switch_button_title_linkManualScene: 'Hubungkan kontrol batch',
  switch_button_title_switchNameSetting: 'Setel nama tombol',
  switch_nav_title_buttonControlLight: 'Lampu cerdas yang dikontrol (${})',
  switch_nav_subtitle_buttonControlLight: 'Hubungkan lampu cerdas ke tombol untuk menghidupkan dan mematikannya serta menjaganya tetap online',
  switch_header_title_selectLightOrGroup: 'Pilih lampu cerdas atau grup lampu',
  switch_nav_title_buttonControlSwitch: 'Sakelar cerdas yang dikontrol (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Hubungkan sakelar untuk menambahkan antarmuka hidup/mati lainnya. Menekan tombol akan menghidupkan dan mematikan sakelar yang dipilih.',
  switch_header_title_selectSwitch: 'Pilih sakelar cerdas',
  switch_nav_title_buttonControlManualScene: 'Kontrol batch yang ditetapkan (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Tetapkan kontrol batch untuk mengoperasikannya dengan menekan tombol',
  switch_header_title_selectManualScene: 'Pilih kontrol batch',
  common_edit: 'Ubah',
  common_reselect: 'Pilih lagi',
  common_deleted: 'Terhapus',
  common_delete: 'Hapus',
  common_delete_failed: 'Tidak dapat menghapus. Periksa setelan jaringan dan coba lagi',
  common_setting_failed: 'Tidak dapat menyetel. Periksa apakah perangkat terhubung ke jaringan dan coba lagi.',
  common_saving: 'Menyimpan…',
  switch_listItem_title_executionType: 'Mode Menjalankan',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Lokal',
  switch_dia_msg_deleteScene: 'Hapus otomatisasi ini?',
  switch_scene_name_toggleSwitchDevice: '${} | Tekan 1x | ${} | Hidup/Mati | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Tekan 1x | ${} | Hidup/Mati | ${}',
  switch_scene_name_executeManualScene: '${} | Tekan 1x | ${} | Jalankan | ${}-${}',
  switch_list_device_unavailable: 'Perangkat yang tidak ditampilkan berarti tidak mendukung fitur ini',
  switch_button_subtitle_notCurrentHome: 'Tidak berada di rumah ini',
  common_list_empty: 'Kosong',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Mode pemasangan',
  switch_title_buttonControlDevice_oneGang: 'Kontrol perangkat'
};