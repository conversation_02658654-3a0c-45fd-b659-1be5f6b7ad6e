export default {
  setting: 'Us<PERSON>wi<PERSON>',
  featureSetting: 'Ustawienia urządzenia',
  commonSetting: 'Ustawi<PERSON> ogólne',
  name: '<PERSON>z<PERSON> urządzenia',
  deviceService: 'Usług<PERSON> urządzeń',
  location: 'Zarządzaj lokalizacjami',
  memberSet: 'Prz<PERSON><PERSON><PERSON>',
  share: 'Udostępnij urządzenie',
  btGateway: 'Bramka BLE',
  voiceAuth: 'Autoryzacja głosowa',
  ifttt: 'Automatyzacja',
  productBaike: 'Informacje o produkcie',
  firmwareUpgrade: 'Aktualizacja oprogramowania sprzętowego',
  firmwareUpdate: 'Aktualizacja oprogramowania sprzętowego',
  more: 'Dodatkowe ustawienia',
  help: 'Pomoc',
  legalInfo: 'Informacje prawne',
  deleteDevice: 'Usuń urządzenie',
  autoUpgrade: 'Automatyczna aktualizacja oprogramowania sprzętowego',
  checkUpgrade: 'Sprawdź aktualizacje oprogramowania sprzętowego',
  security: 'Ustawienia bezpieczeństwa',
  networkInfo: 'Informacje o sieci',
  feedback: 'Opinie',
  timezone: 'Strefa czasowa urządzenia',
  addToDesktop: 'Dodaj do Ekranu głównego',
  open: 'Wł.',
  close: 'Wył.',
  other: 'Inne',
  multipleKeyShowOnHome: 'Liczba przycisków pokazywanych na stronie głównej: {0}',
  // 常用设备
  favoriteDevices: 'Wyświetlaj na stronie głównej Xiaomi Home',
  favoriteCamera: 'Rozmiar karty na pulpicie',
  favoriteAddDevices: 'Dodaj do ulubionych',
  // MHDatePicker
  cancel: 'Anuluj',
  ok: 'Potwierdź',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} miesięcy',
    'one': '{0} miesiąc',
    'two': '{0} miesiące',
    'few': '{0} miesiące',
    'many': '{0} miesięcy',
    'other': '{0} miesiąca'
  },
  numberDay: {
    'zero': '{0} dni',
    'one': '{0} dzień',
    'two': '{0} dni',
    'few': '{0} dni',
    'many': '{0} dni',
    'other': '{0} dnia'
  },
  numberHour: {
    'zero': '{0} godz.',
    'one': '{0} godz.',
    'two': '{0} godz.',
    'few': '{0} godz.',
    'many': '{0} godz.',
    'other': '{0} godz.'
  },
  numberMinute: {
    'zero': '{0} min',
    'one': '{0} min',
    'two': '{0} min',
    'few': '{0} min',
    'many': '{0} min',
    'other': '{0} min'
  },
  numberSecond: {
    'zero': '{0} sek.',
    'one': '{0} sek.',
    'two': '{0} sek.',
    'few': '{0} sek.',
    'many': '{0} sek.',
    'other': '{0} sek.'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Wyjdź',
  firmwareUpgradeUpdate: 'Aktualizuj',
  firmwareUpgradeLook: 'Widok',
  firmwareUpgradeForceUpdate: 'Aktualna wersja oprogramowania sprzętowego jest zbyt stara, aby niektóre funkcje działały poprawnie. Zaktualizuj do najnowszej wersji, aby działo sprawniej.',
  firmwareUpgradeForceUpdating: 'Urządzenie jest aktualizowane. Spróbuj ponownie później.',
  firmwareUpgradeNew_pre: 'Dostępna jest akutalizacja. ',
  firmwareUpgradeNew_sub: 'Zaktualizować teraz?',
  handling: 'Chwileczkę…',
  error: 'Wystąpił błąd. Spróbuj ponownie później.',
  createLightGroup: 'Utwórz grupę oświetlenia (nową)',
  manageLightGroup: 'Zarządzaj grupą oświetlenia (nową)',
  deleteLightGroup: 'Usuń grupę oświetlenia',
  deleteCurtainGroup: 'Usuń grupę urządzeń',
  linkDevice: 'Połącz urządzenia',
  noSuppurtedLinkageDevice: 'Brak dostępnych urządzeń',
  noSuppurtedLinkageTip: '1. Upewnij się, że urządzenia zostały dodane w aplikacji Xiaomi Home i przypisane do pokoi.\\n2. Ustaw urządzenia Bluetooth blisko tego urządzenia, aby pomyślnie je połączyć.',
  supportedLinkageDevices: 'Można połączyć z następującymi urządzeniami:',
  linkageDistanceTip: 'Trzymaj urządzenia blisko siebie, aby mogły się ze sobą połączyć.',
  linkageRemoveTip: 'Aby zmienić połączone urządzenie Bluetooth, najpierw usuń to urządzenie.',
  link: 'Połącz',
  removeLink: 'Usuń',
  linkFail: 'Nie udało się połączyć',
  removeLinkFail: 'Nie udało się usunąć',
  linkConfirm: 'Połączyć teraz z tym urządzeniem?',
  removeLinkConfirm: 'Usunąć teraz?',
  linking: 'Łączenie…',
  linkDeviceBracelet: 'Połącz opaskę',
  scanDeviceBracelet: 'Wyszukiwanie opaski…',
  scanDeviceBraceletTip: 'Aby pomyślnie połączyć, ustaw opaskę Mi Band blisko tego urządzenia i upewnij się, że ma włączony Bluetooth.',
  scanDeviceBraceletEmptyTitle: 'Nie udało się znaleźć opaski Mi Band w pobliżu',
  scanDeviceBraceletEmptyTip1: '1. Upewnij się, że opaska ma włączony Bluetooth.',
  scanDeviceBraceletEmptyTip2: '2. Ustaw opaskę blisko drugiego urządzenia.',
  linkedDeviceBraceletHeaderTip: 'Połączono z następującymi opaskami:',
  availableLinkDeviceBraceletHeaderTip: 'Można połączyć z następującymi opaskami:',
  linkedDeviceBraceletFooterTip: 'Aby zmienić połączoną opaskę, najpierw usuń opaskę.',
  availableLinkDeviceBraceletFooterTip: 'Upewnij się, że opaska ma włączony Bluetooth i ustaw ją blisko drugiego urządzenia.',
  pluginVersion: 'Wersja wtyczki',
  helpAndFeedback: 'Pomoc i opinie',
  offline: 'Offline',
  downloading: 'Pobieranie…',
  installing: 'Instalowanie…',
  upgradeSuccess: 'Pomyślnie zaktualizowano',
  upgradeFailed: 'Nie można zaktualizować. Spróbuj ponownie później.',
  upgradeTimeout: 'Przekroczenie czasu aktualizowania',
  autoUpgradeInfo: 'Spróbuję zaktualizować automatycznie między {0}',
  today: 'Dzisiaj',
  tomorrow: 'Jutro',
  currentIsLatestVersion: 'Ta wersja jest aktualna',
  lastestVersion: 'Najnowsza wersja: ',
  currentVersion: 'Bieżąca wersja: ',
  fetchFailed: 'Nie udało się uzyskać dostępu. Spróbuj ponownie.',
  releaseNote: 'Co nowego',
  releaseVersionHistory: 'Historia aktualizacji oprogramowania układowego',
  firmwareAutoUpdate: 'Automatyczne aktualizacje oprogramowania układowego',
  autoUpdateDescriptionNote: 'Po wykryciu nowego oprogramowania sprzętowego urządzenie podejmie próbę automatycznej aktualizacji między {0}. Aktualizacja zostanie zainstalowana, gdy urządzenie nie będzie używane, a podczas procesu aktualizacji nie będą pokazywane żadne powiadomienia dźwiękowe ani świetlne.',
  updateNow: 'Aktualizacja',
  requireBelMesh: 'Do normalnego działania ta funkcja wymaga bramy Bluetooth typu mesh.',
  createCurtainGroup: 'Utwórz grupę zasłon',
  createCurtainGroupTip: 'Dwa silniki zasłon można połączyć w grupę, którą można sterować jako dwustronną zasłoną.',
  act: 'Przenieś',
  create: 'Utwórz',
  chooseCurtainGroupTitle: 'Wybierz silnik zasłony',
  currentDevice: 'To urządzenie',
  curtain: 'Zasłona',
  noCurtainGroupTip: 'Nie można teraz zgrupować. Dodaj kolejny silnik zasłony i spróbuj ponownie.',
  switchPlugin: 'Standardowa wtyczka',
  defaultPlugin: 'Domyślne',
  selectDefaultHP: 'Domyślne',
  stdPluginTitle: 'Standardowe',
  thirdPluginTitle: 'Tradycyjne',
  stdPluginSubTitle: 'Możesz przełączyć się na starą wersję strony w dodatkowych funkcjach',
  stdGuideDialogTitle: 'Dostępna nowa wersja',
  stdGuideDialogSubTitle: 'Zaktualizuj aplikację, aby korzystać z nowego, usprawnionego interfejsu.',
  stdGuideDialogNote: 'Jeśli po aktualizacji nie możesz znaleźć jakiejś funkcji, być może została ona przeniesiona do sekcji „Dodatkowe funkcje”.',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Przełącznik',
  keyLeft: 'Lewy przełącznik',
  keyMiddle: 'Środkowy przełącznik',
  keyRight: 'Prawy przełącznik',
  keyType: 'Rodzaj przełącznika',
  keyName: 'Nazwa',
  light: 'Lampa',
  updateIcon: 'Zmień ikonę',
  done: 'Koniec',
  modifyName: 'Edytuj nazwę',
  keyUpdateIconTips: 'Gdy ikona zmieni się na „{0}”, możesz poprosić Mi SI o włączenie „{0}”.',
  nameHasChars: 'Nazwa nie może zawierać znaków specjalnych',
  nameTooLong: 'Nazwa może zawierać do 40 znaków',
  nameIsEmpty: 'Nazwa nie może być pusta',
  nameNotSupportEmoji: 'Nazwy nie mogą zawierać emotikonów',
  // 房间
  room: 'Pomieszczenie',
  room_nameInputTips: 'Wprowadź nazwę pomieszczenia',
  room_nameSuggest: 'Zalecana nazwa',
  room_createNew: 'Utwórz nowe pomieszczenie',
  room_bedroom: 'Sypialnia',
  room_masterBedroom: 'Główna sypialnia',
  room_secondBedroom: 'Druga sypialnia',
  room_kitchen: 'Kuchnia',
  room_diningRoom: 'Jadalnia',
  room_washroom: 'Toaleta',
  room_childrensRoom: 'Pokój dziecięcy',
  room_office: 'Biuro',
  room_study: 'Biblioteka',
  room_balcony: 'Balkon',
  room_studio: 'Warsztat',
  room_bathroom: 'Łazienka',
  room_backyard: 'Podwórko',
  room_unassigned: 'Nieprzypisane',
  no_privacy_tip_content: 'Nie udało się załadować Polityki prywatności. Sprawdź ustawienia sieci i spróbuj ponownie lub zgłoś ten problem za pośrednictwem formularza Opinii.',
  moreDeviceInfo: 'Więcej informacji o urządzeniu',
  deviceNet: 'Sieć urządzenia',
  customizeName: 'Nazwa niestandardowa',
  software: 'Oprogramowanie',
  hardware: 'Sprzęt',
  bleMeshGateway: 'Brama Bluetooth Mesh',
  deviceDid: 'ID urządzenia',
  deviceSN: 'Nr seryjny urządzenia',
  mcuVersion: 'Wersja oprogramowania sprzętowego MCU',
  sdkVersion: 'Wersja oprogramowania sprzętowego SDK',
  deviceModel: 'Model urządzenia',
  deviceQR: 'Kod QR urządzenia',
  download: 'Pobierz',
  saveSuccess: 'Zapisano pomyślnie',
  saveFailed: 'Nie udało się zapisać',
  clipboardy: 'Skopiowano pomyślnie',
  connected: 'Połączono',
  notConnected: 'Nie połączono',
  bleConnected: 'Bezpośrednie połączenie Bluetooth',
  deviceOffline: 'Offline',
  deviceConsumables: 'Części zamienne urządzenia',
  consumableStateSufficient: 'Wystarczające',
  consumableStateInsufficient: 'Niewystarczająca ilość',
  consumableStateUnknown: 'Nieznany status',
  consumableStateDepletion: 'Wykorzystane',
  consumableStateRemainPercent: 'Pozostało {0}%',
  consumableStateEstimatedHour: {
    'zero': 'Pozostało {0} godz.',
    'one': 'Pozostała {0} godz.',
    'two': 'Pozostały {0} godz.',
    'few': 'Pozostały {0} godz.',
    'many': 'Pozostało {0} godz.',
    'other': 'Pozostało {0} godz.'
  },
  consumableStateEstimatedDay: {
    'zero': 'Pozostało {0} dni',
    'one': 'Pozostał {0} dzień',
    'two': 'Pozostały {0} dni',
    'few': 'Pozostały {0} dni',
    'many': 'Pozostało {0} dni',
    'other': 'Pozostało {0} dnia'
  },
  changeIcon: 'Zmień ikonę',
  deviceCall: 'Komunikaty alarmowe',
  cloudStorage: 'Powiadomienia dot. pamięci w chmurze',
  cloudStorageVip: 'Otrzymuj powiadomienia na temat statusu członkostwa w chmurze',
  largeCardEvent: 'Pokaż na karcie ostatnio przechwycone wydarzenia',
  // 开关智能
  switch_title_controlDevice: 'Urządzenia sterujące',
  switch_subtitle_controlDeviceType: 'Ustaw typ urządzenia dla każdego przycisku',
  common_listItem_value_unset: 'Nie ustawiono',
  switch_title_buttonControlDevice: 'Kontrolowane urządzenia (${})',
  switch_listItem_title_toWirelessSwitch: 'Zamień na przełącznik bezprzewodowy',
  switch_listItem_subtile_wirelessSwitchSetting: 'Po włączeniu tej funkcji nie będzie można już obsługiwać przełączników za pomocą fizycznych przycisków. Nadal będą jednak dostępne na potrzeby automatyzacji.',
  switch_dia_msg_wirelessSwitchSetting: 'Ten przycisk jest powiązany z innym elementem (${}). Zmień przełącznik na bezprzewodowy, aby z niego skorzystać.',
  switch_listItem_title_voiceControlLoop: 'Komendy głosowe dot. przełączników',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Obsługa przełączników przez Mi SI',
  switch_listItem_value_voiceControlLoopOn: 'Wł.',
  switch_listItem_value_voiceControlLoopOff: 'Wył.',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Obsługa przełączników za pomocą poleceń głosowych za pośrednictwem Mi SI. Jeśli inteligentne oświetlenie jest połączone z przełącznikiem, może zostać wyłączone i odłączone.',
  switch_listItem_title_operationMode: 'Tryb działania',
  switch_listItem_title_speedMode: 'Tryb superszybki',
  switch_listItem_title_standardMode: 'Tryb standardowy',
  switch_listItem_subtile_speedModeDescription: 'Wybierz tę opcję, jeśli chcesz zautomatyzować jedynie „pojedyncze naciśnięcie”. Ta opcja poprawi czas reakcji automatyzacji.',
  switch_listItem_subtile_standardModeDescription: 'Wybierz tę opcję, jeśli chcesz skonfigurować automatyzację przy „podwójnym naciśnięciu” lub „naciśnięciu i przytrzymaniu”.',
  switch_dia_msg_speedModeMessage: 'Już skonfigurowano automatyzację przy „dwukrotnym naciśnięciu” oraz „naciśnięciu i przytrzymaniu” dla tego urządzenia. Po wybraniu trybu superszybkiego nie będzie można korzystać z tego rodzaju automatyzacji. Czy mimo to kontynuować?',
  switch_title_selectDeviceType: 'Wybierz typ urządzenia',
  switch_subtitle_selectDeviceType: 'Wybierz typ urządzenia kontrolowany przez ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Wybierz typ urządzenia kontrolowany przez ${}. Pozostaw jeden przycisk podłączony do zwykłych urządzeń, aby mieć pewność, że przełącznik działa poprawnie.',
  switch_title_deviceType_normalDevice: 'Zwykłe urządzenia (światła i lampy z inteligentnymi funkcjami)',
  switch_title_deviceType_smartLight: 'Inteligentne światła',
  switch_title_deviceType_smartSwitch: 'Inne inteligentne przełączniki',
  switch_title_deviceType_manualScene: 'Kontrolki seryjne',
  switch_title_deviceType_otherSmartDevice: 'Inne inteligentne urządzenia',
  switch_value_deviceType_normalDevice: 'Zwykłe urządzenia',
  switch_value_deviceType_smartLight: 'Inteligentne światła',
  switch_value_deviceType_smartSwitch: 'Inne inteligentne przełączniki',
  switch_value_deviceType_manualScene: 'Kontrolki seryjne',
  switch_value_deviceType_otherSmartDevice: 'Inne inteligentne urządzenia',
  switch_button_title_seeCreatedScene: 'Wyświetl automatyzacje',
  switch_button_title_linkSmartLight: 'Połącz inteligentne światła',
  switch_button_title_linkSmartSwitch: 'Połącz inteligentne przełączniki',
  switch_button_title_linkManualScene: 'Połącz kontrolki seryjne',
  switch_button_title_switchNameSetting: 'Ustaw nazwę przycisku',
  switch_nav_title_buttonControlLight: 'Kontrolowane inteligentne światła (${})',
  switch_nav_subtitle_buttonControlLight: 'Połącz inteligentne światła z przyciskiem, aby móc je włączać i wyłączać oraz utrzymywać je w trybie online',
  switch_header_title_selectLightOrGroup: 'Wybierz inteligentne światła lub grupę świateł',
  switch_nav_title_buttonControlSwitch: 'Kontrolowane inteligentne przełączniki (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Połącz przełączniki, aby dodać kolejny interfejs włączania/wyłączania. Naciśnięcie przycisku będzie powodować włączenie i wyłączenie wybranych przełączników.',
  switch_header_title_selectSwitch: 'Wybierz inteligentne przełączniki',
  switch_nav_title_buttonControlManualScene: 'Przypisane kontrolki seryjne (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Przypisz kontrolki seryjne, aby je uruchomić, naciskając przycisk',
  switch_header_title_selectManualScene: 'Wybierz kontrolki seryjne',
  common_edit: 'Edytuj',
  common_reselect: 'Wybierz ponownie',
  common_deleted: 'Usunięto',
  common_delete: 'Usuń',
  common_delete_failed: 'Nie udało się usunąć. Sprawdź ustawienia sieci i spróbuj ponownie.',
  common_setting_failed: 'Nie udało się ustawić. Sprawdź, czy urządzenie jest połączone z siecią i spróbuj ponownie.',
  common_saving: 'Zapisywanie...',
  switch_listItem_title_executionType: 'Tryb uruchamiania',
  switch_listItem_value_executionTypeCloud: 'Chmura',
  switch_listItem_value_executionTypeLocale: 'Lokalne',
  switch_dia_msg_deleteScene: 'Usunąć tę automatyzację?',
  switch_scene_name_toggleSwitchDevice: '${} | Pojedyncze naciśnięcie | ${} | Wł./Wył. | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Pojedyncze naciśnięcie | ${} | Wł./Wył. | ${}',
  switch_scene_name_executeManualScene: '${} | Pojedyncze naciśnięcie | ${} | Uruchomienie | ${}-${}',
  switch_list_device_unavailable: 'Urządzenia, które się nie wyświetlają, nie obsługują tej funkcji',
  switch_button_subtitle_notCurrentHome: 'Nie w obecnym domu',
  common_list_empty: 'Pusto',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Tryb parowania',
  switch_title_buttonControlDevice_oneGang: 'Sterowanie urządzeniem'
};