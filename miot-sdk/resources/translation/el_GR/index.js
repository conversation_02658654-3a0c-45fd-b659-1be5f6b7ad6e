export default {
  setting: 'Ρυθμίσεις',
  featureSetting: 'Ρυθμίσεις συσκευής',
  commonSetting: 'Γενικές ρυθμίσεις',
  name: 'Όνομα συσκευής',
  deviceService: 'Υπηρεσίες συσκευής',
  location: 'Διαχείριση τοποθεσιών',
  memberSet: 'Κουμπιά',
  share: 'Κοινή χρήση συσκευής',
  btGateway: 'Πύλη BLE',
  voiceAuth: 'Φωνητική εξουσιοδότηση',
  ifttt: 'Αυτοματισμός',
  productBaike: 'Πληροφορίες προϊόντος',
  firmwareUpgrade: 'Ενημέρωση υλικολογισμικού',
  firmwareUpdate: 'Ενημέρωση υλικολογισμικού',
  more: 'Πρόσθετες ρυθμίσεις',
  help: 'Βοήθεια',
  legalInfo: 'Νομικές πληροφορίες',
  deleteDevice: 'Διαγραφή συσκευής',
  autoUpgrade: 'Αυτόματη ενημέρωση υλικολογισμικού',
  checkUpgrade: 'Έλεγχος για ενημερώσεις υλικολογισμικού',
  security: 'Ρυθμίσεις ασφαλείας',
  networkInfo: 'Πληροφορίες δικτύου',
  feedback: 'Ανατροφοδότηση',
  timezone: 'Ζώνη ώρας συσκευής',
  addToDesktop: 'Προσθήκη στην Αρχική οθόνη',
  open: 'Ενεργοποιημένο',
  close: 'Ανενεργό',
  other: 'Άλλα',
  multipleKeyShowOnHome: 'Ο αριθμός των κουμπιών εμφανίζεται στην Αρχική σελίδα: {0}',
  // 常用设备
  favoriteDevices: 'Εμφάνιση στην αρχική σελίδα του Xiaomi Home',
  favoriteCamera: 'Μέγεθος κάρτας ταμπλό',
  favoriteAddDevices: 'Προσθήκη στα αγαπημένα',
  // MHDatePicker
  cancel: 'Ακύρωση',
  ok: 'Επιβεβαίωση',
  am: 'ΠΜ',
  pm: 'ΜΜ',
  numberMonth: {
    'zero': '{0} μήνες',
    'one': '{0} μήνας',
    'two': '{0} μήνες',
    'few': '{0} μήνες',
    'many': '{0} μήνες',
    'other': '{0} μήνες'
  },
  numberDay: {
    'zero': '{0} ημέρες',
    'one': '{0} ημέρα',
    'two': '{0} ημέρες',
    'few': '{0} ημέρες',
    'many': '{0} ημέρες',
    'other': '{0} ημέρες'
  },
  numberHour: {
    'zero': '{0} ώ',
    'one': '{0} ώ',
    'two': '{0} ώ',
    'few': '{0} ώ',
    'many': '{0} ώ',
    'other': '{0} ώ'
  },
  numberMinute: {
    'zero': '{0} λεπ',
    'one': '{0} λεπ',
    'two': '{0} λεπ',
    'few': '{0} λεπ',
    'many': '{0} λεπ',
    'other': '{0} λεπ'
  },
  numberSecond: {
    'zero': '{0} δευ',
    'one': '{0} δευ',
    'two': '{0} δευ',
    'few': '{0} δευ',
    'many': '{0} δευ',
    'other': '{0} δευ'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Έξοδος',
  firmwareUpgradeUpdate: 'Ενημέρωση',
  firmwareUpgradeLook: 'Προβολή',
  firmwareUpgradeForceUpdate: 'Το τρέχον υλικολογισμικό μπορεί να είναι πολύ παλαιό για να εκτελεί ορισμένες λειτουργίες. Κάντε ενημέρωση στην τελευταία έκδοση για καλύτερη εμπειρία.',
  firmwareUpgradeForceUpdating: 'Η συσκευή ενημερώνεται. Προσπαθήστε ξανά αργότερα.',
  firmwareUpgradeNew_pre: 'Υπάρχει διαθέσιμη ενημέρωση. ',
  firmwareUpgradeNew_sub: 'Ενημέρωση τώρα;',
  handling: 'Μια στιγμή…',
  error: 'Παρουσιάστηκε σφάλμα. Προσπαθήστε ξανά αργότερα.',
  createLightGroup: 'Δημιουργία ομάδας φώτων (νέα)',
  manageLightGroup: 'Διαχείριση ομάδας φώτων (νέα)',
  deleteLightGroup: 'Κατάργηση ομαδοποίησης φώτων',
  deleteCurtainGroup: 'Κατάργηση ομαδοποίησης συσκευών',
  linkDevice: 'Σύνδεση συσκευών',
  noSuppurtedLinkageDevice: 'Δεν υπάρχουν διαθέσιμες συσκευές',
  noSuppurtedLinkageTip: '1. Βεβαιωθείτε ότι έχετε προσθέσει τις συσκευές στο Xiaomi Home και τις έχετε αναθέσει σε δωμάτια.\\n2. Διατηρήστε τις συσκευές Bluetooth κοντά σε αυτήν τη συσκευή για να τις συνδέσετε με επιτυχία.',
  supportedLinkageDevices: 'Μπορεί να συνδεθεί με τις ακόλουθες συσκευές:',
  linkageDistanceTip: 'Κρατήστε τις συσκευές κοντά για να είστε σίγουροι ότι μπορούν να συνδεθούν.',
  linkageRemoveTip: 'Για να αλλάξετε τη συνδεδεμένη συσκευή Bluetooth, πρώτα αφαιρέστε τη συσκευή.',
  link: 'Σύνδεση',
  removeLink: 'Αφαίρεση',
  linkFail: 'Αδυναμία σύνδεσης',
  removeLinkFail: 'Αδυναμία αφαίρεσης',
  linkConfirm: 'Σύνδεση με αυτή τη συσκευή τώρα;',
  removeLinkConfirm: 'Κατάργηση τώρα;',
  linking: 'Γίνεται σύνδεση...',
  linkDeviceBracelet: 'Σύνδεση του band',
  scanDeviceBracelet: 'Γίνεται σάρωση για band...',
  scanDeviceBraceletTip: 'Κρατήστε το Mi Band κοντά σε αυτή τη συσκευή και βεβαιωθείτε ότι η λειτουργία Bluetooth είναι ενεργή για το συνδέσετε με επιτυχία.',
  scanDeviceBraceletEmptyTitle: 'Αδυναμία εύρεσης κοντινών Mi Bands',
  scanDeviceBraceletEmptyTip1: '1. Βεβαιωθείτε ότι η λειτουργία Bluetooth του band είναι ενεργή.',
  scanDeviceBraceletEmptyTip2: '2. Κρατήστε το band κοντά στην άλλη συσκευή.',
  linkedDeviceBraceletHeaderTip: 'Συνδέθηκε με τα ακόλουθα bands:',
  availableLinkDeviceBraceletHeaderTip: 'Μπορεί να συνδεθεί με τα ακόλουθα bands:',
  linkedDeviceBraceletFooterTip: 'Για να αλλάξετε το συνδεδεμένο band, πρώτα αφαιρέστε το band.',
  availableLinkDeviceBraceletFooterTip: 'Βεβαιωθείτε ότι η λειτουργία Bluetooth του band είναι ενεργή και κρατήστε το κοντά στην άλλη συσκευή.',
  pluginVersion: 'Έκδοση προσθήκης',
  helpAndFeedback: 'Βοήθεια & Ανατροφοδότηση',
  offline: 'Εκτός σύνδεσης',
  downloading: 'Γίνεται λήψη...',
  installing: 'Γίνεται εγκατάσταση…',
  upgradeSuccess: 'Επιτυχής ενημέρωση',
  upgradeFailed: 'Αδυναμία ενημέρωσης. Προσπαθήστε ξανά αργότερα.',
  upgradeTimeout: 'Το χρονικό όριο ενημέρωσης έληξε',
  autoUpgradeInfo: 'Θα προσπαθήσει να κάνει ενημέρωση αυτόματα μεταξύ {0}',
  today: 'Σήμερα',
  tomorrow: 'Αύριο',
  currentIsLatestVersion: 'Η τρέχουσα έκδοση είναι ενημερωμένη',
  lastestVersion: 'Τελευταία έκδοση: ',
  currentVersion: 'Τρέχουσα έκδοση: ',
  fetchFailed: 'Αδυναμία πρόσβασης. Προσπαθήστε ξανά.',
  releaseNote: 'Τι νέο υπάρχει',
  releaseVersionHistory: 'Ιστορικό ενημέρωσης υλικολογισμικού',
  firmwareAutoUpdate: 'Αυτόματες ενημερώσεις υλικολογισμικού',
  autoUpdateDescriptionNote: 'Όταν εντοπιστεί νέο υλικολογισμικό, η συσκευή θα προσπαθήσει να ενημερωθεί αυτόματα μεταξύ {0}. Η ενημέρωση θα εγκατασταθεί όταν δεν χρησιμοποιείτε τη συσκευή και δεν θα υπάρχουν ηχητικές ή φωτεινές ειδοποιήσεις κατά τη διάρκεια της διαδικασίας.',
  updateNow: 'Ενημέρωση',
  requireBelMesh: 'Αυτή η λειτουργία χρειάζεται πύλη πλέγματος Bluetooth για να λειτουργεί κανονικά.',
  createCurtainGroup: 'Δημιουργία ομάδας κουρτινών',
  createCurtainGroupTip: 'Σε μία ομάδα μπορούν να συνδυαστούν δύο μηχανές κουρτινών σε μια ομάδα, η οποία μπορεί να ελέγχεται ως διπλή κουρτίνα.',
  act: 'Μετακίνηση',
  create: 'Δημιουργία',
  chooseCurtainGroupTitle: 'Επιλογή μηχανής κουρτίνας',
  currentDevice: 'Αυτή η συσκευή',
  curtain: 'Κουρτίνα',
  noCurtainGroupTip: 'Αδυναμία ομαδοποίησης τώρα, Προσθέστε μια άλλη μηχανή κουρτίνας και προσπαθήστε ξανά.',
  switchPlugin: 'Κανονική προσθήκη',
  defaultPlugin: 'Προεπιλεγμένη',
  selectDefaultHP: 'Προεπιλεγμένη',
  stdPluginTitle: 'Κανονική',
  thirdPluginTitle: 'Παραδοσιακή',
  stdPluginSubTitle: 'Μπορείτε να κάνετε εναλλαγή στην παλαιά έκδοση της σελίδας στις πρόσθετες λειτουργίες',
  stdGuideDialogTitle: 'Διατίθεται νέα έκδοση',
  stdGuideDialogSubTitle: 'Ενημερώστε την εφαρμογή για μια νέα, βελτιστοποιημένη εμπειρία.',
  stdGuideDialogNote: 'Εάν δεν μπορείτε να βρείτε κάποια λειτουργία μετά από ενημέρωση, ενδέχεται να έχει μετακινηθεί στις "Πρόσθετες λειτουργίες".',
  stdGuideDialogButtonOK: 'ΟΚ',
  // 多键开关设置
  key: 'Διακόπτης',
  keyLeft: 'Αριστερός διακόπτης',
  keyMiddle: 'Μεσαίος διακόπτης',
  keyRight: 'Δεξιός διακόπτης',
  keyType: 'Τύπος διακόπτη',
  keyName: 'Όνομα',
  light: 'Φωτιστικό',
  updateIcon: 'Αλλαγή εικονιδίου',
  done: 'Ολοκλήρωση',
  modifyName: 'Επεξεργασία ονόματος',
  keyUpdateIconTips: 'Όταν το εικονίδιο είναι γυρισμένο στο "{0}", μπορείτε να ζητήσετε από την Mi AI να το γυρίσει στο "{0}".',
  nameHasChars: 'Το όνομα δεν μπορεί να περιέχει ειδικούς χαρακτήρες',
  nameTooLong: 'Το όνομα μπορεί να περιέχει έως 40 χαρακτήρες',
  nameIsEmpty: 'Το όνομα δεν πρέπει να είναι κενό',
  nameNotSupportEmoji: 'Τα ονόματα δεν μπορεί να περιέχουν emoji',
  // 房间
  room: 'Δωμάτιο',
  room_nameInputTips: 'Εισαγωγή ονόματος δωματίου',
  room_nameSuggest: 'Προτεινόμενο όνομα',
  room_createNew: 'Δημιουργία νέου δωματίου',
  room_bedroom: 'Κρεβατοκάμαρα',
  room_masterBedroom: 'Κύρια κρεβατοκάμαρα',
  room_secondBedroom: 'Δεύτερη κρεβατοκάμαρα',
  room_kitchen: 'Κουζίνα',
  room_diningRoom: 'Τραπεζαρία',
  room_washroom: 'Τουαλέτα',
  room_childrensRoom: 'Παιδικό δωμάτιο',
  room_office: 'Γραφείο',
  room_study: 'Βιβλιοθήκη',
  room_balcony: 'Μπαλκόνι',
  room_studio: 'Εργαστήριο',
  room_bathroom: 'Μπάνιο',
  room_backyard: 'Πίσω αυλή',
  room_unassigned: 'Μη εκχωρημένο',
  no_privacy_tip_content: 'Αδυναμία φόρτωσης της Πολιτικής Απορρήτου. Ελέγξτε τις ρυθμίσεις του δικτύου σας και προσπαθήστε ξανά ή κάντε αναφορά του προβλήματος μέσω της Ανατροφοδότησης.',
  moreDeviceInfo: 'Περισσότερες πληροφορίες συσκευής',
  deviceNet: 'Δίκτυο συσκευής',
  customizeName: 'Προσαρμοσμένο όνομα',
  software: 'Λογισμικό',
  hardware: 'Υλικό',
  bleMeshGateway: 'Πύλη πλέγματος Bluetooth',
  deviceDid: 'Αναγνωριστικό συσκευής',
  deviceSN: 'Σειριακός αριθμός συσκευής',
  mcuVersion: 'Έκδοση του MCU υλικολογισμικού',
  sdkVersion: 'Έκδοση του SDK υλικολογισμικού',
  deviceModel: 'Μοντέλο συσκευής',
  deviceQR: 'Κωδικός QR της συσκευής',
  download: 'Λήψη',
  saveSuccess: 'Αποθηκεύτηκε με επιτυχία',
  saveFailed: 'Δεν ήταν δυνατή η αποθήκευση',
  clipboardy: 'Αντιγράφηκε με επιτυχία',
  connected: 'Σε σύνδεση',
  notConnected: 'Δεν συνδέθηκε',
  bleConnected: 'Άμεση σύνδεση Bluetooth',
  deviceOffline: 'Εκτός σύνδεσης',
  deviceConsumables: 'Αναλώσιμα συσκευής',
  consumableStateSufficient: 'Επαρκεί',
  consumableStateInsufficient: 'Δεν επαρκεί',
  consumableStateUnknown: 'Άγνωστη κατάσταση',
  consumableStateDepletion: 'Εξαντλημένα',
  consumableStateRemainPercent: '{0}% απομένουν',
  consumableStateEstimatedHour: {
    'zero': 'Απομένουν {0} ώ',
    'one': 'Απομένει {0} ώ',
    'two': 'Απομένουν {0} ώ',
    'few': 'Απομένουν {0} ώ',
    'many': 'Απομένουν {0} ώ',
    'other': 'Απομένουν {0} ώ'
  },
  consumableStateEstimatedDay: {
    'zero': 'Απομένουν {0} ημέρες',
    'one': 'Απομένει {0} ημέρα',
    'two': 'Απομένουν {0} ημέρες',
    'few': 'Απομένουν {0} ημέρες',
    'many': 'Απομένουν {0} ημέρες',
    'other': 'Απομένουν {0} ημέρες'
  },
  changeIcon: 'Αλλαγή εικονιδίου',
  deviceCall: 'Ειδοποιήσεις έκτακτης ανάγκης',
  cloudStorage: 'Ειδοποιήσεις χώρου αποθήκευσης στο σύννεφο',
  cloudStorageVip: 'Λαμβάνετε ειδοποιήσεις σχετικά με την κατάσταση συνδρομής στο σύννεφο',
  largeCardEvent: 'Εμφάνιση των τελευταίων γεγονότων που καταγράφηκαν στην κάρτα',
  // 开关智能
  switch_title_controlDevice: 'Έλεγχος συσκευών',
  switch_subtitle_controlDeviceType: 'Ορίστε τύπους συσκευών για κάθε κουμπί',
  common_listItem_value_unset: 'Δεν έχει ρυθμιστεί',
  switch_title_buttonControlDevice: 'Ελεγχόμενες συσκευές (${})',
  switch_listItem_title_toWirelessSwitch: 'Αλλαγή σε ασύρματο διακόπτη',
  switch_listItem_subtile_wirelessSwitchSetting: 'Τα φυσικά κουμπιά δεν θα μπορούν να ελέγχουν τους διακόπτες όταν είναι ενεργοποιημένη αυτή η λειτουργία. Θα εξακολουθείτε να μπορείτε να τα χρησιμοποιείτε για αυτοματισμούς.',
  switch_dia_msg_wirelessSwitchSetting: 'Αυτό το κουμπί σχετίζεται με ένα άλλο στοιχείο (${}). Αλλάξτε τον ασύρματο διακόπτη για να το χρησιμοποιήσετε.',
  switch_listItem_title_voiceControlLoop: 'Φωνητικές εντολές για διακόπτες',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Ελέγξτε τους διακόπτες με Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Ενεργοποιημένος',
  switch_listItem_value_voiceControlLoopOff: 'Απενεργοποιημένος',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Ελέγξτε τους διακόπτες με φωνητικές εντολές μέσω Mi AI. Aν τα έξυπνα φώτα είναι συνδεδεμένα στον διακόπτη, ενδέχεται να απενεργοποιηθούν και να αποσυνδεθούν.',
  switch_listItem_title_operationMode: 'Κατάσταση λειτουργίας',
  switch_listItem_title_speedMode: 'Κατάσταση μέγιστης ταχύτητας',
  switch_listItem_title_standardMode: 'Τυπική κατάσταση λειτουργίας',
  switch_listItem_subtile_speedModeDescription: 'Διαλέξτε αυτήν την επιλογή αν οι αυτοματισμοί πρέπει να ρυθμιστούν μόνο για "ένα πάτημα". Αυτή η επιλογή θα βελτιώσει τον χρόνο απόκρισης αυτοματισμού.',
  switch_listItem_subtile_standardModeDescription: 'Διαλέξτε αυτήν την επιλογή αν η συσκευή χρειάζεται να ρυθμίσει αυτοματισμούς όπως "διπλό πάτημα" ή "πατήστε παρατεταμένα".',
  switch_dia_msg_speedModeMessage: 'Αυτή η συσκευή έχει ήδη ρυθμισμένους τους αυτοματισμούς "διπλό πάτημα" και "πατήστε παρατεταμένα". Αν επιλέξετε την Κατάσταση μέγιστης ταχύτητας, δεν θα μπορείτε πλέον να χρησιμοποιείτε αυτούς τους αυτοματισμούς. Συνέχεια ούτως ή άλλως;',
  switch_title_selectDeviceType: 'Επιλέξτε τύπο συσκευής',
  switch_subtitle_selectDeviceType: 'Επιλέξτε τύπο συσκευής που ελέγχεται από ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Επιλέξτε τύπο συσκευής που ελέγχεται από ${}. Αφήστε ένα κουμπί συνδεδεμένο σε κανονικές συσκευές για να βεβαιωθείτε ότι ο διακόπτης λειτουργεί κανονικά.',
  switch_title_deviceType_normalDevice: 'Κανονικές συσκευές (φώτα και λάμπες χωρίς έξυπνη λειτουργικότητα)',
  switch_title_deviceType_smartLight: 'Έξυπνα φώτα',
  switch_title_deviceType_smartSwitch: 'Άλλοι έξυπνοι διακόπτες',
  switch_title_deviceType_manualScene: 'Έλεγχοι δέσμης',
  switch_title_deviceType_otherSmartDevice: 'Άλλες έξυπνες συσκευές',
  switch_value_deviceType_normalDevice: 'Κανονικές συσκευές',
  switch_value_deviceType_smartLight: 'Έξυπνα φώτα',
  switch_value_deviceType_smartSwitch: 'Άλλοι έξυπνοι διακόπτες',
  switch_value_deviceType_manualScene: 'Έλεγχοι δέσμης',
  switch_value_deviceType_otherSmartDevice: 'Άλλες έξυπνες συσκευές',
  switch_button_title_seeCreatedScene: 'Προβολή αυτοματισμών',
  switch_button_title_linkSmartLight: 'Συνδέστε το έξυπνο φως',
  switch_button_title_linkSmartSwitch: 'Συνδέστε τον έξυπνο διακόπτη',
  switch_button_title_linkManualScene: 'Συνδέστε τον έλεγχο δέσμης',
  switch_button_title_switchNameSetting: 'Ρυθμίστε το όνομα κουμπιού',
  switch_nav_title_buttonControlLight: 'Ελεγχόμενα έξυπνα φώτα (${})',
  switch_nav_subtitle_buttonControlLight: 'Συνδέστε τα έξυπνα φώτα σε ένα κουμπί για να ενεργοποιείτε και να τα απενεργοποιείτε και να τα διατηρείτε σε σύνδεση',
  switch_header_title_selectLightOrGroup: 'Επιλέξτε έξυπνο φως ή ομάδα φωτός',
  switch_nav_title_buttonControlSwitch: 'Ελεγχόμενοι έξυπνοι διακόπτες (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Συνδέστε διακόπτες για να προσθέσετε μια άλλη διεπαφή ενεργοποίησης/απενεργοποίησης. Πατώντας το κουμπί θα ενεργοποιηθούν και θα απενεργοποιηθούν οι επιλεγμένοι διακόπτες.',
  switch_header_title_selectSwitch: 'Επιλογή έξυπνων διακοπτών',
  switch_nav_title_buttonControlManualScene: 'Ανατεθειμένοι έλεγχοι δέσμης (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Αναθέστε έλεγχους δέσμης για να τους εκτελέσετε πατώντας το κουμπί',
  switch_header_title_selectManualScene: 'Επιλέξτε ελέγχους δέσμης',
  common_edit: 'Επεξεργασία',
  common_reselect: 'Επιλογή ξανά',
  common_deleted: 'Έγινε διαγραφή',
  common_delete: 'Διαγραφή',
  common_delete_failed: 'Αδυναμία διαγραφής. Ελέγξτε τις ρυθμίσεις του δικτύου σας και προσπαθήστε ξανά.',
  common_setting_failed: 'Δεν ήταν δυνατή η ρύθμιση. Ελέγξτε εάν η συσκευή είναι συνδεδεμένη στο δίκτυο και δοκιμάστε ξανά.',
  common_saving: 'Γίνεται αποθήκευση…',
  switch_listItem_title_executionType: 'Λειτουργία εκτέλεσης',
  switch_listItem_value_executionTypeCloud: 'Σύννεφο',
  switch_listItem_value_executionTypeLocale: 'Τοπική',
  switch_dia_msg_deleteScene: 'Διαγραφή αυτού του αυτοματισμού;',
  switch_scene_name_toggleSwitchDevice: '${} | Ένα πάτημα | ${} | Ενεργ./Απενεργ. | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Ένα πάτημα | ${} | Ενεργ./Απενεργ. | ${}',
  switch_scene_name_executeManualScene: '${} | Ένα πάτημα | ${} | Εκτέλεση | ${}-${}',
  switch_list_device_unavailable: 'Οι συσκευές που δεν εμφανίζονται δεν υποστηρίζουν αυτήν τη λειτουργία',
  switch_button_subtitle_notCurrentHome: 'Δεν βρίσκεται στο τρέχον σπίτι',
  common_list_empty: 'Δεν υπάρχει τίποτα εδώ ακόμα',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Λειτουργία ζεύξης',
  switch_title_buttonControlDevice_oneGang: 'Έλεγχος Συσκευής'
};