export default {
  setting: '<PERSON><PERSON><PERSON>',
  featureSetting: '<PERSON><PERSON><PERSON> ayarları',
  commonSetting: '<PERSON><PERSON> ayar<PERSON>',
  name: '<PERSON><PERSON><PERSON> adı',
  deviceService: 'Cihaz hizmetleri',
  location: 'Konum yönetimi',
  memberSet: '<PERSON><PERSON><PERSON>mel<PERSON>',
  share: 'Cihazı paylaş',
  btGateway: 'BLE ağ geçidi',
  voiceAuth: 'Ses yetkilendirme',
  ifttt: 'Otomasyon',
  productBaike: 'Ürün bilgileri',
  firmwareUpgrade: 'Üretici yazılımı güncelleştirmesi',
  firmwareUpdate: '<PERSON>retici yazılımı güncelleştirmesi',
  more: 'Ek ayarlar',
  help: 'Yardım',
  legalInfo: 'Yasal bilgiler',
  deleteDevice: 'Cihazı sil',
  autoUpgrade: 'Üretici yazılımını otomatik güncelle',
  checkUpgrade: '<PERSON>ret<PERSON> yazılımı güncelleştirmelerini denetle',
  security: '<PERSON><PERSON><PERSON><PERSON> ayarları',
  networkInfo: 'Ağ bilgileri',
  feedback: 'G<PERSON> bildirim',
  timezone: '<PERSON>ihazın saat dilimi',
  addToDesktop: 'Ana ekrana ekle',
  open: 'Açık',
  close: 'Kapalı',
  other: 'Diğer',
  multipleKeyShowOnHome: 'Ana sayfada gösterilen düğme sayısı: {0}',
  // 常用设备
  favoriteDevices: 'Xiaomi Home ana sayfasında göster',
  favoriteCamera: 'Gösterge paneli kart boyutu',
  favoriteAddDevices: 'Favorilere ekle',
  // MHDatePicker
  cancel: 'İptal et',
  ok: 'Onayla',
  am: 'ÖÖ',
  pm: 'ÖS',
  numberMonth: {
    'zero': '{0} ay',
    'one': '{0} ay',
    'two': '{0} ay',
    'few': '{0} ay',
    'many': '{0} ay',
    'other': '{0} ay'
  },
  numberDay: {
    'zero': '{0} gün',
    'one': '{0} gün',
    'two': '{0} gün',
    'few': '{0} gün',
    'many': '{0} gün',
    'other': '{0} gün'
  },
  numberHour: {
    'zero': '{0} sa.',
    'one': '{0} sa.',
    'two': '{0} sa.',
    'few': '{0} sa.',
    'many': '{0} sa.',
    'other': '{0} sa.'
  },
  numberMinute: {
    'zero': '{0} dk.',
    'one': '{0} dk.',
    'two': '{0} dk.',
    'few': '{0} dk.',
    'many': '{0} dk.',
    'other': '{0} dk.'
  },
  numberSecond: {
    'zero': '{0} sn.',
    'one': '{0} sn.',
    'two': '{0} sn',
    'few': '{0} sn.',
    'many': '{0} sn.',
    'other': '{0} sn.'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Çıkış',
  firmwareUpgradeUpdate: 'Şimdi güncelleştir',
  firmwareUpgradeLook: 'Görüntüle',
  firmwareUpgradeForceUpdate: 'Mevcut ürün yazılımı bazı özellikleri çalıştırmak için çok eski olabilir. Daha iyi bir deneyim için en son sürüme güncelleyin.',
  firmwareUpgradeForceUpdating: 'Cihaz güncelleniyor. Daha sonra tekrar deneyin.',
  firmwareUpgradeNew_pre: 'Bir güncelleme mevcut. ',
  firmwareUpgradeNew_sub: 'Şimdi güncellensin mi?',
  handling: 'Yalnızca bir saniye…',
  error: 'Bir hata oluştu. Daha sonra tekrar deneyin.',
  createLightGroup: 'Işık grubu oluştur (yeni)',
  manageLightGroup: 'Işık grubunu yönet (yeni)',
  deleteLightGroup: 'Işık grubunu dağıt',
  deleteCurtainGroup: 'Cihaz grubunu dağıt',
  linkDevice: 'Cihazları bağla',
  noSuppurtedLinkageDevice: 'Kullanılabilir cihaz yok',
  noSuppurtedLinkageTip: '1. Cihazları Xiaomi Home uygulamasına eklediğinizden ve onları odalara atadığınızdan emin olun.\\n2. Bluetooth cihazlarını başarılı bir şekilde bağlamak için bu cihaza yakın tutun.',
  supportedLinkageDevices: 'Şu cihazlara bağlanabilir:',
  linkageDistanceTip: 'Cihazların bağlanabilmesini sağlamak için çok yakın tutun.',
  linkageRemoveTip: 'Bağlı Bluetooth cihazını değiştirmek için önce cihazı kaldırın.',
  link: 'Bağlantı',
  removeLink: 'Kaldır',
  linkFail: 'Bağlanamadı',
  removeLinkFail: 'Kaldırılamadı',
  linkConfirm: 'Bu cihaza şimdi bağlanılsın mı?',
  removeLinkConfirm: 'Şimdi kaldırılsın mı?',
  linking: 'Bağlanıyor…',
  linkDeviceBracelet: 'Bilekliği bağla',
  scanDeviceBracelet: 'Bileklik taranıyor…',
  scanDeviceBraceletTip: 'Mi Bilekliğin başarılı bir şekilde bağlanması için bu cihaza yakın tutun ve Bluetooth\'unun açık olduğundan emin olun.',
  scanDeviceBraceletEmptyTitle: 'Yakınlarda Mi Bileklik bulunamadı',
  scanDeviceBraceletEmptyTip1: '1. Bilekliğin Bluetooth özelliğinin açık olduğundan emin olun.',
  scanDeviceBraceletEmptyTip2: '2. Bilekliği diğer cihaza yakın tutun.',
  linkedDeviceBraceletHeaderTip: 'Şu bilekliklere bağlı:',
  availableLinkDeviceBraceletHeaderTip: 'Şu bilekliklere bağlanabilir:',
  linkedDeviceBraceletFooterTip: 'Bağlı bilekliği değiştirmek için önce bilekliği kaldırın.',
  availableLinkDeviceBraceletFooterTip: 'Bilekliği Bluetooth özelliğinin açık olduğundan emin olun ve diğer cihaza yakın tutun.',
  pluginVersion: 'Eklenti sürümü',
  helpAndFeedback: 'Yardım ve Geri Bildirim',
  offline: 'Çevrimdışı',
  downloading: 'İndiriliyor…',
  installing: 'Yükleniyor…',
  upgradeSuccess: 'Başarıyla güncellendi',
  upgradeFailed: 'Güncellenemedi. Daha sonra tekrar deneyin.',
  upgradeTimeout: 'Güncelleştirme zaman aşımına uğradı',
  autoUpgradeInfo: '{0} arasında otomatik güncellemeye çalışacağız',
  today: 'Bugün',
  tomorrow: 'Yarın',
  currentIsLatestVersion: 'Mevcut sürüm güncel',
  lastestVersion: 'En son sürüm: ',
  currentVersion: 'Mevcut sürüm: ',
  fetchFailed: 'Erişim sağlanamadı. Tekrar deneyin.',
  releaseNote: 'Yenilikler',
  releaseVersionHistory: 'Üretici yazılımı güncelleme geçmişi',
  firmwareAutoUpdate: 'Otomatik üretici yazılımı güncelleştirmeleri',
  autoUpdateDescriptionNote: 'Yeni bir üretici yazılımı algılandığında, cihaz {0} arasında otomatik güncellemeye çalışacaktır. Güncelleme siz cihazı kullanmazken yüklenecek ve güncelleme sürecinde hiçbir sesli bildirim ya da ışık bildirimi olmayacaktır.',
  updateNow: 'Güncelle',
  requireBelMesh: 'Bu özelliğin normal bir şekilde çalışabilmesi için Bluetooth mesh ağ geçidi gerekli.',
  createCurtainGroup: 'Perde grubu oluştur',
  createCurtainGroupTip: 'İki perde motoru iki yönlü bir perde olarak kontrol edilebilen bir grupta birleştirilebilir.',
  act: 'Taşı',
  create: 'Oluştur',
  chooseCurtainGroupTitle: 'Perde motoru seçin',
  currentDevice: 'Bu cihaz',
  curtain: 'Perde',
  noCurtainGroupTip: 'Şu anda gruplandırılamıyor. Başka bir perde motoru ekleyip tekrar deneyin.',
  switchPlugin: 'Standart eklenti',
  defaultPlugin: 'Varsayılan',
  selectDefaultHP: 'Varsayılan',
  stdPluginTitle: 'Standart',
  thirdPluginTitle: 'Geleneksel',
  stdPluginSubTitle: 'Ek özelliklerde sayfanın eski sürümüne geçebilirsiniz',
  stdGuideDialogTitle: 'Yeni sürüm mevcut',
  stdGuideDialogSubTitle: 'Yeni, daha gelişmiş bir deneyim için uygulamayı güncelleyin.',
  stdGuideDialogNote: 'Bir güncellemenin ardından bir özelliği bulamıyorsanız "Ek özellikler" kısmına taşınmış olabilir.',
  stdGuideDialogButtonOK: 'Tamam',
  // 多键开关设置
  key: 'Değiştir',
  keyLeft: 'Sol anahtar',
  keyMiddle: 'Orta anahtar',
  keyRight: 'Sağ anahtar',
  keyType: 'Anahtar türü',
  keyName: 'Adı',
  light: 'Lamba',
  updateIcon: 'Simgeyi değiştir',
  done: 'Bitti',
  modifyName: 'Adı düzenle',
  keyUpdateIconTips: 'Simge "{0}" konumuna getirildiğinde Mi AI\'nin "{0}" ögesini açmasını isteyebilirsiniz.',
  nameHasChars: 'Adı özel karakter içeremez',
  nameTooLong: 'Adı en fazla 40 karakter içerebilir',
  nameIsEmpty: 'Adı boş olamaz',
  nameNotSupportEmoji: 'Adlar emoji içeremez',
  // 房间
  room: 'Oda',
  room_nameInputTips: 'Bir oda adı girin',
  room_nameSuggest: 'Önerilen ad',
  room_createNew: 'Yeni oda oluştur',
  room_bedroom: 'Yatak odası',
  room_masterBedroom: 'Ebeveyn yatak odası',
  room_secondBedroom: 'İkinci yatak odası',
  room_kitchen: 'Mutfak',
  room_diningRoom: 'Yemek odası',
  room_washroom: 'Tuvalet',
  room_childrensRoom: 'Çocuk odası',
  room_office: 'Çalışma odası',
  room_study: 'Çalışma odası',
  room_balcony: 'Balkon',
  room_studio: 'Stüdyo',
  room_bathroom: 'Banyo',
  room_backyard: 'Arka bahçe',
  room_unassigned: 'Atanmadı',
  no_privacy_tip_content: 'Gizlilik Politikası yüklenemedi. Ağ ayarlarınızı kontrol edip tekrar deneyin veya bu sorunu Geri Bildirim yoluyla bildirin.',
  moreDeviceInfo: 'Daha fazla cihaz bilgisi',
  deviceNet: 'Cihaz ağı',
  customizeName: 'Özel ad',
  software: 'Yazılım',
  hardware: 'Donanım',
  bleMeshGateway: 'Bluetooth mesh ağ geçidi',
  deviceDid: 'Cihaz Kimliği',
  deviceSN: 'Cihaz SN',
  mcuVersion: 'MCU üretici yazılımı sürümü',
  sdkVersion: 'SDK üretici yazılımı sürümü',
  deviceModel: 'Cihaz modeli',
  deviceQR: 'Cihaz QR kodu',
  download: 'İndir',
  saveSuccess: 'Başarıyla kaydedildi',
  saveFailed: 'Kaydedilemedi',
  clipboardy: 'Başarıyla kopyalandı',
  connected: 'Bağlı',
  notConnected: 'Bağlı değil',
  bleConnected: 'Doğrudan Bluetooth bağlantısı',
  deviceOffline: 'Çevrimdışı',
  deviceConsumables: 'Cihaz malzemeleri',
  consumableStateSufficient: 'Yeterli',
  consumableStateInsufficient: 'Yetersiz',
  consumableStateUnknown: 'Bilinmeyen durum',
  consumableStateDepletion: 'Kullanıldı',
  consumableStateRemainPercent: 'Kalan {0}%',
  consumableStateEstimatedHour: {
    'zero': '{0} sa. kaldı',
    'one': '{0} sa. kaldı',
    'two': '{0} sa. kaldı',
    'few': '{0} sa. kaldı',
    'many': '{0} sa. kaldı',
    'other': '{0} sa. kaldı'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} gün kaldı',
    'one': '{0} gün kaldı',
    'two': '{0} gün kaldı',
    'few': '{0} gün kaldı',
    'many': '{0} gün kaldı',
    'other': '{0} gün kaldı'
  },
  changeIcon: 'Simgeyi değiştir',
  deviceCall: 'Acil durum uyarıları',
  cloudStorage: 'Cloud depolama bildirimleri',
  cloudStorageVip: 'Cloud üyelik durumu hakkında bildirimler alın',
  largeCardEvent: 'Kartta en son yakalanan olayları göster',
  // 开关智能
  switch_title_controlDevice: 'Cihazları denetle',
  switch_subtitle_controlDeviceType: 'Her bir tuş için cihaz türlerini seçin',
  common_listItem_value_unset: 'Ayarlanmadı',
  switch_title_buttonControlDevice: 'Denetlenen cihazlar (${})',
  switch_listItem_title_toWirelessSwitch: 'Kablosuz anahtara geç',
  switch_listItem_subtile_wirelessSwitchSetting: 'Bu özellik açık olduğunda fiziksel tuşlar anahtarları kontrol edemez. Yine de onları otomasyonlar için kullanabileceksiniz.',
  switch_dia_msg_wirelessSwitchSetting: 'Bu tuş başka bir öge (${}) ile ilişkilidir. Kullanmak için kablosuz anahtara geçin.',
  switch_listItem_title_voiceControlLoop: 'Anahtarlar için sesli komutlar',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Anahtarları Mi AI ile kontrol et',
  switch_listItem_value_voiceControlLoopOn: 'Açık',
  switch_listItem_value_voiceControlLoopOff: 'Kapalı',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Mi AI aracılığıyla anahtarları sesli komutlar ile kontrol edin. Akıllı lambalar anahtara bağlı ise güçleri ve bağlantıları kesilebilir.',
  switch_listItem_title_operationMode: 'Çalıştırma modu',
  switch_listItem_title_speedMode: 'Süper hız modu',
  switch_listItem_title_standardMode: 'Standart mod',
  switch_listItem_subtile_speedModeDescription: 'Otomasyonların sadece "tek bas" için ayarlanması gerekiyorsa bu seçeneği seçin. Bu seçenek otomasyon yanıt süresini iyileştirecektir.',
  switch_listItem_subtile_standardModeDescription: 'Cihazın "çift bas" veya "basılı tut" otomasyonlarına ayarlanması gerekiyorsa bu seçeneği seçin',
  switch_dia_msg_speedModeMessage: 'Bu cihazda zaten "çift bas" ve "basılı tut" otomasyonları ayarlanmış. Süper hız modunu seçerseniz artık bu otomasyonları kullanamayacaksınız. Yine de devam edilsin mi?',
  switch_title_selectDeviceType: 'Cihaz türünü seçin',
  switch_subtitle_selectDeviceType: '${} tarafından denetlenecek cihaz türünü seçin',
  switch_subtitle_liveWire_selectDeviceType: '${} tarafından denetlenen cihaz türünü seçin. Anahtarın normal çalıştığından emin olmak için br tuşu normal cihazlara bağlı bırakın.',
  switch_title_deviceType_normalDevice: 'Normal cihazlar (akıllı işlevselliğin olmadığı ışıklar ve lambalar)',
  switch_title_deviceType_smartLight: 'Akıllı ışıklar',
  switch_title_deviceType_smartSwitch: 'Diğer akıllı anahtarlar',
  switch_title_deviceType_manualScene: 'Toplu kontroller',
  switch_title_deviceType_otherSmartDevice: 'Diğer akıllı cihazlar',
  switch_value_deviceType_normalDevice: 'Normal cihazlar',
  switch_value_deviceType_smartLight: 'Akıllı ışıklar',
  switch_value_deviceType_smartSwitch: 'Diğer akıllı anahtarlar',
  switch_value_deviceType_manualScene: 'Toplu kontroller',
  switch_value_deviceType_otherSmartDevice: 'Diğer akıllı cihazlar',
  switch_button_title_seeCreatedScene: 'Otomasyonları görüntüle',
  switch_button_title_linkSmartLight: 'Akıllı ışığı bağlayın',
  switch_button_title_linkSmartSwitch: 'Akıllı anahtarı bağlayın',
  switch_button_title_linkManualScene: 'Toplu kontrolü bağlayın',
  switch_button_title_switchNameSetting: 'Tuş adını ayarlayın',
  switch_nav_title_buttonControlLight: 'Denetlenen akıllı ışıklar (${})',
  switch_nav_subtitle_buttonControlLight: 'Işıkları açmak ve kapatmak ve çevrim içi tutmak için akıllı ışıkları bir tuşa bağlayın',
  switch_header_title_selectLightOrGroup: 'Akıllı ışık veya ışık grubu seçin',
  switch_nav_title_buttonControlSwitch: 'Denetlenen akıllı anahtarlar (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Başka bir açma/kapama arayüzü eklemek için anahtarları bağlayın. Tuşa basmak seçili anahtarları açacak ve kapatacaktır.',
  switch_header_title_selectSwitch: 'Akıllı anahtarları seçin',
  switch_nav_title_buttonControlManualScene: 'Atanan toplu kontroller (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Tuşa basarak çalıştırmak için toplu kontroller atayın',
  switch_header_title_selectManualScene: 'Toplu kontrolleri seçin',
  common_edit: 'Düzenle',
  common_reselect: 'Tekrar seçin',
  common_deleted: 'Silindi',
  common_delete: 'Sil',
  common_delete_failed: 'Silinemedi. Ağ ayarlarınızı kontrol edin ve tekrar deneyin',
  common_setting_failed: 'Ayarlanamadı. Cihazın ağa bağlı olup olmadığını kontrol edin ve tekrar deneyin.',
  common_saving: 'Kaydediliyor…',
  switch_listItem_title_executionType: 'Çalıştırma modu',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Yerel',
  switch_dia_msg_deleteScene: 'Bu otomasyon silinsin mi?',
  switch_scene_name_toggleSwitchDevice: '${} | Tek bas | ${} | Açık/Kapalı | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Tek bas | ${} | Açık/Kapalı | ${}',
  switch_scene_name_executeManualScene: '${} | Tek bas | ${} | Çalıştır | ${}-${}',
  switch_list_device_unavailable: 'Gösterilmeyen cihazlar bu özelliği desteklemiyor',
  switch_button_subtitle_notCurrentHome: 'Geçerli evde değil',
  common_list_empty: 'Burada henüz hiçbir şey yok',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Eşleştirme modu',
  switch_title_buttonControlDevice_oneGang: 'Cihaz kontrolleri'
};