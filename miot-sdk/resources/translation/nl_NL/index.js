export default {
  setting: 'Instellingen',
  featureSetting: 'Apparaatinstellingen',
  commonSetting: 'Algemene instellingen',
  name: 'Apparaatnaam',
  deviceService: 'Apparaatservices',
  location: 'Locaties beheren',
  memberSet: 'Knoppen',
  share: 'Apparaat delen',
  btGateway: 'BLE-gateway',
  voiceAuth: 'Spraakautorisatie',
  ifttt: 'Automatisering',
  productBaike: 'Productgegevens',
  firmwareUpgrade: 'Firmware-update',
  firmwareUpdate: 'Firmware-update',
  more: 'Aanvullende instellingen',
  help: 'Help',
  legalInfo: 'Legale informatie',
  deleteDevice: 'Apparaat verwijderen',
  autoUpgrade: 'Firmware automatisch bijwerken',
  checkUpgrade: 'Controleren op firmware-updates',
  security: 'Beveiligingsinstellingen',
  networkInfo: 'Netwerkgegevens',
  feedback: 'Feedback',
  timezone: 'Tijdzone apparaat',
  addToDesktop: 'Toevoegen aan startscherm',
  open: 'Aan',
  close: 'Uit',
  other: 'Andere',
  multipleKeyShowOnHome: 'Het aantal knoppen weergegeven op de startpagina: {0}',
  // 常用设备
  favoriteDevices: 'Weergeven op Xiaomi Home-startpagina',
  favoriteCamera: 'Dashboardkaartformaat',
  favoriteAddDevices: 'Toevoegen aan favorieten',
  // MHDatePicker
  cancel: 'Annuleren',
  ok: 'Bevestigen',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} maanden',
    'one': '{0} maand',
    'two': '{0} maanden',
    'few': '{0} maanden',
    'many': '{0} maanden',
    'other': '{0} maanden'
  },
  numberDay: {
    'zero': '{0} dagen',
    'one': '{0} dag',
    'two': '{0} dagen',
    'few': '{0} dagen',
    'many': '{0} dagen',
    'other': '{0} dagen'
  },
  numberHour: {
    'zero': '{0} uur',
    'one': '{0} uur',
    'two': '{0} uur',
    'few': '{0} uur',
    'many': '{0} uur',
    'other': '{0} uur'
  },
  numberMinute: {
    'zero': '{0} min.',
    'one': '{0} min.',
    'two': '{0} min.',
    'few': '{0} min.',
    'many': '{0} min.',
    'other': '{0} min.'
  },
  numberSecond: {
    'zero': '{0} sec',
    'one': '{0} sec',
    'two': '{0} sec',
    'few': '{0} sec',
    'many': '{0} sec',
    'other': '{0} sec'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}-{1}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Afsluiten',
  firmwareUpgradeUpdate: 'Nu updaten',
  firmwareUpgradeLook: 'Weergeven',
  firmwareUpgradeForceUpdate: 'De huidige firmware is mogelijk te oud om bepaalde functies uit te voeren. Update naar de nieuwste versie voor een betere ervaring.',
  firmwareUpgradeForceUpdating: 'Uw apparaat wordt bijgewerkt, probeer het later nog eens.',
  firmwareUpgradeNew_pre: 'Er is een update beschikbaar. ',
  firmwareUpgradeNew_sub: 'Nu updaten?',
  handling: 'Een ogenblik geduld…',
  error: 'Er is een fout opgetreden, probeer het later opnieuw.',
  createLightGroup: 'Lichtgroep aanmaken (nieuw)',
  manageLightGroup: 'Lichtgroep beheren (nieuw)',
  deleteLightGroup: 'Lichten groeperen',
  deleteCurtainGroup: 'Groeperen apparaten ongedaan maken',
  linkDevice: 'Apparaten koppelen',
  noSuppurtedLinkageDevice: 'Geen apparaten beschikbaar',
  noSuppurtedLinkageTip: '1. Zorg ervoor dat u de apparaten hebt toegevoegd in de Xiaomi Home-app en ze hebt toegewezen aan kamers. \\n2. Om ze te verbinden moet u de Bluetooth-apparaten in de buurt van dit apparaat houden.',
  supportedLinkageDevices: 'Kan worden gekoppeld aan de volgende apparaten:',
  linkageDistanceTip: 'Houd de apparaten vlakbij om ervoor te zorgen dat ze kunnen koppelen.',
  linkageRemoveTip: 'Om het gekoppelde Bluetooth-apparaat te wijzigen, dient u het apparaat eerst te verwijderen.',
  link: 'Koppelen',
  removeLink: 'Verwijderen',
  linkFail: 'Koppelen mislukt',
  removeLinkFail: 'Verwijderen mislukt',
  linkConfirm: 'Nu met dit apparaat koppelen?',
  removeLinkConfirm: 'Nu verwijderen?',
  linking: 'Koppelen...',
  linkDeviceBracelet: 'Band koppelen',
  scanDeviceBracelet: 'Scannen op band…',
  scanDeviceBraceletTip: 'Om verbinding te kunnen maken houdt u de Mi Band in de buurt van dit apparaat en zorgt u ervoor Bluetooth is ingeschakeld.',
  scanDeviceBraceletEmptyTitle: 'Kon geen MI Bands in de buurt vinden.',
  scanDeviceBraceletEmptyTip1: '1. Zorg ervoor dat de Bluetooth van de band ingeschakeld is.',
  scanDeviceBraceletEmptyTip2: '2. Houd de band in de buurt van het andere apparaat.',
  linkedDeviceBraceletHeaderTip: 'Gekoppeld met de volgende banden:',
  availableLinkDeviceBraceletHeaderTip: 'Kan worden gekoppeld met de volgende bandjes:',
  linkedDeviceBraceletFooterTip: 'Om de gekoppelde band te wijzigen, dient u eerste de band te verwijderen.',
  availableLinkDeviceBraceletFooterTip: 'Zorg ervoor dat de Bluetooth van de band ingeschakeld is, en houd de band in de buurt van het andere apparaat.',
  pluginVersion: 'Versie invoegtoepassing',
  helpAndFeedback: 'Hulp en feedback',
  offline: 'Offline',
  downloading: 'Downloaden…',
  installing: 'Installeren…',
  upgradeSuccess: 'Succesvol bijgewerkt',
  upgradeFailed: 'Bijwerken mislukt, probeer het later opnieuw.',
  upgradeTimeout: 'Update mislukt door time-out',
  autoUpgradeInfo: 'Zal proberen automatisch bij te werken tussen {0}',
  today: 'Vandaag',
  tomorrow: 'Morgen',
  currentIsLatestVersion: 'De huidige versie is up-to-date',
  lastestVersion: 'Recentste versie: ',
  currentVersion: 'Huidige versie: ',
  fetchFailed: 'Geen toegang. Probeer opnieuw.',
  releaseNote: 'Wat is er nieuw',
  releaseVersionHistory: 'Updategeschiedenis firmware',
  firmwareAutoUpdate: 'Automatische firmware-updates',
  autoUpdateDescriptionNote: 'Zodra nieuwe firmware is gedetecteerd, zal het apparaat proberen automatisch een update uit te voeren tussen {0}. De update wordt geïnstalleerd wanneer u het apparaat niet gebruikt en u krijgt geen audio- of lichtmeldingen tijdens het updateproces.',
  updateNow: 'Bijwerken',
  requireBelMesh: 'Om deze functie normaal te laten functioneren is een Bluetooth mesh-gateway nodig.',
  createCurtainGroup: 'Een gordijnengroep maken',
  createCurtainGroupTip: 'Twee gordijnmotoren kunnen worden gecombineerd tot een groep die kan worden bediend als een dubbelzijdig gordijn.',
  act: 'Verplaatsen',
  create: 'Aanmaken',
  chooseCurtainGroupTitle: 'Selecteer een gordijnmotor',
  currentDevice: 'Dit apparaat',
  curtain: 'Gordijn',
  noCurtainGroupTip: 'Kan nu geen groep maken. Voeg een andere gordijnmotor toe en probeer het opnieuw.',
  switchPlugin: 'Standaard invoegtoepassing',
  defaultPlugin: 'Standaard',
  selectDefaultHP: 'Standaard',
  stdPluginTitle: 'Standaard',
  thirdPluginTitle: 'Traditioneel',
  stdPluginSubTitle: 'Bij de aanvullende functies kunt u terugschakelen naar de oude versie van de pagina',
  stdGuideDialogTitle: 'Nieuwe versie beschikbaar',
  stdGuideDialogSubTitle: 'Werk de app bij voor een nieuwe, meer gestroomlijnde ervaring.',
  stdGuideDialogNote: 'Als u de functie na de update niet meer kunt vinden, kan het zijn dat deze is verplaatst naar "Aanvullende functies".',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Schakelen',
  keyLeft: 'Linkerschakelaar',
  keyMiddle: 'Middelste schakelaar',
  keyRight: 'Rechterschakelaar',
  keyType: 'Type schakelaar',
  keyName: 'Naam',
  light: 'Lamp',
  updateIcon: 'Pictogram wijzigen',
  done: 'Klaar',
  modifyName: 'Naam bewerken',
  keyUpdateIconTips: 'Wanneer het pictogram gewijzigd is in "{0}", kunt u Mi AI vragen "{0}" in te schakelen.',
  nameHasChars: 'Naam mag geen speciale tekens bevatten',
  nameTooLong: 'Naam mag maximaal 40 tekens omvatten',
  nameIsEmpty: 'Naam mag niet leeg zijn',
  nameNotSupportEmoji: 'Naams mogen geen emoji omvatten',
  // 房间
  room: 'Ruimte',
  room_nameInputTips: 'Voer een naam voor de ruimte in',
  room_nameSuggest: 'Aanbevolen naam',
  room_createNew: 'Maak een nieuwe ruimte aan',
  room_bedroom: 'Slaapkamer',
  room_masterBedroom: 'Ouderslaapkamer',
  room_secondBedroom: 'Tweede slaapkamer',
  room_kitchen: 'Keuken',
  room_diningRoom: 'Eetkamer',
  room_washroom: 'Toilet',
  room_childrensRoom: 'Kinderkamer',
  room_office: 'Studeerkamer',
  room_study: 'Bibliotheek',
  room_balcony: 'Balkon',
  room_studio: 'Werkplaats',
  room_bathroom: 'Badkamer',
  room_backyard: 'Achtertuin',
  room_unassigned: 'Niet toegewezen',
  no_privacy_tip_content: 'Laden Privacybeleid mislukt. Controleer uw netwerkinstellingen en probeer het opnieuw, of rapporteer het probleem via Feedback.',
  moreDeviceInfo: 'Meer apparaatinformatie',
  deviceNet: 'Netwerk apparaat',
  customizeName: 'Aangepaste naam',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Bluetooth mesh-gateway',
  deviceDid: 'Apparaat-ID',
  deviceSN: 'Apparaat-SN',
  mcuVersion: 'Firmwareversie MCU',
  sdkVersion: 'Firmwareversie SDK',
  deviceModel: 'Apparaatmodel',
  deviceQR: 'QR-code van apparaat',
  download: 'Downloaden',
  saveSuccess: 'Succesvol opgeslagen',
  saveFailed: 'Opslaan niet gelukt',
  clipboardy: 'Succesvol gekopieerd',
  connected: 'Verbonden',
  notConnected: 'Niet verbonden',
  bleConnected: 'Directe Bluetooth-verbinding',
  deviceOffline: 'Offline',
  deviceConsumables: 'Apparaatbenodigdheden',
  consumableStateSufficient: 'Voldoende',
  consumableStateInsufficient: 'Onvoldoende',
  consumableStateUnknown: 'Status onbekend',
  consumableStateDepletion: 'Verbruikt',
  consumableStateRemainPercent: '{0}% resterend',
  consumableStateEstimatedHour: {
    'zero': '{0} uur resterend',
    'one': '{0} uur resterend',
    'two': '{0} uur resterend',
    'few': '{0} uur resterend',
    'many': '{0} uur resterend',
    'other': '{0} uur resterend'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} dagen resterend',
    'one': '{0} dag resterend',
    'two': '{0} dagen resterend',
    'few': '{0} dagen resterend',
    'many': '{0} dagen resterend',
    'other': '{0} dagen resterend'
  },
  changeIcon: 'Icoon wijzigen',
  deviceCall: 'Noodwaarschuwingen',
  cloudStorage: 'Cloud-opslag meldingen',
  cloudStorageVip: 'Ontvang meldingen over de status van het cloud-lidmaatschap',
  largeCardEvent: 'Laat de laatst vastgelegde gebeurtenissen op de kaart zien',
  // 开关智能
  switch_title_controlDevice: 'Apparaten bedienen',
  switch_subtitle_controlDeviceType: 'Stel apparaattypen in voor elke knop',
  common_listItem_value_unset: 'Niet ingesteld',
  switch_title_buttonControlDevice: 'Bestuurde apparaten (${})',
  switch_listItem_title_toWirelessSwitch: 'Veranderen in draadloze schakelaar',
  switch_listItem_subtile_wirelessSwitchSetting: 'Fysieke knoppen kunnen geen schakelaars bedienen als deze functie is ingeschakeld. U kunt ze nog steeds gebruiken voor automatiseringen.',
  switch_dia_msg_wirelessSwitchSetting: 'Deze knop is gekoppeld aan een ander item (${}). Verander in draadloze schakelaar om deze te gebruiken.',
  switch_listItem_title_voiceControlLoop: 'Spraakopdrachten voor schakelaars',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Schakelaars bedienen met Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Aan',
  switch_listItem_value_voiceControlLoopOff: 'Uit',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Bedien schakelaars met spraakopdrachten via Mi AI. Als er slimme verlichting op de schakelaar is aangesloten, kan deze worden uitgeschakeld en losgekoppeld.',
  switch_listItem_title_operationMode: 'Bedieningsmodus',
  switch_listItem_title_speedMode: 'Supersnelle modus',
  switch_listItem_title_standardMode: 'Standaardmodus',
  switch_listItem_subtile_speedModeDescription: 'Selecteer deze optie als automatiseringen alleen hoeven te worden ingesteld voor "druk één keer". Deze optie verbetert de reactietijd van de automatisering.',
  switch_listItem_subtile_standardModeDescription: 'Selecteer deze optie als het apparaat automatiseringen voor "twee keer drukken" of "houd ingedrukt" moet instellen',
  switch_dia_msg_speedModeMessage: 'Voor dit apparaat zijn al "twee keer drukken" en "houd ingedrukt" automatiseringen ingesteld. Als u de Supersnelle modus selecteert, kunt u deze automatiseringen niet meer gebruiken. Toch doorgaan?',
  switch_title_selectDeviceType: 'Selecteer apparaattype',
  switch_subtitle_selectDeviceType: 'Selecteer apparaattype bediend door ${}',
  switch_subtitle_liveWire_selectDeviceType: 'Selecteer apparaattype bediend door ${}. Laat één knop aangesloten op reguliere apparaten om ervoor te zorgen dat de schakelaar normaal werkt.',
  switch_title_deviceType_normalDevice: 'Reguliere apparaten (lichten en lampen zonder slimme functionaliteit)',
  switch_title_deviceType_smartLight: 'Slimme lichten',
  switch_title_deviceType_smartSwitch: 'Andere slimme schakelaars',
  switch_title_deviceType_manualScene: 'Batchbeheer',
  switch_title_deviceType_otherSmartDevice: 'Andere slimme apparaten',
  switch_value_deviceType_normalDevice: 'Reguliere apparaten',
  switch_value_deviceType_smartLight: 'Slimme lichten',
  switch_value_deviceType_smartSwitch: 'Andere slimme schakelaars',
  switch_value_deviceType_manualScene: 'Batchbeheer',
  switch_value_deviceType_otherSmartDevice: 'Andere slimme apparaten',
  switch_button_title_seeCreatedScene: 'Automatiseringen bekijken',
  switch_button_title_linkSmartLight: 'Verbinding maken met slimme lamp',
  switch_button_title_linkSmartSwitch: 'Verbinding maken met slimme schakelaar',
  switch_button_title_linkManualScene: 'Verbinding maken met batchbeheer',
  switch_button_title_switchNameSetting: 'Knopnaam instellen',
  switch_nav_title_buttonControlLight: 'Bestuurde slimme lampen (${})',
  switch_nav_subtitle_buttonControlLight: 'Verbind slimme lichten met een knop om ze aan en uit te zetten en online te houden',
  switch_header_title_selectLightOrGroup: 'Selecteer slim licht of lichtgroep',
  switch_nav_title_buttonControlSwitch: 'Bestuurde slimme schakelaars (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Verbind schakelaars om nog een aan/uit-interface toe te voegen. Als u op de knop drukt, worden de geselecteerde schakelaars in- en uitgeschakeld.',
  switch_header_title_selectSwitch: 'Selecteer slimme schakelaars',
  switch_nav_title_buttonControlManualScene: 'Toegewezen batchbeheer (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Wijs batchbeheer toe om ze uit te voeren door op de knop te drukken',
  switch_header_title_selectManualScene: 'Batchbeheer selecteren',
  common_edit: 'Bewerken',
  common_reselect: 'Selecteer opnieuw',
  common_deleted: 'Verwijderd',
  common_delete: 'Verwijderen',
  common_delete_failed: 'Kon niet verwijderen. Controleer uw netwerkinstellingen en probeer het opnieuw.',
  common_setting_failed: 'Kon niet instellen. Controleer of het apparaat met het netwerk is verbonden en probeer het opnieuw.',
  common_saving: 'Opslaan...',
  switch_listItem_title_executionType: 'Uitvoeringsmodus',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Lokaal',
  switch_dia_msg_deleteScene: 'Deze automatisering verwijderen?',
  switch_scene_name_toggleSwitchDevice: '${} | Druk één keer | ${} | Aan/Uit | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Druk één keer | ${} | Aan/Uit | ${}',
  switch_scene_name_executeManualScene: '${} | Druk één keer | ${} | Uitvoeren | ${}-${}',
  switch_list_device_unavailable: 'Apparaten die niet worden weergegeven, ondersteunen deze functie niet',
  switch_button_subtitle_notCurrentHome: 'Geen dergelijk apparaat in het huidige huis',
  common_list_empty: 'Nog niets om weer te geven',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Koppelingsmodus',
  switch_title_buttonControlDevice_oneGang: 'Apparaatbediening'
};