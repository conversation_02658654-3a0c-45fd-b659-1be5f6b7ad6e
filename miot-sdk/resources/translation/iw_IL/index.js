export default {
  setting: 'הגדרות',
  featureSetting: 'הגדרו<PERSON> המכשיר',
  commonSetting: 'הגדרות כלליות',
  name: 'שם המכשיר',
  deviceService: 'שירותי המכשיר',
  location: 'ניהול מיקומים',
  memberSet: 'מקשים',
  share: 'שיתוף מכשיר',
  btGateway: 'gateway (שער) BLE',
  voiceAuth: 'הרשאה קולית',
  ifttt: 'אוטומציה',
  productBaike: 'נתוני המוצר',
  firmwareUpgrade: 'עדכון קושחה',
  firmwareUpdate: 'עדכון קושחה',
  more: 'הגדרות נוספות',
  help: 'עזרה',
  legalInfo: 'מידע משפטי',
  deleteDevice: 'מחיקת מכשיר',
  autoUpgrade: 'עדכ<PERSON> קושחה אוטומטי',
  checkUpgrade: 'בדי<PERSON><PERSON> עדכוני קושחה',
  security: 'הגדרו<PERSON> אבטחה',
  networkInfo: 'מידע על הרשת',
  feedback: 'משוב',
  timezone: 'אזור הזמן של המכשיר',
  addToDesktop: 'הוספה למסך הבית',
  open: 'פועל',
  close: 'כיבוי',
  other: 'אחר',
  multipleKeyShowOnHome: 'מספר המקשים המוצגים במסך הבית: {0}',
  // 常用设备
  favoriteDevices: 'להציג בדף הבית Xiaomi Home',
  favoriteCamera: 'הגודל של כרטיס לוח המחוונים',
  favoriteAddDevices: 'להוסיף למועדפים',
  // MHDatePicker
  cancel: 'ביטול',
  ok: 'אישור',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} חודשים',
    'one': 'חודש אחד ({0})',
    'two': '{0} חודשים',
    'few': '{0} חודשים',
    'many': '{0} חודשים',
    'other': '{0} חודשים'
  },
  numberDay: {
    'zero': '{0} ימים',
    'one': 'יום אחד ({0})',
    'two': '{0} ימים',
    'few': '{0} ימים',
    'many': '{0} ימים',
    'other': '{0} ימים'
  },
  numberHour: {
    'zero': '{0} שעות',
    'one': 'שעה אחת ({0})',
    'two': '{0} שעות',
    'few': '{0} שעות',
    'many': '{0} שעות',
    'other': '{0} שעות'
  },
  numberMinute: {
    'zero': '{0} דקות',
    'one': 'דקה אחת ({0})',
    'two': '{0} דקות',
    'few': '{0} דקות',
    'many': '{0} דקות',
    'other': '{0} דקות'
  },
  numberSecond: {
    'zero': '{0} שניות',
    'one': 'שנייה אחת ({0})',
    'two': '{0} שניות',
    'few': '{0} שניות',
    'many': '{0} שניות',
    'other': '{0} שניות'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{0}-{1}-{2}',
  // 2019年06月03日
  time24SubTitle: '{1}:{0}',
  // 11:43
  time12SubTitle: '{2}:{1} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'יציאה',
  firmwareUpgradeUpdate: 'עדכון',
  firmwareUpgradeLook: 'צפייה',
  firmwareUpgradeForceUpdate: 'גרסת הקושחה הנוכחית עשויה להיות ישנה מדי להפעלת תכונות מסוימות. עדכן לגרסה העדכנית ביותר לחוויה טובה יותר.',
  firmwareUpgradeForceUpdating: 'המכשיר מתעדכן. נא לנסות שוב מאוחר יותר.',
  firmwareUpgradeNew_pre: 'קיים עדכון זמין. ',
  firmwareUpgradeNew_sub: 'שדרג עכשיו?',
  handling: 'רק רגע…',
  error: 'אירעה שגיאה. נא לנסות שוב מאוחר יותר.',
  createLightGroup: 'צור קבוצת נורות (חדשה)',
  manageLightGroup: 'נהל קבוצת נורות (חדשה)',
  deleteLightGroup: 'בטל קיבוץ נורות',
  deleteCurtainGroup: 'ביטול של קיבוץ מכשירים',
  linkDevice: 'קישור מכשירים',
  noSuppurtedLinkageDevice: 'אין מכשירים זמינים',
  noSuppurtedLinkageTip: '1. יש לוודא שהוספתם את המכשירים ביישום Xiaomi Home והקציתם אותם לחדרים.\\n2. יש להחזיק את מכשירי ה-Bluetooth קרוב למכשיר זה כדי שהמכשירים יתחברו בהצלחה.',
  supportedLinkageDevices: 'ניתן לקשר למכשירים הבאים:',
  linkageDistanceTip: 'יש להחזיק את המכשירים בסביבה הקרובה כדי לוודא שניתן יהיה לקשר אותם.',
  linkageRemoveTip: 'לשינוי מכשיר ה-Bluetooth המקושר צריך קודם להסיר את המכשיר הקיים.',
  link: 'קישור',
  removeLink: 'להסיר',
  linkFail: 'לא ניתן לקשר',
  removeLinkFail: 'לא ניתן להסיר',
  linkConfirm: 'לקשר למכשיר זה עכשיו?',
  removeLinkConfirm: 'להסיר עכשיו?',
  linking: 'מקשר…',
  linkDeviceBracelet: 'קישור צמיד',
  scanDeviceBracelet: 'מחפש צמיד…',
  scanDeviceBraceletTip: 'יש להחזיק את צמיד Mi קרוב למכשיר זה ולוודא כי Bluetooth מופעל כדי לקשר אותו בהצלחה.',
  scanDeviceBraceletEmptyTitle: 'לא נמצאו צמידי Mi בסביבה הקרובה',
  scanDeviceBraceletEmptyTip1: '1. יש לוודא שה-Bluetooth בצמיד מופעל.',
  scanDeviceBraceletEmptyTip2: '2. יש להחזיק את הצמיד בקרבת המכשיר האחר.',
  linkedDeviceBraceletHeaderTip: 'מקושר לצמידים הבאים:',
  availableLinkDeviceBraceletHeaderTip: 'ניתן לקשר לצמידים הבאים:',
  linkedDeviceBraceletFooterTip: 'לשינוי הצמיד המקושר צריך קודם להסיר את הצמיד הקיים.',
  availableLinkDeviceBraceletFooterTip: 'יש לוודא שה-Bluetooth בצמיד מופעל ולהחזיק את הצמיד קרוב למכשיר השני.',
  pluginVersion: 'גרסת התוסף',
  helpAndFeedback: 'עזרה ומשוב',
  offline: 'לא מקוון',
  downloading: 'מוריד...',
  installing: 'מתקין…',
  upgradeSuccess: 'עודכן בהצלחה',
  upgradeFailed: 'לא ניתן היה לעדכן. נא לנסות שוב מאוחר יותר.',
  upgradeTimeout: 'פג תוקף העדכון',
  autoUpgradeInfo: 'ינסה להתקין באופן אוטומטי בין השעות {0}',
  today: 'היום',
  tomorrow: 'מחר',
  currentIsLatestVersion: 'הגרסה הנוכחית מעודכנת',
  lastestVersion: 'הגרסה האחרונה: ',
  currentVersion: 'הגרסה הנוכחית: ',
  fetchFailed: 'לא ניתן לקבל גישה. נא לנסות שוב.',
  releaseNote: 'מה חדש',
  releaseVersionHistory: 'היסטוריית עדכוני קושחה',
  firmwareAutoUpdate: 'עדכוני קושחה אוטומטיים',
  autoUpdateDescriptionNote: 'לאחר זיהוי קושחה חדשה, המכשיר ינסה לבצע עדכון באופן אוטומטי בין השעות {0}. העדכון יותקן בזמן שלא משתמשים במכשיר, ולא יהיו התראות שמע או התראות תאורה במהלך העדכון.',
  updateNow: 'לעדכן',
  requireBelMesh: 'מאפיין זה מחייב שער (gateway) רשת Bluetooth כדי לעבוד כנדרש.',
  createCurtainGroup: 'ליצור קבוצת וילונות',
  createCurtainGroupTip: 'ניתן לשלב את המנועים של שני וילונות לקבוצה בה ניתן לשלוט בתור וילון דו-צדדי.',
  act: 'העברה',
  create: 'הגדר',
  chooseCurtainGroupTitle: 'בחירת מנוע וילון',
  currentDevice: 'מכשיר זה',
  curtain: 'וילון',
  noCurtainGroupTip: 'לא ניתן לקבץ כרגע. נא להוסיף מנוע וילון נוסף ולנסות שוב.',
  switchPlugin: 'תוסף סטנדרטי',
  defaultPlugin: 'ברירת מחדל',
  selectDefaultHP: 'ברירת מחדל',
  stdPluginTitle: 'סטנדרטי',
  thirdPluginTitle: 'מסורתי',
  stdPluginSubTitle: 'ניתן לעבור לגרסה הישנה של הדף תחת המאפיינים הנוספים',
  stdGuideDialogTitle: 'זמינה גרסה חדשה',
  stdGuideDialogSubTitle: 'מומלץ לעדכן את היישום לחוויית שימוש חדשה ויעילה יותר',
  stdGuideDialogNote: 'אם לא הצלחת למצוא מאפיין מסוים אחרי עדכון, ייתכן שהוא הועבר אל "מאפיינים נוספים".',
  stdGuideDialogButtonOK: 'אישור',
  // 多键开关设置
  key: 'מתג',
  keyLeft: 'מתג שמאלי',
  keyMiddle: 'מתג אמצעי',
  keyRight: 'מתג ימני',
  keyType: 'סוג המתג',
  keyName: 'שם',
  light: 'נורה',
  updateIcon: 'שינוי הצלמית',
  done: 'בוצע',
  modifyName: 'עריכת השם',
  keyUpdateIconTips: 'כשהצלמית במצב "{0}", ניתן לבקש מ-Mi AI להפעיל את "{0}".',
  nameHasChars: 'שם לא יכול להכיל תווים מיוחדים',
  nameTooLong: 'שם יכול להכיל עד 40 תווים',
  nameIsEmpty: 'שדה השם לא יכול להיות ריק',
  nameNotSupportEmoji: 'שם לא יכול לכלול אימוג\'י',
  // 房间
  room: 'חדר',
  room_nameInputTips: 'נא להזין שם חדר',
  room_nameSuggest: 'שם מומלץ',
  room_createNew: 'ליצור חדר חדש',
  room_bedroom: 'חדר שינה',
  room_masterBedroom: 'חדר השינה הגדול',
  room_secondBedroom: 'חדר שינה שני',
  room_kitchen: 'מטבח',
  room_diningRoom: 'חדר אוכל',
  room_washroom: 'שירותים',
  room_childrensRoom: 'חדרי ילדים',
  room_office: 'חדר עבודה',
  room_study: 'ספרייה',
  room_balcony: 'מרפסת',
  room_studio: 'בית מלאכה',
  room_bathroom: 'אמבטיה',
  room_backyard: 'חצר אחורית',
  room_unassigned: 'ללא הקצאה',
  no_privacy_tip_content: 'לא ניתן היה לטעון את מדיניות הפרטיות. נא לבדוק את הגדרות הרשת ולנסות שוב, או לדווח על הבעיה באמצעות המשוב.',
  moreDeviceInfo: 'נתוני מכשיר נוספים',
  deviceNet: 'רשת המכשיר',
  customizeName: 'שם מותאם אישית',
  software: 'תוכנה',
  hardware: 'חומרה',
  bleMeshGateway: 'שער (gateway) רשת Bluetooth',
  deviceDid: 'מזהה מכשיר',
  deviceSN: 'SN מכשיר',
  mcuVersion: 'גרסת קושחת MCU',
  sdkVersion: 'גרסת קושחת SDK',
  deviceModel: 'דגם המכשיר',
  deviceQR: 'קוד ה-QR של המכשיר',
  download: 'הורדה',
  saveSuccess: 'נשמר בהצלחה',
  saveFailed: 'לא ניתן לשמור',
  clipboardy: 'הועתק בהצלחה',
  connected: 'מחובר',
  notConnected: 'לא מחובר',
  bleConnected: 'חיבור Bluetooth ישיר',
  deviceOffline: 'לא מקוון',
  deviceConsumables: 'ציוד מתכלה למכשיר',
  consumableStateSufficient: 'מספיק',
  consumableStateInsufficient: 'לא מספיק',
  consumableStateUnknown: 'סטטוס לא ידוע',
  consumableStateDepletion: 'נגמר',
  consumableStateRemainPercent: 'נותרו {0}%',
  consumableStateEstimatedHour: {
    'zero': 'נותרו {0} שעות',
    'one': 'נותרה שעה אחת ({0})',
    'two': 'נותרו {0} שעות',
    'few': 'נותרו {0} שעות',
    'many': 'נותרו {0} שעות',
    'other': 'נותרו {0} שעות'
  },
  consumableStateEstimatedDay: {
    'zero': 'נותרו {0} ימים',
    'one': 'נותר יום אחד ({0})',
    'two': 'נותרו {0} ימים',
    'few': 'נותרו {0} ימים',
    'many': 'נותרו {0} ימים',
    'other': 'נותרו {0} ימים'
  },
  changeIcon: 'שנה סמל',
  deviceCall: 'התראות חירום',
  cloudStorage: 'התראות אחסון בענן',
  cloudStorageVip: 'קבלת התראות על מצב המינוי לשירות הענן',
  largeCardEvent: 'להציג את האירועים האחרונים שתועדו בכרטיס',
  // 开关智能
  switch_title_controlDevice: 'שליטה במכשירים',
  switch_subtitle_controlDeviceType: 'הגדר סוגי מכשירים עבור כל לחצן',
  common_listItem_value_unset: 'לא הוגדר',
  switch_title_buttonControlDevice: 'מכשירים נשלטים (${})',
  switch_listItem_title_toWirelessSwitch: 'עבור למתג אלחוטי',
  switch_listItem_subtile_wirelessSwitchSetting: 'כאשר מאפיין זה מופעל, לחצנים פיזיים לא יכולים לשלוט במתגים. עדיין ניתן להשתמש בהם עבור אוטומציות.',
  switch_dia_msg_wirelessSwitchSetting: 'לחצן זה משויך לפריט אחר (${}). שנה למתג אלחוטי כדי להשתמש בו.',
  switch_listItem_title_voiceControlLoop: 'פקודות קוליות עבור מתגים',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'שליטה במתגים באמצעות Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'מופעל',
  switch_listItem_value_voiceControlLoopOff: 'כבוי',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'ניתן לשלוט במתגים באמצעות פקודות קוליות דרך Mi AI. אם נורות חכמות מחוברות למתג, אפשר לכבות ולנתק אותן.',
  switch_listItem_title_operationMode: 'מצב תפעול',
  switch_listItem_title_speedMode: 'מצב מהירות-על',
  switch_listItem_title_standardMode: 'מצב רגיל',
  switch_listItem_subtile_speedModeDescription: 'יש לבחור באפשרות הזו אם צריך להגדיר את האוטומציות רק עבור "לחיצה אחת". האפשרות הזו תשפר את זמן התגובה של האוטומציה.',
  switch_listItem_subtile_standardModeDescription: 'יש לבחור באפשרות הזו אם צריך להגדיר אוטומציות מסוג "לחיצה כפולה" או "לחיצה ממושכת"',
  switch_dia_msg_speedModeMessage: 'כבר הוגדרו במכשיר זה אוטומציות מסוג "לחיצה כפולה" וגם "לחיצה ממושכת". אם ברצונך לבחור במצב מהירות-על, לא ניתן יהיה יותר להשתמש באוטומציות האלה. להמשיך בכל זאת?',
  switch_title_selectDeviceType: 'בחר סוג מכשיר',
  switch_subtitle_selectDeviceType: 'בחר את סוג המכשיר שנשלט על ידי ${}',
  switch_subtitle_liveWire_selectDeviceType: 'בחר את סוג המכשיר שנשלט על ידי ${}. השאר לחצן אחד מחובר למכשירים רגילים כדי לוודא שהמתג פועל כרגיל.',
  switch_title_deviceType_normalDevice: 'מכשירים רגילים (נורות ומנורות ללא פונקציונליות חכמה)',
  switch_title_deviceType_smartLight: 'נורות חכמות',
  switch_title_deviceType_smartSwitch: 'מתגים חכמים אחרים',
  switch_title_deviceType_manualScene: 'בקרות אצווה',
  switch_title_deviceType_otherSmartDevice: 'מכשירים חכמים אחרים',
  switch_value_deviceType_normalDevice: 'מכשירים רגילים',
  switch_value_deviceType_smartLight: 'נורות חכמות',
  switch_value_deviceType_smartSwitch: 'מתגים חכמים אחרים',
  switch_value_deviceType_manualScene: 'בקרות אצווה',
  switch_value_deviceType_otherSmartDevice: 'מכשירים חכמים אחרים',
  switch_button_title_seeCreatedScene: 'הצג אוטומציות',
  switch_button_title_linkSmartLight: 'חבר נורה חכמה',
  switch_button_title_linkSmartSwitch: 'חבר מתג חכם',
  switch_button_title_linkManualScene: 'חבר בקרת אצווה',
  switch_button_title_switchNameSetting: 'הגדר את שם הלחצן',
  switch_nav_title_buttonControlLight: 'נורות חכמות נשלטות (${})',
  switch_nav_subtitle_buttonControlLight: 'חבר נורות חכמות ללחצן כדי להדליק ולכבות אותן ולהשאיר אותן במצב מקוון',
  switch_header_title_selectLightOrGroup: 'בחר נורה חכמה או קבוצת נורות',
  switch_nav_title_buttonControlSwitch: 'מתגים חכמים נשלטים (${})',
  switch_nav_subtitle_buttonControlSwitch: 'חבר מתגים כדי להוסיף ממשק הפעלה/כיבוי נוסף. לחיצה על הלחצן תפעיל ותכבה את המתגים שנבחרו.',
  switch_header_title_selectSwitch: 'בחר מתגים חכמים',
  switch_nav_title_buttonControlManualScene: 'בקרות אצווה שהוקצו (${})',
  switch_nav_subtitle_buttonControlManualScene: 'הקצה בקרות אצווה שיופעלו על ידי לחיצה על הלחצן',
  switch_header_title_selectManualScene: 'בחר בקרות אצווה',
  common_edit: 'ערוך',
  common_reselect: 'בחר שוב',
  common_deleted: 'נמחק',
  common_delete: 'מחק',
  common_delete_failed: 'לא ניתן היה למחוק. בדוק את הגדרות הרשת ונסה שוב.',
  common_setting_failed: 'לא ניתן היה להגדיר. בדוק אם המכשיר מחובר לרשת ונסה שוב.',
  common_saving: 'שומר...',
  switch_listItem_title_executionType: 'מצב הפעלה',
  switch_listItem_value_executionTypeCloud: 'ענן',
  switch_listItem_value_executionTypeLocale: 'מקומי',
  switch_dia_msg_deleteScene: 'האם למחוק את האוטומציה?',
  switch_scene_name_toggleSwitchDevice: '${} | לחיצה אחת | ${} | הפעלה/כיבוי | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | לחיצה אחת | ${} | הפעלה/כיבוי | ${}',
  switch_scene_name_executeManualScene: '${} | לחיצה אחת | ${} | פועל | ${}-${}',
  switch_list_device_unavailable: 'המכשירים שאינם מוצגים לא תומכים במאפיין זה',
  switch_button_subtitle_notCurrentHome: 'לא בבית הנוכחי',
  common_list_empty: 'עדיין אין כאן כלום',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'מצב צימוד',
  switch_title_buttonControlDevice_oneGang: 'בקרות המכשיר'
};