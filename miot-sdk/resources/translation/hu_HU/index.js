export default {
  setting: 'Be<PERSON>ll<PERSON>tások',
  featureSetting: '<PERSON><PERSON><PERSON><PERSON>lék-beállítások',
  commonSetting: '<PERSON><PERSON><PERSON><PERSON><PERSON> beállítások',
  name: 'K<PERSON>züléknév',
  deviceService: 'Készülék-szolgáltatások',
  location: 'Helyek kezelése',
  memberSet: 'Gombok',
  share: 'Készülék megosztása',
  btGateway: 'BLE-átjáró',
  voiceAuth: 'Hang engedélyezése',
  ifttt: 'Automatizálás',
  productBaike: 'Termékinformáció',
  firmwareUpgrade: 'Firmware-frissítés',
  firmwareUpdate: 'Firmware-frissítés',
  more: 'Tov<PERSON><PERSON>i be<PERSON>ll<PERSON>t<PERSON>',
  help: 'Súgó',
  legalInfo: 'Jogi információ',
  deleteDevice: 'Készülék törlése',
  autoUpgrade: 'Firmware automatikus frissítése',
  checkUpgrade: 'Firmware-frissítések keresése',
  security: 'Biztonsági be<PERSON>llítások',
  networkInfo: 'Hálózati információ',
  feedback: 'Visszajelzés',
  timezone: 'Készülék időzónája',
  addToDesktop: 'Hozzáadás a Kezdőképernyőhöz',
  open: 'Be',
  close: 'Ki',
  other: 'Egyéb',
  multipleKeyShowOnHome: 'A kezdőoldalon megjelenített gombok száma: {0}',
  // 常用设备
  favoriteDevices: 'Megjelenítés a Xiaomi Home kezdőlapon',
  favoriteCamera: 'Műszerfali kártya mérete',
  favoriteAddDevices: 'Hozzáadás a Kedvencekhez',
  // MHDatePicker
  cancel: 'Mégse',
  ok: 'Megerősítés',
  am: 'DE',
  pm: 'DU',
  numberMonth: {
    'zero': '{0} hónap',
    'one': '{0} hónap',
    'two': '{0} hónap',
    'few': '{0} hónap',
    'many': '{0} hónap',
    'other': '{0} hónap'
  },
  numberDay: {
    'zero': '{0} nap',
    'one': '{0} nap',
    'two': '{0} nap',
    'few': '{0} nap',
    'many': '{0} nap',
    'other': '{0} nap'
  },
  numberHour: {
    'zero': '{0} óra',
    'one': '{0} óra',
    'two': '{0} óra',
    'few': '{0} óra',
    'many': '{0} óra',
    'other': '{0} óra'
  },
  numberMinute: {
    'zero': '{0} perc',
    'one': '{0} perc',
    'two': '{0} perc',
    'few': '{0} perc',
    'many': '{0} perc',
    'other': '{0} perc'
  },
  numberSecond: {
    'zero': '{0} mp',
    'one': '{0} mp',
    'two': '{0} mp',
    'few': '{0} mp',
    'many': '{0} mp',
    'other': '{0} mp'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{1}-{2}-{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Kilépés',
  firmwareUpgradeUpdate: 'Frissítés',
  firmwareUpgradeLook: 'Megtekintés',
  firmwareUpgradeForceUpdate: 'A jelenlegi firmware túl régi, ezért bizonyos funkciók nem működnek rajta. A jobb élmény érdekében frissítse a verziót.',
  firmwareUpgradeForceUpdating: 'A készülék frissít. Próbálja meg később.',
  firmwareUpgradeNew_pre: 'Egy frissítés elérhető. ',
  firmwareUpgradeNew_sub: 'Frissítés most?',
  handling: 'Egy pillanat…',
  error: 'Hiba történt. Próbálja újra később.',
  createLightGroup: 'Lámpacsoport létrehozása (új)',
  manageLightGroup: 'Lámpacsoport kezelése (új)',
  deleteLightGroup: 'Lámpacsoport bontása',
  deleteCurtainGroup: 'Készülékcsoport bontása',
  linkDevice: 'Készülékek összekapcsolása',
  noSuppurtedLinkageDevice: 'Nincs elérhető készülék',
  noSuppurtedLinkageTip: '1. Ellenőrizze, hogy hozzáadta az eszközöket a Xiaomi Home alkalmazásban, és hozzárendelte őket a helyiségekhez.\\n2. Tartsa a Bluetooth-eszközöket az eszköz közelében a sikeres csatlakozáshoz.',
  supportedLinkageDevices: 'A következő készülékekkel kapcsolható össze:',
  linkageDistanceTip: 'Ahhoz, hogy a készülékek össze tudjanak kapcsolódni, tartsa őket egymás közelében.',
  linkageRemoveTip: 'A csatlakoztatott Bluetooth-eszköz módosításához először távolítsa el az eszközt.',
  link: 'Párosítás',
  removeLink: 'Eltávolítás',
  linkFail: 'Nem sikerült párosítani',
  removeLinkFail: 'Nem sikerült eltávolítani',
  linkConfirm: 'Párosítja ezzel az eszközzel most?',
  removeLinkConfirm: 'Eltávolítja most?',
  linking: 'Párosítás...',
  linkDeviceBracelet: 'Karkötő párosítása',
  scanDeviceBracelet: 'Karkötő keresése…',
  scanDeviceBraceletTip: 'Tartsa a Mi Bandet az eszköz közelében, és a sikeres csatlakoztatás érdekében győződjön meg róla, hogy a Bluetooth be van kapcsolva.',
  scanDeviceBraceletEmptyTitle: 'A közelben nem található Mi Band',
  scanDeviceBraceletEmptyTip1: '1. Ellenőrizze, hogy a Bluetooth legyen bekapcsolva.',
  scanDeviceBraceletEmptyTip2: '2. Tartsa a karkötőt közel a másik eszközhöz.',
  linkedDeviceBraceletHeaderTip: 'A következő karkötőkkel lett párosítva:',
  availableLinkDeviceBraceletHeaderTip: 'A következő karkötőkkel lehet párosítani:',
  linkedDeviceBraceletFooterTip: 'A párosított karkötő módosításához először el kell távolítania a karkötőt.',
  availableLinkDeviceBraceletFooterTip: 'Ellenőrizze, hogy a Bluetooth be van-e kapcsolva a karkötőn, és tartsa közel a másik készülékhez.',
  pluginVersion: 'Plugin verziója',
  helpAndFeedback: 'Súgó és visszajelzés',
  offline: 'Offline',
  downloading: 'Letöltés…',
  installing: 'Telepítés…',
  upgradeSuccess: 'Sikeres frissítés',
  upgradeFailed: 'Nem lehet frissíteni. Próbálja újra később.',
  upgradeTimeout: 'A frissítés időtúllépés miatt megszakadt',
  autoUpgradeInfo: 'Ekkor fog megpróbálkozni az automatikus frissítéssel: {0}',
  today: 'Ma',
  tomorrow: 'Holnap',
  currentIsLatestVersion: 'A jelenlegi verzió naprakész',
  lastestVersion: 'Legújabb verzió: ',
  currentVersion: 'Jelenlegi verzió: ',
  fetchFailed: 'Nem sikerült hozzáférni. Próbálja újra.',
  releaseNote: 'Újdonságok',
  releaseVersionHistory: 'Firmware-frissítés előzmények',
  firmwareAutoUpdate: 'Automatikus firmware-frissítések',
  autoUpdateDescriptionNote: 'Ha új firmware-t észlel, a készülék megpróbálja automatikusan frissíteni {0} között. A frissítés akkor lesz telepítve, amikor Ön nem használja a készüléket, és a frissítési folyamat alatt nem lesznek hang- vagy fény értesítések.',
  updateNow: 'Frissítés',
  requireBelMesh: 'A funkció megfelelő működéséhez Bluetooth háló átjáróra van szükség.',
  createCurtainGroup: 'Függönycsoport létrehozása',
  createCurtainGroupTip: 'Két függönymotor egy csoportba rendezhető, és így két oldalra nyíló függöny is vezérelhető.',
  act: 'Áthelyezés',
  create: 'Létrehozás',
  chooseCurtainGroupTitle: 'Függönymotor kiválasztása',
  currentDevice: 'Ez a készülék',
  curtain: 'Függöny',
  noCurtainGroupTip: 'Most nem lehetséges a csoportosítás. Adjon hozzá egy másik függönymotort, és próbálkozzon újra.',
  switchPlugin: 'Normál plugin',
  defaultPlugin: 'Alapértelmezett',
  selectDefaultHP: 'Alapértelmezett',
  stdPluginTitle: 'Normál',
  thirdPluginTitle: 'Hagyományos',
  stdPluginSubTitle: 'Az oldal régi verziójára a további funkciók között válthat vissza',
  stdGuideDialogTitle: 'Új verzió érhető el',
  stdGuideDialogSubTitle: 'Frissítse az alkalmazást egy új, még gördülékenyebb élményre.',
  stdGuideDialogNote: 'Ha egy frissítés után nem talál meg egy funkciót, lehet, hogy az átkerült a "További funkciók" közé.',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Kapcsoló',
  keyLeft: 'Bal oldali kapcsoló',
  keyMiddle: 'Középső kapcsoló',
  keyRight: 'Jobb oldali kapcsoló',
  keyType: 'Kapcsoló típusa',
  keyName: 'Név',
  light: 'Lámpa',
  updateIcon: 'Ikon cseréje',
  done: 'Kész',
  modifyName: 'Név szerkesztése',
  keyUpdateIconTips: 'Amikor az ikon "{0}"-ra vált, megkérheti a Mi AI-t, hogy kapcsolja be a "{0}"-t.',
  nameHasChars: 'A név nem tartalmazhat speciális karaktereket',
  nameTooLong: 'A név legfeljebb 40 karaktert tartalmazhat',
  nameIsEmpty: 'A név mező nem lehet üres',
  nameNotSupportEmoji: 'A névben nem szerepelhet emoji',
  // 房间
  room: 'Szoba',
  room_nameInputTips: 'Adjon meg egy szobanevet',
  room_nameSuggest: 'Ajánlott név',
  room_createNew: 'Új szoba létrehozása',
  room_bedroom: 'Hálószoba',
  room_masterBedroom: 'Szülői hálószoba',
  room_secondBedroom: 'Második hálószoba',
  room_kitchen: 'Konyha',
  room_diningRoom: 'Étkező',
  room_washroom: 'WC',
  room_childrensRoom: 'Gyerekszoba',
  room_office: 'Dolgozó',
  room_study: 'Könyvtár',
  room_balcony: 'Erkély',
  room_studio: 'Műhely',
  room_bathroom: 'Fürdőszoba',
  room_backyard: 'Hátsó kert',
  room_unassigned: 'Nincs hozzárendelve',
  no_privacy_tip_content: 'Nem lehet betölteni az Adatvédelmi irányelveket. Ellenőrizze a hálózati beállításokat és próbálja újra, vagy jelentse a problémát a Visszajelzéseken keresztül.',
  moreDeviceInfo: 'Még több eszközinformáció',
  deviceNet: 'Készülék hálózat',
  customizeName: 'Egyedi név',
  software: 'Szoftver',
  hardware: 'Hardver',
  bleMeshGateway: 'Bluetooth Mesh átjáró',
  deviceDid: 'Készülékazonosító',
  deviceSN: 'Készülék SN-száma',
  mcuVersion: 'MCU firmware-verzió',
  sdkVersion: 'SDK firmware-verzió',
  deviceModel: 'Eszközmodell',
  deviceQR: 'Eszköz QR-kódja',
  download: 'Letöltés',
  saveSuccess: 'Sikeresen elmentve',
  saveFailed: 'Sikertelen mentés',
  clipboardy: 'Sikeresen másolva',
  connected: 'Csatlakoztatva',
  notConnected: 'Nincs csatlakoztatva',
  bleConnected: 'Közvetlen Bluetooth-kapcsolat',
  deviceOffline: 'Offline',
  deviceConsumables: 'Készülék tartozékok',
  consumableStateSufficient: 'Elegendő',
  consumableStateInsufficient: 'Nem elegendő',
  consumableStateUnknown: 'Ismeretlen státusz',
  consumableStateDepletion: 'Felhasználva',
  consumableStateRemainPercent: '{0}% maradt',
  consumableStateEstimatedHour: {
    'zero': '{0} óra van hátra',
    'one': '{0} óra van hátra',
    'two': '{0} óra van hátra',
    'few': '{0} óra van hátra',
    'many': '{0} óra van hátra',
    'other': '{0} óra van hátra'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} nap van hátra',
    'one': '{0} nap van hátra',
    'two': '{0} nap van hátra',
    'few': '{0} nap van hátra',
    'many': '{0} nap van hátra',
    'other': '{0} nap van hátra'
  },
  changeIcon: 'Ikon cseréje',
  deviceCall: 'Vészjelzések',
  cloudStorage: 'Felhőtárhely értesítések',
  cloudStorageVip: 'Értesítéseket kap a felhőtagsága állapotáról',
  largeCardEvent: 'A legfrissebb rögzített események megjelenítése a kártyán',
  // 开关智能
  switch_title_controlDevice: 'Készülékek vezérlése',
  switch_subtitle_controlDeviceType: 'Készüléktípusok beállítása az egyes gombokhoz',
  common_listItem_value_unset: 'Nincs beállítva',
  switch_title_buttonControlDevice: 'Vezérelt készülékek (${})',
  switch_listItem_title_toWirelessSwitch: 'Váltás vezeték nélküli kapcsolóra',
  switch_listItem_subtile_wirelessSwitchSetting: 'A fizikai gombok nem fogják tudni vezérelni a kapcsolókat, ha ez a funkció be van kapcsolva. Automatizáláshoz továbbra is használhatja őket.',
  switch_dia_msg_wirelessSwitchSetting: 'Ez a gomb egy másik elemhez kapcsolódik (${}). Használatához váltson a vezeték nélküli kapcsolóra.',
  switch_listItem_title_voiceControlLoop: 'Hangutasítások a kapcsolókhoz',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Kapcsolók vezérlése a Mi AI segítségével',
  switch_listItem_value_voiceControlLoopOn: 'Be',
  switch_listItem_value_voiceControlLoopOff: 'Ki',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Kapcsolók vezérlése hangutasításokkal a Mi AI segítségével. Ha a kapcsolóhoz intelligens lámpák vannak csatlakoztatva, akkor azok kikapcsolhatók és leválaszthatók.',
  switch_listItem_title_operationMode: 'Üzemmód',
  switch_listItem_title_speedMode: 'Szupersebesség mód',
  switch_listItem_title_standardMode: 'Normál mód',
  switch_listItem_subtile_speedModeDescription: 'Válassza ezt a lehetőséget, ha az automatizmusokat csak a "sima megnyomáshoz" kell beállítani. Ez az opció javítja az automatició válaszidejét.',
  switch_listItem_subtile_standardModeDescription: 'Válassza ezt a lehetőséget, ha a készüléknek "dupla megnyomás" vagy "hosszan tartó nyomás" automatizációkat kell beállítania.',
  switch_dia_msg_speedModeMessage: 'Ez a készülék már rendelkezik a "dupla megnyomás" és a "hosszan tartó nyomás" automatizálással. Ha a Szuper sebesség módot választja, akkor ezeket az automatizmusokat már nem tudja használni. Mindenképpen folytatja?',
  switch_title_selectDeviceType: 'Készüléktípus kiválasztása',
  switch_subtitle_selectDeviceType: '${} által vezérelt készüléktípus kiválasztása',
  switch_subtitle_liveWire_selectDeviceType: '${} által vezérelt készüléktípus kiválasztása. A kapcsoló normál működéséhez hagyjon egy gombot a normál készülékekhez csatlakoztatva.',
  switch_title_deviceType_normalDevice: 'Normál készülékek (világítások és lámpák intelligens funkció nélkül)',
  switch_title_deviceType_smartLight: 'Intelligens világítás',
  switch_title_deviceType_smartSwitch: 'Egyéb intelligens kapcsolók',
  switch_title_deviceType_manualScene: 'Batch vezérlés',
  switch_title_deviceType_otherSmartDevice: 'Egyéb intelligens eszközök',
  switch_value_deviceType_normalDevice: 'Normál készülékek',
  switch_value_deviceType_smartLight: 'Intelligens világítás',
  switch_value_deviceType_smartSwitch: 'Egyéb intelligens kapcsolók',
  switch_value_deviceType_manualScene: 'Batch vezérlés',
  switch_value_deviceType_otherSmartDevice: 'Egyéb intelligens eszközök',
  switch_button_title_seeCreatedScene: 'Automatizálások megtekintése',
  switch_button_title_linkSmartLight: 'Intelligens világítás csatlakoztatása',
  switch_button_title_linkSmartSwitch: 'Intelligens kapcsoló csatlakoztatása',
  switch_button_title_linkManualScene: 'Batch vezérlés csatlakoztatása',
  switch_button_title_switchNameSetting: 'Gomb nevének beállítása',
  switch_nav_title_buttonControlLight: 'Vezérelt intelligens világítások (${})',
  switch_nav_subtitle_buttonControlLight: 'Csatlakoztassa az intelligens világítást egy gombhoz a ki- és bekapcsoláshoz, valamint az online állapotban tartáshoz',
  switch_header_title_selectLightOrGroup: 'Intelligens világítás vagy világításcsoport kiválasztása',
  switch_nav_title_buttonControlSwitch: 'Vezérelt intelligens kapcsolók (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Kapcsolók csatlakoztatása egy újabb be-/kikapcsoló felület hozzáadásához. A gomb megnyomásával a kiválasztott kapcsolók be- és kikapcsolhatók.',
  switch_header_title_selectSwitch: 'Intelligens kapcsolók kiválasztása',
  switch_nav_title_buttonControlManualScene: 'Hozzárendelt batch vezérlők (${})',
  switch_nav_subtitle_buttonControlManualScene: 'A gomb megnyomásával rendelje hozzá a batch vezérlőket a futtatásukhoz',
  switch_header_title_selectManualScene: 'Batch vezérlők kiválasztása',
  common_edit: 'Szerkesztés',
  common_reselect: 'Kiválasztás újra',
  common_deleted: 'Törölve',
  common_delete: 'Törlés',
  common_delete_failed: 'Nem sikerült törölni. Ellenőrizze a hálózati beállításokat és próbálja újra.',
  common_setting_failed: 'Nem sikerült beállítani. Ellenőrizze, hogy a készülék csatlakozik-e a hálózathoz, és próbálja újra.',
  common_saving: 'Mentés…',
  switch_listItem_title_executionType: 'Futtatás mód',
  switch_listItem_value_executionTypeCloud: 'Felhő',
  switch_listItem_value_executionTypeLocale: 'Helyi',
  switch_dia_msg_deleteScene: 'Törli ezt az automatizálást?',
  switch_scene_name_toggleSwitchDevice: '${} | Egyszeri megnyomás | ${} | On/Off | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Egyszeri megnyomás | ${} | On/Off | ${}',
  switch_scene_name_executeManualScene: '${} | Egyszeri megnyomás | ${} | Futtatás | ${}-${}',
  switch_list_device_unavailable: 'A nem jelzett készülékek nem támogatják ezt a funkciót.',
  switch_button_subtitle_notCurrentHome: 'Nincs a jelenlegi otthonban',
  common_list_empty: 'Itt még nincs semmi',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Párosítási mód',
  switch_title_buttonControlDevice_oneGang: 'Eszközvezérlők'
};