export default {
  setting: 'Einstellungen',
  featureSetting: 'Geräteeinstellungen',
  commonSetting: 'Allgemeine Einstellungen',
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  deviceService: 'Gerätedienste',
  location: 'Standorte verwalten',
  memberSet: 'Tasten',
  share: 'Ger<PERSON> teilen',
  btGateway: 'BLE-Gateway',
  voiceAuth: 'Sprachautorisierung',
  ifttt: 'Automatisierung',
  productBaike: 'Produktinformation',
  firmwareUpgrade: 'Firmware-Update',
  firmwareUpdate: 'Firmware-Update',
  more: 'Weitere Einstellungen',
  help: 'Hilfe',
  legalInfo: 'Rechtsinformation',
  deleteDevice: 'Gerät entfernen',
  autoUpgrade: 'Firmware automatisch aktualisieren',
  checkUpgrade: 'Nach Firmware-Updates suchen',
  security: 'Sicherheitseinstellungen',
  networkInfo: 'Netzwerkinformationen',
  feedback: 'Feedback',
  timezone: 'Gerätezeitzone',
  addToDesktop: 'Zum Startbildschirm hinzufügen',
  open: 'Ein',
  close: 'Aus',
  other: 'Sonstiges',
  multipleKeyShowOnHome: 'Die Anzahl der auf der Startseite angezeigten Tasten: {0}',
  // 常用设备
  favoriteDevices: 'Auf der Xiaomi-Home-Startseite anzeigen',
  favoriteCamera: 'Dashboard-Kartengröße',
  favoriteAddDevices: 'Zu Favoriten hinzufügen',
  // MHDatePicker
  cancel: 'Abbrechen',
  ok: 'Bestätigen',
  am: 'AM',
  pm: 'PM',
  numberMonth: {
    'zero': '{0} Monate',
    'one': '{0} Monat',
    'two': '{0} Monate',
    'few': '{0} Monate',
    'many': '{0} Monate',
    'other': '{0} Monate'
  },
  numberDay: {
    'zero': '{0} Tage',
    'one': '{0} Tag',
    'two': '{0} Tage',
    'few': '{0} Tage',
    'many': '{0} Tage',
    'other': '{0} Tage'
  },
  numberHour: {
    'zero': '{0} Std.',
    'one': '{0} Std.',
    'two': '{0} Std.',
    'few': '{0} Std.',
    'many': '{0} Std.',
    'other': '{0} Std.'
  },
  numberMinute: {
    'zero': '{0} Min.',
    'one': '{0} Min.',
    'two': '{0} Min.',
    'few': '{0} Min.',
    'many': '{0} Min.',
    'other': '{0} Min.'
  },
  numberSecond: {
    'zero': '{0} Sek.',
    'one': '{0} Sek.',
    'two': '{0} Sek.',
    'few': '{0} Sek.',
    'many': '{0} Sek.',
    'other': '{0} Sek.'
  },
  months: '个月',
  // 复数
  days: '天',
  // 复数
  hours: '小时',
  // 复数
  minutes: '分钟',
  // 复数
  seconds: '秒钟',
  // 复数
  month: '个月',
  // 单数
  day: '天',
  // 单数
  hour: '小时',
  // 单数
  minute: '分钟',
  // 单数
  second: '秒钟',
  // 单数
  yearUnit: ' ',
  // 单数
  monthUnit: ' ',
  // 单数
  dayUnit: ' ',
  // 单数
  hourUnit: ' ',
  // 单数
  minuteUnit: ' ',
  // 单数
  secondUnit: ' ',
  // 单数
  dateSubTitle: '{2}.{1}.{0}',
  // 2019年06月03日
  time24SubTitle: '{0}:{1}',
  // 11:43
  time12SubTitle: '{1}:{2} {0}',
  // 上午 11:43
  singleSubTitle: '{0} {1}',
  // 5 小时
  // 升级相关
  firmwareUpgradeExit: 'Beenden',
  firmwareUpgradeUpdate: 'Aktualisieren',
  firmwareUpgradeLook: 'Anzeigen',
  firmwareUpgradeForceUpdate: 'Die aktuelle Firmware ist möglicherweise zu alt, um einige Funktionen auszuführen. Aktualisieren Sie auf die neueste Version, um eine verbesserte Funktionsausführung zu erzielen.',
  firmwareUpgradeForceUpdating: 'Gerät wird aktualisiert. Bitte später erneut versuchen.',
  firmwareUpgradeNew_pre: 'Ein Update ist verfügbar. ',
  firmwareUpgradeNew_sub: 'Jetzt aktualisieren?',
  handling: 'Einen Moment, bitte …',
  error: 'Ein Fehler ist aufgetreten. Bitte später erneut versuchen.',
  createLightGroup: 'Lichtgruppe erstellen (neu)',
  manageLightGroup: 'Lichtgruppe verwalten (neu)',
  deleteLightGroup: 'Lichtergruppierung aufheben',
  deleteCurtainGroup: 'Gerätegruppierung aufheben',
  linkDevice: 'Geräte verlinken',
  noSuppurtedLinkageDevice: 'Keine Geräte verfügbar',
  noSuppurtedLinkageTip: '1. Vergewissere dich, dass du die Geräte in der Xiaomi-Home-App hinzugefügt und sie Räumen zugewiesen hast.\\n2. Sorge dafür, dass sich die Bluetooth-Geräte in der Nähe dieses Geräts befinden, damit eine Verbindung hergestellt werden kann.',
  supportedLinkageDevices: 'Lässt sich mit den folgenden Geräten verlinken:',
  linkageDistanceTip: 'Sorgen Sie dafür, dass sich die Bluetooth-Geräte in der Nähe dieses Geräts befinden, damit sie miteinander verlinkt werden können.',
  linkageRemoveTip: 'Zur Änderung des verlinkten Bluetooth-Geräts muss es zunächst entfernt werden.',
  link: 'Verbinden',
  removeLink: 'Entfernen',
  linkFail: 'Verlinken fehlgeschlagen',
  removeLinkFail: 'Entfernen fehlgeschlagen',
  linkConfirm: 'Jetzt mit diesem Gerät verlinken?',
  removeLinkConfirm: 'Jetzt entfernen?',
  linking: 'Wird verlinkt...',
  linkDeviceBracelet: 'Link-Band',
  scanDeviceBracelet: 'Band wird gesucht ...',
  scanDeviceBraceletTip: 'Sorgen Sie dafür, dass sich das Mi Band in der Nähe dieses Geräts befindet und Bluetooth eingeschaltet ist, damit eine Verbindung hergestellt werden kann.',
  scanDeviceBraceletEmptyTitle: 'Keine Mi Bands in der Nähe gefunden',
  scanDeviceBraceletEmptyTip1: '1. Vergewissern Sie sich, dass Bluetooth auf dem Band eingeschaltet ist.',
  scanDeviceBraceletEmptyTip2: '2. Sorgen Sie dafür, dass sich das Band in der Nähe des anderen Geräts befindet.',
  linkedDeviceBraceletHeaderTip: 'Verlinkt mit folgenden Bändern:',
  availableLinkDeviceBraceletHeaderTip: 'Kann mit folgenden Bändern verlinkt werden:',
  linkedDeviceBraceletFooterTip: 'Zur Änderung des verlinkten Bands muss es zunächst entfernt werden.',
  availableLinkDeviceBraceletFooterTip: 'Stellen Sie sicher, dass Bluetooth auf dem Band eingeschaltet ist und es sich in der Nähe des anderen Geräts befindet.',
  pluginVersion: 'Plugin-Version',
  helpAndFeedback: 'Hilfe und Feedback',
  offline: 'Offline',
  downloading: 'Download läuft …',
  installing: 'Installation läuft …',
  upgradeSuccess: 'Erfolgreich aktualisiert',
  upgradeFailed: 'Aktualisierung fehlgeschlagen. Später erneut versuchen.',
  upgradeTimeout: 'Zeitüberschreitung beim Updaten',
  autoUpgradeInfo: 'Versuch eines automatischen Updates zwischen {0}',
  today: 'Heute',
  tomorrow: 'Morgen',
  currentIsLatestVersion: 'Derzeitige Version ist aktuell',
  lastestVersion: 'Neueste Version: ',
  currentVersion: 'Aktuelle Version: ',
  fetchFailed: 'Zugriff fehlgeschlagen. Erneut versuchen.',
  releaseNote: 'Was ist neu',
  releaseVersionHistory: 'Updateverlauf der Firmware',
  firmwareAutoUpdate: 'Automatische Firmware-Updates',
  autoUpdateDescriptionNote: 'Sobald eine neue Firmware erkannt wird, versucht das Gerät, innerhalb {0} automatisch zu aktualisieren. Das Update wird installiert, wenn Sie das Gerät nicht verwenden. Während des Aktualisierungsvorgangs gibt es keine Audio- oder Lichtbenachrichtigungen.',
  updateNow: 'Aktualisieren',
  requireBelMesh: 'Damit diese Funktion normal funktioniert, ist ein Bluetooth-Mesh-Gateway erforderlich.',
  createCurtainGroup: 'Vorhanggruppe erstellen',
  createCurtainGroupTip: 'Zwei Vorhangmotore können zu einer Gruppe zusammengeschlossen und als zweiseitiger Vorhang gesteuert werden.',
  act: 'Bewegen',
  create: 'Erstellen',
  chooseCurtainGroupTitle: 'Einen Vorhangmotor auswählen',
  currentDevice: 'Dieses Gerät',
  curtain: 'Vorhang',
  noCurtainGroupTip: 'Gruppieren fehlgeschlagen. Fügen Sie einen anderen Vorhangmotor hinzu und versuchen Sie es erneut.',
  switchPlugin: 'Standard-Plugin',
  defaultPlugin: 'Standard',
  selectDefaultHP: 'Standard',
  stdPluginTitle: 'Serienmäßig',
  thirdPluginTitle: 'Traditionell',
  stdPluginSubTitle: 'In den Zusatzfunktionen kann man zu der alten Seitenversion wechseln',
  stdGuideDialogTitle: 'Neue Version verfügbar',
  stdGuideDialogSubTitle: 'Aktualisieren Sie die App für ein noch effizienteres Erlebnis.',
  stdGuideDialogNote: 'Sollten Sie nach einer Aktualisierung eine Funktion vermissen, finden Sie diese eventuell in den „Zusatzfunktionen“ wieder.',
  stdGuideDialogButtonOK: 'OK',
  // 多键开关设置
  key: 'Schalter',
  keyLeft: 'Schalter links',
  keyMiddle: 'Mittlerer Schalter',
  keyRight: 'Schalter rechts',
  keyType: 'Schalterart',
  keyName: 'Name',
  light: 'Lampe',
  updateIcon: 'Symbol ändern',
  done: 'Fertig',
  modifyName: 'Name bearbeiten',
  keyUpdateIconTips: 'Bei Umstellung des Symbols auf „{0}“ können Sie Mi AI bitten, „{0}“ einzuschalten.',
  nameHasChars: 'Name darf keine Sonderzeichen enthalten',
  nameTooLong: 'Der Name darf aus bis zu 40 Zeichen bestehen',
  nameIsEmpty: 'Nicht in Geräteliste anzeigen',
  nameNotSupportEmoji: 'Namen dürfen keine Emojis enthalten',
  // 房间
  room: 'Raum',
  room_nameInputTips: 'Raumname eingeben',
  room_nameSuggest: 'Empfohlener Name',
  room_createNew: 'Neuen Raum erstellen',
  room_bedroom: 'Schlafzimmer',
  room_masterBedroom: 'Hauptschlafzimmer',
  room_secondBedroom: 'Zweites Schlafzimmer',
  room_kitchen: 'Küche',
  room_diningRoom: 'Esszimmer',
  room_washroom: 'WC',
  room_childrensRoom: 'Kinderzimmer',
  room_office: 'Büro',
  room_study: 'Bibliothek',
  room_balcony: 'Balkon',
  room_studio: 'Werkstatt',
  room_bathroom: 'Badezimmer',
  room_backyard: 'Hinterhof',
  room_unassigned: 'Nicht zugewiesen',
  no_privacy_tip_content: 'Datenschutzerklärung konnte nicht geladen werden. Netzwerkeinstellungen überprüfen und erneut versuchen oder das Problem per Feedback melden.',
  moreDeviceInfo: 'Weitere Geräteinformationen',
  deviceNet: 'Gerätenetzwerk',
  customizeName: 'Name anpassen',
  software: 'Software',
  hardware: 'Hardware',
  bleMeshGateway: 'Bluetooth-Mesh-Gateway',
  deviceDid: 'Geräte-ID',
  deviceSN: 'Geräte-SN',
  mcuVersion: 'MCU-Firmware-Version',
  sdkVersion: 'SDK-Firmware-Version',
  deviceModel: 'Gerätemodell',
  deviceQR: 'Geräte-QR-Code',
  download: 'Herunterladen',
  saveSuccess: 'Erfolgreich gespeichert',
  saveFailed: 'Speichern fehlgeschlagen',
  clipboardy: 'Erfolgreich kopiert',
  connected: 'Verbunden',
  notConnected: 'Nicht verbunden',
  bleConnected: 'Direkte Bluetooth-Verbindung',
  deviceOffline: 'Offline',
  deviceConsumables: 'Geräteversorgung',
  consumableStateSufficient: 'Ausreichend',
  consumableStateInsufficient: 'Unzureichend',
  consumableStateUnknown: 'Status unbekannt',
  consumableStateDepletion: 'Verbraucht',
  consumableStateRemainPercent: 'Rest: {0}%',
  consumableStateEstimatedHour: {
    'zero': '{0} Std. verbleibend',
    'one': '{0} Std. verbleibend',
    'two': '{0} Std. verbleibend',
    'few': '{0} Std. verbleibend',
    'many': '{0} Std. verbleibend',
    'other': '{0} Std. verbleibend'
  },
  consumableStateEstimatedDay: {
    'zero': '{0} Tage verbleibend',
    'one': '{0} Tag verbleibend',
    'two': '{0} Tage verbleibend',
    'few': '{0} Tage verbleibend',
    'many': '{0} Tage verbleibend',
    'other': '{0} Tage verbleibend'
  },
  changeIcon: 'Symbol ändern',
  deviceCall: 'Notfallmeldungen',
  cloudStorage: 'Cloudspeicher-Benachrichtigungen',
  cloudStorageVip: 'Benachrichtigungen über den Cloud-Mitgliedschaftsstatus erhalten',
  largeCardEvent: 'Anzeigen der zuletzt erfassten Ereignisse auf der Karte',
  // 开关智能
  switch_title_controlDevice: 'Geräte steuern',
  switch_subtitle_controlDeviceType: 'Gerätetypen für jede Taste festlegen',
  common_listItem_value_unset: 'Nicht festgelegt',
  switch_title_buttonControlDevice: 'Gesteuerte Geräte (${})',
  switch_listItem_title_toWirelessSwitch: 'Wechsel zu kabellosen Schalter',
  switch_listItem_subtile_wirelessSwitchSetting: 'Wenn diese Funktion aktiviert ist, kannst du mit den physischen Tasten keine Schalter steuern. Du kannst sie aber weiterhin für Automatisierungen verwenden.',
  switch_dia_msg_wirelessSwitchSetting: 'Diese Taste ist mit einem anderen Element (${}) verbunden. Wechsle zu einem kabellosen Schalter, um sie zu verwenden.',
  switch_listItem_title_voiceControlLoop: 'Sprachbefehle für Schalter',
  switch_listItem_title_xiaoAiVoiceControlLoop: 'Steuere Schalter mit Mi AI',
  switch_listItem_value_voiceControlLoopOn: 'Ein',
  switch_listItem_value_voiceControlLoopOff: 'Aus',
  switch_listItem_subtile_xiaoAiVoiceControlLoopDescription: 'Steuere Schalter mit Sprachbefehlen über Mi AI. Wenn smarte Lampen mit dem Schalter verbunden sind, können sie ausgeschaltet und getrennt werden.',
  switch_listItem_title_operationMode: 'Betriebsmodus',
  switch_listItem_title_speedMode: 'Super-Speed-Modus',
  switch_listItem_title_standardMode: 'Standard-Modus',
  switch_listItem_subtile_speedModeDescription: 'Wähle diese Option, wenn Automatisierungen nur für „einmaliges Drücken“ eingerichtet werden müssen. Mit dieser Option wird die Reaktionszeit der Automatisierung verbessert.',
  switch_listItem_subtile_standardModeDescription: 'Wähle diese Option, wenn das Gerät Automatisierungen für „doppeltes Drücken“ oder „Drücken und halten“ einrichten soll.',
  switch_dia_msg_speedModeMessage: 'Auf diesem Gerät sind bereits die Automatisierungen „Doppelt drücken“ und „Drücken und Halten“ eingerichtet. Wenn du den Super-Speed-Modus wählst, kannst du diese Automatisierungen nicht mehr verwenden. Trotzdem fortfahren?',
  switch_title_selectDeviceType: 'Gerätetyp auswählen',
  switch_subtitle_selectDeviceType: 'Gerätetyp auswählen, der von ${} gesteuert wird',
  switch_subtitle_liveWire_selectDeviceType: 'Gerätetyp auswählen, der von ${} gesteuert wird. Eine Taste mit normalen Geräten verbunden lassen, um sicherzustellen, dass der Schalter normal funktioniert.',
  switch_title_deviceType_normalDevice: 'Normale Geräte (Lichter und Lampen ohne smarte Funktionalität)',
  switch_title_deviceType_smartLight: 'Smarte Lichter',
  switch_title_deviceType_smartSwitch: 'Andere smarte Schalter',
  switch_title_deviceType_manualScene: 'Batch-Steuerungen',
  switch_title_deviceType_otherSmartDevice: 'Andere smarte Geräte',
  switch_value_deviceType_normalDevice: 'Normale Geräte',
  switch_value_deviceType_smartLight: 'Smarte Lichter',
  switch_value_deviceType_smartSwitch: 'Andere smarte Schalter',
  switch_value_deviceType_manualScene: 'Batch-Steuerungen',
  switch_value_deviceType_otherSmartDevice: 'Andere smarte Geräte',
  switch_button_title_seeCreatedScene: 'Automatisierungen anzeigen',
  switch_button_title_linkSmartLight: 'Smarte Lichter verbinden',
  switch_button_title_linkSmartSwitch: 'Smarte Schalter verbinden',
  switch_button_title_linkManualScene: 'Batch-Steuerung verbinden',
  switch_button_title_switchNameSetting: 'Tastenname festlegen',
  switch_nav_title_buttonControlLight: 'Gesteuerte smarte Lichter (${})',
  switch_nav_subtitle_buttonControlLight: 'Verbinde smarte Lichter mit einer Taste, um sie ein- und auszuschalten und sie online zu halten',
  switch_header_title_selectLightOrGroup: 'Wähle smarte Lichter oder Lichtgruppen',
  switch_nav_title_buttonControlSwitch: 'Gesteuerte smarte Schalter (${})',
  switch_nav_subtitle_buttonControlSwitch: 'Verbinde Schalter, um andere Ein-/Aus-Schnittstellen hinzuzufügen. Durch Drücken der Taste werden ausgewählte Schalter ein- und ausgeschaltet.',
  switch_header_title_selectSwitch: 'Smarte Schalter auswählen',
  switch_nav_title_buttonControlManualScene: 'Zugewiesene Batch-Steuerungen (${})',
  switch_nav_subtitle_buttonControlManualScene: 'Batch-Steuerungen zuweisen, um sie durch Drücken der Taste auszuführen',
  switch_header_title_selectManualScene: 'Batch-Steuerungen auswählen',
  common_edit: 'Bearbeiten',
  common_reselect: 'Erneut auswählen',
  common_deleted: 'Gelöscht',
  common_delete: 'Löschen',
  common_delete_failed: 'Löschen fehlgeschlagen. Netzwerkeinstellungen überprüfen und erneut versuchen.',
  common_setting_failed: 'Festlegen fehlgeschlagen. Prüfe, ob das Gerät mit dem Netzwerk verbunden ist und versuche es erneut.',
  common_saving: 'Speichern…',
  switch_listItem_title_executionType: 'Ausführungsmodus',
  switch_listItem_value_executionTypeCloud: 'Cloud',
  switch_listItem_value_executionTypeLocale: 'Lokal',
  switch_dia_msg_deleteScene: 'Diese Automatisierung löschen?',
  switch_scene_name_toggleSwitchDevice: '${} | Einmal Drücken | ${} | Ein/Aus | ${}-${}',
  switch_scene_name_toggleLightDevice: '${} | Einmal Drücken | ${} | Ein/Aus | ${}',
  switch_scene_name_executeManualScene: '${} | Einmal Drücken | ${} | Ausführen | ${}-${}',
  switch_list_device_unavailable: 'Nicht angezeigte Geräte unterstützen diese Funktion nicht',
  switch_button_subtitle_notCurrentHome: 'Nicht im derzeitigen Zuhause',
  common_list_empty: 'Hier ist noch nichts',
  switch_dia_msg_repeatScene: '当前按键已创建了单击自动化，关联控制设备后，自动化可能会重复执行，是否需删除控制设备的关联？',
  common_loading: '加载中，请稍后...',
  pairMode: 'Kopplungsmodus',
  switch_title_buttonControlDevice_oneGang: 'Gerätesteuerungen'
};