import React from 'react';
import { Text, Image, StyleSheet, View, TouchableOpacity } from 'react-native';
import { Separator } from 'mhui-rn';
import { localStrings as LocalizedStrings } from "../../MHLocalizableString";
import Util from '../../util2/Util';

export default class Toolbar extends React.Component {

  _handleDownloadPressed() {
    if (this.props.downloadPressed) {
      this.props.downloadPressed();
    }
  }

  _handleDeletePressed() {
    if (this.props.deletePressed) {
      this.props.deletePressed();
    }
  }

  render() {
    const { disabled } = this.props;
    return <View style = {disabled ? styles.disabledContainer : styles.container}>
      {/* <Separator/> */}
      <View style = {styles.bar}>
        <TouchableOpacity disabled={disabled} onPress = {() => this._handleDownloadPressed()} style = {[styles.button, { marginRight: 16, display: this.props.showDownload ? "flex" : "none" }]}>
          <Image style={styles.icon} source={Util.isDark() ? require("../../../resources2/images/icon_videorecord_download_w.png") : require("../../../resources2/images/icon_videorecord_download_b.png")}></Image>
          <Text style={styles.label}>{LocalizedStrings['f_download']}</Text>
        </TouchableOpacity>
        <TouchableOpacity disabled={disabled} onPress = {() => this._handleDeletePressed()} style = {[styles.button, { marginLeft: this.props.showDownload ? 16 : 0 }]}>
          <Image style={styles.icon} source={Util.isDark() ? require("../../../resources2/images/icon_videorecord_delete_w.png") : require("../../../resources2/images/icon_videorecord_delete_b.png")}></Image>
          <Text style={styles.label}>{LocalizedStrings['f_delete']}</Text>
        </TouchableOpacity>
      </View>

    </View>;
  }
}

const styles = StyleSheet.create({
  container: {
    height: 100,
    width: '100%',
    backgroundColor: 'white'
  },
  disabledContainer: {
    height: 100,
    width: '100%',
    backgroundColor: 'white'
  },
  bar: {
    marginTop: 30,
    flexDirection: 'row',
    justifyContent: 'center'
  },
  button: {
    width: 90,
    height: 40,
    alignItems: 'center',
    justifyContent: 'space-around'
  },
  label: {
    color: 'rgba(0, 0, 0, 0.8)'
  },
  icon: {
    width: 25,
    height: 25
  }
});
