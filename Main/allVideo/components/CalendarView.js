import React from 'react';
import { Text, StyleSheet, View, FlatList, TouchableOpacity } from 'react-native';
import { SCREEN_WIDTH } from '../../util2/Const';
import { Separator } from 'mhui-rn';

const NumColumns = 6;

const itemSpace = 8;
const itemWidth = (SCREEN_WIDTH - itemSpace * NumColumns) / NumColumns;

export default class CalendarView extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      selectedDateItem: null
    };
  }

  _handleDayButtonPressed(item) {
    if (!item.enabled) {
      return;
    }
    this.setState({ selectedDateItem: item });
    this._selectedDayChanged(item);
    if (this.props.dayButtonPressed) {
      this.props.dayButtonPressed(item);
    }
  }

  _selectedDayChanged(item) {
    for (let i = 0; i < this.props.dataSource.length; i++) {
      if (this.props.dataSource[i] == item) {
        item.selected = true;
        this.props.dataSource[i].selected = true;
      } else {
        this.props.dataSource[i].selected = false;
      }
    }
    this.setState({});
  }

  render() {
    return <View style = {styles.container}>
      <FlatList
        ref={(ref) => { this.list = ref; }}
        style={styles.list}
        data={this.props.dataSource}
        renderItem={({ item }) => this._renderCell(item)}
        horizontal={true}
        initialScrollIndex={this.props.dataSource.length - 1}
        showsHorizontalScrollIndicator={false}
        getItemLayout={(data, index) => (
          { length: itemWidth, offset: itemWidth * index, index }
        )}
        keyExtractor={(item, index) => index.toString()}
      />
      <Separator/>
    </View>;
  }

  _renderCell = (item) => {
    let cellStyle = styles.cell;
    let dayStyle = { color: 'black', fontWeight: 'bold', fontSize: 20, textAlign: "center" };
    let monthStyle = { color: 'gray', fontSize: 12 };
    if (!item.enabled) {
      cellStyle = styles.disabledCell;
      dayStyle = { color: 'gray', fontWeight: 'bold', fontSize: 20 };
      monthStyle = { color: 'gray', fontSize: 12 };
    } else if (item.selected) {
      cellStyle = styles.selectedCell;
      dayStyle = { color: 'white', fontWeight: 'bold', fontSize: 20 };
      monthStyle = { color: 'white', fontSize: 12 };
    }
    return (
      <TouchableOpacity style={styles.cellContainer} onPress={() => this._handleDayButtonPressed(item)}>
        <View style={cellStyle}>
          <Text style={dayStyle}>{item.dayStr}</Text>
          <Text style={monthStyle}>{item.monthStr}</Text>
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    height: 80,
    width: '100%',
    backgroundColor: 'white'
  },
  list: {
    flexDirection: 'row',
    width: '100%'
  },
  cellContainer: {
    width: itemWidth,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white'
  },
  disabledCell: {
    backgroundColor: 'white',
    marginHorizontal: 4,
    marginVertical: 10,
    justifyContent: 'center',
    alignItems: 'center'
  },
  cell: {
    backgroundColor: 'white'
  },
  selectedCell: {
    width: itemWidth - itemSpace,
    height: itemWidth - itemSpace,
    borderRadius: (itemWidth - itemSpace) / 2,
    backgroundColor: '#5db8be',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
