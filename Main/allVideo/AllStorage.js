import React from 'react';
import { Text, View, SafeAreaView, StatusBar, Platform, TouchableOpacity } from 'react-native';
import ImageButton from "miot/ui/ImageButton";
import ScrollableTabView, { ScrollableTabBar } from 'react-native-scrollable-tab-view';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import BasePage, { BaseStyles } from "../BasePage";
import StorageUI, { MaxSel } from "./StorageUI";
import Singletons from "../framework/Singletons";
import TrackUtil from '../util/TrackUtil';
import CameraConfig from '../util/CameraConfig';
import StorageKeys from '../StorageKeys';

import { DescriptionConstants } from '../Constants';


import { SCREEN_WIDTH } from "../util2/Const";
import LocalEvLdr from "../framework/LocalEventLoader";
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import Util from "../util2/Util";

export const Tabs = {
  Cloud: 0,
  Local: 1,
  Card: 2,
  Cnt: 3
};

export default class AllStorage extends BasePage {
  static navigationOptions = (navigation) => {
    // if (true) {//不要导航条
    //   return null;
    // }
    return {
      headerTransparent: true,
      header:
        null
    };
  };

  constructor(props) {
    super(props);
    this.initState({
      editing: false,
      selectAll: false, //select all by select all button action
      tapSelectAll: false, // select all by tap item action
      showSelAll: false, // only for icon show
      editInfo: { title: LocalizedStrings["storage_sel_init"] },
      locked: false
    });
    this.mPages = new Array(Tabs.Cnt);
    let expectedPageIndex = this.props.navigation.getParam("initPageIndex") || 0;
    console.log('allstorage mCurIdx: ', expectedPageIndex);
    this.mCurIdx = expectedPageIndex;
    this.mSdcardLoader = SdcardEventLoader.getInstance();
    this.mSupportCloud = this.props.navigation.getParam('isSupportCloud');
    this.mTabWidth = SCREEN_WIDTH * (this.mSupportCloud ? 0.25 : 0.35);
  }


  onBack() {
    //返回的时候 取消选中
    this.setState({
      selectAll: false,
    });
    if (this.state.editing) {
      this.setState({
        editing: false,
      });
      return true;
    }
  }

  // 存储卡 全选 下载 删除
  renderHeader() {
    StatusBar.setBarStyle("dark-content");
    let top = Platform.OS == "android" ? StatusBar.currentHeight : 44;
    let autoHiding = { display: this.state.editing ? "flex" : "none" };
    let source1 = Util.isDark() ? require("../../Resources/Images/icon_back_black_nor_dark.png") : require("../../Resources/Images/icon_back_black.png");
    let source = this.state.editing ? (Util.isDark() ? require("../../Resources/Images/icon_cancle_white.png") : require("../../Resources/Images/icon_cancle_black.png")) : source1;
    return (
      <View style={[BaseStyles.row, {
        position: "absolute", height: 50, paddingLeft: 12,
        backgroundColor: "#ffffff",
        width: this.state.editing ? "100%" : "11%",
        paddingRight: this.state.editing ? 12 : 0,
        zIndex:1
      }]}>

        <ImageButton
          style={BaseStyles.icon40}
          source={source}
          accessibilityLabel={
            this.state.editing ? DescriptionConstants.lc_21:DescriptionConstants.yc_23
          }
          onPress={() => {
            console.log(this.tag, "nav back");
            if (this.state.editing) {
              this.setState({ selectAll: false }, () => {
                this.setState({ editing: false });
              });
            } else {
              this.naviBack();
            }
          }} />
        <Text style={[BaseStyles.text18, { fontWeight: "bold" }, autoHiding]}>{this.state.editInfo.title}</Text>

        <ImageButton
          style={[BaseStyles.icon40, autoHiding]}
          source={this.state.showSelAll ? require("../../resources2/images/icon_select_active.png") : (Util.isDark() ? require("../../Resources/Images/icon_select_white.png") : require("../../Resources/Images/icon_select_black.png"))}
          accessibilityLabel={
            DescriptionConstants.lc_23
          }
          onPress={() => {
            console.log(this.tag, "sel all");
            this.setState({ selectAll: !(this.state.selectAll), showSelAll: !this.state.showSelAll });
          }} />
      </View>
    );
  }

  onChangeTab(arg) {
    let from = arg.from;
    let to = arg.i;
    this.mCurIdx = to;
    console.log(this.tag, "onChangeTab", from, "=>", to);
    from !== to && this.mTabChangeStat(to);
    if (from != to) {
      if (to == Tabs.Card) {
        StorageKeys.IS_SHOW_SDCARD_PAGE_ALL_STORAGE = true;
      } else if (to == Tabs.Cloud) {
        StorageKeys.IS_SHOW_SDCARD_PAGE_ALL_STORAGE = false;
      }
    }
    if (this.mPages[to]) {
      if (from !== to) {
        if (typeof (this.mPages[from]?.pause) == 'function') {
          this.mPages[from].pause();
        }
        if (typeof (this.mPages[to].resume) == 'function') {
          this.mPages[to].resume();
        }
      } else {
        if (typeof (this.mPages[to].resume) == 'function') {
          this.mPages[to].resume();
        }
      }
    }
  }

  mTabChangeStat(clickedTabId) {
    let id = null;
    switch (clickedTabId) {
      case Tabs.Cloud:
        id = 'Storage_CloudStorage_ClickNum';// Storage_CloudStorage_ClickNum
        break;
      case Tabs.Local:
        id = 'Storage_LocalAlbum_ClickNum';// Storage_LocalAlbum_ClickNum
        break;
      case Tabs.Card:
        id = 'Storage_MemoryCard_ClickNum';// Storage_MemoryCard_ClickNum
        break;
    }
    if (id) {
      TrackUtil.reportClickEvent(id);
      console.log('stat: ', id);
    }

  }
  CoverLayerState(state) {
    this.setState({
      locked : state
    });
  }
  // renderTabBar={() => this.renderTab()}
  render() {
    console.log(this.tag, "this.state.editing", this.state.editing);

    return (
      <SafeAreaView style={[BaseStyles.pageRoot, { paddingTop: StatusBar.currentHeight }]}>
        <View style={BaseStyles.pageRoot}>
          <ScrollableTabView
            ref={(ref) => { this.mScrooableTabView = ref; }}
            initialPage={this.mCurIdx}
            locked={this.state.locked || this.state.editing}
            onChangeTab={(arg) => { this.onChangeTab(arg); }}
            renderTabBar={() => <ScrollableTabBar
              // style={{ borderWidth: 0, marginLeft: SCREEN_WIDTH * 0.25, marginRight: SCREEN_WIDTH * 0.12 }}
              style={{ borderWidth: 0, marginRight: 45, marginLeft: 40, alignItems: 'center' }}
              underlineStyle={{ display: "none", height: 0 }}
              tabsContainerStyle={{ width: '100%' }}
              renderTab={(aName, aPage, active, onPressHandler) => {
                return (
                  <TouchableOpacity key={aPage} style={{ justifyContent: 'center', marginHorizontal: Util.isLanguageCN() ? 20 : 18  }}
                    onPress={() => {
                      onPressHandler(aPage);
                    }}
                  >
                    <Text style={{
                      fontWeight: 'bold', fontSize: Util.isLanguageCN() ? 18 : 14, maxWidth: this.mTabWidth,
                      textAlign: "center", textAlignVertical: 'center',
                      color: active ? "#32BAC0" : "#999999"
                    }}>{aName}</Text>
                  </TouchableOpacity>);
              }}
            />}
          >
          
            {this.mSupportCloud ?
              <StorageUI tabLabel={LocalizedStrings["storage_cloud"]}
                ref={(ref) => { this.mPages[Tabs.Cloud] = ref; }}
                navigation={this.props.navigation}
                loader={Singletons.CloudEventLoader}
                CoverLayerState={(state) => this.CoverLayerState(state)}
                startEdit={(aInfo) => this.startEdit(aInfo, Tabs.Cloud)}
                isEditing={this.state.editing}
                selectAll={(Tabs.Cloud == this.mCurIdx) && this.state.selectAll}
                vip={this.props.navigation.state.params.vip}
                type={Tabs.Cloud}
                selectCB = {this.mSelectCB}
              /> : null}
            <StorageUI tabLabel={LocalizedStrings["storage_local"]}
              ref={(ref) => { this.mPages[!this.mSupportCloud ? Tabs.Local - 1 : Tabs.Local] = ref; }}
              navigation={this.props.navigation}
              loader={LocalEvLdr}
              CoverLayerState={(state) => this.CoverLayerState(state)}
              emptyDes={LocalizedStrings["storage_local_dld_empty"]}
              startEdit={(aInfo) => this.startEdit(aInfo, Tabs.Local)}
              isEditing={this.state.editing}
              selectAll={(this.mSupportCloud ? (Tabs.Local == this.mCurIdx) : (Tabs.Local - 1 == this.mCurIdx)) && this.state.selectAll}
              vip={this.props.navigation.state.params.vip}
              type={Tabs.Local}
              selectCB = {this.mSelectCB}
            />



            <StorageUI tabLabel={LocalizedStrings["eu_storage_sdcard"] + " "}
              ref={(ref) => { this.mPages[!this.mSupportCloud ? Tabs.Card - 1 : Tabs.Card] = ref; }}
              navigation={this.props.navigation}
              loader={this.mSdcardLoader}
              CoverLayerState={(state) => this.CoverLayerState(state)}
              emptyDes={LocalizedStrings["sdcard_page_desc_empty"]}
              startEdit={(aInfo) => this.startEdit(aInfo, Tabs.Card)}
              isEditing={this.state.editing}
              selectAll={(this.mSupportCloud ? (Tabs.Card == this.mCurIdx) : (Tabs.Card - 1 == this.mCurIdx)) && this.state.selectAll}
              vip={this.props.navigation.state.params.vip}
              type={Tabs.Card}
              selectCB = {this.mSelectCB}
            />

          </ScrollableTabView>
          {this.renderHeader()}
        </View>
      </SafeAreaView>
    );
  }

  startEdit(aInfo, aTab) {
    // console.log(this.tag, "start edit", aInfo, aTab);
    if (aInfo != null) {
      this.setState({ editing: true, editInfo: aInfo});
    } else {
      this.setState({ editing: false, showSelAll: false });
    }
  }

  mSelectCB =(tapSelectAll, selectCnt) => {
    if (this.state.tapSelectAll != tapSelectAll && selectCnt > 0) {
      this.setState({ selectAll: tapSelectAll || this.state.selectAll, tapSelectAll: tapSelectAll, showSelAll: tapSelectAll });
    }
    if (selectCnt == 0) {
      this.setState({ tapSelectAll: tapSelectAll, selectAll: false, showSelAll: false });
    }
  }
}
