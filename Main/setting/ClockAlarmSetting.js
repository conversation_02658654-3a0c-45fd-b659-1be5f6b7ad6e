'use strict';

import { Device } from "miot";
import { strings as I18n, Styles } from 'miot/resources';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { StyleSheet, ScrollView, View, Image, Text, SafeAreaView, TouchableOpacity, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from '../components/Toast';
import Host from "miot/Host";
import { styles } from './SettingStyles';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import StorageKeys from '../StorageKeys';
import VersionUtil from "../util/VersionUtil";
import Service from "miot/Service";
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";

import AlarmUtilV2, {
  KEY_ALARM_REMIND_DURATION_PIID, KEY_ALARM_REMIND_INTERVAL_PIID, KEY_ALARM_REMIND_REPEAT_PIID,
  KEY_ALARM_REMIND_SIID,
  PIID_CAMERA_CORRECTION,
  SIID_CAMERA_CONTROL
} from "../util/AlarmUtilV2";
import CameraConfig from '../util/CameraConfig';
import BaseSettingPage from "../BaseSettingPage";
import { ChoiceDialog } from "mhui-rn";

const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead
const imageWidth = (kWindowWidth - 60) / 2;
const imageHeight = imageWidth / 1.5;
const sensitiveOptions = [
  { title: LocalizedStrings['alarm_level_high_title'], value: LocalizedStrings['pss_h'] },
  { title: LocalizedStrings['alarm_level_middle_title'], value: LocalizedStrings['pss_m'] },
  { title: LocalizedStrings['alarm_level_low_title'], value: LocalizedStrings['pss_l'] }
];
export default class ClockAlarmSetting extends BaseSettingPage {


  constructor(props, context) {
    super(props, context);

    this.state = {
      alarmDurationDialog: false,
      alarmIntervalDialog: false,
      alarmCountDialog: false,
      durationIndex: 0,
      intervalIndex: 0,
      countIndex: 0
    };
    this.durationData = [
      { title: `1${ LocalizedStrings['tip_time_minute'] }`, value: 1 },
      { title: `5${ LocalizedStrings['tip_time_minute'] }`, value: 5 },
      { title: `10${ LocalizedStrings['tip_time_minute'] }`, value: 10 },
      { title: `15${ LocalizedStrings['tip_time_minute'] }`, value: 15 },
      { title: `20${ LocalizedStrings['tip_time_minute'] }`, value: 20 },
      { title: `25${ LocalizedStrings['tip_time_minute'] }`, value: 25 },
      { title: `30${ LocalizedStrings['tip_time_minute'] }`, value: 30 }
    ];
    this.intervalData = [
      { title: `1${ LocalizedStrings['tip_time_minute'] }`, value: 1 },
      { title: `3${ LocalizedStrings['tip_time_minute'] }`, value: 3 },
      { title: `5${ LocalizedStrings['tip_time_minute'] }`, value: 5 },
      { title: `10${ LocalizedStrings['tip_time_minute'] }`, value: 10 }
    ];
    this.timesData = [
      { title: `1${ LocalizedStrings['times'] }`, value: 1 },
      { title: `2${ LocalizedStrings['times'] }`, value: 2 },
      { title: `3${ LocalizedStrings['times'] }`, value: 3 },
      { title: `4${ LocalizedStrings['times'] }`, value: 4 },
      { title: `5${ LocalizedStrings['times'] }`, value: 5 }
    ];
  }

  getTitle() {
    return LocalizedStrings['more_setting'];
  }

  componentDidMount() {
    super.componentDidMount();
    this.getAlarmSetting();
  }

  getAlarmSetting() {
    const params = [{ sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_DURATION_PIID },
      { sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_INTERVAL_PIID },
      { sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_REPEAT_PIID }
    ];

    AlarmUtilV2.getSpecPValue(params).then((res) => {
      console.log("+++++++++++++++++",res);
      if (res[0].code == 0) {
        let durationIndex = this.durationData.findIndex((item) => {
          return item.value == res[0].value;
        });
        let intervalIndex = this.intervalData.findIndex((item) => {
          return item.value == res[1].value;
        });
        let repeatIndex = this.timesData.findIndex((item) => {
          return item.value == res[2].value;
        });
        this.setState({
          durationIndex: durationIndex,
          intervalIndex: intervalIndex,
          countIndex: repeatIndex
        })
      } else {
        Toast.fail('c_get_fail');
      }
    }).catch((error) => {
      Toast.fail('c_get_fail');
    });
  }

  setDuration(value) {
    console.log("+++++++++++++++++++",value);
    const params = [{ sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_DURATION_PIID, value: this.durationData[value].value }];
    AlarmUtilV2.setSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        Toast.success('c_set_success');
        this.setState({durationIndex: value});
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((error) => {
      Toast.fail('c_set_fail');
    });
  }
  setInterval(value) {
    console.log("+++++++++++++++++++",value);
    const params = [{ sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_INTERVAL_PIID, value: this.intervalData[value].value }];
    AlarmUtilV2.setSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        Toast.success('c_set_success');
        this.setState({intervalIndex: value});
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((error) => {
      Toast.fail('c_set_fail');
    });
  }

  setRepeat(value) {
    console.log("+++++++++++++++++++",value);
    const params = [{ sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_REPEAT_PIID, value: this.timesData[value].value }];
    AlarmUtilV2.setSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        Toast.success('c_set_success');
        this.setState({countIndex: value});
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((error) => {
      Toast.fail('c_set_fail');
    });
  }

  renderSettingContent() {
    return (
      <View style={ styles.container }>
        <ListItem
          title={ LocalizedStrings["clock_alarm_duration"] }
          value={this.durationData[this.state.durationIndex].title}
          showSeparator={ false }
          onPress={ () => {
            // TrackUtil.reportClickEvent("setting_sleepNumber");
            // this.props.navigation.navigate('SleepSetting');
            this.setState({ alarmDurationDialog: true });
          }
          }
          titleStyle={ { fontWeight: 'bold' } }
          containerStyle={ { paddingVertical: 10 } }
          titleNumberOfLines={ 3 }
          key={ 1 }
        />
        <ListItem
          title={ LocalizedStrings["clock_alarm_interval"] }
          showSeparator={ false }
          value={this.intervalData[this.state.intervalIndex].title}
          onPress={ () => {
            // TrackUtil.reportClickEvent("setting_sleepNumber");
            this.setState({ alarmIntervalDialog: true });
          }
          }
          titleStyle={ { fontWeight: 'bold' } }
          containerStyle={ { paddingVertical: 5 } }
          titleNumberOfLines={ 3 }
          key={ 2 }
        />
        <ListItem
          title={ LocalizedStrings['clock_alarm_count'] }
          showSeparator={ false }
          value={this.timesData[this.state.countIndex].title}
          containerStyle={ { paddingVertical: 10 } }
          onPress={ () => {
            this.setState({ alarmCountDialog: true });
          }
          }
          titleStyle={ { fontWeight: 'bold' } }
          titleNumberOfLines={ 3 }
          key={ 3 }
        />
        { this._renderAlarmDurationDialog() }
        { this._renderAlarmIntervalDialog() }
        { this._renderAlarmCountDialog() }
      </View>
    );
  }

  _renderAlarmDurationDialog() {
    return (
      <ChoiceDialog
        visible={ this.state.alarmDurationDialog }
        title={ LocalizedStrings['clock_alarm_duration'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: false,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        } }
        buttons={ [{
          text: I18n.cancel,
          callback: () => {
            this.setState({ alarmDurationDialog: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("ssssss", res);
            const newPluginType = res?.[0];
            if (newPluginType === this.state.durationIndex) {
              this.setState({ alarmDurationDialog: false });
              return;
            }
            this.setState({ alarmDurationDialog: false });
            this.setDuration(newPluginType);
          }
        }] }
        onDismiss={ () => {
          this.setState({ alarmDurationDialog: false });
        } }
        options={ this.durationData }
        itemStyleType={ 2 }
        selectedIndexArray={ [this.state.durationIndex] }
      />
    );
  }

  _renderAlarmIntervalDialog() {
    return (
      <ChoiceDialog
        visible={ this.state.alarmIntervalDialog }
        title={ LocalizedStrings['clock_alarm_interval'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: false,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        } }
        buttons={ [{
          text: I18n.cancel,
          callback: () => {
            this.setState({ alarmIntervalDialog: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("ssssss", res);
            const newPluginType = res?.[0];
            if (newPluginType === this.state.intervalIndex) {
              this.setState({ alarmIntervalDialog: false });
              return;
            }
            this.setState({ alarmIntervalDialog: false });
            this.setInterval(newPluginType);
          }
        }] }
        onDismiss={ () => {
          this.setState({ alarmIntervalDialog: false });
        } }
        options={ this.intervalData }
        itemStyleType={ 2 }
        selectedIndexArray={ [this.state.intervalIndex] }
      />
    );
  }

  _renderAlarmCountDialog() {
    return (
      <ChoiceDialog
        visible={ this.state.alarmCountDialog }
        title={ LocalizedStrings['clock_alarm_count'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: false,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        } }
        buttons={ [{
          text: I18n.cancel,
          callback: () => {
            this.setState({ alarmCountDialog: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("ssssss", res);
            const newPluginType = res?.[0];
            if (newPluginType === this.state.countIndex) {
              this.setState({ alarmCountDialog: false });
              return;
            }
            this.setState({ alarmCountDialog: false });
            this.setRepeat(newPluginType);
          }
        }] }
        onDismiss={ () => {
          this.setState({ alarmCountDialog: false });
        } }
        options={ this.timesData }
        itemStyleType={ 2 }
        selectedIndexArray={ [this.state.countIndex] }
      />
    );
  }

}
