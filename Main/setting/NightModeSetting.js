'use strict';

import { Device } from "miot";
import { Styles } from 'miot/resources';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { StyleSheet, ScrollView, View, Image, Text, SafeAreaView, TouchableOpacity, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from '../components/Toast';
import Host from "miot/Host";
import { styles } from './SettingStyles';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import StorageKeys from '../StorageKeys';
import VersionUtil from "../util/VersionUtil";
import Service from "miot/Service";
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";

import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import AlarmUtilV2, {
  CAMERA_NIGHT_MODE_CLOSE_SCREEN_PIID,
  CAMERA_NIGHT_MODE_PERIOD_PIID,
  CAMERA_NIGHT_MODE_SIID,
  CAMERA_NIGHT_MODE_SWITCH_PIID,
  PIID_CAMERA_CORRECTION,
  SIID_CAMERA_CONTROL
} from "../util/AlarmUtilV2";
import CameraConfig from '../util/CameraConfig';
import BaseSettingPage from "../BaseSettingPage";


const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead
const imageWidth = (kWindowWidth - 48);
const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;

/**
 * @Author: byh
 * @Date: 2023/11/9
 * @explanation:
 * 夜间模式
 ******************************************************** */
export default class NightModeSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);

    this.state = {
      isNightModeEnable: false,
      closeScreenSwitch: false,
      period: ''
    };
    this.isLDCEnable = false;
  }

  getTitle() {
    return LocalizedStrings['cs_night_mode'];
  }

  renderSettingContent() {
    return (
      <View style={styles.container}>
        <View style={{ marginHorizontal: 24, marginBottom: 15, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
          <View
            style={{ alignItems: 'center' }}
          >
            <Image
              style={{ width: imageWidth, borderRadius: 12, height: viewHeight }}
              source={require('../../Resources/Images/icon_night_mode.webp')}
              accessibilityLabel={DescriptionConstants.example_image}
            />
          </View>
        </View>

        <View style={{ marginHorizontal: 28 }}
          key={6}
        >

          <Text
            accessibilityLabel={DescriptionConstants.sz_4_61}
            style={{ color: "rgba(0, 0, 0, 0.6)",
              fontSize: 14,
              marginTop: 10,
              lineHeight: 21,
              fontFamily: "MI Lan Pro",
              fontWeight: "300" }}
          >
            {LocalizedStrings['cs_night_mode_desc']}
          </Text>

          <Text
            accessibilityLabel={DescriptionConstants.sz_4_61}
            style={{ color: "rgba(0, 0, 0, 0.6)",
              fontSize: 14,
              marginTop: 10,
              lineHeight: 21,
              fontFamily: "MI Lan Pro",
              fontWeight: "300"
            }}
          >
            {LocalizedStrings['cs_night_mode_attention']}
          </Text>
        </View>
        <View style={{
          height: 0.5,
          marginHorizontal: 28,
          backgroundColor: "#e5e5e5",
          marginBottom: 20,
          marginTop: 20 }}
        key={8}
        />
        <ListItemWithSwitch
          accessibilityLabel={DescriptionConstants.sz_4_59_1}
          title={LocalizedStrings['cs_night_mode']}
          value={this.state.isNightModeEnable}
          onValueChange={(value) => this.onNightModeValueChange(value)}
          showSeparator={false}
          titleStyle={{ fontWeight: 'bold' }}
          unlimitedHeightEnable={true}
          titleNumberOfLines={3}
          key={3}
          onPress={() => {
          }}
          accessibilitySwitch={{
            accessibilityLabel: DescriptionConstants.sz_4_59_1
          }}
        />
        {this.state.isNightModeEnable ? 
          <View>
            <ListItem
              title={LocalizedStrings['cs_effect_time']}
              showSeparator={false}
              onPress={() => {
                this.props.navigation.navigate("NightModeTimeSetting", {
                  nightModeCallBack: ((value) => {

                  }),
                  nightMode: this.state.nightMode
                });
              }
              }
              titleStyle={{ fontWeight: 'bold' }}
              accessibilityLabel={DescriptionConstants.sz_4_12}
            />
            <ListItemWithSwitch
              accessibilityLabel={DescriptionConstants.sz_4_59_1}
              title={LocalizedStrings['cs_close_screen']}
              subtitle={this.state.closeScreenSwitch ? LocalizedStrings['cs_open_screen_desc'] : LocalizedStrings['cs_close_screen_desc']}
              subtitleNumberOfLines={3}
              value={this.state.closeScreenSwitch}
              onValueChange={(value) => this.onScreenCloseValueChange(value)}
              showSeparator={false}
              titleStyle={{ fontWeight: 'bold' }}
              unlimitedHeightEnable={true}
              titleNumberOfLines={3}
              key={4}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_4_59_1
              }}
            />
          </View>
          : null}      
      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    this.getNightModeSetting();
  }

  getNightModeSetting() {
    let params = [
      { sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_SWITCH_PIID },
      { sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_CLOSE_SCREEN_PIID },
      { sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_PERIOD_PIID }
    ];
    AlarmUtilV2.getSpecPValue(params)
      .then((res) => {
        console.log("night mode:", res);
        if (res[0].code == 0) {
          this.setState({
            isNightModeEnable: res[0].value,
            closeScreenSwitch: res[1].value,
            period: res[2].value
          });
        } else {
          Toast.fail('c_get_fail');
        }
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
  }

  onNightModeValueChange(value) {
    const params = [{ sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_SWITCH_PIID, value: value }];
    AlarmUtilV2.setSpecPValue(params)
      .then((res) => {
        if (res[0].code == 0) {
          Toast.success('c_set_success');
          this.setState({ isNightModeEnable: value });
          if (this.props.navigation.state.params.nightModeCallBack) {
            this.props.navigation.state.params.nightModeCallBack(value);
          }
        } else {
          Toast.fail('c_set_fail');
          this.setState({ isNightModeEnable: !value });
        }
      }).catch((err) => {
        Toast.fail('c_set_fail');
        this.setState({ isNightModeEnable: !value });
      });
  }

  onScreenCloseValueChange(value) {
    const params = [{ sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_CLOSE_SCREEN_PIID, value: value }];
    AlarmUtilV2.setSpecPValue(params)
      .then((res) => {
        if (res[0].code == 0) {
          Toast.success('c_set_success');
          this.setState({ closeScreenSwitch: value });
        } else {
          Toast.fail('c_set_fail');
          this.setState({ closeScreenSwitch: !value });
        }
      }).catch((err) => {
        Toast.fail('c_set_fail');
        this.setState({ closeScreenSwitch: !value });
      });
  }

  onLDCValueChange(value) {
    TrackUtil.reportClickEvent("LensDistortionCorrectionOnOff_ClickNum");
    if (CameraConfig.isDeviceCorrect(Device.model)) {
      const params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_CORRECTION, value: value }];
      AlarmUtilV2.setSpecPValue(params)
        .then((res) => {
          if (res[0].code == 0) {
            this.setState({
              isLDCEnable: value
            });
            if (this.props.navigation.state.params.selectLDCCallBack) {
              this.props.navigation.state.params.selectLDCCallBack(value);
            }
          } else {
            this.setState({
              isLDCEnable: !value
            });
            Toast.fail('c_set_fail');
          }
        })
        .catch((err) => {
          this.setState({
            isLDCEnable: !value
          });
          Toast.fail('c_set_fail', err);
        });
    } else {
      StorageKeys.IS_LENS_DISTORTION_CORREECTION = value;
      this.setState({
        isLDCEnable: value
      });
      Toast.success('c_set_success');
      if (this.props.navigation.state.params.selectLDCCallBack) {
        this.props.navigation.state.params.selectLDCCallBack(value);
      }
    }
  }

  get_ldc() {
    
    this.isRequsting = true;
    return new Promise((resolve, reject) => {

      if (CameraConfig.isDeviceCorrect(Device.model)) {
        const params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_CORRECTION }];
        AlarmUtilV2.getSpecPValue(params, 2)
          .then((res) => {
            if (res[0].code == 0) {
              resolve(res[0].value);
            } else {
              reject();
            }
          })
          .catch((err) => {
            reject();
          });
      } else {
        StorageKeys.IS_LENS_DISTORTION_CORREECTION.then((res) => {
          resolve(res);
        }).catch(() => {
          resolve(false);
        });
      }
    });

  }

}
