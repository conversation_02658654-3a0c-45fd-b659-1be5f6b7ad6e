'use strict';

import React from 'react';
import { ScrollView, View, BackHandler, Platform, Text } from 'react-native';

import { Service, Device, Host } from "miot";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import { strings } from 'miot/resources';

import MHPicker from '../ui/MHPicker';
import { ChoiceDialog } from 'miot/ui/Dialog';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import API from '../API';
import Toast from '../components/Toast';
import StorageKeys from '../StorageKeys';
import { PackageEvent } from 'miot/Package';
import CameraConfig from '../util/CameraConfig';
import AlarmUtil from '../util/AlarmUtil';
import VersionUtil from '../util/VersionUtil';
import ImageButton from "miot/ui/ImageButton";
import RPC from '../util/RPC';
import { NavigationBar } from 'mhui-rn';
import TrackUtil from '../util/TrackUtil';

const ALL_DAY_START_TIME = "00:00:00";
const ALL_DAY_END_TIME = "23:59:59";
const ALL_DAY_END_TIME_MIN = "23:59";
const DAY_START_TIME = "08:00:00";
const DAY_END_TIME = "20:00:00";

const NIGHT_START_TIME = "20:00:00";
const NIGHT_END_TIME = "08:00:00";

const SENSITIVE_HIGH = 3;
const SENSITIVE_MED = 2;
const SENSITIVE_LOW = 1;
const SENSITIVE_NONE = 0;


export default class SurvelillanceSettingOld extends React.Component {

  constructor(props, context) {
    super(props, context);

    this.state = {
      detectionSwitch: false,
      interval: 5,
      alarmSensitivity: 1,
      alarmLevelDialogVisible: false,

      // not use
      startTime: '',
      endTime: '',

      pushSwitch: false,

      isPickerVisiable: false,

      periodStr: "",

      enableAlarmInterval: true,

      isHumanPushOn: false
    };

    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this._onPause();
      }
    );
    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (res) { // 是vip
        if (!CameraConfig.isNewChuangmi(Device.model)) {
          this.setState({ enableAlarmInterval: false });// 是vip && 不使用v2协议的model  不展示报警时间间隔.
        }
      }
    });
    this.isGoingAlarmList = false;
    // this.didFocusListener = Platform.OS === "android" ? PackageEvent.packageDidResume.addListener(this.resumeListener) : PackageEvent.packageViewWillAppear.addListener(this.resumeListener);
  }


  _onResume() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    if (this.isGoingAlarmList) {
      this.props.navigation.popToTop();
      return;
      // 看家设置跳看家列表  回来后，要主动退出回到首页。
    }
  }

  _onPause() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }
  render() {
    let sensValue = this.state.alarmSensitivity <= SENSITIVE_LOW ? LocalizedStrings['alarm_level_low_title'] : LocalizedStrings['alarm_level_high_title'];
    let timeItem = (
      <View style={[styles.featureSetting, { marginTop: 0 }]}
        key={11}
      >

        <ListItem title={LocalizedStrings['ss_partition_sensitivity_settings']} 
          showSeparator={false}
          value={sensValue}
          onPress={() =>
            this._onSensitivitySetting()
          } />

        <ListItem
          title={LocalizedStrings['ss_alarm_interval']}
          showSeparator={false}
          subtitle={LocalizedStrings['ss_alarm_interval_description']}
          value={this.state.interval + LocalizedStrings["tip_time_minute"]}
          onPress={() =>
            this.setState({ isPickerVisiable: true })
          }
        />

        
        <ListItemWithSwitch
          title={LocalizedStrings['ss_push_alarm_notifications']}
          showSeparator={false}
          subtitle={LocalizedStrings['ss_push_alarm_notifications_description']}
          value={this.state.pushSwitch}
          onValueChange={(value) => this._onPushValueChange(value)}
        />
        {
          this.state.pushSwitch
            ? (<ListItemWithSwitch
              title={LocalizedStrings['alarm_only_people_detected']}
              showSeparator={false}
              subtitle={LocalizedStrings["people_move_triggered_alarm"]}
              value={this.state.isHumanPushOn}
              onValueChange={(value) => {
                this._setHumanPush(value);
              }
                // this.props.navigation.navigate('NotificationTypeSetting')
              }
            />)
            : []
        }
        

      </View>
    );

    return (
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>

          <View style={styles.featureSetting}
            key={2}
          >
            <ListItemWithSwitch
              title={LocalizedStrings['ss_home_survelillance']}
              showSeparator={false}
              value={this.state.detectionSwitch}
              onValueChange={(value) => this._onEnableValueChange(value)}
            />
            {
              this.state.detectionSwitch ?
                <ListItem
                  title={LocalizedStrings['ss_home_survelillance_period']}
                  showSeparator={false}
                  onPress={() =>
                    this.props.navigation.navigate('SurvelillancePeriodSetting', { startTime: this.state.startTime, endTime: this.state.endTime, commitCallback: (startTime, endTime) => {
                      let { hour, minute } = this.extractHourAndMinute(startTime);
                      let startHour = hour;
                      let startMinute = minute;
                      let data = this.extractHourAndMinute(endTime);
                      let endHour = data.hour;
                      let endMinute = data.minute;
                      let onOff = this.onOff;
                      if ((startTime === (ALL_DAY_START_TIME)) && (endTime === (ALL_DAY_END_TIME) || endTime.includes(ALL_DAY_END_TIME_MIN))) {
                        onOff = 1;
                      } else {
                        onOff = 2;
                      }
                      let param = [onOff, startHour, startMinute, endHour, endMinute, this.state.pushSwitch ? 1 : 0, this.state.interval];


                      return new Promise((resolve, reject) => {
                        this._syncSetting(param)
                          .then((res) => {
                            if (res.code == 0) {
                              this.onOff = onOff;
                              startTime = AlarmUtil.formatTimeString(startTime);
                              endTime = AlarmUtil.formatTimeString(endTime);
  
                              let periodStr = this.getPeriodStr(startTime, endTime, this.onOff);
                              this.setState({
                                startTime: startTime,
                                endTime: endTime,
                                periodStr: periodStr,
                                detectionSwitch: this.onOff != 0
                              });
                              resolve(res);
                            } else {
                              reject(res);
                            }
                            
                          })
                          .catch((err) => {
                            reject(err);
                          });
                      });
                    } })
                  }
                  value={this.state.periodStr}
                />
                :
                null
            }

          </View>
          {this.state.detectionSwitch ? [timeItem] : []}
        </ScrollView>
        <MHPicker
          key={5}
          visible={this.state.isPickerVisiable}
          title={LocalizedStrings['ss_alarm_interval']}
          dataSource={['3', '5', '10', '30']}
          unit={strings.minuteUnit}
          currentValue={this.state.interval.toString()}
          onConfirm={(value) =>
            this._onIntervalChanged(value)
          }
          onDismiss={() =>
            this.setState({
              isPickerVisiable: false
            })
          }
        />
        {this._renderSensitivityDialog()}
      </View>
    );
  }

  extractHourAndMinute(timeStr) {
    let firstCommen = timeStr.indexOf(":");
    let lastCommen = timeStr.lastIndexOf(":");
    if (firstCommen != -1 && lastCommen != -1 && firstCommen != lastCommen && timeStr.length == 8) {
      return { hour: Number.parseInt(timeStr.substring(0, firstCommen)), minute: Number.parseInt(timeStr.substring(firstCommen + 1, lastCommen)) };
    }

  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings['s_survelillance_setting'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { 
            if (this.props.navigation.state.params.onGoBack) {
              this.handleGoAlarmList();
            } else {
              this.props.navigation.goBack();
            }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
      
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
    if (!Host.isAndroid) {// ios监听原生页面回到插件页面的事件通知
      this.appearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        this._onResume();
      });
    } else { // android监听activity onresume即可
      this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
        this._onResume();
      });
    }

    this.isPtz = CameraConfig.isPTZCamera(Device.model);
    if (this.isPtz) {
      // this._getSensitivitySetting();
    }
    this._getHumanPush();
  }

  // android返回键处理
  onBackHandler = () => {
    console.log("why!, survelillance onBackHandler");
    if (this.props.navigation.state.params.onGoBack) {
      this.handleGoAlarmList();
      return true;
    }
    return false;
  }

  handleGoAlarmList() {
    Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.props.navigation.state.params.vip), Device.did, true);
    this.isGoingAlarmList = true;
  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
  }

  _getSetting() {
    
    RPC.callMethod("getAlarmConfig", [])
      .then((res) => {
        let resultArray = res.result;
        console.log("getSetting: ", res.result);
        if (resultArray == null || resultArray.length < 6) {
          Toast.fail('c_get_fail');
        } else {
          let onOff = resultArray[0];
          this.onOff = Number.parseInt(onOff);
          let index = 0;
          if (VersionUtil.judgeIsV1(Device.model) && resultArray.length > 7) {
            // v1 老固件的第一次获取数据可能是7个元素，没有第二个强制参数1
            index = 1;
          }
          let startHour = Number.parseInt(resultArray[index + 1]);
          let startMinute = Number.parseInt(resultArray[index + 2]);
          let endHour = Number.parseInt(resultArray[index + 3]);
          let endMinute = Number.parseInt(resultArray[index + 4]);
          this.startHour = startHour;
          this.startMinute = startMinute;
          this.endHour = endHour;
          this.endMinute = endMinute;

          let pushSwitch = resultArray[index + 5] != 0;
          let interval = 5;
          let updateInterval = false;
          if (resultArray.length > 6) {
            interval = Number.parseInt(resultArray[6]);
          }
          if (VersionUtil.judgeIsV1(Device.model) && resultArray.length > 7) {
            interval = Number.parseInt(resultArray[7]);
          }
          if (![3, 5, 10, 30].includes(interval) || !interval) {
            interval = 5;
            updateInterval = true;
          }
          let startTime = `${ startHour > 9 ? startHour : `0${ startHour }` }` + `:${ startMinute > 9 ? startMinute : `0${ startMinute }` }` + `:00`;
          let endTime = `${ endHour > 9 ? endHour : `0${ endHour }` }` + `:${ endMinute > 9 ? endMinute : `0${ endMinute }` }` + `:00`;
          let periodStr = this.getPeriodStr(startTime, endTime, onOff);
          this.setState({
            detectionSwitch: onOff != 0,
            startTime: startTime,
            endTime: endTime,
            pushSwitch: pushSwitch,
            interval: interval,
            periodStr: periodStr
          }, () => {
            if (updateInterval) {
              this._onIntervalChanged(interval);
            }
          });
        }
      })
      .catch((err) => {
        Toast.fail('c_get_fail');

      });
    RPC.callMethod("get_motion_region", [])
      .then((result) => {
        let resultData = result.result;
        if (resultData == null || resultData.length != 32) {
          return;
        }
        let sensitivityValue = resultData[0];
        this.setState({ alarmSensitivity: sensitivityValue });
      })
      .catch((err) => {
        Toast.fail('c_get_fail');
      });

  }

  _syncSetting(param) {
    console.log("input param: ", param);
    if (VersionUtil.judgeIsV1(Device.model) && param.length < 8) {
      // https://jira.n.xiaomi.com/browse/MIIO-58573?filter=1029787 v1老固件需要8个参数，第二个是固定的1.
      // 本页面目前只有009 和 v1的老固件才会进入
      param.splice(1, 0, 1);
      console.log("input param for v1 old firmware: ", param);
    }
    let pressedTime = new Date().getTime();
    if (this.lastPressedTime != null && (pressedTime - this.lastPressedTime < 1000)) {
      return Promise.reject("click too fast");
    }
    return new Promise((resolve, reject) => {
    
      RPC.callMethod("setAlarmConfig", param)
        .then((result) => {
          resolve(result);
        })
        .catch((err) => {
          reject(err);
        });

    });
    
  }

  _onEnableValueChange(value) {
    Toast.loading('c_setting');
   
    Toast.loading('c_setting');
    let param = null;
    let startHour = 8;
    let startMinute = 0;
    let endHour = 20;
    let endMinute = 0;
    if (value) { // 只要重新打开就是默认的白天时间段
      param = [2, startHour, startMinute, endHour, endMinute, 1, this.state.interval];
    } else {
      param = [0, this.startHour, this.startMinute, this.endHour, this.endMinute, 0, this.state.interval];
    }

    this._syncSetting(param)
      .then((res) => {
        this._setHumanPush(value);
        let mSwitch = res.code == 0 ? value : !value;
        if (value) {
          let startTime = DAY_START_TIME;
          let endTime = DAY_END_TIME;
          let periodStr = LocalizedStrings["alarm_time_day"];
          this.setState({ detectionSwitch: mSwitch, periodStr: periodStr, pushSwitch: mSwitch, startTime: startTime, endTime: endTime });
        } else {
          this.setState({ detectionSwitch: mSwitch });
        }
        if (res.code == 0) {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
        // here should notify change;
      })
      .catch(() => {
        Toast.fail('c_set_fail');
      });

  }

  _onIntervalChanged(value) {
    try {
      let intValue = Number.parseInt(value);
      if (isNaN(intValue) || intValue == Infinity) {
        Toast.fail("c_set_fail", "value is Nan or infinity");
        return;
      }
    } catch (err) {
      Toast.fail("c_set_fail", err);
      return;
    }
    this.setState({ isPickerVisiable: false });

    Toast.loading('c_setting');
    let param = [this.onOff, this.startHour, this.startMinute, this.endHour, this.endMinute, this.state.pushSwitch ? 1 : 0, Number.parseInt(value)];
    this._syncSetting(param)
      .then((res) => {
        console.log(`why!, put modetion res: ${ JSON.stringify(res) }`);
        this.setState({ interval: res.code == 0 ? parseInt(value) : this.state.interval });
        if (res.code == 0) {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      })
      .catch((error) => {
        Toast.fail('c_set_fail');
      });
  }

  _onPushValueChange(value) {
    Toast.loading('c_setting');
    let param = [this.onOff, this.startHour, this.startMinute, this.endHour, this.endMinute, value ? 1 : 0, this.state.interval];
    this._syncSetting(param)
      .then((res) => {
        this.setState({ pushSwitch: res.code == 0 ? value : !value });
        if (res.code == 0) {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      })
      .catch(() => {
        Toast.fail('c_set_fail');

      });
    
  }


  _onSensitivitySetting() { // 灵敏度设置
    this.setState({ alarmLevelDialogVisible: true });
  }

  _renderSensitivityDialog() {
    let selectedIndex = this.state.alarmSensitivity <= SENSITIVE_LOW ? 1 : 0;
    this.selectedIndexArray = [selectedIndex];

    return (
      <ChoiceDialog
        style={{ width: 100 }}
        dialogStyle={{ itemSubtitleNumberOfLines: 2 }}
        useNewType={false}
        visible={this.state.alarmLevelDialogVisible}
        title={LocalizedStrings["alarm_sensitivity"]}
        options={[
          { title: LocalizedStrings["alarm_level_high_title"], subtitle: LocalizedStrings["alarm_level_high_desc"] },
          { title: LocalizedStrings["alarm_level_low_title"], subtitle: LocalizedStrings["alarm_level_low_desc"] }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ alarmLevelDialogVisible: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this._setSensitivitySetting(result);
          this.setState({ alarmLevelDialogVisible: false });
        }}
        buttons={[
        ]}
      />
    );
  }

  _setSensitivitySetting(result) {
    let sensitiveValue = (result == 0) ? SENSITIVE_HIGH : SENSITIVE_LOW; 
    let sensitiveArray = [];
    for (let i = 0; i < 32; ++i) {
      sensitiveArray.push(sensitiveValue);
    }
    
    RPC.callMethod("set_motion_region", sensitiveArray)
      .then((result) => {
        this.setState({ alarmSensitivity: sensitiveValue });
        Toast.success('c_set_success');
      })
      .catch((error) => {
        Toast.fail('c_set_fail', error);
      });
  }

  _setHumanPush(on) {
    on ?
      TrackUtil.reportResultEvent("MonitoringSetting_MovementPush_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("MonitoringSetting_MovementPush_Status", "type", 2);
    Toast.success("c_setting");
    let obj = {};
    obj.open = on;
    API.post("/miot/camera/app/v1/put/pedestrianDetectionPushSwitch", "business.smartcamera", obj)
      .then((result) => {
        Toast.success("c_set_success");
        console.log(result);
        this.setState({ isHumanPushOn: on });
      })
      .catch((error) => {
        Toast.success("c_set_fail");
        console.log(error);
        this.setState({ isHumanPushOn: this.state.isHumanPushOn });
      });
  }

  _getHumanPush() {
    API.get("/miot/camera/app/v1/get/alarmSwitch", "business.smartcamera", {})
      .then((result) => {
        let data = result.data;
        let isOpen = data.pedestrianDetectionPushSwitch;
        this.setState({ isHumanPushOn: isOpen });
      })
      .catch((error) => {

      });
  }



  getPeriodStr(startTime, endTime, onOff) {
    if (onOff == 1) {
      return LocalizedStrings["alarm_time_all"];
    }

    if ((startTime === (ALL_DAY_START_TIME)) && (endTime === (ALL_DAY_END_TIME) || endTime.includes(ALL_DAY_END_TIME_MIN))) {
      return LocalizedStrings["alarm_time_all"];
    } 
    // else if (this.compareTimeValue(startTime, ALL_DAY_START_TIME) && this.compareTimeValue(endTime, ALL_DAY_END_TIME)) {
    //   return LocalizedStrings["alarm_time_all"];
    // } 
    else if ((startTime === (DAY_START_TIME)) && (endTime === (DAY_END_TIME))) {
      return LocalizedStrings["alarm_time_day"];
    } else if ((this.compareTimeValue(startTime, DAY_START_TIME)) && this.compareTimeValue(endTime, DAY_END_TIME)) {
      return LocalizedStrings["alarm_time_day"];
    } else if ((startTime === (NIGHT_START_TIME)) && (endTime === (NIGHT_END_TIME))) {
      return LocalizedStrings["alarm_time_night"];
    } else if ((this.compareTimeValue(startTime, NIGHT_START_TIME)) && (this.compareTimeValue(endTime, NIGHT_END_TIME))) {
      return LocalizedStrings["alarm_time_night"];
    } else {
      return LocalizedStrings["alarm_time_user"];
    }
  }

  compareTimeValue(startTime, endTime) {
    let threeMinutes = 3 * 60 * 1000;

    let strArray1 = startTime.split(":");
    let strArray2 = endTime.split(":");
    let date = new Date();
    date.setHours(strArray1[0]);
    date.setMinutes(strArray1[1]);
    date.setSeconds(strArray1[2]);
    let timestamp1 = date.getTime();
    date.setHours(strArray2[0]);
    date.setMinutes(strArray2[1]);
    date.setSeconds(strArray2[2]);
    let timestamp2 = date.getTime();
    return Math.abs(timestamp1 - timestamp2) < threeMinutes;

  }
}
