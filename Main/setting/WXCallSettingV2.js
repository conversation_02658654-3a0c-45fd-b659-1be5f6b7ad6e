import React from 'react';
import {
  <PERSON><PERSON>View,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions, NativeModules
} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { AbstractDialog, ChoiceDialog, ImageButton, InputDialog, MessageDialog, NavigationBar } from 'mhui-rn';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import { Service, Host, Device, Entrance } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import API from '../API';
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import { styles } from "../setting/SettingStyles";
import AlarmUtilV2, {
  PIID_COUGH_SENSITIVITY,
  PIID_COUGH_SWITCH,
  PIID_CRY_SENSITIVITY,
  PIID_CRY_SWITCH,
  PIID_MOVE_SENSITIVITY,
  PIID_MOVE_SWITCH,
  PIID_PEOPLE_SWITCH,
  PIID_SOUND_SENSITIVITY,
  PIID_SOUND_SWITCH, SIID_AI_DETECTION, SIID_AI_SENSITIVITY
} from "../util/AlarmUtilV2";
import { Event } from '../config/base/CfgConst';
import Util from '../util2/Util';
import ListItemWithIcon from "../widget/ListItemWithIcon";
import ChoiceItem from "mhui-rn/dist/components/listItem/ChoiceItem";
import { BaseStyles } from "../BasePage";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import CallUtil from "../util/CallUtil";
import BaseSettingPage from "../BaseSettingPage";
import { RkButton } from "react-native-ui-kitten";
import { Styles } from "miot/resources";
import TrackUtil from "../util/TrackUtil";

const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

const TAG = "MotionDetectionPage";
const sensitiveOptions = [
  { title: LocalizedStrings['alarm_level_high_title'] },
  { title: LocalizedStrings['alarm_level_middle_title'] },
  { title: LocalizedStrings['alarm_level_low_title'] }
];

/**
 * @Author: byh
 * @Date: 2023/11/13
 * @explanation:
 * 微信音视频通话
 *********************************************************/
export default class WXCallSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.pageType = this.props.navigation.getParam("type");
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: false,
      clickSwitchValue: false,
      gestureSwitchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      showRenameDialog: false,
      showChoiceContactDialog: false,
      commentErr: null,
      oneClickSet: true,
      doubleClickSet: false,
      longClickSet: false,
      contactsData: [],
      showEmpowerDialog: false,
      showEmpowerTip: false,
      showServiceReminderDialog: false,
      contactIsFull: false
    };
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.titleStr = LocalizedStrings['detect_move'];
    this.detectionDesc = LocalizedStrings['ai_move_desc'];
    this.attentionDesc = LocalizedStrings['ai_note_attention'];
    this.topImageSrc = require("../../Resources/Images/wx_call_banner.webp");
  }
  getTitle(): string {
    return LocalizedStrings['cs_wx_call'];
  }

  componentDidMount() {
    super.componentDidMount();
    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });
    // 获取浮条状态
    StorageKeys.WX_EMPOWER_TIP.then((res) => {
      console.log("+++++++++++++++++++",res)
      let empTips = res;
      if (typeof (res) === "string" || res == "" || res == null || res == undefined) {
        empTips = false;
      }
      this.setState({ showEmpowerTip: !empTips });
    }).catch(() => {
      this.setState({ showEmpowerTip: true });
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });

    this._getSetting();
  }

  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }


  _getSetting() {
    DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(res => {
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = data;
          let stateProps = {};
          let contacts = [];
          if (data.hasOwnProperty("switch")) {
            stateProps.switchValue = data.switch.wx;
            stateProps.clickSwitchValue = data.switch.mijia;
            // stateProps.gestureSwitchValue = data.switch.hand;
          }

          if (data.hasOwnProperty("key1")) {
            let singleData = data.key1;
            contacts.push(singleData);
          }

          if (data.hasOwnProperty("key2")) {
            let doubleData = data.key2;
            contacts.push(doubleData);
          }

          if (data.hasOwnProperty("key3")) {
            let longData = data.key3;
            contacts.push(longData);
          }

          if (data.hasOwnProperty("hand1")) {
            let handData = data.hand1;
            contacts.push(handData);
          }
          // 先去重
          let hash = {};
          contacts = contacts.reduce((item:any, next:any)=>{
            // hash[next['mijia']] ? '' : hash[next['mijia']] = true && item.push(next);
            if (!hash[next['mijia']]) {
              hash[next['mijia']] = true;
              item.push(next)
            }
            return item;
          }, []);
          contacts.map((item, index) => {
            if (item.mijia == Service.account.ID) {
              contacts.unshift(contacts.splice(index, 1)[0]);
            }
          });

          stateProps.contactsData = contacts;
          this.setState(stateProps);
        }
      }
    }).catch(error => {
      console.log("======= error: ", error);
    });
  }

  _onSwitchValue(value) {
    if (!value) {
      //关闭的时候弹框提示
      this.updateCallSettingSwitch(value);
    } else if (!this.state.clickSwitchValue) {
    // } else if (!this.state.clickSwitchValue && !this.state.gestureSwitchValue) {
      // 开启微信视频通话开关 按键通话开关未打开
      this.setState({ showAIRequestDialog: true });
    } else {
      this.updateCallSettingSwitch(value);
    }
  }

  renderSettingContent() {
    let containerStyleAllRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleTopRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleBottomRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    return (
      <View style={ {
        display: "flex",
        height: "100%",
        flex: 1,
        flexDirection: "column",
        backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF',
        alignItems: "center"
      } }>
        <View key={ 102 }>

          <View style={ { alignItems: "center", marginHorizontal: 20, marginTop: 20 } }>
            <Image style={ { width: '100%', height: 200, borderRadius: 9 } }
                   source={ this.topImageSrc }/>
          </View>

          <View style={ stylesWX.white_blank }/>
          <Text style={ styles.desc_subtitle }>{ LocalizedStrings['ai_wx_call_desc'] }</Text>

          <View style={ stylesWX.whiteblank }/>


          <ListItemWithSwitch
            titleNumberOfLines={ 3 }
            unlimitedHeightEnable={ true }
            showSeparator={ false }
            title={ LocalizedStrings['voice_video_call'] }
            value={ this.state.switchValue }
            onValueChange={ (val) => this._onSwitchValue(val) }
            containerStyle={ { backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF', minHeight: 60 } }
            titleStyle={ { fontWeight: 'bold' } }
            accessibilitySwitch={ {
              accessibilityLabel: LocalizedStrings['care_screen_close_show_protect']
            } }
          />
          {
            this.state.switchValue ? <Text style={ {
              color: "#8C93B0",
              fontSize: 12,
              marginTop: 8,
              fontFamily: "MI Lan Pro",
              marginHorizontal: 27
            } }>{ LocalizedStrings['contacts'] }</Text> : null
          }

          { this._renderEmpowerTips() }
          {
            this.state.switchValue ?
              <FlatList
                data={ this.state.contactsData }
                style={ { marginTop: 15, paddingBottom: 70 } }
                renderItem={ this._renderContactItem }
                ItemSeparatorComponent={ () => <View style={ { height: 12 } }/> }
                keyExtractor={ (item, index) => `key_${ index }` }
              /> : null
          }
          {
            this.state.switchValue ?
              <View style={ {
                zIndex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                bottom: 30,
                left: 0,
                right: 0
              } } key={ 12 }>
                <TouchableOpacity
                  onPress={ () => {
                    this.setState({ showCancelEmpowerDialog: true });
                  } }
                >
                  <Text style={ {
                    color: '#32BAC0',
                    fontSize: 14,
                    fontWeight: '400',
                    textAlign: 'center',
                    textDecorationLine: 'underline',
                    paddingTop: 10,
                    paddingHorizontal: 10
                  } }>
                    { LocalizedStrings['cs_wx_cancel_invite'] }
                  </Text>
                </TouchableOpacity>
              </View> : null
          }

          {/*<View style={ { height: 30 } }/>*/ }

        </View>

        { this._renderRenameDialog() }
        { this._renderChoiceContactDialog() }
        { this._renderToEmpowerDialog() }
        { this._renderCancelEmpowerDialog() }
        { this._renderServerWarning() }
      </View>
    );
  }
  // renderSettingBottomContent() {
  //   return (
  //     <TouchableOpacity
  //       style={ { position: 'absolute', right: 0, bottom: 0 } }
  //       onPress={ () => {
  //         // this.onAddItem();
  //       } }>
  //       <Image style={ { width: 120, height: 120 } }
  //              source={ this.state.contactIsFull ? require("../../Resources/Images/icon_add_timer_new_dis.webp") : require("../../Resources/Images/icon_add_timer_new.webp") }/>
  //     </TouchableOpacity>
  //   )
  // }

  _renderContactItem = ({ item, index }) => {

    console.log("_renderDayItem", item);
    let isSelected = index === 0;
    let itemBgColor = isSelected ? "rgba(50, 186, 192, 0.10)" : Util.isDark() ? "#FFFFFFB2" : "#F0F0F0";
    let disabled = index === 1;
    let opacity = disabled ? { opacity: 0.5 } : { opacity: 1 };
    let nickname = item.nickname ? item.nickname : "";
    if (item.mijia == Service.account.ID) {
      nickname = `${ nickname }${ LocalizedStrings["call_me"] }`;
    }
    return (
      <ListItemWithIcon
        icon={ item.icon ? { uri: item.icon } : require("../../Resources/Images/icon_user.png") }
        iconStyle={ { width: 28, height: 28, borderRadius: 14 } }
        style={ {
          backgroundColor: "#F0F0F0",
          marginLeft: 12,
          borderRadius: 12,
          marginRight: 12,
          width: screenWidth - 24,
          alignSelf: "center"
        } }
        title={ nickname }
        sub_title={ item.mijia }
        value={ item.mijia == Service.account.ID ? LocalizedStrings['cs_empower'] : LocalizedStrings['cs_wx_invite_empower'] }
        subTitleStyle={ { fontSize: 13, color: "rgba(0, 0, 0, 0.60)" } }
        onPress={ () => {
          console.log("滴滴滴，哒哒哒");
          // 判断是否是当前账号授权，当前账号直接授权
          if (item.mijia == Service.account.ID) {
            // 本账号点击去授权，后续进入插件不再弹出授权引导
            StorageKeys.WX_WARNING_DIALOG = true;
            // 本账号直接授权
            let params = {
              paramType: "requestWxDeviceVoIP",
              did: Device.deviceID.toString(),
              userId: Service.account.ID.toString(),
              deviceName: Device.name,
              model: Device.model
            };
            Host.ui.requestWxDeviceVoIP(params);
          } else {
            // 其他账号，弹出邀请授权弹框
            this.inviteUser = item;
            this.setState({ showEmpowerDialog: true });
          }

        } }
      />
    );
  };

  _renderEmpowerTips = () => {
    if (!this.state.switchValue) {
      return null;
    }
    if (!this.state.showEmpowerTip) {
      return null;
    }

    let backgroundColor = Util.isDark() ? "#25A9AF32" : '#32BAC019';
    let channel = "videodetails_button";
    let tipsText = LocalizedStrings['cs_wx_call_empower_tips'];
    let tipsTextColor = Util.isDark() ? "#25A9AF" : "#32BAC0";

    let backIcon = require('../../Resources/Images/close_tips.png');
    let style = {
      flexDirection: "row",
      flexWrap: 'nowrap',
      alignItems: "center",
      justifyContent: "space-between",
      backgroundColor: backgroundColor,
      paddingLeft: 16,
      paddingRight: 8,
      paddingVertical: 11,
      borderRadius: 16
    };
    let mBgStyleBase = { alignItems: "center", justifyContent: "center" };
    let mBgStyle = [mBgStyleBase, {
      marginVertical: 10,
      backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF",
      marginHorizontal: 12,
      borderRadius: 16
    }];
    return (
      <View style={ mBgStyle }>
        <TouchableOpacity
          style={ style }

          ref={ (ref) => this.mCloudBuyTip = ref }
          onLayout={ (e) => {
            this.mCloudBuyTipWidth = e.nativeEvent.layout.width;
          } }
          // accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.kj_2_5 : DescriptionConstants.kj_2_30}
        >
          <Text
            numberOfLines={ 3 }
            style={ [BaseStyles.text14, {
              paddingRight: 0,
              color: tipsTextColor,
              textAlign: "left",
              textAlignVertical: 'center',
              flex: 1
            }] }>{ tipsText }</Text>
          <ImageButton
            onPress={ () => {
              StorageKeys.WX_EMPOWER_TIP = true;
              this.setState({ showEmpowerTip: false });
            } }
            style={ { width: 22, height: 22 } }
            source={ backIcon }
          />
        </TouchableOpacity>
      </View>
    );
  };

  _renderToEmpowerDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showEmpowerDialog }
        title={ LocalizedStrings['cs_invite_empower'] }
        subtitle={ LocalizedStrings['cs_invite_empower_desc'] }
        dialogStyle={ {
          titleStyle: {
            fontSize: 16.5
          }
        } }
        showSubtitle={ false }
        useNewTheme
        canDismiss={ false }
        onDismiss={ () => {
          this.setState({ showEmpowerDialog: false });
        } }
        buttons={ [
          {
            text: LocalizedStrings["cs_not_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
            }
          },
          {
            text: LocalizedStrings["cs_wx_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
              if (this.inviteUser) {
                let params = {
                  // paramType: "requestWxDeviceVoIP",
                  paramType: "shareWxDeviceVoIP",
                  did: Device.deviceID.toString(),
                  userId: `${ this.inviteUser.mijia }`,
                  deviceName: Device.name,
                  model: Device.model
                };
                console.log("======", params);
                // Host.ui.requestWxDeviceVoIP(params)
                Host.ui.shareWxDeviceVoIP(params);
              }
            }
          }
        ] }>
        <View>
          <Text style={ {
            fontSize: 16,
            fontWeight: "400",
            marginHorizontal: 28,
            lineHeight: 24
          } }>{ LocalizedStrings["cs_invite_empower_desc"] }</Text>

          <View style={{
            margin: 28,
            display: 'flex',
            flexDirection: "row",
          }}>
            <Image style={ {
              height: 170,
              borderRadius: 12,
              flex: 1
            } } source={ require("../../Resources/Images/wx_empower.webp") }/>
          </View>
        </View>
      </AbstractDialog>
    );
  }

  _renderServerWarning() {
    return (
      <MessageDialog
        visible={ this.state.showServiceReminderDialog }
        title={ LocalizedStrings['service_reminder'] }
        message={ LocalizedStrings['wx_server_warning'] }
        useNewType={ true }
        onDismiss={ () => {
          this.setState({ showServiceReminderDialog: false });
        } }
        buttons={ [{
          text: LocalizedStrings['offline_divice_ok'],
          colorType: 'grayLayerBlack',
          callback: () => {
            this.setState({ showServiceReminderDialog: false });
          }
        }] }/>
    );
  }


  _renderCancelEmpowerDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showCancelEmpowerDialog }
        title={ LocalizedStrings['cs_wx_cancel_invite_title'] }
        dialogStyle={ {
          titleStyle: {
            fontSize: 16.5
          }
        } }
        showSubtitle={ false }
        useNewTheme
        canDismiss={ false }
        onDismiss={ () => {
          this.setState({ showCancelEmpowerDialog: false });
        } }
        buttons={ [
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: 'grayLayerBlack',
            callback: (_) => {
              this.setState({ showCancelEmpowerDialog: false });
            }
          }
        ] }>
        <View style={{

          // flex: 1
        }}>
          <Text style={ {
            fontSize: 16,
            fontWeight: "400",
            marginHorizontal: 28,
            lineHeight: 24
          } }>{ LocalizedStrings["cs_wx_cancel_invite_msg"] }</Text>

          <View style={{
            margin: 28,
            display: 'flex',
            flexDirection: "row",
          }}>
            <Image style={ {
              height: 170,
              borderRadius: 12,
              flex: 1
            } }
                   source={ require("../../Resources/Images/pic_cancel_empower.webp") }/>
          </View>

        </View>
      </AbstractDialog>
    );
  }

  openAllCallSettingSwitch() {
    if (!this.callSettingData) {
      Toast.fail("c_set_fail");
      return;
    }

    let upData = JSON.parse(JSON.stringify(this.callSettingData));
    upData.switch.wx = 1;
    upData.switch.mijia = 1;
    upData.switch.hand = 1;
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData)).then((res) => {
      this.callSettingData = upData;
      CallUtil.updateSettingToDevice([JSON.stringify(upData)]);
      this.setState({ switchValue: true, clickSwitchValue: true, gestureSwitchValue: true, showServiceReminderDialog: true });
    }).catch((error) => {
      this.setState({ switchValue: false });
      Toast.fail("c_set_fail");
    });
  }

  updateCallSettingSwitch(value) {
    let upData = JSON.parse(JSON.stringify(this.callSettingData));
    upData.switch.wx = value ? 1 : 0;
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData)).then((res) => {
      this.callSettingData = upData;
      CallUtil.updateSettingToDevice([JSON.stringify(upData)]);
      this.setState({ switchValue: value, showServiceReminderDialog: value });
    }).catch((error) => {
      this.setState({ switchValue: !value });
      Toast.fail("c_set_fail");
    });
  }

  _renderRenameDialog() {
    return (
      <InputDialog
        visible={ this.state.showRenameDialog }
        title={ LocalizedStrings["ai_call_name"] }
        inputs={ [{
          placeholder: LocalizedStrings["please_enter_name"],
          defaultValue: this.phoneNum,
          textInputProps: {
            autoFocus: true
          },
          onChangeText: (text) => {
            if (this.state.commentErr != null) {
              this.setState({ commentErr: null });
            }
          },
          type: 'DELETE',
          isCorrect: this.state.commentErr == null
        }] }
        inputWarnText={ this.state.commentErr }
        noInputDisButton={ true }
        buttons={ [
          {
            // style: { color: 'lightpink' },
            callback: () => this.setState({ showRenameDialog: false, commentErr: null })
          },
          {
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`结果`, result.textInputArray[0]);
              // 检测手机号正则
              let isPhoneNumber = result.textInputArray[0].match(/^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/);
              let text = result.textInputArray[0].trim();
              if (text.length > 0 && !Util.containsEmoji(text)) {

                this.setState({ showRenameDialog: false, commentErr: null });
              } else {
                if (Util.containsEmoji(text)) {
                  this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
                }
              }
            }
          }
        ] }
        onDismiss={ () => this.setState({ showRenameDialog: false, commentErr: null }) }
      />
    );
  }

  _renderChoiceContactDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showChoiceContactDialog }
        useNewTheme={ true }
        title={ LocalizedStrings["edit_contact"] }
        dialogStyle={ {
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        } }
        onDismiss={ (_) => this.setState({ showChoiceContactDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showChoiceContactDialog: false });
            }
          }
        ] }
      >
        <View style={ {
          marginBottom: 16,
          marginTop: 10
        } }>
          { [{
            "title": LocalizedStrings['delete_contact'],
            onPress: () => {
              this.setState({ showChoiceContactDialog: false });
            }
          }, {
            "title": LocalizedStrings['replace_contact'],
            onPress: () => {
              this.setState({ showChoiceContactDialog: false });
              this.props.navigation.navigate("ChoiceContactPage", { type: 1 });
            }
          }].map((option, index) => <View key={ (option.title || '') + index }>
            <ChoiceItem
              type={ ChoiceItem.TYPE.SINGLE }
              titleStyle={ {
                fontSize: 16,
                fontWeight: "bold"
              } }
              title={ option.title || '' }
              onPress={ () => option.onPress() }/>
          </View>) }
        </View>
      </AbstractDialog>
    );
  }

  selectUser() {

    this.props.navigation.navigate("ChoiceContactPage", { type: 0 });
  }
}

const stylesWX = StyleSheet.create({
  white_blank: {
    height: 0.5,
    marginTop: 20
  },

  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 38

  }

});