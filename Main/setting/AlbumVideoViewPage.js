import React from 'react';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlbumHelper from '../util/AlbumHelper';
import Toast from '../components/Toast';
import {
  StyleSheet,
  View,
  Text,
  TouchableWithoutFeedback,
  TouchableOpacity,
  Image,
  BackHandler,
  StatusBar,
  Platform,
  SafeAreaView,
  ActivityIndicator,
  PixelRatio,
  DeviceEventEmitter,
  I18nManager
} from 'react-native';
import { Host, PackageEvent, DarkMode, Service } from "miot";
import { MessageDialog } from "miot/ui";
import LinearGradient from 'react-native-linear-gradient';
import Video from "react-native-video";
import ImageButton from "miot/ui/ImageButton";
import Slider from "react-native-slider";
import Orientation from 'react-native-orientation';
import { Dimensions } from "react-native";
import { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import CameraConfig from '../util/CameraConfig';
import { DescriptionConstants } from '../Constants';
import StatusBarUtil from '../util/StatusBarUtil';
import Util from '../util2/Util';
import { PermissionsAndroid } from 'react-native';
import CommonMsgDialog from '../ui/CommonMsgDialog';

const kIsCN = Util.isLanguageCN();
const iconSize = 40; // 图标尺寸
const TAG = "AlbumVideoViewPage";

export default class AlbumVideoViewPage extends React.Component {

  static navigationOptions = ({ navigation }) => {
    return {
      headerTransparent: true,
      header: null,
      headerShown: false,
      headerStyle: {
        height: 0
      }
    };
  };
  constructor(props) {
    super(props);
    this.dateTime = new Date();
    this.receiverMap = new Map();
    this.receiveUrlArray = [];
    this.deleteIndex = -1;
    this.dialogDeleteContent = LocalizedStrings["delete_title"];
    this.title = null;
    this.video = null;
    this.duration = 0;
    /*   this.dataMapValue = {
          path: '',
          name: ''
      } */
    this.state = {
      videoPath: null,
      // showLoadingDialog: true,
      deleteDialogVisible: false,
      isFullscreen: false,
      showPlayToolBar: true,
      startTimeStr: "",
      endTimeStr: "",
      progress: 0,
      isPlaying: false,
      playSpeed: 1.0,
      isMute: CameraConfig.getUnitMute(),
      showLoading: true,
      duration: 100,
      step: 1,
      isSeeking: false,
      showPermissionDialog: false
    };

    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true;
    this.mSeek = -1;
    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("will focus");
        this.restoreOri();
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.mRestorePreOrientationSwitch = false;

      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        console.log("did blur");

        this.isPageForeGround = false;

      }
    );


    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.props.navigation.isFocused()) {
        return;
      }

      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this.restoreOri();
      if (this.duration != 0) {
        this.setState({ isMute: this.state.playSpeed != 1 ? this.state.isMute : CameraConfig.getUnitMute(), isPlaying: this.isLocalPlaying });// here ignore
      }
      this.mRestorePreOrientationSwitch = false;

      // this.setState({ isMute: true, isPlaying: true });
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {

      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onpause
      this.isLocalPlaying = this.state.isPlaying;

      if (this.duration != 0) {
        this.setState({ isMute: true, isPlaying: false });
      }
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        if (this.duration != 0) {
          this.setState({ isMute: CameraConfig.getUnitMute(), isPlaying: this.isLocalPlaying });
        }
        this.mRestorePreOrientationSwitch = false;
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = false;// rnactivity调用了onpause
        this.isLocalPlaying = this.state.isPlaying;
        if (this.duration != 0) {
          this.setState({ isMute: true, isPlaying: false });
        }
      });
    }
    this.mOri = "PORTRAIT";
    this.destroyed = false;
    this.lastTimePlayBtnPressed = 0;
    if (Host.isAndroid) {
      Host.getPhoneScreenInfo()
        .then((result) => {
          this.displayCutoutTop = PixelRatio.roundToNearestPixel(result.displayCutoutTop / PixelRatio.get() || 0);
          if (isNaN(this.displayCutoutTop)) {
            this.displayCutoutTop = 0;
          }
          console.log(TAG, "result:", result);
        })
        .catch((error) => {

        });
    } else {
      this.displayCutoutTop = StatusBarUtil._getInset("top");
    }

    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    this.screenWidth = Math.min(winWidth, winHeight);
    this.videoHeight = this.screenWidth * 9 / 16;

  }


  componentDidMount() {
    console.log('this.props.navigation.state.params', this.props.navigation.state.params);
    Orientation.lockToPortrait();// 切换回竖屏
    // if(this.props.navigation.state.params.videoName){
    //   this.title = this.props.navigation.state.params.videoName
    // }
    if (this.props.navigation.state.params && this.props.navigation.state.params.url) {
      this.currentIndex = -1;
      this.mType = this.props.navigation.state.params.type;
      this.mUrl = this.props.navigation.state.params.url;
    } else if (this.props.navigation.state.params && this.props.navigation.state.params.index) {
      this.currentIndex = this.props.navigation.state.params.index;
    } else {
      this.currentIndex = 0;// 默认播放最后一个视频
    }

    if (this.props.navigation.state.params && this.props.navigation.state.params.item) {
      this.currentIndex = 0;
      let videoItem = this.props.navigation.state.params.item;
      this.title = AlbumHelper.getFileName(true, videoItem.modificationDate * 1000);
      this.setState({ currentIndex: this.currentIndex, videoPath: videoItem, isPlaying: true, showLoading: false });
      setTimeout(() => { this.setState({ isPlaying: false }); });
    } else {
      // 这是是从别得页面跳过来 然后找名字
      this.loadFiles();
    }

    if (Platform.OS === "android") {
      BackHandler.addEventListener('hardwareBackPress', this.onBack);
    }
    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);



    let colorScheme = DarkMode.getColorScheme();
    if (colorScheme == 'dark') {
      this.darkMode = true;
    } else {
      this.darkMode = false;
    }

  }

  componentWillUnmount() {
    this.destroyed = true;
    this.toPortrait();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBack);
    }
    Orientation.removeOrientationListener(this._orientationListener);
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
  }

  onBack = () => {
    if (this.state.isFullscreen) {
      this.toPortrait();
      return true;
    }
    this.mRestorePreOrientation();
    this.props.navigation.goBack();
    return true;
  }

  loadFiles() {
    AlbumHelper.checkPermission()
      .then(() => {
        AlbumHelper.getAlbumFiles()
          .then((result) => {
            // 肯定是array了
            let videoArray = result.filter((item) => item.mediaType == 2);// 过滤所有的视频
            let videoItem = null;
            if (videoArray.length == 0) {
              Toast._showToast(LocalizedStrings["action_failed"]);
              this.props.navigation.goBack();// 操作失败
              DeviceEventEmitter.emit('goBack');
              // 索引出错。
              return;
            }
            if (this.mUrl) {
              for (let i = 0; i < result.length; i++) {
                if (this.mUrl == result[i].url) {
                  this.currentIndex = i;
                  break;
                }
              }
            }

            if (this.currentIndex < 0) {
              this.currentIndex = 0;// 直接拿最后一个，从直播页或者其他页面跳过来的。
              videoItem = videoArray[this.currentIndex];
            } else {

              if (result.length <= this.currentIndex || result[this.currentIndex].mediaType != 2) {
                Toast._showToast(LocalizedStrings["action_failed"]);
                this.props.navigation.goBack();// 操作失败
                DeviceEventEmitter.emit('goBack');
                // 索引出错。
                return;
              }
              videoItem = result[this.currentIndex];
            }
            this.title = AlbumHelper.getFileName(true, videoItem.modificationDate * 1000);
            this.setState({ currentIndex: this.currentIndex, videoPath: videoItem, isPlaying: true });
            setTimeout(() => { this.setState({ isPlaying: false }); });//
            this.setState({ showLoading: false });
          })
          .catch((error) => {
            console.log(error);
            Toast._showToast(LocalizedStrings["action_failed"]);
            this.props.navigation.goBack();// 操作失败
            DeviceEventEmitter.emit('goBack');
          });
      })
      .catch((result) => {
        if (result == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          this.setState({ showPermissionDialog: true });
        } else {
          Toast.success("camera_no_write_permission");
        }
      });

  }


  deleteImgClick() {
    if (Platform.OS == "ios") {
      this.confirmDelete();
    } else {
      this.setState({
        deleteDialogVisible: true
      });
    }
  }

  shareImage() {
    // todo
    if (this.state.isFullscreen) {
      this.isToShare = true;
      this.toPortrait();
    } else {
      let path = this.state.videoPath.url;
      Host.ui.openSystemShareWindow(path);// 调用系统的分享界面。
    }

  }

  confirmDelete() {

    let url = this.state.videoPath != null ? this.state.videoPath.url : null;
    console.log("看看这里的url:",this.state.videoPath)
    if (url == null) {
      return;
    }

    AlbumHelper.checkPermission()
      .then(() => {

        AlbumHelper.deleteAlbumFilesByUrl([url])
          .then(() => {

            Toast.success("delete_success");

            // 仿照android的逻辑，点删除了 退出吧。。。相册流览图控件刷新数据很慢。。。。
            this.mRestorePreOrientation();
            // this.toPortrait()
            this.props.navigation.goBack();
            if (this.props && this.props.navigation.state.params.mCbDeleted) {
              this.props.navigation.state.params.mCbDeleted();
            }
          })
          .catch((error) => {
            // console.log(error);
            if (Platform.OS != "android") {
              return;
            }
            Toast.fail("delete_failed", error);
          });
      })
      .catch((result) => {
        if (result == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          this.setState({ showPermissionDialog: true });
        } else {
          if (Host.isAndroid) {
            Toast.success("camera_no_write_permission");
          }
        }
      });

  }

  onSlidedProgress(value) { // 0-duration
    console.log("=======onSlidedProgress",value,this.mSeek,this.state.progress);
    if (this.mSeek !== -1) {
      this.mSeek = -1;
      // 换算时间
      !this.destroyed && this.video && this.video.seek(value);// 拖拽过去
      this.setState({ progress: value, isSeeking: true },()=>{
        this.updateTimeStr();
      });
    } else {
      if (this.state.progress == 0) {
        this.updateTimeStr(true);
      }
    }
  }

  handleSeek(aPos) {
    console.log("=======handleSeek",aPos);
    this.mSeek = aPos;
    this.setState({ progress: aPos },() => {
      this.updateTimeStr();
    });
  }

  onProgress = (data) => {
    let progress = data.currentTime;


    let progressNum = Math.floor(progress);
    if (progress < 0 || progressNum > this.duration) {
      console.log(`invalid progress${ progress }`);
      return;
    }
    if (this.state.isSeeking) {
      return; // seek中时，不响应progress的改变
    }
    if (this.mSeek < 0) {
      this.setState({ progress: progressNum });
      this.updateTimeStr();
    }
    // this.setState({ currentTime: data.currentTime });
  };

  onError = (error) => { // 修改进度条
    // Toast.fail("action_failed", console.log(error));
    this.setState({ isPlaying: false });
    this.updateTimeStr();
  }

  onEnd = () => {
    this.setState({ isPlaying: false, progress: 0 });
    if (Platform.OS == "android") {
      !this.destroyed && this.video && this.video.seek(0);
    }
    this.updateTimeStr(true);
  }

  onAudioBecomingNoisy = () => {
    this.setState({ isPlaying: false });
  };

  onLoad = (info) => {
    let duration = info.duration;// seconds
    this.duration = Math.floor(duration);

    this.updateTimeStr();
    this.setState({ isPlaying: true, duration: this.duration });
  }

  onSeekComplete = (info) => {
    // 这里seek成功
    this.setState({ isSeeking: false });
  }
  formatTime = (millisecond)=>{
    let seconds = Math.round(millisecond);
    let result = [];
    let count = 2;
    while (count >= 0) {
      let current = Math.floor(seconds / (60 ** count));
      result.push(current);
      seconds -= current * (60 ** count);
      --count;
    }
    return result.map(item => item <= 9 ? `0${item}` : item).join(":");
  }
  updateTimeStr(isEnd = false) {
    let currentTime = Math.floor(this.state.progress);
    if (isEnd) {
      currentTime = 0;
    }
    let beginStr = this.formatTime(currentTime);
    let endStr = this.formatTime(this.duration);
    this.setState({ startTimeStr: beginStr, endTimeStr: endStr });
  }

  togglePlay(isPlay) {
    let cur = new Date().getTime();
    let mMinClickInterval = 1500;
    if (this.state.progress > 0) {
      mMinClickInterval = 500;
    }
    if (cur - this.lastTimePlayBtnPressed < mMinClickInterval) {
      return;
    }
    this.lastTimePlayBtnPressed = cur;
    if (Platform.OS == "ios" && this.state.progress == 0) {
      !this.destroyed && this.video && this.video.seek(0);
    }
    this.setState({ isPlaying: isPlay });
  }

  _toggleAudio(isMute) {

    this.setState({ isMute: isMute });
    CameraConfig.setUnitMute(isMute);
  }

  _toggleSpeed(speedRate) {

    this.setState({ playSpeed: speedRate });
    if (speedRate == 2.0) {
      this.setState({ isMute: true });
    } else {
      this.setState({ isMute: CameraConfig.getUnitMute() });
    }
  }

  render() {
    let bgColor = !this.state.isFullscreen ? "#ffffff" : "black";
    let path = (this.state.videoPath == null ? "" : this.state.videoPath.path);


    let imgStyle = {
      width: 25,
      height: 25
    };

    if (this.darkMode || this.state.isFullscreen) {
      imgStyle.tintColor = IMG_DARKMODE_TINT;
    }
    let buttonMarginTop = 10;
    return (

      <View style={{ display: "flex", flexDirection: "column", width: "100%", height: "100%", flexWrap: "nowrap", backgroundColor: bgColor }} >
        <SafeAreaView style={{ backgroundColor: bgColor }}></SafeAreaView>
        {this.renderTitleView()}
        <View style={[!this.state.isFullscreen ? { justifyContent: "center", position: "relative", flexGrow: 1, width: "100%" } : { position: "relative", height: "100%", width: "100%" }]}>
          <View style={{ display: "flex", flexDirection: "column", width: "100%", height: "100%", position: "absolute", alignItems: "center", justifyContent: "center" }}>

            
            <TouchableWithoutFeedback onPress={() => this.setState({ showPlayToolBar: !this.state.showPlayToolBar })}>
              <View style={[{ width: "100%" }, this.state.isFullscreen ? { height: "100%" } : { aspectRatio: 1920 / 1080 }]}>
                <Video
                  ref={(ref) => { this.video = ref; }}
                  source={{ uri: path }}
                  style={{ width: "100%", height: "100%" }}
                  paused={!this.state.isPlaying}
                  rate={this.state.playSpeed}
                  muted={this.state.isMute}
                  onProgress={this.onProgress}
                  onEnd={this.onEnd}
                  onError={this.onError}
                  repeat={false}
                  resizeMode={'contain'}
                  onLoad={this.onLoad}
                  ignoreSilentSwitch={"ignore"}
                  onSeek={this.onSeekComplete}
                  onPress={() => this.setState({ showPlayToolBar: !this.state.showPlayToolBar })}
                  onAudioBecomingNoisy={this.onAudioBecomingNoisy}
                />


                {this._renderVideoControlView()}
              </View>
            </TouchableWithoutFeedback>



          </View>


        </View>

        {this.renderHeaderControlBar(buttonMarginTop, imgStyle)}
        {this.renderDialog()}
        {this.renderLoadingView()}
        {this._renderPermissionDialog()}
        <SafeAreaView></SafeAreaView>
      </View>
    );
  }

  renderHeaderControlBar(buttonMarginTop, imgStyle) {
    let showBar = true;
    if (this.state.isFullscreen) {
      showBar = this.state.showPlayToolBar;
    }
    let useBlack = true;
    if (this.darkMode || this.state.isFullscreen) {
      useBlack = false;
    }
    let StatusBarheight = null;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight;
      if (StatusBarheight <= 0) {
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }
    return showBar ? (
      <LinearGradient
        colors={this.state.isFullscreen ? ['#00000099', '#00000000'] : ["#00000000", "#00000000"]}
        style={this.state.isFullscreen ? { position: "absolute",  display: "flex", flexDirection: "row", width: "100%", height: 69, justifyContent: "space-between" } : { width: "100%", height: 69, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" }}>

        {
          this.state.isFullscreen ? <TouchableOpacity
            style={{ width: 50, display: "flex", alignItems: "center", marginTop: (Host.isPad) ? 40 : buttonMarginTop, marginLeft: StatusBarheight }}
            onPress={() => { this.toPortrait(); }}>
            <Image
              style={{ width: 35, height: 35 }}
              source={require("../../Resources/Images/icon_back_black_nor_dark.png")}
              accessibilityLabel={DescriptionConstants.sd_1}
            />
          </TouchableOpacity> : null
        }
        <View style={{ display: "flex", flexDirection: 'row', marginRight: 30 }}>
          <TouchableOpacity
            accessibilityLabel={this.state.isFullscreen ? DescriptionConstants.lc_13 : null}
            style={{  display: "flex", alignItems: "center", marginTop: this.state.isFullscreen ? (Host.isPad) ? 40 : buttonMarginTop : 0 }}
            onPress={() => { this.shareImage(); }}>
            <Image
              style={imgStyle}
              source={useBlack ?
                require("../../Resources/Images/camera_icon_loc_pic_share.png") :
                require("../../Resources/Images/camera_icon_loc_pic_share_white.png")}
              // accessibilityLabel={DescriptionConstants.lc_13}
            />
            {
              this.state.isFullscreen ? null :
                <Text style={{ fontSize: kIsCN ? 11 : 9, color: 'black', marginTop: 3 }}>{LocalizedStrings['share_files']}</Text>
            }

          </TouchableOpacity>
          <TouchableOpacity
            accessibilityLabel={this.state.isFullscreen ? DescriptionConstants.lc_14 : null}
            style={{ marginLeft: 30,  display: "flex", alignItems: "center", marginTop: this.state.isFullscreen ? (Host.isPad ? 40 : buttonMarginTop) : 0 }}
            onPress={() => this.deleteImgClick()} >
            <Image
              style={imgStyle}
              source={useBlack ? require("../../Resources/Images/camera_icon_loc_pic_delete.png") : require("../../Resources/Images/camera_icon_loc_pic_delete_white.png")}
              // accessibilityLabel={DescriptionConstants.lc_14}
            />
            {
              this.state.isFullscreen ? null :
                <Text style={{ fontSize: kIsCN ? 11 : 9, color: 'black', marginTop: 3 }}>{LocalizedStrings['delete_files']}</Text>

            }
          </TouchableOpacity>
        </View>
      </LinearGradient>
    ) : null;
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);

    return (
      <CommonMsgDialog
        title={message}
        confirmText={LocalizedStrings["setting"]}
        cancelText={LocalizedStrings["action_cancle"]}
        onConfirmPress={(e) => {
          Host.ui.openTerminalDeviceSettingPage(1);
          this.setState({ showPermissionDialog: false });
        }}
        onCancelPress={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  renderTitleView() {
    // first change statusBar
    if (this.state.isFullscreen) {
      StatusBar.setBarStyle('light-content');
    } else {
      DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    }

    // second get statusBar height;
    let containerHeight = StatusBar.currentHeight || 0;
    containerHeight += 65;
    let statusBarHeight = StatusBar.currentHeight;

    const textContainerStyle = {
      flexGrow: 1,
      alignSelf: 'stretch', // 控制自己填充满父类的高度
      display: "flex",
      flexDirection: "column",
      justifyContent: 'center',
      alignItems: 'stretch', // 控制子类填充满本身的宽度
      marginHorizontal: 5
    };

    const titleTextStyle = {
      fontSize: kIsCN ? 16 : 14,
      // lineHeight: 22,
      fontFamily: 'D-DINCondensed-Bold',
      textAlignVertical: 'center',
      textAlign: 'center'
    };
    // const darkTitleColor = '#ffffff'; // 深色背景下标题颜色
    const lightTitleColor = '#000000'; // 浅色背景下标题颜色

    const titleColor = { color: lightTitleColor };
    if (this.state.isFullscreen) {
      return null;
    }

    let imgStyle = {
      width: iconSize,
      height: iconSize,
      position: "absolute"
    };
    if (this.darkMode) {
      imgStyle.tintColor = IMG_DARKMODE_TINT;
    }


    return (

      <View style={{ width: "100%", height: containerHeight, display: "flex", flexDirection: "row", flexWrap: "nowrap", alignItems: "center", paddingTop: statusBarHeight }}>
        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}
          
        >

          <ImageButton
            style={imgStyle}
            accessibilityLabel={DescriptionConstants.sd_1}
            source={require("../../Resources/Images/icon_back_black.png")}
            highlightedSource={require("../../Resources/Images/icon_back_black.png")}
            onPress={() => this.onBack()}
          />
        </View>

        <View style={textContainerStyle}
          // accessibilityLabel={DescriptionConstants.sz_8_27}
          >
          <Text
            numberOfLines={1}
            style={[titleTextStyle, titleColor]}
          >
            {this.title}
          </Text>

        </View>

        <View

          style={{ width: iconSize, height: iconSize, position: "relative" }}>
        </View>
      </View>
    );
  }

  renderDialog() {
    return (
      <MessageDialog
        visible={this.state.deleteDialogVisible}
        title={this.dialogDeleteContent}
        cancel={LocalizedStrings["action_cancle"]}
        confirm={LocalizedStrings["delete_confirm"]}
        onCancel={(e) => {
          console.log('onCancel', e);
        }}
        cancelable={true}
        onConfirm={(e) => {
          console.log('onConfirm', e);
          this.confirmDelete();
        }}
        onDismiss={() => {
          console.log('onDismiss');
          this.setState({ deleteDialogVisible: false });
        }}
      />
    );
  }

  _renderVideoControlView() {
    if (!this.state.showPlayToolBar) {
      return null;
    }

    const playIcons = [
      {
        source: require('../../Resources/Images/icon_camera_pause.png'),
        highlightedSource: null,
        onPress: () => { this.isUserPause = true; this.togglePlay(false); },
        accessibilityLabel:this.state.isPlaying? DescriptionConstants.lc_1:DescriptionConstants.lc_7
      },
      {
        source: require('../../Resources/Images/icon_camera_play.png'),
        highlightedSource: null,
        onPress: () => { this.isUserPause = false; this.togglePlay(true); }, // 开始播放
        accessibilityLabel: this.state.isPlaying? DescriptionConstants.lc_1:DescriptionConstants.lc_7
      }
    ];
    const audioIcons = [
      {
        source: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        onPress: () => this._toggleAudio(true),
        accessibilityLabel: this.state.isMute ? DescriptionConstants.lc_5 : DescriptionConstants.lc_11

      },
      {
        source: require('../../Resources/Images/icon_camera_mute_playback.png'),
        highlightedSource: require("../../Resources/Images/icon_camera_mute_playback.png"),
        onPress: () => this._toggleAudio(false), // 默认是这个状态
        accessibilityLabel: this.state.isMute ? DescriptionConstants.lc_5 : DescriptionConstants.lc_11
      },
      {
        source: require("../../Resources/Images/icon_camera_mute_playback_dis.png"),
        accessibilityLabel: this.state.isMute ? DescriptionConstants.lc_5 : DescriptionConstants.lc_11
      }
    ];

    const speedIcons = [
      {
        source: require('../../Resources/Images/playback_1x_nor.png'),
        highlightedSource: require('../../Resources/Images/playback_1x_nor.png'),
        onPress: () => this._toggleSpeed(2.0),
        accessibilityLabel: DescriptionConstants.lc_15.replace('1',1)

      },
      {
        source: require('../../Resources/Images/playback_2x_nor.png'),
        highlightedSource: require("../../Resources/Images/playback_2x_nor.png"),
        onPress: () => this._toggleSpeed(1.0), // 默认是这个状态
        accessibilityLabel: DescriptionConstants.lc_15.replace('1',2)
        
      }
    ];

    const fullScreenIcons = [
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        onPress: () => { this.toLandscape(); },
        accessibilityLabel: !this.state.isFullscreen ? DescriptionConstants.lc_6 : DescriptionConstants.lc_12
      },
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        onPress: () => { this.toPortrait(); },
        accessibilityLabel: !this.state.isFullscreen ? DescriptionConstants.lc_6 : DescriptionConstants.lc_12
      }
    ];
    let playIndex = this.state.isPlaying ? 0 : 1;
    // let audioIndex = this.state.isMute ? 1 : 0;
    let speedIndex = this.state.playSpeed == 1 ? 0 : 1;
    let audioIndex = speedIndex == 0 ? (this.state.isMute ? 1 : 0) : 2;


    let videoHeight = this.state.isFullscreen ? "100%" : this.videoHeight;

    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight;
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }

    return (
      // <View style={{width: "100%", height: videoHeight}}>
      <LinearGradient
        colors={['#00000000', '#00000099']}
        style={[{ bottom: 0, position: "absolute", width: "100%", height: 54 }]}>

        <View style={[{ display: "flex", flexDirection: "row", flexWrap: "nowrap", alignItems: "center", height: "100%", flex: 1 }, this.state.isFullscreen ? { paddingLeft: StatusBarheight, paddingRight: StatusBarheight } : null]}>

          <View style={styles.videoControlBarItem}>

            <ImageButton
              onPress={playIcons[playIndex].onPress}
              style={styles.videoControlBarItemImg}
              source={playIcons[playIndex].source}
              highlightedSource={playIcons[playIndex].highlightedSource}
              accessibilityLabel={playIcons[playIndex].accessibilityLabel}
              fadeDuration={0}
              accessibilityState={{
                selected: this.state.isPlaying
              }
              }
            />
          </View>
          
          <View style={{ flex: 1, flexDirection: "row", alignItems: "center" }}>
            <Text style={{ fontSize: kIsCN ? 10 : 8, color: "#ffffff", width: 60 }} accessibilityLabel={!this.state.isFullscreen ? DescriptionConstants.lc_2 : DescriptionConstants.lc_8}>
              {this.state.startTimeStr || "00:00"}
            </Text>

            <Slider
              style={{ flexGrow: 1, height: 30, marginLeft: 10, marginRight: 10 }}
              maximumValue={this.state.duration}
              minimumValue={0}
              step={1}
              minimumTrackTintColor={"#32BAC0"}
              maximumTrackTintColor={"rgba(255,255,255,0.2)"}
              value={this.state.progress}
              onSlidingComplete={(value) => this.onSlidedProgress(value)}
              onValueChange={(aPos) => this.handleSeek(aPos)}
              thumbTintColor={"#ffffff"}
              accessible={true}
              accessibilityLabel={!this.state.isFullscreen ? DescriptionConstants.lc_3 : DescriptionConstants.lc_9}
            />

            <Text style={{ fontSize: kIsCN ? 10 : 8, color: "#ffffff", width: 60 }}
              accessibilityLabel={!this.state.isFullscreen ? DescriptionConstants.lc_4 : DescriptionConstants.lc_10}
            >
              {this.state.endTimeStr || "00:00"}
            </Text>
          </View>



          <View style={styles.videoControlBarItem}>
            <ImageButton
              disabled={this.state.playSpeed == 2 ? true : false}
              onPress={audioIcons[audioIndex].onPress}
              style={styles.videoControlBarItemImg}
              source={audioIcons[audioIndex].source}
              highlightedSource={audioIcons[audioIndex].highlightedSource}
              accessibilityLabel={audioIcons[audioIndex].accessibilityLabel}
              fadeDuration={0}
              accessibilityState={{
                selected: this.state.isMute
              }
              }
            />
          </View>
          {/* 倍速 */}
          <View style={styles.videoControlBarItem}>
            <ImageButton
              onPress={speedIcons[speedIndex].onPress}
              style={styles.videoControlBarItemImg}
              source={speedIcons[speedIndex].source}
              highlightedSource={speedIcons[speedIndex].highlightedSource}
              accessibilityLabel={speedIcons[speedIndex].accessibilityLabel}
              fadeDuration={0}
            />
          </View>

          {
            this.state.isFullscreen ?
              null :
              <View style={styles.videoControlBarItem}>
                <ImageButton
                  onPress={fullScreenIcons[this.state.isFullscreen ? 1 : 0].onPress}
                  style={styles.videoControlBarItemImg}
                  source={fullScreenIcons[this.state.isFullscreen ? 1 : 0].source}
                  highlightedSource={fullScreenIcons[this.state.isFullscreen ? 1 : 0].highlightedSource}
                  accessibilityLabel={fullScreenIcons[this.state.isFullscreen ? 1 : 0].accessibilityLabel}
                />
              </View>
          }

        </View>
      </LinearGradient>
      // </View>
    );
  }

  restoreOri() {
    console.log(TAG, "restoreOri");

    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait() {
    if (this.mRestorePreOrientationSwitch) return;
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
  StatusBar.setHidden(false);
    
  }

  toLandscape() {
    StatusBar.setHidden(true);
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      if (Platform.OS == 'ios' && Host.isPad) {
        Service.miotcamera.enterFullscreenForPad(true);
      } else {
        Orientation.lockToLandscapeRight();
      }
    }
    StatusBar.setHidden(true);

  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPageForeGround || !this.isPluginForeGround || !this.isAppForeground || this.mRestorePreOrientationSwitch) {
      return;
    }
    console.log(TAG, `device orientation changed :${ orientation } want ${ this.mOri } fullscreen ${ this.state.isFullscreen }`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.isFullscreen) || (this.mOri === 'PORTRAIT' && !this.state.isFullscreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this.setState({ isFullscreen: true });
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(true);
        }
      } else {
        // do something with portrait layout
        this.setState((state) => { return { isFullscreen: false }; }, () => {
          if (this.isToShare) {
            let path = this.state.videoPath.url;
            Toast._hideLastToast();
            Host.ui.openSystemShareWindow(path);
            this.isToShare = false;
          }
        });
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(false);
        }
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  }

  renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}
        />
        <Text style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#000000" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }

  mRestorePreOrientation = () => {
    if (this.props.navigation.state.params.preOri) {
      this.mRestorePreOrientationSwitch = true;
      if (this.props.navigation.state.params.preOri == "landscape") {
        if (Platform.OS == 'android') {
          Orientation.lockToLandscape();
        } else {
          Orientation.lockToLandscapeRight();
        }
      } else {
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(false);
        }
        Orientation.lockToPortrait();
      }
    }
  }
}


const styles = StyleSheet.create({


  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    backgroundColor: '#FFF1',
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 20
  },
  videoControlBarFull: {
    backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    justifyContent: "flex-end"
  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },
  videoControlSeekbar: {
    flexGrow: 1,
    alignItems: "center"
  },

  videoControlBarItemImg: {
    width: 40,
    height: 40
  }

});