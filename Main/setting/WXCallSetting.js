import React from "react";
import {
  <PERSON>roll<PERSON>iew,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  NativeModules,
  TouchableHighlight,
  PermissionsAndroid, DeviceEventEmitter
} from "react-native";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { AbstractDialog, ChoiceDialog, ImageButton, InputDialog, MessageDialog, NavigationBar } from "mhui-rn";
import StorageKeys from "../StorageKeys";
import Toast from "../components/Toast";
import { Service, Host, Device, Entrance } from "miot";
import LogUtil from "../util/LogUtil";
import { PackageEvent } from "miot/Package";
import API from "../API";
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import { styles } from "../setting/SettingStyles";
import Util from "../util2/Util";
import ChoiceItem from "mhui-rn/dist/components/listItem/ChoiceItem";
import { BaseStyles } from "../BasePage";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import CallUtil, { CALL_TYPE } from "../util/CallUtil";
import BaseSettingPage from "../BaseSettingPage";

import FDSUtil from "../util/FDSUtil";
import PermissionUtil from "../util/PermissionUtil";
import ImagePicker from "react-native-image-picker";
import ParsedText from "react-native-parsed-text";
import CustomSwiper from "../components/CustomSwiper/CustomSwiper";
import { DarkMode } from "miot/Device";
import WXCallSettingMoreView from "./WXCallSettingMoreView";
import XiaoaiTTSUtil from "../util/XiaoaiTTSUtil";
import AvatarFileManager from "../util/AvatarFileManager";
import CameraPlayer from "../util/CameraPlayer";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

const viewWidth = Dimensions.get("window").width - 48;
const viewHeight = ((Dimensions.get("window").width - 48) * 9) / 16;


const TAG = "MotionDetectionPage";
const DEVICE_VERSION = '5.3.2_0601';
/**
 * @Author: byh
 * @Date: 2023/11/13
 * @explanation:
 * 微信音视频通话
 ******************************************************** */
export default class WXCallSetting extends React.Component {
  static navigationOptions = (navigation) => {
    return {// 不要导航条
      headerTransparent: true,
      header:
        null
    };
  };
  
  constructor(props, context) {
    super(props, context);
    this.pageType = this.props.navigation.getParam("type");
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: true,
      clickSwitchValue: false,
      gestureSwitchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      showRenameDialog: false,
      showEditContactDialog: false,
      showAvatarIntroDialog: false,
      commentErr: null,
      oneClickSet: true,
      doubleClickSet: false,
      longClickSet: false,
      contactsData: [],
      showEmpowerDialog: false,
      showOwnerEmpowerDialog: false,
      showEmpowerTip: false,
      showServiceReminderDialog: false,
      contactIsFull: false,
      showAvatarContactDialog: false,
      showDelDialog: false,
      showBarTitle: false,
      imageUrlArray: Util.isDark() ? [
        { props: { source: require("../../Resources/Images/wx_call_banner1_dark.png") }, width: viewWidth, height: viewHeight },
        { props: { source: require("../../Resources/Images/wx_call_banner2_dark.png") }, width: viewWidth, height: viewHeight }
      ] : [
        { props: { source: require("../../Resources/Images/wx_call_banner1.png") }, width: viewWidth, height: viewHeight },
        { props: { source: require("../../Resources/Images/wx_call_banner2.png") }, width: viewWidth, height: viewHeight }
      ],
      currentIndex: 0,
      updateData:false
    };
    this.callSettingData={}
    this.currentItem = null;
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.titleStr = LocalizedStrings["detect_move"];
    this.detectionDesc = LocalizedStrings["ai_move_desc"];
    this.attentionDesc = LocalizedStrings["ai_note_attention"];
    this.topImageSrc = require("../../Resources/Images/wx_call_banner.webp");

    // 添加主题图片下载完成的事件监听
    this.contactAvatarDownloadListener = DeviceEventEmitter.addListener(
      'contactAvatarDownloadComplete',
      (data) => {
        console.log('联系人头像图片下载完成:', data);
        if (data.success) {
          // 刷新UI或执行其他操作
          if (this.state.contactsData.length === 0) {
            return;
          }
          
          // 创建一个Promise数组，用于跟踪所有异步操作
          const promises = [];
          
          for (let i = 0; i < this.state.contactsData.length; ++i) {
            let item = this.state.contactsData[i];
            console.log("=====================00000000download");
            
            if (item.objName) {
              // 将每个异步操作包装成一个Promise
              const promise = FDSUtil.getLocalImgUrlNew(item.objName)
                .then(localFileName => {
                  return Host.file.isFileExists(localFileName)
                    .then(fileExist => {
                      if (fileExist) {
                        item.localFileName = localFileName;
                      }
                    });
                });
              
              promises.push(promise);
            }
          }
          
          // 等待所有Promise完成后再执行forceUpdate
          Promise.all(promises)
            .then(() => {
              this.forceUpdate();
            })
            .catch(error => {
              console.error("处理联系人头像时出错:", error);
              // 即使出错也尝试更新UI
              this.forceUpdate();
            });
        }
      }
    );
  }

  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    // let flag = y > 28;
    let flag = y > this.titleHeight;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.setState({ showBarTitle: true });
    } else {
      this.showTitle = false;
      this.setState({ showBarTitle: false });
    }
  };
  getTitle() {
    return LocalizedStrings["cs_wx_call"];
  }

  componentDidMount() {
    console.log(viewWidth, viewHeight, "viewWidth");
    // CallUtil.checkIsWarningToShowDialog(1).then((value) => {
    //   console.log("judgeToAddOwnerToSingleClickCall",value)
    //   if (value) {
    //     // 需要显示提醒弹框
    //     this.setState({ showOwnerEmpowerDialog: true });
    //   }
    // });
    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });
    // 获取浮条状态
    StorageKeys.WX_EMPOWER_TIP.then((res) => {
      console.log("+++++++++++++++++++", res);
      let empTips = res;
      if (typeof res === "string" || res == "" || res == null || res == undefined) {
        empTips = false;
      }
      this.setState({ showEmpowerTip: !empTips });
    }).catch(() => {
      this.setState({ showEmpowerTip: true });
    });

    this.willFocusSubscription = this.props.navigation.addListener("willFocus", () => {
   
      this._getSetting(true);
    });
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
      // // 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });
    
    
  }
  renderTitleBar() {
    // 自定义图标
    let titleBarContent = ({
      title: this.state.showBarTitle ? this.getTitle() : " ",
      type: NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      // right: showMoreSetting(Device.lastVersion, !Device.isReadonlyShared || Device.isFamily || Device.isOwner) ? [
      //   {
      //     key: NavigationBar.ICON.MORE,
      //     onPress: () => {
      //       this.props.navigation.push("WXCallSettingMore");
      //     }
      //   }
      // ] : [],
      titleStyle: {
        fontSize: 18,
        color: "#333333",
        fontWeight: 500
      }
    });
    return (
      <NavigationBar { ...titleBarContent } />
    );
  }
  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
    // 移除事件监听
    if (this.contactAvatarDownloadListener) {
      this.contactAvatarDownloadListener.remove();
    }
  }

  initSettingData(data, contact) {
    if (!data) {
      return;
    }
  
    
    const rawData = data;
    let initCallSettingData = rawData.call_setting ? JSON.parse(rawData.call_setting) : null;
    console.log(initCallSettingData, "initCallSettingData");
    const initKey = initCallSettingData?.onekeycalluser;
    const initId = initCallSettingData?.onekeycalluserid;
    // 只有有onekeycalluser，并且没有onekeycalluserid的时候才给他设置userid
    // 有初始化数据说明已经初始化一回了，这个时候找不到onekeycalluser就把值设置为null
    let onekeycalluser = initKey || "",
      onekeycalluserid = initId || '';
    // 这种情况说明已经初始化一回了  
    if (initKey && initId) {
      onekeycalluser = '';
      onekeycalluserid = '';
    }
    if (contact?.length) {
      onekeycalluser = '';
      onekeycalluserid = '';
      contact.forEach((item) => {
        // 有onekeycalluserid 说明已经初始化过了
        if (initId) {
          if (item?.key === initKey && item?.mijia === onekeycalluserid) {
            onekeycalluser = item?.key;
            onekeycalluserid = `${ item?.mijia }`;
          }
        }  
        if (initKey) {
          if (item?.key === initKey) {
            onekeycalluser = item?.key;
            onekeycalluserid = `${ item?.mijia }`;
          }
        }
       
      });
    }
    if (initCallSettingData) {
      initCallSettingData.switch = {
        hand: initCallSettingData?.switch?.hand ?? 0,
        mijia: initCallSettingData?.switch?.mijia ?? 1,
        wx: initCallSettingData?.switch?.wx ?? 1,
        autoanswer: initCallSettingData?.switch?.autoanswer,
        onekeycall: initCallSettingData?.switch?.onekeycall,
        callnametts: initCallSettingData?.switch?.callnametts ?? 0
      };
      initCallSettingData.onekeycalluser = onekeycalluser;
      initCallSettingData.onekeycalluserid = `${ onekeycalluserid }`;
    } else {
      initCallSettingData = {
        switch: {
          hand: 0,
          mijia: 1,
          wx: 1,
          autoanswer: Device.lastVersion > DEVICE_VERSION ? 0 : 1, // 封样版本以前的要打开，现在是关闭
          onekeycall: 1,
          callnametts: 1
        },
        onekeycalluser: 'key1',
        onekeycalluserid: ''
      };
    }
   
    let upData = initCallSettingData;
  
    
     CallUtil.sortByIndexNew(upData);
     console.log("upData", upData);
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData))
      .then(() => {
        CallUtil.updateSettingToDevice(JSON.stringify(upData))
          .then(() => {
          });
          
      }); 
      
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  _getSetting(checkDialog = false) {
    // DeviceSettingUtil.getDeviceSettingByPrefix("call_").then((res2) => {
    //   console.log("+++++++=getDeviceSettingByPrefix success",res2);
    // }).catch(err => {
    //   console.log("+++++++=getDeviceSettingByPrefix success",err);
    // });
    DeviceSettingUtil.getDeviceSettingByPrefix(DeviceSettingUtil.callPrefixKey)
      .then((res) => {
        if (res.code == 0) {
       
      
          LogUtil.logOnAll(TAG, "_getSetting",res.result.settings?JSON.stringify(res.result.settings): JSON.stringify(res));
          let settingsData = res.result.settings;
        
          if (settingsData && settingsData.call_setting) {
            let data = JSON.parse(settingsData.call_setting);
            this.callSettingData = JSON.parse(JSON.stringify(data));
            console.log("callSettingData22222",data,this.state.updateData);
          
            // this.setState({updateData:!this.state.updateData},()=>{
            //   console.log("callSettingData1111111",this.callSettingData);
              
            // })
            let stateProps = {};
            let contacts = [];
            let callnametts = 0;
            if (data.hasOwnProperty("switch")) {
              stateProps.switchValue = data.switch.mijia;
              stateProps.clickSwitchValue = data.switch.mijia;
              callnametts = data.switch?.callnametts ?? 0;
              // stateProps.gestureSwitchValue = data.switch.hand;
            }

            Object.keys(data).forEach((key) => {
              if (key.indexOf("key") != -1 && key !== 'onekeycalluser' && key !== 'onekeycalluserid') {
                let item = data[key];
                item.key = key;
                item.index = item.index || 0;
                contacts.push(item);
              }
            });

            // 取头像
            Object.keys(settingsData).forEach((key) => {
              console.log("{{{{{{{{{{{{{{{{", key);
              if (key.indexOf("call_key") != -1) {
                // 图片后缀key,需要匹配到联系人中去
                let iconSuffixKey = key.substring(key.lastIndexOf("_") + 1);
                let iconData = JSON.parse(settingsData[key]);
                console.log("{{{{{{{{{{{{{{{{", iconSuffixKey, iconData, typeof iconData);
                contacts.map((item) => {
                  if (item.key === iconSuffixKey) {
                    console.log(iconData.fdsUrl, "iconData");
                    
                    item.icon = iconData.icon;
                    item.fdsUrl = iconData.fdsUrl ? iconData.fdsUrl : "";
                    item.objName = iconData.objName ? iconData.objName : "";
                    item.callNameTts = iconData.callNameTts ? iconData.callNameTts : "";
                    iconData.callName = item.callName;
                    return item;
                  }
                  return item;
                });
              }
            });

            // 先去重
            let hash = {};
            contacts = contacts.reduce((item, next) => {
              // hash[next['mijia']] ? '' : ((hash[next['mijia']] = true) && item.push(next));
              if (!hash[next["mijia"]]) {
                hash[next["mijia"]] = true;
                item.push(next);
              }
              return item;
            }, []);
            // 按index排序 升序
           
            contacts.sort((a, b) => a.index - b.index);
            console.log(contacts, '是否授权11');
            const newList = [];
            let needUploadTts = [];
            contacts.forEach((item) => {
             
              if (item.mijia == Service.account.ID) {
                console.log(item, '是否授权');
                newList.unshift(item);
                // contacts.unshift(contacts.splice(index, 1)[0]);
              } else {
                newList.push(item);
              }
             
              
              // 只有开关打开
              if (callnametts === 1 && XiaoaiTTSUtil.isSupportedContactsTts()) {
            
                let iconKey = "call_" + item.key;
                let iconData = JSON.parse(settingsData[iconKey]);
                iconData.key = item.key;
          
                if (!iconData?.callNameTts && iconData?.callName) {
                 
                  needUploadTts.push(iconData);
                }
              }
            });
        
            stateProps.contactsData = newList;
          
            this.setState(stateProps, () => {
              this.downloadImage();
            });
            needUploadTts.forEach((item, index) => {
              // 1. 先获取TTS
              Service.xiaoai.callXiaoaiTTS({ text: item.callName })
                .then((res) => {
                  console.log(`getXiaoaiTTS wxcallsettings callname success ${JSON.stringify(res)}`);
                  let { data: { url } } = res;
                  let iconUpData = JSON.parse(JSON.stringify(item));
                  iconUpData.callNameTts = url;

                  let contactKey = `call_${iconUpData.key}`;
                  delete iconUpData.key;

                  let updateIconObj = { [contactKey]: iconUpData };
                  let updateIconParams = JSON.stringify(updateIconObj);
                  CallUtil.updateIconSettingToDevice(updateIconParams)
                    .then(() => {
                      // 再更新头像设置
                      return DeviceSettingUtil.setDeviceSetting(contactKey, JSON.stringify(iconUpData));
                    })
                    .then(() => {
                      // 更新本地状态
                      stateProps.contactsData[index].callNameTts = url;
                      this.setState(stateProps, () => {

                      })
                      console.log("补传联系人tts 成功");

                    })
                    .catch((error) => {
                      Toast.fail("补传联系人tts 失败");
                    });
                }).catch((err) => {
                console.log(`getXiaoaiTTS callname fail ${err}`);
              });
            });
            if (checkDialog) {
              console.log("进来了");
              
              this.initSettingData(settingsData, contacts);
              this.checkDialogShouldShow()
                .then((value) => {
                  console.log("=======================dialog", value);
                  if (value) {
                    // 需要显示提醒弹框
                    // this.setState({ showOwnerEmpowerDialog: true });
                  }
                })
                .catch((err) => {});
            }
          
          } else {
            this.initSettingData(settingsData, []);
          }
         
        }
      })
      .catch((error) => {
        console.log("======= error: ", error);
      });
  }

  checkDialogShouldShow() {
    // 获取本人是否有微信id，进而来判断是否取消授权
    console.log("Service.account.ID", Service.account.ID);
    
    return CallUtil.getOpenIdByUid(Service.account.ID.toString());
    // return new Promise((resolve, reject) => {
    //   CallUtil.shouldShowWarningDialog(settingsData, 1, resolve, reject);
    // });
  }
 
  updateUserInfo(index, data, isDel) {
    // 说明是删除联系人
    if (isDel) {
      this.state.contactsData.splice(index, 1);
      this.setState({ contactsData: [...this.state.contactsData] });
      return;
    }
    const list = this.state.contactsData;
    list[index] = JSON.parse(data);
    this.setState({ contactsData: [...this.state.contactsData] });
  }

  async downloadImage() {
    if (this.state.contactsData.length == 0) {
      return;
    }
    // 检查自定义头像本地是否存在
    let needGetAvatar = [];
    for (let i = 0; i < this.state.contactsData.length; ++i) {
      try {
        let item = this.state.contactsData[i];
        console.log("=====================00000000download");
        if (item.objName) {
          if (XiaoaiTTSUtil.CURRENT_VERSION >= XiaoaiTTSUtil.MIN_CONTACTS_AVATAR_FW_VERSION) {
            const localFileName = await FDSUtil.getLocalImgUrlNew(item.objName);
            let fileExist = await Host.file.isFileExists(localFileName);
            if (fileExist) {
              item.localFileName = localFileName;
              this.forceUpdate();
            } else {
              let needDownloadItem = {
                key: item.key,
                localFileName: localFileName,
                objName: item.objName,
              };
              needGetAvatar.push(needDownloadItem);
            }
          } else {
            item.localFileName = await FDSUtil.getLocalImgUrl(item.objName);
            this.forceUpdate();
          }

        }
      } catch (err) {
        console.log("download fds error", err);
        LogUtil.logOnAll(TAG, `download fds Imag ${ JSON.stringify(err) }`);
      }
    }
    // 从固件拉取头像数据
    if (needGetAvatar.length > 0 && XiaoaiTTSUtil.CURRENT_VERSION >= XiaoaiTTSUtil.MIN_CONTACTS_AVATAR_FW_VERSION) {
      if (CameraPlayer.getInstance().isConnected()) {
        AvatarFileManager.getInstance().startRequestContactsImgData(needGetAvatar);
      }
    }
    // this.setState({});
  }
  render() {
   
    return (
      <View style={stylesWX.container}>
        { this.renderTitleBar() }
        <ScrollView style={{ backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF' }} showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" onScroll={this.scrollViewScroll} scrollEventThrottle={10} contentContainerStyle={[{ paddingBottom: 40, flexGrow: 1 }, {}]}>
        
          <View style={{ flexDirection: "row", flexWrap: "wrap", backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF' }} key={0} onLayout={({ nativeEvent: { layout: { height } } }) => {
            this.titleHeight = height - 30;
          }}>
            <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23, fontFamily: 'MI-LANTING--GBK1-Light' }}>
              {this.getTitle()}
            </Text>
          </View>
          {this.renderSettingContent()}
        </ScrollView>
        {this.renderSettingBottomContent()}
       
      </View>
    );
  }
  renderSettingContent() {
    // let containerStyleAllRadius = {
    //   backgroundColor: "#ffffff",
    //   marginLeft: 12,
    //   marginRight: 12,
    //   marginTop: 12,
    //   width: screenWidth - 24,
    //   borderBottomLeftRadius: 16,
    //   borderBottomRightRadius: 16,
    //   borderTopLeftRadius: 16,
    //   borderTopRightRadius: 16,
    //   paddingLeft: 20,
    //   height: 70,
    // };

    // let containerStyleTopRadius = {
    //   backgroundColor: "#ffffff",
    //   marginLeft: 12,
    //   marginRight: 12,
    //   marginTop: 12,
    //   width: screenWidth - 24,
    //   borderTopLeftRadius: 16,
    //   borderTopRightRadius: 16,
    //   paddingLeft: 20,
    //   height: 70,
    // };

    // let containerStyleBottomRadius = {
    //   backgroundColor: "#ffffff",
    //   marginLeft: 12,
    //   marginRight: 12,
    //   width: screenWidth - 24,
    //   borderBottomLeftRadius: 16,
    //   borderBottomRightRadius: 16,
    //   paddingLeft: 20,
    //   height: 70,
    // };
    // let imageUrlArray = [{ props: {source: require('../../Resources/Images/wx_call_banner.webp')},width: viewWidth, height: viewHeight },{ props: {source: require('../../Resources/Images/wx_call_banner_dark.webp')}, width: viewWidth, height: viewHeight }]
    // console.log("[[[[[[[[[[[[",imageUrlArray);
    
    return (
      <View
        style={{
          display: "flex",
      
          flex: 1,
          flexDirection: "column",
          backgroundColor: Util.isDark() ? "#xm000000" : "#FFFFFF",
          // backgroundColor: Util.isDark() ? "#xm000000" : "#FFFFFF",
          alignItems: "center"
          // marginBottom: 0
        }}
      >
        <View key={102} style={{ flex: 1 }}>
          {/* <View style={{ alignItems: "center", marginHorizontal: 24 }}>
            <Image
              style={{ width: "100%", height: viewHeight, borderRadius: 9 }}
              source={Util.isDark() ? require("../../Resources/Images/wx_call_banner_dark.webp") : require("../../Resources/Images/wx_call_banner.webp")}
            />
          </View> */}
          
          <CustomSwiper
            containerStyle={stylesWX.wrapper}
            horizontal={true}
            loop={true}
            autoplay={true}
            height={viewHeight}
            showsPagination={true}
            paginationStyle={{ bottom: -20 }}
            removeClippedSubviews={true}
            showsButtons={false}
            autoplayTimeout={3}
            activeDotColor={"rgba(50, 186, 192, 1)"}
            dotColor={Util.isDark() ? "xmrgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.1)"}
          >
            {this.state.imageUrlArray.map((item, index) => {
              return (
                <TouchableOpacity style={stylesWX.slide} key={index}>
                  <Image key={index} style={stylesWX.bannerImage} source={item?.props?.source}></Image>
                </TouchableOpacity>
              );
            })}
          </CustomSwiper> 

          {/* <ImageViewer */}
          {/*  ref={(ref) => this.imageViewer = ref} */}
          {/*  //  accessibilityLabel={DescriptionConstants.sz_8_19} */}
          {/*  // style={{ flex: 1 }} */}
          {/*  style={{  width: viewWidth, height: viewHeight, marginHorizontal: 24 }} */}
          {/*  backgroundColor={ Util.isDark() ? "#xm000000" : '#FFFFFF'} */}
          {/*  imageUrls={this.state.imageUrlArray} */}
          {/*  key={this.state.imageUrlArray} */}
          {/*  index={this.state.currentIndex} */}
          {/*  doubleClickInterval={300} */}
          {/*  saveToLocalByLongPress={false} */}
          {/*  loadingRender={() => { */}
          {/*    return <ActivityIndicator size="large" color={"black"} />; */}
          {/*  }} */}
          {/*  onMove={(data) => { */}
          {/*    console.log("{{{{{{{{{{{",data); */}
          {/*  }} */}
          {/*  responderRelease={()=>{ */}
          {/*    console.log("{{{{{{{{{{{responderRelease"); */}
          {/*  }} */}
          {/*  horizontalOuterRangeOffset={()=>{ */}
          {/*  console.log("{{{{{{{{{{{horizontalOuterRangeOffset"); */}

          {/*  }} */}
          {/*  // onChange={this._onChange.bind(this)} */}
          {/*  // onClick={this.mMediaTypeIsVideo ? this.play.bind(this) : null} */}
          {/*  renderImage={(prop) => { */}
          {/*    console.log("--------------1",prop); */}
          {/*    prop.style = { width: '100%', height: viewHeight, borderRadius: 9 }; */}
          {/*    console.log("--------------2",prop); */}
          {/*    return ( */}

          {/*      <Image */}
          {/*        {...prop} */}
          {/*        // style={{ width: '100%', height: viewHeight, borderRadius: 9 }} */}
          {/*        resizeMode={"contain"} */}
          {/*      /> */}
          {/*    ); */}

          {/*  }} */}
          {/*  renderIndicator={(index, count) => { */}
          {/*    console.log("render indicator:", index, count); */}
          {/*    return React.createElement( */}
          {/*      View, */}
          {/*      { style: { position: 'absolute', */}
          {/*          left: 0, */}
          {/*          right: 0, */}
          {/*          top: 38, */}
          {/*          zIndex: 13, */}
          {/*          justifyContent: 'center', */}
          {/*          alignItems: 'center', */}
          {/*          backgroundColor: 'transparent' } }, */}
          {/*      React.createElement(Text, { style: { color: 'black', */}
          {/*          fontSize: 14, */}
          {/*          backgroundColor: 'transparent', */}
          {/*          textShadowColor: 'rgba(0, 0, 0, 0.3)', */}
          {/*          textShadowOffset: { */}
          {/*            width: 0, */}
          {/*            height: 0.5 */}
          {/*          }, */}
          {/*          textShadowRadius: 0 } }, `${ index }/${ count }`) */}
          {/*    ); */}
          {/*  }} */}

          {/* /> */}
          <ParsedText
            style={[styles.desc_subtitle, { fontWeight: "400", paddingHorizontal: 28, marginTop: 20 }]}
            parse={[
              { pattern: /\%(.+?)\%/g, style: { color: "#333", fontWeight: "bold" }, renderText: this.renderText }
             
            ]}
            childrenProps={{ allowFontScaling: false }}
          >
            {LocalizedStrings["ai_wx_call_desc_v2"]}
          </ParsedText>
          <ParsedText
            style={[styles.desc_subtitle, { fontWeight: "400", paddingHorizontal: 28, marginTop: 20 }]}
            parse={[
              {
                pattern: /\[1(.+?)\]/g,
                style: { color: "#32BAC0", textDecorationLine: "underline" },
                onPress: () => {
                  this.setState({ showServiceReminderDialog: true });
                },
                renderText: this.renderText1
              },
              { pattern: /\%(.+?)\%/g, style: { color: "#333", fontWeight: "bold" }, renderText: this.renderText }
            ]}
            childrenProps={{ allowFontScaling: false }}
          >
            {LocalizedStrings["ai_wx_call_desc_v4"]}
          </ParsedText>
          <View style={stylesWX.whiteblank} />

          {/*   {this.state.switchValue ? (
           
          ) : null} */}
        </View>
        {/*   {this.state.contactsData && this.state.contactsData.length > 0 ? this._renderEmpowerTips() : null} */}  
        
        <View style={{ justifyContent: 'flex-start', flex: 1 }}>
        
          <Text
            style={{
              color: "#8C93B0",
              fontSize: 12,
              marginTop: 8,
              fontFamily: "MI Lan Pro",
              marginHorizontal: 27,
              marginBottom: 8
              
            }}
          >
            {LocalizedStrings["wx_call"]}
            {LocalizedStrings["contacts"]}
          </Text>
         
          <FlatList
            data={this.state.contactsData}
            style={{ marginTop: 10, paddingBottom: 12 }}
            renderItem={this._renderContactItem}
            ListEmptyComponent={this._renderEmptyView}
            ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
            keyExtractor={(item, index) => `key_${ item?.mijia }`}
          />
          {showMoreSetting(XiaoaiTTSUtil.CURRENT_VERSION, !Device.isReadonlyShared || Device.isFamily || Device.isOwner) ?
            <View style={{ justifyContent: 'flex-start', flex: 1 }}>
              <View style={stylesWX.whiteblank} />
              <Text
                style={{
                  color: "#8C93B0",
                  fontSize: 12,
                  marginTop: 8,
                  fontFamily: "MI Lan Pro",
                  marginHorizontal: 27,
                  marginBottom: 8
              
                }}
              >
                {LocalizedStrings["more_setting"]}
              </Text>
              <WXCallSettingMoreView props={this.props} updateSetting={this._getSetting.bind(this)}/> 
            </View> : null}  
         
        </View>  
        {/* { this.state.contactsData.length > 0 ? this._renderBottomItem() : null } */}

        {/* <View style={ { height: 120 } }/> */}
      
     
      
        {this._renderToEmpowerDialog()}
        {this._renderCancelEmpowerDialog()}
        {this._renderServerWarning()}
        {/*
        {this._renderRenameDialog()}
        {this._renderEditContactDialog()}
        {this._renderAvatarIntroDialog()}
        {this._renderChooseAvatarDialog()}
        {this.renderDeleteDialog()} */}
        {this._renderOwnerToEmpowerDialog()}
        {this._renderPermissionDialog()}
     
      </View>
    );
  }

  _renderOwnerToEmpowerDialog() {
    return (
      <AbstractDialog
        visible={this.state.showOwnerEmpowerDialog}
        title={LocalizedStrings["cs_invite_empower_warning"]}
        subtitle={LocalizedStrings["cs_invite_empower_warning_desc"]}
        dialogStyle={{
          titleStyle: {
            fontSize: 16.5
          }
        }}
        showSubtitle={false}
        useNewTheme
        canDismiss={false}
        onDismiss={() => {
          this.setState({ showOwnerEmpowerDialog: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            style: Util.isLanguageCN() ? null : { fontSize: 12 },
            callback: (_) => {
              StorageKeys.WX_WARNING_DIALOG = true;
              this.setState({ showOwnerEmpowerDialog: false });
            }
          },
          {
            text: LocalizedStrings["cs_wx_empower"],
            style: Util.isLanguageCN() ? null : { fontSize: 12 },
            callback: (_) => {
              // 标记，我已经弹出过，下次正常进插件就不会再弹出来
              StorageKeys.WX_WARNING_DIALOG = true;
              StorageKeys.WX_WARNING_LIST_DIALOG = true;
              this.setState({ showOwnerEmpowerDialog: false });
              let params = {
                paramType: "requestWxDeviceVoIP",
                did: Device.deviceID.toString(),
                userId: Service.account.ID.toString(),
                deviceName: Device.name,
                model: Device.model
              };
              Host.ui.requestWxDeviceVoIP(params);
            }
          }
        ]}
      >
        <View>
          <Text
            style={{
              fontSize: 16,
              fontWeight: "400",
              marginHorizontal: 28,
              lineHeight: 24
            }}
          >
            {LocalizedStrings["cs_invite_empower_warning_desc"]}
          </Text>

          <View
            style={{
              margin: 28,
              display: "flex",
              flexDirection: "row"
            }}
          >
            <Image
              style={{
                height: 170,
                borderRadius: 12,
                flex: 1
              }}
              source={require("../../Resources/Images/wx_empower.webp")}
            />
          </View>
        </View>
      </AbstractDialog>
    );
  }

  renderSettingBottomContent() {
    if (!Device.isOwner) {
      // 共享用户不显示添加按钮
      return null;
    }

    const disabled = this.state.contactsData && this.state.contactsData.length >= 10;
    return (
      <TouchableOpacity
        style={{ position: "absolute", right: 0, bottom: 42 }}
        pointerEvents="none"
        onPress={() => {
          // this.onAddItem();
          if (disabled) {
            Toast.fail("add_contact_already_max");
            return;
          } 
          this.props.navigation.navigate("ChoiceContactPage", {
            contactsData: this.state.contactsData,
            callback: (data) => {
              this._getSetting();
            }
          });
        }}
      >
        <Image
          style={{ width: 128, height: 128 }}
          source={disabled ? require("../../Resources/Images/icon_add_timer_new_dis.webp") : require("../../Resources/Images/icon_add_timer_new.webp")}
        />
      </TouchableOpacity>
    );
  }

  _renderContactItem = ({ item, index }) => {
    console.log("_renderDayItem", item);
    let isSelected = index === 0;
    let itemBgColor = isSelected ? "rgba(50, 186, 192, 0.10)" : Util.isDark() ? "#FFFFFFB2" : "#F0F0F0";
    let disabled = index === 1;
    let opacity = disabled ? { opacity: 0.5 } : { opacity: 1 };
    let nickname = item.callName ? item.callName : "";
  
    if (item.mijia == Service.account.ID) {
      nickname = `${ nickname }${ LocalizedStrings["call_me"] }`;
    }

    let icon = null;
    if (item.objName && item.localFileName) {
      icon = `file://${ Host.file.storageBasePath }/${ item.localFileName }`;
    } else if (item.icon?.indexOf("http") < 0) {
      icon = null;
    } else if (item.icon) {
      icon = item.icon;
    } 
    
     
    return (
      <TouchableOpacity
        style={{ borderRadius: 16, backgroundColor: "#F6F6F6", marginHorizontal: 12, width: screenWidth - 24 }}
        onPress={() => {
          if (Device.isOwner) {
            console.log("下个页面1111111",this.callSettingData);
            
            this.props.navigation.navigate("ContactsSetting", { item, callSettingData: this.callSettingData, index, callback: (index, data, isDel) => this.updateUserInfo(index, data, isDel) });
          } else {
            Toast.show(LocalizedStrings["share_cannot_set"]);
          }
        }}
      >
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center", padding: 20, width: "100%" }}>
          <Image style={{ width: 40, height: 40, borderRadius: 20 }} source={icon != null ? { uri: icon } : require("../../Resources/Images/icon_user.png")} />
          <View style={{ display: "flex", flexDirection: "row", flexGrow: 1, paddingLeft: 20, paddingRight: 15, flex: 1, alignItems: "center" }}>
            <View style={{ display: "flex", flexDirection: "column" }}>
              <Text style={{ fontSize: 16, fontWeight: '400', lineHeight: 22, color: "#000000" }}>{nickname}</Text>
              <Text style={{ color: "#999999", fontSize: 11 }}>{item.mijia}</Text>
            </View>
          </View>
          <View
            style={{
              width: 10
            }}
          />

          {/* {Device.isOwner && (
            <ImageButton
              style={{ width: 32, height: 32 }}
              source={Util.isDark() ? require("../../Resources/Images/icon_call_edit_dark.png") : require("../../Resources/Images/icon_call_edit.png")}
              onPress={() => {
                console.log("=======edit", item);
                if (Device.isReadonlyShared) {
                  Toast.success("cloud_share_hint");
                  return;
                }
                this.currentItem = item;

                this.setState({ showEditContactDialog: true });
              }}
            />
          )} */}
          {Device.isOwner ? <Image style={{ width: 7.5, height: 13.5 }} source={Util.isDark() ? require(`../../Resources/Images/icon_right_dark.png`) : require(`../../Resources/Images/icon_right.png`)}></Image> : null}
          {/*   <View
            style={[
              {
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "flex-start",
              },
              { maxWidth: "30%" },
            ]}
          >
            {this.props.value ? (
              <Text
                numberOfLines={valueLine}
                ellipsizeMode="tail"
                style={[
                  Styles.common.subtitle,
                  {
                    color: "rgba(0, 0, 0, 0.40)",
                    ...FontSecondary,
                  },
                  valueStyle,
                ]}
                {...getAccessibilityConfig({
                  accessible: false,
                })}
              >
                {this.props.value}
              </Text>
            ) : null}
          </View> */}
        </View>
       
      </TouchableOpacity>
    );
  };

  _renderEmptyView = () => {
    return (
      <View style={{ alignItems: "center", marginBottom: 60, marginTop: 40 }}>
        <Image style={{ height: 60, width: 92 }} source={require("../../Resources/Images/icon_share_no.webp")} />
        <Text style={{ color: "#00000066", fontSize: 14, marginTop: 12 }}>{LocalizedStrings["no_contact"]}</Text>
        <Text style={{ color: "#00000066", fontSize: 14, marginTop: 3 }}>{LocalizedStrings["no_contact_add_desc"]}</Text>
      </View>
    );
  };
  _renderBottomItem() {
    return (
      <View style={{ borderRadius: 16, backgroundColor: "#F6F6F6", marginHorizontal: 12, width: screenWidth - 24, padding: 20, marginBottom: 60 }}>
        <TouchableOpacity
          style={{ alignSelf: "flex-start" }}
          onPress={() => {
            this.setState({ showServiceReminderDialog: true });
          }}
        >
          <Text style={{ color: "#32BAC0", fontSize: 14, textDecorationLine: "underline", lineHeight: 18 }}>{LocalizedStrings["wx_server"]}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ marginTop: 12, alignSelf: "flex-start" }}
          onPress={() => {
            this.setState({ showCancelEmpowerDialog: true });
          }}
        >
          <Text style={{ color: "#32BAC0", fontSize: 14, textDecorationLine: "underline", lineHeight: 18 }}>{LocalizedStrings["cs_wx_cancel_invite"]}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  _renderEmpowerTips = () => {
    if (!this.state.showEmpowerTip) {
      return null;
    }

    let backgroundColor = Util.isDark() ? "#25A9AF32" : "#32BAC019";
    let channel = "videodetails_button";
    let tipsText = LocalizedStrings["cs_wx_call_empower_tips"];
    let tipsTextColor = Util.isDark() ? "#25A9AF" : "#32BAC0";

    let backIcon = require("../../Resources/Images/close_tips.png");
    let style = {
      flexDirection: "row",
      flexWrap: "nowrap",
      alignItems: "center",
      justifyContent: "space-between",
      backgroundColor: backgroundColor,
      paddingLeft: 16,
      paddingRight: 8,
      paddingVertical: 11,
      borderRadius: 16
    };
    let mBgStyleBase = { alignItems: "center", justifyContent: "center" };
    let mBgStyle = [
      mBgStyleBase,
      {
        marginTop: 10,
        backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF",
        marginHorizontal: 12,
        borderRadius: 16
      }
    ];
    return (
      <View style={mBgStyle}>
        <TouchableOpacity
          style={style}
          ref={(ref) => (this.mCloudBuyTip = ref)}
          onLayout={(e) => {
            this.mCloudBuyTipWidth = e.nativeEvent.layout.width;
          }}
          // accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.kj_2_5 : DescriptionConstants.kj_2_30}
        >
          <Text
            numberOfLines={3}
            style={[
              BaseStyles.text14,
              {
                paddingRight: 10,
                color: tipsTextColor,
                textAlign: "left",
                textAlignVertical: "center",
                fontSize: 13,
                flex: 1
              }
            ]}
          >
            {tipsText}
          </Text>
          <ImageButton
            onPress={() => {
              StorageKeys.WX_EMPOWER_TIP = true;
              this.setState({ showEmpowerTip: false });
            }}
            style={{ width: 22, height: 22 }}
            source={backIcon}
          />
        </TouchableOpacity>
      </View>
    );
  };

  _renderToEmpowerDialog() {
    return (
      <AbstractDialog
        visible={this.state.showEmpowerDialog}
        title={LocalizedStrings["cs_invite_empower"]}
        subtitle={LocalizedStrings["cs_invite_empower_desc"]}
        dialogStyle={{
          titleStyle: {
            fontSize: 16.5
          }
        }}
        showSubtitle={false}
        useNewTheme
        canDismiss={false}
        onDismiss={() => {
          this.setState({ showEmpowerDialog: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["cs_not_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
            }
          },
          {
            text: LocalizedStrings["cs_wx_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
              if (this.inviteUser) {
                let params = {
                  // paramType: "requestWxDeviceVoIP",
                  paramType: "shareWxDeviceVoIP",
                  did: Device.deviceID.toString(),
                  userId: `${ this.inviteUser.mijia }`,
                  deviceName: Device.name,
                  model: Device.model
                };
                console.log("======", params);
                // Host.ui.requestWxDeviceVoIP(params)
                Host.ui.shareWxDeviceVoIP(params);
              }
            }
          }
        ]}
      >
        <View>
          <Text
            style={{
              fontSize: 16,
              fontWeight: "400",
              marginHorizontal: 28,
              lineHeight: 24
            }}
          >
            {LocalizedStrings["cs_invite_empower_desc"]}
          </Text>

          <View
            style={{
              margin: 28,
              display: "flex",
              flexDirection: "row"
            }}
          >
            <Image
              style={{
                height: 170,
                borderRadius: 12,
                flex: 1
              }}
              source={require("../../Resources/Images/wx_empower.webp")}
            />
          </View>
        </View>
      </AbstractDialog>
    );
  }

  _renderServerWarning() {
    return (
      <MessageDialog
        visible={this.state.showServiceReminderDialog}
        title={LocalizedStrings["wx_server"]}
        dialogStyle={{ titleStyle: {
          fontWeight: 'bold'
        } }}
        message={LocalizedStrings["wx_invite_dialog_msg2"]}
        useNewType={true}
        onDismiss={() => {
          this.setState({ showServiceReminderDialog: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showServiceReminderDialog: false });
            }
          }
        ]}
      />
    );
  }

  _renderCancelEmpowerDialog() {
    return (
      <AbstractDialog
        visible={this.state.showCancelEmpowerDialog}
        title={LocalizedStrings["cs_wx_cancel_invite_title"]}
        dialogStyle={{
          titleStyle: {
            fontSize: 16.5
          }
        }}
        showSubtitle={false}
        useNewTheme
        canDismiss={false}
        onDismiss={() => {
          this.setState({ showCancelEmpowerDialog: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: "grayLayerBlack",
            callback: (_) => {
              this.setState({ showCancelEmpowerDialog: false });
            }
          }
        ]}
      >
        <View
          style={
            {
              // flex: 1
            }
          }
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "400",
              marginHorizontal: 28,
              marginBottom: 26,
              lineHeight: 24
            }}
          >
            {LocalizedStrings["cs_wx_cancel_invite_msg"]}
          </Text>

          {/* <View style={{ */}
          {/*  margin: 28, */}
          {/*  display: 'flex', */}
          {/*  flexDirection: "row", */}
          {/* }}> */}
          {/*  <Image style={ { */}
          {/*    height: 170, */}
          {/*    borderRadius: 12, */}
          {/*    flex: 1 */}
          {/*  } } */}
          {/*         source={ require("../../Resources/Images/pic_cancel_empower.webp") }/> */}
          {/* </View> */}
        </View>
      </AbstractDialog>
    );
  }

  // _renderRenameDialog() {
  //   return (
  //     <InputDialog
  //       visible={this.state.showRenameDialog}
  //       title={LocalizedStrings["ai_call_name"]}
  //       inputs={[
  //         {
  //           placeholder: LocalizedStrings["please_enter_name"],
  //           defaultValue: this.state.tempName,
  //           textInputProps: {
  //             autoFocus: true
  //           },
  //           onChangeText: (text) => {
  //             if (this.state.commentErr != null) {
  //               this.setState({ commentErr: null });
  //             }
  //             let isEmoji = Util.containsEmoji(text);
  //             let length = text.length;
  //             // let isCommon = this.isTextcommon(result);
  //             if (isEmoji) {
  //               this.setState({ isErrorName: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
  //             } else if (length > 10) {
  //               this.setState({ isErrorName: true, commentErr: LocalizedStrings["input_name_too_long2"] });
  //             } else if (length <= 0 || text.trim().length == 0) {
  //               this.setState({ isErrorName: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
  //             } else {
  //               this.setState({ isErrorName: false, commentErr: "error" });
  //             }
  //           },
  //           type: "DELETE",
  //           isCorrect: !this.state.isErrorName
  //         }
  //       ]}
  //       inputWarnText={this.state.commentErr}
  //       noInputDisButton={true}
  //       buttons={[
  //         {
  //           callback: () => this.setState({ showRenameDialog: false, commentErr: null, isErrorName: false })
  //         },
  //         {
  //           callback: (result) => {
  //             console.log(`结果`, result.textInputArray[0]);
  //             if (this.state.isErrorName) {
  //               return;
  //             }
  //             let text = result.textInputArray[0].trim();
  //             if (text.length > 0 && !Util.containsEmoji(text)) {
  //               this.setState({ showRenameDialog: false, commentErr: null });
  //               if (!Device.isOnline) {
  //                 Toast.success("c_set_fail");
  //                 return;
  //               }
  //               let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
  //               LogUtil.logOnAll("KeyCallSetting", this.changeContactType, needUpData.hasOwnProperty("key1"), needUpData.hasOwnProperty("key2"), needUpData.hasOwnProperty("key3"));
  //               if (this.currentItem && needUpData[this.currentItem.key]) {
  //                 needUpData[this.currentItem.key].callName = text;
  //               }
  //               let paramsStr = JSON.stringify(needUpData);
  //               // 先更新设备端，再更新云端
  //               // needUpData['method'] = "0";
  //               let updateParams = JSON.stringify(needUpData);
  //               CallUtil.updateSettingToDevice(updateParams)
  //                 .then((res) => {
  //                   DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr)
  //                     .then((res) => {
  //                       // delete needUpData['method'];
  //                       this.callSettingData = needUpData;
  //                       Toast.success("c_set_success");
  //                       this.setState({
  //                         contactsData: this.state.contactsData.map((item, _index) => {
  //                           console.log("==================", item.key, this.currentItem.key);
  //                           if (item.key == this.currentItem.key) {
  //                             return { ...item, callName: text };
  //                           } else {
  //                             return item;
  //                           }
  //                         })
  //                       });
  //                     })
  //                     .catch((error) => {
  //                       Toast.fail("c_set_fail");
  //                     });
  //                 })
  //                 .catch((err) => {
  //                   Toast.fail("c_set_fail");
  //                 });
  //             } else {
  //               if (Util.containsEmoji(text)) {
  //                 this.setState({ commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
  //               }
  //             }
  //           }
  //         }
  //       ]}
  //       onDismiss={() => this.setState({ showRenameDialog: false, commentErr: null, isErrorName: false })}
  //     />
  //   );
  // }

  _renderEditContactDialog() {
    let itemData = [
      {
        title: LocalizedStrings["edit_contact_avatar"],
        textColor: "#000000",
        onPress: () => {
          if (!Device.isOnline) {
            // 设备已经离线了
            this.setState({ showEditContactDialog: false });
            Toast.success("c_set_fail");
            return;
          }
          this.setState({ showEditContactDialog: false, showAvatarIntroDialog: true });
        }
      },
      {
        title: LocalizedStrings["rename_contact"],
        textColor: "#000000",
        onPress: () => {
          this.setState({
            showEditContactDialog: false,
            showRenameDialog: true,
            tempName: this.currentItem ? this.currentItem.callName : ""
          });
          // this.setState({ showEditContactDialog: false });
          // this.props.navigation.navigate("ChoiceContactPage", { type: 1 });
        }
      }
    ];
    if (Device.isOwner) {
      itemData.push({
        title: LocalizedStrings["delete_contact"],
        textColor: "#F43F31",
        onPress: () => {
          this.setState({ showEditContactDialog: false, showDelDialog: true });
        }
      });
    }
    return (
      <AbstractDialog
        visible={this.state.showEditContactDialog}
        useNewTheme={true}
        title={LocalizedStrings["edit_contact"]}
        dialogStyle={{
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        }}
        onDismiss={(_) => this.setState({ showEditContactDialog: false })}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showEditContactDialog: false });
            }
          }
        ]}
      >
        <View
          style={{
            marginBottom: 16
          }}
        >
          {itemData.map((option, index) => (
            <View key={(option.title || "") + index}>
              <ChoiceItem
                type={ChoiceItem.TYPE.SINGLE}
                titleStyle={{
                  fontSize: 16,
                  fontWeight: "bold",
                  color: option.textColor || "#000000"
                }}
                title={option.title || ""}
                onPress={() => option.onPress()}
              />
            </View>
          ))}
        </View>
      </AbstractDialog>
    );
  }

  // renderDeleteDialog() {
  //   return (
  //     <MessageDialog
  //       visible={this.state.showDelDialog}
  //       message={LocalizedStrings["delete_contact_confirm"]}
  //       messageStyle={{ textAlign: "center" }}
  //       canDismiss={true}
  //       onDismiss={() => this.setState({ showDelDialog: false })}
  //       buttons={[
  //         {
  //           text: LocalizedStrings["btn_cancel"],
  //           callback: (_) => {
  //             this.setState({ showDelDialog: false });
  //           }
  //         },
  //         {
  //           text: LocalizedStrings["delete_files"],
  //           callback: (_) => {
  //             this.setState({ showDelDialog: false });
  //             // 删除按键联系人
  //             this.deleteClickContact();
  //           }
  //         }
  //       ]}
  //     />
  //   );
  // }

  /**
   * 20241016@byh 更新
   * 更新点：先更新设备端数据，再更新云端数据
   */
  // deleteClickContact() {
  //   if (!Device.isOnline) {
  //     Toast.success("c_set_fail");
  //     return;
  //   }
  //   let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
  //   const delUid = needUpData[this.currentItem.key]?.mijia;
  //   const onlyWx = needUpData[this.currentItem.key]?.only_wx;
  //   if (this.currentItem && needUpData[this.currentItem.key]) {
  //     delete needUpData[this.currentItem.key];
  //   }
  //   // CallUtil.sortByIndex(needUpData);
  //   let paramsStr = JSON.stringify(needUpData);
  //   // needUpData['method'] = "0";
  //   let updateParams = JSON.stringify(needUpData);
  //   // 只有是微信用户才走外部接口进行删除 
  //   // 暂时注释，以后放出来
  //   if (onlyWx) {
  //     CallUtil.delWxCallOnlyContact({
  //       uid: delUid, // 联系人的uid
  //       did: Device.deviceID,
  //       owner_uid: Service.account.ID // 设备owner的uid
  //     }).then((apiRes) => {
  //       if (apiRes.succ === true) {
  //         delContact();
  //       }
  //     });

  //   } else {
  //     CallUtil.updateSettingToDevice(updateParams)
  //       .then((res) => {
  //         DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr)
  //           .then((res) => {
  //             // 成功后
  //             // delete needUpData['method'];
  //             delContact();
  //           })
  //           .catch((error) => {
  //             Toast.fail("c_set_fail");
  //           });
  //       })
  //       .catch((err) => {
  //         Toast.fail("c_set_fail");
  //       }); 
  //   }
  //   const delContact = () => {
  //     this.callSettingData = needUpData;
  //     Toast.success("delete_contact_success");
  //     this.setState({
  //       contactsData: this.state.contactsData.filter((item, _index) => {
  //         if (item.key != this.currentItem.key) {
  //           return item;
  //         }
  //       })
  //     });
  //     // 删除setting中的头像数据
  //     let deleteKey = `call_${ this.currentItem.key }`;
  //     console.log(deleteKey, "deleteKey");
      
  //     DeviceSettingUtil.delDeviceSetting([deleteKey]).then((res) => {});
  //     // 删除本地缓存图片
  //     if (this.currentItem.objName && this.currentItem.objName != "") {
  //       let fileName = this.currentItem.objName.substring(this.currentItem.objName.lastIndexOf("/") + 1);
  //       Host.file.deleteFile(fileName).then(() => {
  //         console.log(fileName, "fileName");
  //       });
  //     }
  //   };
  // }

  // _renderAvatarIntroDialog() {
  //   return (
  //     <AbstractDialog
  //       visible={this.state.showAvatarIntroDialog}
  //       useNewTheme={true}
  //       title={LocalizedStrings["edit_contact_avatar"]}
  //       dialogStyle={{
  //         allowFontScaling: false,
  //         titleStyle: {
  //           fontSize: 16,
  //           fontWeight: "bold"
  //         }
  //       }}
  //       onDismiss={(_) => this.setState({ showAvatarIntroDialog: false })}
  //       buttons={[
  //         {
  //           text: LocalizedStrings["action_cancle"],
  //           colorType: "grayLayerBlack",
  //           callback: () => {
  //             this.setState({ showAvatarIntroDialog: false });
  //           }
  //         }
  //       ]}
  //     >
  //       <View
  //         style={{
  //           marginBottom: 16,
  //           flexDirection: "column",
  //           alignItems: "center"
  //         }}
  //       >
  //         <Image style={{ width: 72, height: 72 }} source={require("../../Resources/Images/icon_user.png")} />
  //         <Text style={{ fontSize: 12, color: "rgba(0, 0, 0, 0.6)", marginTop: 6 }}>{LocalizedStrings["edit_contact_avatar_desc"]}</Text>
  //         <View style={{ width: "100%", marginTop: 26 }}>
  //           <TouchableHighlight
  //             style={{ marginHorizontal: 27, backgroundColor: "#32BAC0", justifyContent: "center", alignItems: "center", borderRadius: 23, height: 46 }}
  //             underlayColor={"#32BAC0CC"}
  //             onPress={() => {
  //               this.setState({ showAvatarContactDialog: true, showAvatarIntroDialog: false });
  //             }}
  //           >
  //             <Text style={{ fontSize: 16, color: "#FFFFFF", textAlign: "center" }}>{LocalizedStrings["upload_avatar"]}</Text>
  //           </TouchableHighlight>
  //         </View>
  //       </View>
  //     </AbstractDialog>
  //   );
  // }

  // _renderChooseAvatarDialog() {
  //   return (
  //     <AbstractDialog
  //       visible={this.state.showAvatarContactDialog}
  //       useNewTheme={true}
  //       title={LocalizedStrings["edit_contact_avatar"]}
  //       dialogStyle={{
  //         allowFontScaling: false,
  //         titleStyle: {
  //           fontSize: 16,
  //           fontWeight: "bold"
  //         }
  //       }}
  //       onDismiss={(_) => this.setState({ showAvatarContactDialog: false })}
  //       buttons={[
  //         {
  //           text: LocalizedStrings["action_cancle"],
  //           colorType: "grayLayerBlack",
  //           callback: () => {
  //             this.setState({ showAvatarContactDialog: false });
  //           }
  //         }
  //       ]}
  //     >
  //       <View
  //         style={{
  //           marginBottom: 16
  //         }}
  //       >
  //         {[
  //           {
  //             title: LocalizedStrings["takePhoto"],
  //             onPress: () => {
  //               this.setState({ showAvatarContactDialog: false });
  //               this.takePicture();
  //             }
  //           },
  //           {
  //             title: LocalizedStrings["select_dialog_album"],
  //             redText: true,
  //             onPress: () => {
  //               this.setState({ showAvatarContactDialog: false });
  //               this.selectPicture();
  //             }
  //           }
  //         ].map((option, index) => (
  //           <View key={(option.title || "") + index}>
  //             <ChoiceItem
  //               type={ChoiceItem.TYPE.SINGLE}
  //               titleStyle={{
  //                 fontSize: 16,
  //                 fontWeight: "bold"
  //               }}
  //               title={option.title || ""}
  //               onPress={() => option.onPress()}
  //             />
  //           </View>
  //         ))}
  //       </View>
  //     </AbstractDialog>
  //   );
  // }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 相机
    //
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace(
        "%s",
        Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]
      );
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog}
      />
    );
  }


  renderText(matchingString, matches) {
    let find = "\\%";
    let re = new RegExp(find, "g");
    return matchingString.replace(re, "");
  }
  renderText1(matchingString, matches) {
    let find = "\\[|\\]|1|2";
    let re = new RegExp(find, "g");
    return matchingString.replace(re, "");
  }
}

const showMoreSetting = (deviceVersion, permission) => { 
  
  if (deviceVersion <= DEVICE_VERSION) {
    return false;
  }
  return permission;
};

const stylesWX = StyleSheet.create({
  white_blank: {
    height: 0.5,
    marginTop: 20
  },

  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 38
  },
  wrapper: {
    marginBottom: 10,
    marginLeft: 24,
    marginRight: 24
  },
  slide: {
   
    justifyContent: "center",
    alignItems: "center"
  
  },
  bannerImage: {
    width: viewWidth,
    height: viewHeight
  },
  desc_container: {
    
  },
  container: {
    display: "flex",
    height: "100%",
    width: "100%",
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    position: 'relative'
  }
});
