'use strict';

import React from 'react';
import { ScrollView, View, StyleSheet, Text } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';

import { NavigationBar, MessageDialog } from 'mhui-rn';

import SingleRadioView from "../ui/SingleRadioView";

import MHDatePicker from "miot/ui/MHDatePicker";
import BaseSettingPage from "../BaseSettingPage";
import AlarmUtilV2, { CAMERA_NIGHT_MODE_PERIOD_PIID, CAMERA_NIGHT_MODE_SIID } from "../util/AlarmUtilV2";
import Toast from "../components/Toast";

export default class NightModeTimeSetting extends React.Component {
  // static navigationOptions = ({ navigation }) => {
  //   return {
  //     header: (
  //       <NavigationBar
  //         type={NavigationBar.TYPE.LIGHT}
  //         left={[
  //           {
  //             key: NavigationBar.ICON.CLOSE,
  //             onPress: (_) => navigation.goBack()
  //           }
  //         ]}
  //         right={[
  //           {
  //             key: NavigationBar.ICON.COMPLETE,
  //             onPress: (_) => {
  //               this.saveTimeData();
  //             }
  //           }
  //         ]}
  //         title={navigation.getParam("navTitle")}
  //         titleStyle={{
  //           fontSize: 18,
  //           color: '#333333',
  //           fontWeight: 500
  //         }}
  //         backgroundColor={"#F5F5F5"}
  //       />
  //     )
  //   };
  // };
  constructor(props, context) {
    super(props, context);
    this.state = {
      // 生效时间类型
      modeType: -1,
      showDialog: false,
      chooseTimeType: 0,
      startRawString: "22:00",
      endRawString: "07:00",
      startRawArray: ["12", "00"],
      endRawArray: ["09", "00"],
      showSaveDialog: false
    };
    this.initStartRawString = "22:00";
    this.initEndRawString = "07:00";
    this.initModeType = -1;
  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.setBar(LocalizedStrings['cs_effect_time']);
    } else {
      this.showTitle = false;
      this.setBar("");
    }
  };


  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={LocalizedStrings['exit_change_disappear']}
        messageStyle={{ textAlign: "center" }}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
            }
          },
          {
            text: LocalizedStrings["exit"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
              this.props.navigation.goBack();
            }
          }
        ]}
      />
    );
  }

  render() {
    let subValue2 = this.getEndTimeValue(this.state.startRawString, this.state.endRawString);
    return (
      <View style={ [styles.container] }>
        <ScrollView onScroll={this.scrollViewScroll} showsVerticalScrollIndicator={ false } contentContainerStyle={ { flexGrow: 1 } }>
          <View style={ stylesNightMode.container } key={ 103 }>
            <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0}>
              <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23, fontFamily: 'MI-LANTING--GBK1-Light' }}>
                {LocalizedStrings['cs_effect_time']}
              </Text>
            </View>
            <View style={ { height: 15 } }/>

            <SingleRadioView
              title={ LocalizedStrings['cs_screen_night'] }
              value={ this.state.modeType == 0 }
              radioValue={ 0 }
              onCheckChange={ (value) => {
                this.setState({ modeType: 0 });
              } }/>
            <View style={ { height: 15 } }/>

            <SingleRadioView
              title={ LocalizedStrings['cs_time_period'] }
              value={ this.state.modeType == 1 }
              radioValue={ 0 }
              subtitle={ LocalizedStrings['csps_start'] }
              subtitle2={ LocalizedStrings['csps_end'] }
              subValue={ this.state.startRawString }
              subValue2={ subValue2 }
              onCheckChange={ (value) => {
                this.setState({ modeType: 1 });
              } }
              onSubClick={ () => {
                this.setState({ chooseTimeType: 0, showDialog: true });
              } }
              onSubClick2={ () => {
                this.setState({ chooseTimeType: 1, showDialog: true });
              } }/>

            <View style={ { height: 15 } }/>

          </View>
          <MHDatePicker
            visible={ this.state.showDialog }
            title={ this.state.chooseTimeType === 0 ? LocalizedStrings['csps_start'] : LocalizedStrings['csps_end'] }
            type={ MHDatePicker.TYPE.TIME24 }
            datePickerStyle={{
              rightButtonStyle: { color: "#FFFFFF" },
              rightButtonBgStyle: { bgColorNormal: "#32BAC0", bgColorPressed: "#32BAC099" },
              pickerInnerStyle: { selectFontSize: 26, selectTextColor: "#32BAC0", unitTextColor: "#32BAC0", lineColor: 'transparent' }
            }}
            onDismiss={ (_) => this.onDismiss() }
            onSelect={ (res) => this.onSelect(res) }
            showSubtitle={ true }
            current={ this.state.chooseTimeType === 0 ? this.state.startRawArray : this.state.endRawArray } // 进入默认显示00时,05分
          />
        </ScrollView>
        {this._renderBackDialog()}
      </View>
    );
  }

  getEndTimeValue(start, end) {
    let text = end;
    let startValue = parseInt(start.split(":")[0]) * 60 + parseInt(start.split(":")[1]);
    let endValue = parseInt(end.split(":")[0]) * 60 + parseInt(end.split(":")[1]);
    if (startValue > endValue) {
      text = `${ LocalizedStrings['setting_monitor_next_day'] }${ end }`;
    }
    return text;
  }

  onDismiss() {
    this.setState({ showDialog: false });
  }

  onSelect(res) {
    if (this.state.chooseTimeType === 0) {
      this.setState({ startRawString: res.rawString, startRawArray: res.rawArray });
    } else {
      this.setState({ endRawString: res.rawString, endRawArray: res.rawArray });
    }
  }

  getTimeData() {
    let params = [{ sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_PERIOD_PIID }];
    AlarmUtilV2.getSpecPValue(params)
      .then((res) => {
        if (res[0].code == 0) {
          let timeValue = res[0].value;
          if (timeValue) {
            let jsonTime = JSON.parse(timeValue);
            let startArr = jsonTime.starttime.split(":");
            let endArr = jsonTime.endtime.split(":");
            console.log("========", startArr, endArr);
            this.setState({
              modeType: jsonTime.type,
              startRawString: jsonTime.starttime,
              endRawString: jsonTime.endtime,
              startRawArray: startArr,
              endRawArray: endArr
            });
            this.initEndRawString = jsonTime.endtime;
            this.initStartRawString = jsonTime.starttime;
            this.initModeType = jsonTime.type;
          }
        } else {
          Toast.fail("c_get_fail");
        }
      }).catch((err) => {
        Toast.fail("c_get_fail", err);
      });
  }

  saveTimeData() {
    if (this.state.modeType == -1) {
      // 未正确设置生效时间段类型
      Toast.fail("c_set_fail");
      return;
    }
    let valueObj = { type: this.state.modeType, starttime: this.state.startRawString, endtime: this.state.endRawString };
    let valueStr = JSON.stringify(valueObj);
    let params = [{ sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_PERIOD_PIID, value: valueStr }];
    AlarmUtilV2.setSpecPValue(params)
      .then((res) => {
        if (res[0].code == 0) {
          Toast.success("c_set_success");
          this.props.navigation.goBack();
        } else {
          Toast.fail("c_set_fail");
        }
      }).catch((err) => {
        Toast.fail("c_set_fail", err);
      });


  }

  componentDidMount() {
    this.getTimeData();
    this.setBar("");
  }
 
  getSelectStatus() {
    if (this.initEndRawString !== this.state.endRawString || this.initStartRawString !== this.state.startRawString || this.initModeType !== this.state.modeType) {
      this.setState({ showSaveDialog: true });
      return;
    }
    this.props.navigation.goBack();
  }

  setBar(title) {
    console.log("+++++++++++++", title);
    this.props.navigation.setParams({
      // show:true
      title: title,
      titleNumberOfLines: 2,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            this.getSelectStatus()
          }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
             
            if (this.state.startRawString == this.state.endRawString) {
              Toast.fail("imi_start_equal_end");
              return;
            }
            this.saveTimeData();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      },
      backgroundColor: "#F5F5F5"
    });
  }
}
const stylesNightMode = StyleSheet.create({
  container: {
    backgroundColor: '#f5f5f5',
    height: "100%"
  }
});
