import { Platform, ScrollView, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { LocalizedStrings } from "miot/ui";
import { localStrings } from "../MHLocalizableString";
import { ListItem, NavigationBar } from "mhui-rn";
import Util from "../util2/Util";

import { Styles as SettingStyles } from "miot/resources";
import BaseSettingPage from "../BaseSettingPage";

import AlarmUtilV2, {
  CAMERA_NIGHT_MODE_SIID,
  CAMERA_NIGHT_MODE_SWITCH_PIID,
  PIID_THEME,
  SCREEN_SETTING_BRIGHTNESS_PIID,
  SCREEN_SETTING_DISPLAY_STATE_PIID,
  SCREEN_SETTING_SIID,
  SIID_THEME_SETTINGS,
} from "../util/AlarmUtilV2";
import Toast from "../Toast";
import { Device } from "miot";

const ThemeRightnessSettingRender = ({ props }) => {
  const [settings, setSettings] = useState({ nightMode: null, screenLightValue: null, screenTheme: null, screenLightMode: 1 });
  useEffect(() => {
    props.navigation.setParams({
      title: LocalizedStrings.display_and_light,
      type: NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            props.navigation.goBack();
          },
        },
      ],
      titleStyle: {
        fontSize: 18,
        color: "#333333",
        fontWeight: 500,
      },
    });
    let params = [
      { sname: CAMERA_NIGHT_MODE_SIID, pname: CAMERA_NIGHT_MODE_SWITCH_PIID },
      { sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_DISPLAY_STATE_PIID },
      { sname: SIID_THEME_SETTINGS, pname: PIID_THEME },
      { sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_BRIGHTNESS_PIID },
    ];
    const didFocusListener = props.navigation.addListener(
      // 回到当前页面 或者第一次进来
      "didFocus",
      () => {
        AlarmUtilV2.getSpecPValue(params, 2)
          .then((result) => {
            let isFail = false;
            console.log(result, "result111");

            // 夜间模式
            if (result[0].code === 0) {
              settings.nightMode = result[0].value;
            } else {
              isFail = true;
            }

            // 屏幕亮度
            if (result[1].code === 0) {
              settings.screenLightMode = result[1].value;
            } else {
              isFail = true;
            }
            // 主题设置
            if (result[2].code === 0) {
              settings.screenTheme = result[2].value;
            } else {
              isFail = true;
            }
            if (result[3].code === 0) {
              settings.screenLightValue = result[3].value;
            } else {
              isFail = true;
            }

            if (isFail) {
              Toast.fail("c_get_fail");
            }
            setSettings({ ...settings });
          })
          .catch((err) => {
            Toast.fail("c_get_fail", err);
          });
      }
    );
    return () => {
      didFocusListener?.remove && didFocusListener?.remove();
    };
  }, []);

  const getThemeTitle = () => {
    if(settings.screenTheme===null){
return null
    }
    if (settings.screenTheme == 3) {
      return localStrings["display_weather"];
    }
    if (settings.screenTheme == 4) {
      return localStrings["display_clock_point"];
    }
    if (settings.screenTheme == 5) {
      return localStrings["home_theme"];
    }
    return localStrings["number_clock"];
  };
  
 const getScreenLight=()=>{
  
  if(settings.screenLightMode===null||settings.screenLightValue===null){
    return null
  }
  return settings.screenLightMode == 0 ? localStrings["camera_quality_auto"] : `${settings.screenLightValue}%`
 }

  const getNightModeValue = () => {
    if (settings.nightMode === null) {
      return null
    }
    return settings.nightMode ? localStrings["on"] : localStrings["off"]
  }
  return (
    <View styles={styles.container}>
      <ScrollView bounces={Platform.OS === "ios" ? false : true} showsVerticalScrollIndicator={false} scrollEventThrottle={10}>
        <ListItem
          titleNumberOfLines={3}
          unlimitedHeightEnable={true}
          showSeparator={false}
          showVerticalLine={true}
          title={localStrings["cs_screen_light"]}
          value={getScreenLight()}
          containerStyle={{ height: 60 }}
          titleStyle={{ fontWeight: "bold", fontSize: 16 }}
          onPress={() => {
            props.navigation.navigate("ScreenLightSetting", {
              selectScreenLightCallBack: (data) => {
                if (data.value != settings.screenLightValue || data.mode != settings.screenLightMode) {
                  settings.screenLightValue = data.value;
                  settings.screenLightMode = data.mode;
                  setSettings({ ...settings });
                }
              },
              screenLightValue: settings.screenLightValue,
              screenLightMode: settings.screenLightMode,
            });
          }}
        />
        <ListItem
          titleNumberOfLines={3}
          unlimitedHeightEnable={true}
          showSeparator={false}
          showVerticalLine={true}
          title={localStrings["theme_setting"]}
          value={getThemeTitle()}
          containerStyle={{ height: 60 }}
          titleStyle={{ fontWeight: "bold", fontSize: 16 }}
          onPress={() => {
            if (!Device.isOwner) {
              Toast.success("share_cannot_set");
              return;
            }

            props.navigation.navigate("ThemeSetting", {
              callback: (data) => {
                if (data.theme != settings.screenTheme) {
                  settings.screenTheme = data.theme;
                  setSettings({ ...settings });
                }
              },
            });
          }}
        />
        <ListItem
          titleNumberOfLines={3}
          unlimitedHeightEnable={true}
          showSeparator={false}
          showVerticalLine={true}
          title={localStrings["cs_night_mode"]}
          value={getNightModeValue()}
          containerStyle={{ height: 60 }}
          titleStyle={{ fontWeight: "bold", fontSize: 16 }}
          onPress={() => {
            props.navigation.navigate("NightModeSetting", {
              nightModeCallBack: (value) => {
                settings.nightMode = value;
                setSettings({ ...settings });
              },
              nightMode: settings.nightMode,
            });
          }}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : "#F6F6F6",
    flex: 1,
  },
  titleContainer: {
    height: 32,
    backgroundColor: Util.isDark() ? "#xm000000" : "white",
    justifyContent: "center",
    paddingLeft: SettingStyles.common.padding,
  },
  title: {
    fontSize: 12,
    color: "#8c93b0",
    lineHeight: 14,
  },
});

export default class ThemeRightnessSetting extends BaseSettingPage {
  constructor(props, context) {
    super(props, context);
    this.props = props;
  }
  getTitle() {
    return localStrings["display_and_light"];
  }

  renderSettingContent() {
    return <ThemeRightnessSettingRender props={this.props} />;
  }
}
