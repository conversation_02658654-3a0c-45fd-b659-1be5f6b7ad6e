import React from 'react';
import { Text, StyleSheet, View, Image, TouchableOpacity, SafeAreaView, TouchableWithoutFeedback, ActivityIndicator } from 'react-native';
import { Device, Host, Package, Service, DarkMode, ToastView } from 'miot';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import StorageKeys from '../StorageKeys';
import BasePage, { BaseStyles } from "../BasePage";
import { evList2SectionList } from "../widget/EventSectionList";
import { ChoiceDialog } from "miot/ui/Dialog";
import { MessageDialog } from 'miot/ui/Dialog';
import { ChoiceDlgWithIconSel } from "../widget/ChoiceDlgEx";
import Util, { DayInMilli } from "../util2/Util";
import dayjs from 'dayjs';
import { Order } from "../framework/EventLoaderInf";
import { Event } from "../config/base/CfgConst";
import Singletons from "../framework/Singletons";
import { CldDldTypes } from "../framework/CloudEventLoader";
import ImageButton from "miot/ui/ImageButton";
import { Separator } from 'mhui-rn';
import VersionUtil from '../util/VersionUtil';
import { SCREEN_WIDTH } from '../util2/Const';
import TrackUtil from '../util/TrackUtil';
import CameraConfig from '../util/CameraConfig';
import { DescriptionConstants } from '../Constants';
import { StatusBar } from 'react-native';
import MHDatePicker from 'miot/ui/MHDatePicker';
import Toast from '../Toast';
import LogUtil from '../util/LogUtil';
const LiveHeaderH = 158;
const CommonHeaderH = 46;
const DefFilter = "Default";
const Filters = [{ name: LocalizedStrings.all_events, key: DefFilter },
{ name: LocalizedStrings['event_desc_unknown_people'], key: Event.KnownFace, needVip: true }, // 目前本地人脸识别只有022，暂时不更新
// { name: LocalizedStrings['event_desc_unknown_people'], key: Event.Face, needVip: true },
{ name: LocalizedStrings['baby_cry_desc'], key: Event.BabyCry, needVip: true, isBabyCry: true }, // 009 019 v3需要考虑是否是vip状态
{ name: LocalizedStrings['event_desc_people_motion'], key: Event.PeopleMotion, needVip: false },
{ name: LocalizedStrings['event_desc_obj_motion'], key: Event.ObjectMotion, needVip: false },
{ name: LocalizedStrings['event_desc_ai_scene'], key: Event.AI, needVip: false }
];
const dayOfYear = require('dayjs/plugin/dayOfYear');
const TAG = 'customFeedbackMessage';
export default class customFeedbackMessage extends BasePage {

  constructor(props, context) {
    super(props, context);
    this.initState({
      modalVisible: false, // 弹出筛选框是否可见
      curDate: new Date(), // 当前选中的日期
      evFilter: Filters[0],
      selectedEventKey: DefFilter,
      isOnline: false,
      showEvFilter: false,
      showSTimeEvFilter: false,
      showSDateEvFilter: false,
      showETimeEvFilter: false,
      showEDateEvFilter: false,
      startDate: 0,
      startTime: 0,
      endDate: 0,
      endTime: 0,
      showLoading: false,
      confirmDialogVisible: false,
      isVip: undefined,
      prompt: null,
      extFilterTop: 114,
      showExtFilter: true,
      showDataNull: false,
      showTimeoutDialog: false,
      onPressConfirmEnd: 0,
      onPressConfirmStart: 0
    });
    this.mEv = [];
    this.motionDetection = false;
    this.updateFaceInfo = false;
    this.mSupportNonVipBabyCry = CameraConfig.isSupportNonVipBabyCry(Device.model);
  }


  onResume() {
    super.onResume();
    StatusBar.setBarStyle('dark-content');
    this.setState({ modalVisible: false });
    if (this.updateFaceInfo) {
      this.updateFaceInfo = false;
      this.mEvLst.updateAllItems();
    }
  }


  componentDidMount() {
    super.componentDidMount();
    StatusBar.setBarStyle('dark-content');
    this.mEvFilter = Filters
    this.props.navigation.setParams({
      show: true
    });
  }

  // 获取所有数据
  async loadAllEvent() {
    const { startDate, startTime, endDate, endTime } = this.state;
    // let bgn = null;
    // let end = null;
    this.bgn = new Date(startDate.rawArray[0], startDate.rawArray[1] - 1, startDate.rawArray[2], startTime.rawArray[0], startTime.rawArray[1], 0);
    this.end = new Date(endDate.rawArray[0], endDate.rawArray[1] - 1, endDate.rawArray[2], endTime.rawArray[0], endTime.rawArray[1], 0);
    console.log("this.bgn:", this.bgn);
    console.log("this.end:", this.end);
    let res = await Util.getEventList1(this.bgn, this.end, this.state.evFilter.key)
    this.setState({
      onPressConfirmStart: Date.now()
    })
    console.log(TAG, "onPressConfirmStart", this.state.onPressConfirmStart)
    // console.log("res:", res);
    this.mEvGetter(res)
  }

  // 转换成年月日时分秒
  timestampToTime(timestamp) {
    var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    var Y = date.getFullYear() + '-';
    var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
    var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
    var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
    var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
    var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
    
    let strDate = Y+M+D+h+m+s;
  　console.log(strDate) //2020-05-08 17:44:56　
    return strDate;
  }

  // 循环获取数据
  async mEvGetter(aEvLst) {
    this.mEv = this.mEv.concat(aEvLst.items);
    console.log("数据请求完毕，刷新数据： aEvLst:" + aEvLst.hasMore + " nextTime:" + aEvLst.nextTime)
    if (aEvLst.hasMore) {
      try {
        let res = await Util.getEventList1(this.bgn, aEvLst.nextTime, this.state.evFilter.key)
        this.mEvGetter(res);
        this.setState({
          onPressConfirmEnd: Date.now()
        })
        console.log(TAG, "this.state.onPressConfirmEnd", this.state.onPressConfirmEnd);
        console.log(TAG, "this.state.onPressConfirmStart - this.state.onPressConfirmEnd", this.state.onPressConfirmStart - this.state.onPressConfirmEnd);
        // 当一直处于加载状态 不变时 即 开始点击加载数据时 和循环拉取数据的时间做对比 如果超过两分钟 就显示超时
        if (this.state.onPressConfirmEnd - this.state.onPressConfirmStart > 120000) {
          this.setState({
            showTimeoutDialog: true
          })
          return
        }
      } catch (err) {
        // 中途关闭网络 或者 出现其他问题时 显示弹框
        this.setState({
          showTimeoutDialog: true
        })
        console.log(TAG, "数据请求出错了", err);
      }
      
    } else {
      // console.log("aEvLst.length",this.mEv)
      this.mEv = this.improveData(this.mEv);
      if (this.mEv.length !== 0) {
        // all data received
        // begin to open choose file
        Host.ui.openDirectorySelectPage()
        .then((res)=> {
          if (res.code == 0) {
            this.onSelectedDir(res.data);
          }
        })
        .catch(() => {

        })
      } else {
        this.setState({
          showDataNull: true,
          showLoading: false
        })
      }

      
    }
  }

  // 优化数据 截取规定时间范围内的数据信息 由于fileid一致时服务端返回的数据会出现开始时间之前的数据  
  improveData(eList) {
    let evList = []
    for (let item of eList) {
      if(item.createTime >= this.bgn.getTime() && item.createTime <= this.end.getTime()) {
        evList.push(item)
      }
    }
    return evList
  }
  


  onSelectedDir(dir) {
    console.log("aaa", "update ui");
    // console.log("this.mEV:", this.mEv);
    // console.log("this.mEV.length:", this.mEv.length);
    let resStr = this.concatRes(this.mEv);
    // console.log("resStr:", resStr)
    let fileName = ''.concat(this.bgn.getTime(), this.end.getTime(), this.state.evFilter.key, Math.round(Math.random() * 100),'.csv')
    console.log("fileName:", fileName)
    Host.file.writeFile(fileName, resStr).then(() => {
      //写入成功
      console.log('write success')
      let copy_params = {
        srcPath: fileName,
        dstPath: `${fileName}_copy`,
        dstDir: dir
      }
      Host.file.copyFile(copy_params).then((res) => {
        alert(JSON.stringify(res));
        // Host.file.readFileList('').then(res => {
        //     alert(JSON.stringify(res))
        // })
      }).catch((res) => {
        alert(JSON.stringify(res));
      });
    })
    LogUtil.logOnAll("customFeedbackMessage", resStr);
    this.setState({ showLoading: false, confirmDialogVisible: true })
  }

  onStartDateSelect(date) {
    if (this.state.endDate) {
      console.log("EndDate:", date)
      const { endDate } = this.state;
      let bgn = new Date(date?.rawArray[0], date?.rawArray[1] - 1, date?.rawArray[2]).getTime();
      let end = new Date(endDate?.rawArray[0], endDate?.rawArray[1] - 1, endDate?.rawArray[2]).getTime();
      if (end < bgn) {
        return
      }
    }
    console.log("startdata:", date)
    this.setState({
      startDate: date
    })
  }
  onEndDateSelect(date) {
    if (this.state.startDate) {
      console.log("EndDate:", date)
      const { startDate } = this.state;
      let bgn = new Date(startDate?.rawArray[0], startDate?.rawArray[1] - 1, startDate?.rawArray[2]).getTime();
      let end = new Date(date?.rawArray[0], date?.rawArray[1] - 1, date?.rawArray[2]).getTime();
      if (end < bgn) {
        return
      }
    }
    this.setState({
      endDate: date
    })
  }
  // 开始时分选择
  onStartTimeSelect(date) {
    this.setState({
      startTime: date
    })
  }
  // 结束时分选择
  onEndTimeSelect(date) {
    this.setState({
      endTime: date
    })

  }
  // 确定开始反馈事件
  onPressConfirm() {
    if (this.state.startDate && this.state.startTime && this.state.endTime && this.state.endDate) {
      const { startDate, startTime, endTime, endDate } = this.state;
      let bgn = new Date(startDate.rawArray[0], startDate.rawArray[1] - 1, startDate.rawArray[2], startTime.rawArray[0], startTime.rawArray[1], 0).getTime();
      let end = new Date(endDate.rawArray[0], endDate.rawArray[1] - 1, endDate.rawArray[2], endTime.rawArray[0], endTime.rawArray[1], 0).getTime();
      if (end < bgn) {
        Toast.fail("action_failed");
        return
      }
    }
    if (this.state.evFilter && this.state.startTime && this.state.startDate && this.state.endTime && this.state.endDate) {
      this.setState({
        showLoading: true
      })
      console.log("this.state.evFilter:", this.state.evFilter)
      this.loadAllEvent()
    } else {
      Toast.fail("action_select");
    }
  }
  // 将拿到的事件拼接成字符串
  concatRes(Evlist) {
    var EvlistStr = ''.concat('desc', ",", 'type',  ",", "fileId", ",", "offset", ",", "createTime","\r");
    for (let evListItem of Evlist) {
      EvlistStr = EvlistStr.concat(evListItem.desc, ",",  evListItem.type,  ",", evListItem.fileId, ",", evListItem.offset, ",", this.timestampToTime(evListItem.createTime),"\r")
    }
    return EvlistStr
  }
  render() {
    let mHeaderHeigth = 40 + 50;
    if (Host.isPad) {
      mHeaderHeigth = 50;
    }
    let mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.all'];
    switch (this.state.evFilter.key) {
      case Event.BabyCry:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.baby.cry'];
        break;
      case Event.PeopleMotion:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.people.motion'];
        break;
      // case Event.Face:
      //   mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.face'];
      //   break;
      case Event.KnownFace:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.face'];
        break;
      case Event.ObjectMotion:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.object.motion'];
        break;
      case Event.AI:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.ai'];
        break;
      default:
        break;
    }

    return (
      <View style={[BaseStyles.pageRoot, { flexDirection: 'column', backgroundColor: BaseStyles.mainBg.backgroundColor }]}>
        {this.renderHeader()}
        <View style={{ flex: 1, backgroundColor: BaseStyles.mainBg.backgroundColor, marginTop: mHeaderHeigth }}>
          <View style={{ alignItems: 'center' }}>
            <Separator style={{ width: "90%", backgroundColor: '#E5E5E5' }} />
          </View>
          <View style={[BaseStyles.row, { backgroundColor: "#F7F7F7", left: 0, marginTop: 10, marginBottom: 8 }]}
            zIndex={1}>
            {this.mCommonDateHeader(0)}
          </View>
          <View style={[BaseStyles.row, { backgroundColor: "#F7F7F7", left: 0, marginTop: 10, marginBottom: 8 }]}
            zIndex={1}>
            {this.mCommonTimeHeader(0)}
          </View>

          <View style={[BaseStyles.row, { backgroundColor: "#F7F7F7", left: 0, marginTop: 10, marginBottom: 8 }]}
            zIndex={1}>
            {this.mCommonDateHeader(1)}
          </View>
          <View style={[BaseStyles.row, { backgroundColor: "#F7F7F7", left: 0, marginTop: 10, marginBottom: 8 }]}
            zIndex={1}>
            {this.mCommonTimeHeader(1)}
          </View>
          <View style={[BaseStyles.row, { backgroundColor: "#F7F7F7", left: 0, marginTop: 10, marginBottom: 8 }]}
            zIndex={1}>
            {this.mCommonEventHeader(0)}
          </View>
          {/* 开始反馈的确认按钮 */}
          {this.renderButton()}
          {/* 日期选择 */}
          {this.renderDateFilterDlg()}
          {this.renderTimeFilterDlg()}
          {/* 事件类型筛选框 */}
          {this.renderEvFilterDlg()}

          {/* 加载中的loading */}
          {this.renderLoadingView()}
          {/* 退出 */}
          {this.renderDialog()}
          {/* {数据为空时的弹框} */}
          {this.renderDataNullDialog()}
          {/* {网络超时或一直显示加载时显示的弹框} */}
          {this._renderTimeoutDialog()}
        </View>
      </View>
    );
  }
  // 网络连接超时
  _renderTimeoutDialog() {
    return (
      <MessageDialog
        message={LocalizedStrings["network_fake_connected"]}
        buttons={
          [
            {
              text: LocalizedStrings['action_confirm'],
              callback: () => {
                this.setState({
                  showLoading:false,
                  showTimeoutDialog:false
                })
              }
            }
          ]
        }
        onDismiss={() => {
          // this.setState({ showTimeoutDialog: false });
        }}
        visible={this.state.showTimeoutDialog}
    >
    </MessageDialog>
  )
}

  // 事件类型筛选
  renderEvFilterDlg() {
    if (this.state.showEvFilter && this.mEvFilter) {
      let options = this.mEvFilter.map((itm) => { return { title: itm.name, icon: Util.getIconFromType(itm.key, 'anyname') }; });
      console.log(this.tag, options, this.eventFilterItems);
      return (<ChoiceDlgWithIconSel
        animationType={'slide'}
        accessible={true}
        type={ChoiceDialog.TYPE.SINGLE}
        useNewType={true}
        title={LocalizedStrings["ev_filter_title"]}

        visible={this.state.showEvFilter}
        selectedIndexArray={[this.mEvFilter.indexOf(this.state.evFilter)]}
        options={options}
        onDismiss={() => {
          this.setState({ showEvFilter: false });
        }}

        onSelectEx={(aSel) => {
          console.log(this.tag, "sel", aSel, this.mEvFilter[aSel[0]]);
          this.setState({ evFilter: this.mEvFilter[aSel[0]], showEvFilter: false });
          let sValue = 1;
          switch (this.mEvFilter[aSel[0]].key) {
            case Event.KnownFace:
              sValue = 5;
              break;
            case Event.PeopleMotion:
              sValue = 3;
              break;
            case Event.BabyCry:
              sValue = 4;
              break;
            case Event.AI:
              sValue = 6;
              break;
            case Event.ObjectMotion:
              sValue = 2;
              break;
            default:
              sValue = 1;
          }
          console.log(this.tag, 'Monitoring_Motion_Status', sValue);
          TrackUtil.reportResultEvent('Monitoring_Motion_Status', 'type', sValue);
        }}
        buttons={
          [
            {
              text: LocalizedStrings['action_cancle'],
              callback: () => {
                this.setState({ showEvFilter: false });
              },
              colorType: "grayLayerBlack"
            }
          ]
        }
      />);
    }
  }
  // 日期筛选
  renderDateFilterDlg() {
    if (this.state.showSDateEvFilter) {
      return (
        <MHDatePicker
          visible={true}
          title='开始日期'
          type={MHDatePicker.TYPE.DATE}
          onDismiss={() => this.setState({ showSDateEvFilter: false })}
          onSelect={res => this.onStartDateSelect(res)}
        />
      )
    } else if (this.state.showEDateEvFilter) {
      return (
        <MHDatePicker
          visible={true}
          title='结束日期'
          type={MHDatePicker.TYPE.DATE}
          onDismiss={() => this.setState({ showEDateEvFilter: false })}
          onSelect={res => this.onEndDateSelect(res)}
        />
      )
    }
  }
  // 时分筛选
  renderTimeFilterDlg() {
    if (this.state.showSTimeEvFilter) {
      return (
        <MHDatePicker
          visible={true}
          title='开始时间'
          type={MHDatePicker.TYPE.TIME24}
          onDismiss={() => this.setState({ showSTimeEvFilter: false })}
          onSelect={res => this.onStartTimeSelect(res)}
        />
      )
    } else if (this.state.showETimeEvFilter) {
      return (
        <MHDatePicker
          visible={true}
          title='结束时间'
          type={MHDatePicker.TYPE.TIME24}
          onDismiss={() => this.setState({ showETimeEvFilter: false })}
          onSelect={res => this.onEndTimeSelect(res)}
        />
      )
    }
  }
  // 确定按钮
  renderButton() {
    return (
      <TouchableWithoutFeedback
        onPress={() => {
          this.onPressConfirm()
        }}>
        <Text style={{ textAlign: "center", borderRadius: 180, margin: 10, marginStart: 30, marginEnd: 30, padding: 10, fontSize: 15, backgroundColor: "#32BAC0", color: "#ffffff" }}>
          {LocalizedStrings["csps_right"]}
        </Text>
      </TouchableWithoutFeedback>
    )
  }
  //点击确定后进行事件反馈时显示loading
  renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", backgroundColor: "#ccc", opacity: 0.8 }} zIndex={1}>
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={"xm#fff"}
          size={"large"}
        />
        <Text style={{ marginTop: 10, fontSize: 12, color: "#fff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }
  // 反馈完毕之后 显示是否退出本页面的弹框
  renderDialog() {
    return (
      <MessageDialog
        visible={this.state.confirmDialogVisible}
        title={'反馈完毕'}
        message={'请确认是否退出反馈页面？'}
        canDismiss={true}
        onDismiss={(_) => {
          this.setState({ confirmDialogVisible: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            // style: { color: 'lightpink' },
            callback: (_) => {
              console.log('onCancel');
              this.setState({ confirmDialogVisible: false });
            }
          },
          {
            text: LocalizedStrings["action_confirm"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              console.log('22222')
              this.setState({ confirmDialogVisible: false });
              this.props.navigation.goBack();
            }
          }
        ]}

      />
    );
  }

   // 反馈时 数据为空 弹出提示框
   renderDataNullDialog() {
    return (
      <MessageDialog
        visible={this.state.showDataNull}
        title={'反馈数据为空'}
        message={'请确认是否继续反馈？'}
        canDismiss={true}
        onDismiss={(_) => {
          this.setState({ showDataNull: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            // style: { color: 'lightpink' },
            callback: (_) => {
              console.log('onCancel');
              this.setState({ showDataNull: false });
              this.props.navigation.goBack();
            }
          },
          {
            text: LocalizedStrings["action_confirm"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              console.log('22222')
              this.setState({ showDataNull: false });
              
            }
          }
        ]}

      />
    );
  }

  mCommonEventHeader = (aExtraPadding = 0) => {
    return (
      <TouchableOpacity style={[BaseStyles.row, {
        alignItems: "center", justifyContent: "flex-start",
        height: CommonHeaderH, paddingTop: 14, paddingBottom: 12, paddingRight: 10, paddingLeft: 28
      }]}
        onPress={() => { this.setState({ showEvFilter: true }); }}>
        <Text

          style={[BaseStyles.text12, { paddingLeft: aExtraPadding, color: "#8C93B0", fontWeight: "bold" }]}>{this.state.evFilter.name}</Text>
        <Image
          accessibilityLabel={DescriptionConstants.kj_1_8}
          style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
      </TouchableOpacity>
    );
  }

  mCommonDateHeader = (aExtraPadding = 0) => {
    if (aExtraPadding == 0) {
      return (
        <TouchableOpacity style={[BaseStyles.row, {
          alignItems: "center", justifyContent: "flex-start",
          height: CommonHeaderH, paddingTop: 14, paddingBottom: 12, paddingRight: 10, paddingLeft: 28
        }]}
          onPress={() => { this.setState({ showSDateEvFilter: true }); }}>
          <Text

            style={[BaseStyles.text12, { paddingLeft: aExtraPadding, color: "#8C93B0", fontWeight: "bold" }]}>{this.state.startDate == 0 ? "开始日期" : this.state.startDate.rawString}</Text>
          <Image
            accessibilityLabel={DescriptionConstants.kj_1_8}
            style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
        </TouchableOpacity>
      );
    } else {
      return (
        <TouchableOpacity style={[BaseStyles.row, {
          alignItems: "center", justifyContent: "flex-start",
          height: CommonHeaderH, paddingTop: 14, paddingBottom: 12, paddingRight: 10, paddingLeft: 28
        }]}
          onPress={() => { this.setState({ showEDateEvFilter: true }); }}>
          <Text

            style={[BaseStyles.text12, { paddingLeft: aExtraPadding, color: "#8C93B0", fontWeight: "bold" }]}>{this.state.endDate == 0 ? "结束日期" : this.state.endDate.rawString}</Text>
          <Image
            accessibilityLabel={DescriptionConstants.kj_1_8}
            style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
        </TouchableOpacity>
      );
    }

  }
  mCommonTimeHeader = (aExtraPadding = 0) => {
    if (aExtraPadding == 0) {
      return (
        <TouchableOpacity style={[BaseStyles.row, {
          alignItems: "center", justifyContent: "flex-start",
          height: CommonHeaderH, paddingTop: 14, paddingBottom: 12, paddingRight: 10, paddingLeft: 28
        }]}
          onPress={() => { this.setState({ showSTimeEvFilter: true }); }}>
          <Text
            style={[BaseStyles.text12, { paddingLeft: aExtraPadding, color: "#8C93B0", fontWeight: "bold" }]}>{this.state.startTime == 0 ? "开始时间" : this.state.startTime.rawString}</Text>
          <Image
            accessibilityLabel={DescriptionConstants.kj_1_8}
            style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
        </TouchableOpacity>
      );
    } else {
      return (
        <TouchableOpacity style={[BaseStyles.row, {
          alignItems: "center", justifyContent: "flex-start",
          height: CommonHeaderH, paddingTop: 14, paddingBottom: 12, paddingRight: 10, paddingLeft: 28
        }]}
          onPress={() => { this.setState({ showETimeEvFilter: true }); }}>
          <Text
            style={[BaseStyles.text12, { paddingLeft: aExtraPadding, color: "#8C93B0", fontWeight: "bold" }]}>{this.state.endTime == 0 ? "结束时间" : this.state.endTime.rawString}</Text>
          <Image
            accessibilityLabel={DescriptionConstants.kj_1_8}
            style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
        </TouchableOpacity>
      );
    }

  }

  mLiveHeader = () => {
    return null;
  }
  // 标题
  renderHeader() {
    if (this.state.fullScreen) {
      return null;
    }
    let marginTop = 40;
    if (Host.isPad) {
      marginTop = 0;
    }
    return (
      <View style={[BaseStyles.row, {
        position: "absolute", height: 50, paddingLeft: 12, marginTop: marginTop, paddingRight: 12, // 12+40
        backgroundColor: BaseStyles.mainBg.backgroundColor,
        width: "100%"
      }]}>

        <ImageButton
          accessibilityLabel={DescriptionConstants.kj_1_1}
          style={BaseStyles.icon40}
          source={Util.isDark() ? require("../../Resources/Images/icon_back_black_nor_dark.png") : require("../../Resources/Images/icon_back_black.png")}
          onPress={() => {
            this.naviBack();
          }} />
        <Text
          accessibilityLabel={DescriptionConstants.kj_1_2}
          style={[BaseStyles.text18, { fontWeight: "bold", textAlign: 'center' }]}>
          {"用户反馈"}
        </Text>
        <TouchableOpacity style={BaseStyles.icon40}></TouchableOpacity>
      </View>
    );
  }

  // override
  naviBack() {
    this.mPauseL();
    if (this.props.navigation.state.params.shouldPopTopWhenBack) {
      this.props.navigation.popToTop(); // 弹出到栈顶。
    } else {
      this.props.navigation.goBack();
    }
  }

  onBack() {
    if (this.props.navigation.state.params.shouldPopTopWhenBack) {
      this.props.navigation.popToTop(); // 弹出到栈顶。
      return true;// 拦截
    } else {
      // this.props.navigation.goBack();
      return false;
    }
  }
}
