'use strict';

import { Device } from "miot";
import { Styles } from 'miot/resources';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { StyleSheet, ScrollView, View, Image, Text, SafeAreaView, TouchableOpacity, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from '../components/Toast';
import Host from "miot/Host";
import { styles } from './SettingStyles';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import StorageKeys from '../StorageKeys';
import VersionUtil from "../util/VersionUtil";
import Service from "miot/Service";
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";

// PropertyParam{did='1020163363', siid=2, piid=5, value=null, timestamp=0, resultCode=-1}
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import AlarmUtilV2, { PIID_CAMERA_HDR, PIID_CAMERA_WDR, SIID_CAMERA_CONTROL } from "../util/AlarmUtilV2";
import BaseSettingPage from "../BaseSettingPage";
import CameraConfig from '../util/CameraConfig';

const WDR_SPEC = {
  Key: { did: Device.deviceID, siid: 2, piid: 5 },
  ON: [{ did: Device.deviceID, siid: 2, piid: 5, value: true }],
  OFF: [{ did: Device.deviceID, siid: 2, piid: 5, value: false }]
};

const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead
const imageWidth = (kWindowWidth - 48) / 2;
const imageHeight = imageWidth / 1.5;
const viewWidth = Dimensions.get("window").width - 48;
const viewHeight = ((Dimensions.get("window").width - 48) * 9) / 16;
export default class WDRSetting extends BaseSettingPage {



  constructor(props, context) {
    super(props, context);

    this.state = {
      wdrMode: false
    };
    this.wdrMode = false;
  }
  getTitle() {
    return LocalizedStrings['setting_wdr'];
  }
  renderSettingContent() {
    return (
      <View style={styles.container}>
        {/*   <View style={{ marginHorizontal: 24,  marginBottom: 20, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
            {
              [
                { text: LocalizedStrings['ldc_before_activation'], uri: require('../../Resources/Images/wdr_before.webp') },
                { text: LocalizedStrings['ldc_after_activation'], uri: require('../../Resources/Images/wdr_after.webp') }
              ].map((item, i) => {
                return (
                  <View
                    style={{ alignItems: 'center' }}
                    key={i}
                  >
                    <Image
                      key={i}
                      style={[{ width: imageWidth,height:imageWidth*1.12},i===0&&{borderBottomLeftRadius:12,borderTopLeftRadius:12},i===1&&{borderBottomRightRadius:12,borderTopRightRadius:12}]}
                      source={item.uri}
                      accessibilityLabel={DescriptionConstants.example_image}
                    />
                    <Text
                      numberOfLines={2}
                      style={{ paddingHorizontal: 10, position: "absolute", bottom: 4, fontSize: 12, color: 'xm#ffffff' }}
                    >
                      {item.text}
                    </Text>
                  </View>
                );
              })
            }
          </View> */}
        <View style={{ position: 'relative', marginHorizontal: 24, marginBottom: 20, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
          <Image
                 
            resizeMode={"contain"}
            style={[{ width: viewWidth, height: viewHeight }]}
            source={require('../../Resources/Images/wdr_Icon.png')}
            accessibilityLabel={DescriptionConstants.example_image}
          />
          <View style={{ position: "absolute", bottom: 8, width: viewWidth, flexDirection: 'row' }}
          >
            <Text
              numberOfLines={2}
              
              style={{ width: viewWidth / 2, fontSize: 12, color: 'xm#ffffff', textAlign: 'center' }}
            >
              {LocalizedStrings['ldc_before_activation']}
            </Text>
            <Text
              numberOfLines={2}
              style={{ width: viewWidth / 2, fontSize: 12, color: 'xm#ffffff', textAlign: 'center' }}
            >
              {LocalizedStrings['ldc_after_activation']}
            </Text>
          </View>
        </View>
       

        <View style={{ marginHorizontal: 28 }}
          key={6}
        >

          <Text
            accessibilityLabel={DescriptionConstants.sz_4_61}
            style={{ fontSize: 14, color: 'rgba(0,0,0,0.5)',lineHeight:21 }}
          >
            {/* { `${LocalizedStrings['function_desc']  }\n${  LocalizedStrings['wdr_tip']}` } */}
            { LocalizedStrings['wdr_tip']}
          </Text>

          <Text 
            accessibilityLabel={DescriptionConstants.sz_4_66}
            style={{ marginTop: 12, fontSize: 14, color: 'rgba(0,0,0,0.5)' ,lineHeight:21 }}>
            {LocalizedStrings['common_notice']}{LocalizedStrings['wdr_notify_V2']}
          </Text>
          {/*  <Text 
              // accessibilityLabel={DescriptionConstants.sz_4_67}
              style={{ fontSize: 13, color: 'rgba(0,0,0,0.5)' }}>
              {LocalizedStrings['wdr_notify_V2']}
            </Text> */}
        </View>
        <View style={{
          height: 0.5,
          marginHorizontal: 28,
          backgroundColor: "#e5e5e5",
          marginBottom: 20,
          marginTop: 20 }}
        key={8}
        />
        <View style={[styles.featureSetting, { justifyContent: 'center' }]}>
          <ListItemWithSwitch
            accessibilityLabel={DescriptionConstants.sz_4_59_1}
            title={LocalizedStrings['setting_wdr']}
            value={this.state.wdrMode}
            onValueChange={(value) => this._onWDRValueChange(value)}
            showSeparator={false}
            titleStyle={{ fontWeight: 'bold' }}
            unlimitedHeightEnable={true}
            titleNumberOfLines={3}
            key={3}
            onPress={() => {
            }}
            accessibilitySwitch={{
              accessibilityLabel: DescriptionConstants.sz_4_59_1
            }}
          />
        </View>
      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    if (WDRSetting.isRequsting) {
      // delay 1000ms再请求；
      setTimeout(() => {
        WDRSetting.get_wdr().then((res) => {
          this.setState({
            wdrMode: res
          });
        }).catch((err) => {
          Toast.fail('c_get_fail', err);
        });
      }, 1000);
      
    } else if (this.props.navigation.state?.params?.wdrMode != null) {
      // reuse state;
      this.setState({ wdrMode: this.props.navigation.state.params.wdrMode });
    } else {
      WDRSetting.get_wdr().then((res) => {
        this.setState({
          wdrMode: res
        });
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
    }
    

  }

  _onWDRValueChange(value) {
    TrackUtil.reportClickEvent("WDROnOff_ClickNum");
    Toast.loading('c_setting');
    console.log(`change wdr${ value }`);

    if (VersionUtil.isUsingSpec(Device.model)) {
      let param = [{ "sname": SIID_CAMERA_CONTROL, "pname": CameraConfig.isSupportHDR(Device.model) ? PIID_CAMERA_HDR : PIID_CAMERA_WDR, value: value }];
      // let setValue = value ? WDR_SPEC.ON : WDR_SPEC.OFF;
      AlarmUtilV2.setSpecPValue(param)
        .then((result) => {

          let success = result[0].code == 0;
          if (success) {
            this.setState({
              wdrMode: value
            });
            Toast.success('c_set_success');
            StorageKeys.WDR_MODE = value ? 'on' : 'off';
          } else {
            this.setState({
              wdrMode: this.state.wdrMode
            });
            Toast.fail('c_set_fail');
          }

          
          if (this.props.navigation.state.params.selectWDRCallBack) {
            console.log(`callback wdr${ value }`);
            this.props.navigation.state.params.selectWDRCallBack(success ? value : !value);
          }
        }).catch((err) => {
          Toast.fail('c_set_fail', err);
        });
    } else {
      RPC.callMethod("set_wdr", [
        value ? 'on' : 'off'
      ]).then((res) => {
        console.log(`myth res:${ res.result[0] }`);
        let wdr = res.result[0] == 'OK' ? value : !value;
        StorageKeys.WDR_MODE = wdr ? 'on' : 'off';
        this.setState({
          wdrMode: wdr
        });
        if (this.props.navigation.state.params.selectWDRCallBack) {
          console.log(`callback wdr${ wdr }`);
          this.props.navigation.state.params.selectWDRCallBack(wdr);
        }
        if (res.result[0] == 'OK') {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({
          wdrMode: !value
        });
        Toast.fail('c_set_fail', err);
      });

    }
  }

  static get_wdr() {
    
    this.isRequsting = true;
    return new Promise((resolve, reject) => {
      if (VersionUtil.isUsingSpec(Device.model)) {
        let param = [{ "sname": SIID_CAMERA_CONTROL, "pname": CameraConfig.isSupportHDR(Device.model) ? PIID_CAMERA_HDR : PIID_CAMERA_WDR }];
        AlarmUtilV2.getSpecPValue(param, 2)
          .then((result) => {
            this.isRequsting = false;
            console.log(result);
            if (result instanceof Array && result.length >= 1) {
              let htValue = result[0].value;
              console.log(`why!, htValue=${ htValue }`);
              resolve(htValue);
            } else {
              reject("invalid response");
            }
          })
          .catch((error) => {
            this.isRequsting = false;
            console.log(error);
            reject(error);
          });

      } else {
        RPC.callMethod("get_prop", [
          'wdr'
        ]).then((res) => {
          this.isRequsting = false;
          console.log(`device get_wdr${ res.result[0] }`);
          resolve(res.result[0] == 'on');
        }).catch((err) => {
          this.isRequsting = false;
          reject(err);
        });

      }

    });

  }

}
