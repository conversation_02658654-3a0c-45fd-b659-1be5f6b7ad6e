import React from "react";
import { ScrollView, View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableOpacity, Linking, DeviceEventEmitter, AppState } from "react-native";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import StorageKeys from "../StorageKeys";
import Toast from "../components/Toast";
import CameraConfig from "../util/CameraConfig";
import { Service, Host, Device, Entrance, DarkMode, API_LEVEL } from "miot";
import LogUtil from "../util/LogUtil";

import { PackageEvent } from "miot/Package";
import API from "../API";
import Util from "../util2/Util";
import CallUtil, { CALL_TYPE } from "../util/CallUtil";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import BaseSettingPage from "../BaseSettingPage";
import VersionUtil from "../util/VersionUtil";
import { ChoiceDialog, MessageDialog, AbstractDialog } from "mhui-rn";
import { strings as I18n } from "miot/resources";

import ChoiceItem from "../widget/ChoiceItem";
const TAG = "ChoiceContactPage";

/**
 * @Author: byh
 * @Date: 2024/6/11
 * @explanation:
 * 选择、更换联系人
 * 1、可多选
 * 2、已添加的联系人不可取消，不可选择
 * 3、最多选择五个联系人
 * 4、仅可查看联系人不可选择
 ******************************************************** */

const MIN_SDK=10101
export default class ChoiceContactPage extends BaseSettingPage {
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      contactsData: [],

      shareContactsData: [],
      // 专门存储已经设置了的联系人的uid，不包含当前更好的联系人
      usedUidArr: [],
      canAdd: false,
      showMemberTypeDialog: false,
      selectUploadIntervalIndex: 0,
      showEmpowerDialog: false,
      showUpdateAppDialog: false,
      isGoBack: false
    };
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.callSettingData = null;
    this.homeMemberLength = 0;
    this.inviteUser = {};
    this.appState = AppState.currentState;
  }

  getTitle() {
    return LocalizedStrings["wx_invite_contact"];
  }

  componentDidMount() {
    super.componentDidMount();
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });
  
    this.willFocusSubscription = this.props.navigation.addListener("willFocus", () => {
      // this._getSetting();
      // console.log("进来了11111");
      // if(this.state.isGoBack){
      //   console.log("进来了");
      // }
    });
    this.willPauseListener = PackageEvent.packageDidResume.addListener(() => {
      //在此处控制返回上一页
      if (this.state.isGoBack) {
        setTimeout(() => {
          this.props?.navigation?.goBack();
        }, 500);
       
      }
      console.log("进来了1111111111",this.state.isGoBack);
    });
    
    this.willBlurSubscription = this.props.navigation.addListener("willBlur", () => {
      // this._getSetting();
      console.log("离开了");
      this.setState({ isGoBack: false });
      
    });
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
      // // 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });

    // let did = Device.extraObj?.split?.parentId;

    this.getContactData();
  }

  async getContactData() {
    let setContactData = await this.getSetContactData();
    let contactsData = await this.getHomeMemberContactData(setContactData);
    let shareContactsData = await this.getShareContactData(setContactData);
    const list = [];
   
    const lastPageContactsData = this.props?.navigation?.state?.params?.contactsData;
    this.homeMemberLength = contactsData?.length;
    contactsData?.forEach((item) => {
      if (lastPageContactsData.findIndex((data) => data.mijia === item.userid) < 0) {
        list.push(item);
      }
    });
    const shareList = [];
    shareContactsData.forEach((item) => {
      if (lastPageContactsData.findIndex((data) => data.mijia === item.userid) < 0) {
        shareList.push(item);
      }
    });
    let count = this.getAlreadyContact(list, shareList);

    this.setState({
      contactsData: list,
      usedUidArr: setContactData ? setContactData : [],
      shareContactsData: shareList ? shareList : [],
      isLimit: count === 10
    });
  }

  async getSetContactData() {
    return await DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting)
      .then((res) => {
        console.log("=======prefix success", res);
        if (res.code == 0) {
          let settingsData = res.result.settings;
          if (settingsData && settingsData.call_setting) {
            let data = JSON.parse(settingsData.call_setting);
            let index = 0;
            let uidArr = [];
            // 手势：联系人都可以设置
            Object.keys(data).forEach((key) => {
              if (key.indexOf("key") != -1) {
                let key1Data = data[key];
                uidArr.push(key1Data.mijia);
                // // 处理用于排序的index,兼容老数据
                // if (!key1Data.hasOwnProperty('index')) {
                //   key1Data['index'] = index;
                //   index++;
                // }
              }
            });
            this.callSettingData = data;
            // this.setState({ usedUidArr: uidArr });
            return uidArr;
          }
        }
        return [];
      })
      .catch((error) => {
        console.log("======= error: ", error);
        return [];
      });
  }
  async getHomeMemberContactData(setContactData = []) {
    return await Device.getHomeMemberList({})
      .then((res) => {
        console.log("++++++1111111111", res);
        let resData = [];
        if (res.code == 0) {
          resData = res.data;
          // let index
          resData.map((item) => {
            item.nickname = item.nick_name;
            item.userid = item.uid;
            // 已经添加为联系人
            item.isAdded = setContactData ? setContactData.includes(item.uid) || setContactData.includes(parseInt(item.uid)) : false;
            item.isSelect = setContactData ? setContactData.includes(item.uid) || setContactData.includes(parseInt(item.uid)) : false;
            return item;
          });
          // 排序，把当前账号的排在第一个
          resData.forEach((item, index) => {
            if (item.userid == Service.account.ID) {
              resData.unshift(resData.splice(index, 1)[0]);
            }
          });

          // this.setState({ contactsData: resData, usedUidArr: setContactData });
          // ********** - ********** = **********
        }
        return resData;
      })
      .catch((error) => {
        console.log("++++++err", error);
        return [];
      });
  }
  async getShareContactData(setContactData) {
    return await Service.callSmartHomeAPI("/share/get_share_user", { did: Device.deviceID, pid: Device.pd_id })
      .then((res) => {
        // let shareList = res.list;
        let newArray = res.list.filter((item) => item.status == 1);

        // let newArray = res.list;
        newArray.map((item) => {
          item.isAdded = setContactData ? setContactData.includes(item.userid) || setContactData.includes(parseInt(item.userid)) : false;
          item.isSelect = setContactData ? setContactData.includes(item.userid) || setContactData.includes(parseInt(item.userid)) : false;
          return item;
        });
        console.log("res=====", newArray);
        // this.setState({ shareContactsData: newArray });
        return newArray;
      })
      .catch((e) => {
        console.log("'''''", e);
        return [];
      });
  }

  getAlreadyContact(contactsData, shareContactsData) {
    let count = 0;
    contactsData.forEach((item) => {
      if (item.isSelect) {
        count += 1;
      }
    });

    shareContactsData.forEach((item) => {
      if (item.isSelect) {
        count += 1;
      }
    });

    return count;
  }
  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.willBlurSubscription && this.willBlurSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
    this._willFocusListener && this._willFocusListener.remove();
    this.willPauseListener && this.willPauseListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }
  // renderSettingBottomContent() {
  //   let btnContainerStyle = {
  //     width: "85%",
  //     height: 46,
  //     backgroundColor: this.state.canAdd ? "#32BAC0" : "#C1EAEC",
  //     justifyContent: "center",
  //     borderRadius: 23,
  //   };

  //   let btnTextStyle = {
  //     fontSize: 16,
  //     color: "#ffffff",
  //     fontWeight: "bold",
  //     textAlign: "center",
  //   };
  //   return (
  //     <View style={{ alignItems: "center", marginBottom: 27, marginTop: 18 }}>
  //       <Text style={{ marginBottom: 18, fontSize: 14, color: "#00000099" }}>{LocalizedStrings["add_contact_limit"]}</Text>

  //       <TouchableOpacity
  //         style={btnContainerStyle}
  //         disabled={!this.state.canAdd}
  //         onPress={() => {
  //           this.uploadCallSetting();
  //         }}
  //       >
  //         <Text style={btnTextStyle}>{LocalizedStrings["add"]}</Text>
  //       </TouchableOpacity>
  //     </View>
  //   );
  // }
  renderSettingContent() {
    // ios 和安卓的API_LEVEL不一样，要进行区分 以后都是10101
    const isShowInviteView = API_LEVEL >= MIN_SDK;
    const list = this.state.contactsData.concat(this.state.shareContactsData);
    return (
      <View
        style={{
          display: "flex",
          height: "100%",
          flex: 1,
          flexDirection: "column",
          backgroundColor: Util.isDark() ? "#xm000000" : "#FFFFFF"
        }}
      >
        <View style={styles.container} key={102}>
          {/* <Text style={styles.title_group}>{LocalizedStrings["family_contact"].replace("%d", this.state.contactsData.length)}</Text> */}

          <FlatList
            // data={this.state.contactsData.concat(this.state.shareContactsData)}
            data={list}
           
            renderItem={this._renderContactItem}
            ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
            keyExtractor={(item, index) => `key_${ index }_${ item.isMax }`}
          />
          {list?.length ? <View style={{ marginBottom: 15 }}></View> : null}
          <View style={styles.inviteMembers}>
            <View style={{ flex: 1, justifyContent: 'center' }}>
              <Text style={styles.inviteMembersTitle}>{LocalizedStrings["wx_invite_member_call"]}</Text>
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity
                  style={styles.inviteMembersButton}
                  onPress={() => {
                    if (!isShowInviteView) {
                      // Toast.show(LocalizedStrings["place_update_app"]);
                      this.setState({ showUpdateAppDialog: true });
                      return;
                    }
                    if (this.homeMemberLength >= 11) {
                      Toast.show(LocalizedStrings["member_maximum_limit"]);
                      return;
                    }

                    this.setState({ showMemberTypeDialog: true });
                  }}
                >
                  <Text style={styles.inviteMembersButtonText}>{LocalizedStrings["wx_invite_new_member"]}</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ alignItems: "center", flex: 1 }}>
              <Image  resizeMode="contain" style={styles.inviteMembersImage} source={require("../../Resources/Images/inviteMembersImage.png")}></Image>
            </View>
          </View>
        
          {/*  <ListItem
            containerStyle={{ display: CameraConfig.isSupportWDR(Device.model) ? "flex" : "none" }}
            title={"邀请成员"}
            value={""}
            showSeparator={false}
            onPress={() => {
              this.setState({ showMemberTypeDialog: true });
            }}
            titleStyle={{ fontWeight: "bold" }}
            valueNumberOfLines={3}
            titleNumberOfLines={2}
          /> */}
          {/* <ListItem */}
          {/*  containerStyle={{display: CameraConfig.isSupportWDR(Device.model) ? "flex" : "none"}} */}
          {/*  title={'仅可通话'} */}
          {/*  value={""} */}
          {/*  showSeparator={false} */}
          {/*  onPress={() => { */}
          {/*    this.goWxInvite('call',LocalizedStrings['wx_invite_only_call']); */}
          {/*  }} */}
          {/*  titleStyle={{fontWeight: 'bold'}} */}
          {/*  valueNumberOfLines={3} */}
          {/*  titleNumberOfLines={2} */}
          {/* /> */}
          {/* <Text style={[styles.title_group, { marginTop: 18 }]}>
            {LocalizedStrings["share_contact"].replace("%d", this.state.shareContactsData ? this.state.shareContactsData.length : 0)}
          </Text>
          {this.state.shareContactsData && this.state.shareContactsData.length > 0 ? (
            <FlatList
              data={this.state.shareContactsData}
              ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
              renderItem={this._renderContactItem}
              keyExtractor={(item, index) => `key_${ index }_${ item.isMax }`}
            />
          ) : (
            <View style={{ alignItems: "center", marginBottom: 60, marginTop: 50 }}>
              <Image style={{ height: 60, width: 92 }} source={require("../../Resources/Images/icon_share_no.webp")} />
              <Text style={styles.empty_share_title}>{LocalizedStrings["no_share_user"]}</Text>
              <Text style={styles.empty_share_subtitle}>{LocalizedStrings["no_share_user_desc"]}</Text>
              <TouchableOpacity
                style={{
                  marginTop: 12,
                  paddingHorizontal: 20,
                  paddingVertical: 8,
                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                  minHeight: 34,
                  borderRadius: 17
                }}
                onPress={() => {
                  let newData = { url: "https://home.mi.com/views/article.html?articleId=577935712000000001" };
                  this.props.navigation.navigate("NativeWebPage", newData);
                }}
              >
                <Text style={{ color: "rgba(0, 0, 0, 0.80)", fontSize: 13, fontWeight: "bold" }}>{LocalizedStrings["know_more"]}</Text>
              </TouchableOpacity>
            </View>
          )} */}
        
          <View style={[styles.inviteMembers, styles.inviteCallMembers]}>
            <View style={{ flex: 1, justifyContent: 'center' }}>
              <Text style={[styles.inviteMembersTitle, styles.inviteCallColor]}>{LocalizedStrings["wx_invite_only_for_call"]}</Text>
              <Text style={[styles.descTitle, styles.inviteCallColor]}>{LocalizedStrings["wx_invite_only_for_call_desc"]}</Text>
            
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity
                  style={[styles.inviteMembersButton, styles.inviteCallBgColor]}
                  onPress={() => {
                    if (!isShowInviteView) {
                      // Toast.show(LocalizedStrings["place_update_app"]);
                      this.setState({ showUpdateAppDialog: true });
                      return;
                    }
                    this.goWxInvite("call", LocalizedStrings["wx_invite_only_call"]);
                  }}
                >
                  <Text style={styles.inviteMembersButtonText}>{LocalizedStrings["wx_invite_wechat_user"]}</Text>
                </TouchableOpacity>
              </View>
             
            </View>
            <View style={{ alignItems: "center" }}>
              <Image style={[styles.inviteCallMembersImage, styles.callMembersImage]} source={require("../../Resources/Images/inviteCallMembersImage.png")}></Image>
            </View>
          </View>
        
        </View>
        {this.memberTypeDialog()}
        {this._renderToEmpowerDialog()}
        {this._renderUpdateAppDialog()}
      </View>
    );
  }

  openAppStore() {
    let yourAppID = "957323480";
    // const appStoreUrl = `https://apps.apple.com/app/${yourAppID}`;
    const appStoreUrl = `https://apps.apple.com/app/id${ yourAppID }`;
    Linking.openURL(appStoreUrl).then((res) => {
      console.log("openURL success", res);
    }).catch((err) => console.error('An error occurred', err));
  }

  memberTypeDialog() {
    const options = [
      { title: LocalizedStrings["wx_invite_permission_member"], subtitle: LocalizedStrings["wx_invite_permission_member_desc"] },
      { title: LocalizedStrings["wx_invite_permission_admin"], subtitle: LocalizedStrings["wx_invite_permission_admin_desc"] }
    ];
    return (
      <AbstractDialog
        visible={this.state.showMemberTypeDialog}
        title={LocalizedStrings["wx_invite_please_choose_permission"]}
        useNewType={true}
        useNewTheme={true}
        dialogStyle={{
          allowFontScaling: false,
          unlimitedHeightEnable: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          },
          itemSubtitleNumberOfLines: 3,
          itemSubtitleStyle: {
            marginRight: 10,
            fontSize: 13
      
          }
        }}
        color={"#32BAC0"}
        buttons={[
          {
            text: I18n.cancel,
            callback: () => {
              this.setState({ showMemberTypeDialog: false, selectUploadIntervalIndex: 0 });
            }
          },
          {
            text: I18n.ok,
            callback: (res) => {
              console.log("ssssss", res);
              // "call" "homeMemberAndCall" "homeAdminAndCall"
              this.setState({ showMemberTypeDialog: false, selectUploadIntervalIndex: 0 });
              let inviteType = this.state.selectUploadIntervalIndex === 0 ? "homeMemberAndCall" : "homeAdminAndCall";
              // let inviteType = res[0] === 0 ? 'homeAdmin' : 'homeMember';
              this.goWxInvite(inviteType);
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showMemberTypeDialog: false, selectUploadIntervalIndex: 0 });
        }}
      >
        <View style={{ marginBottom: 12 }}>
          {options.map((item, i) => {
            return (
              <ChoiceItem
                title={item?.title}
                subtitle={item?.subtitle}
              
                key={i + 100}
                keyString={i + 100}
                containerStyle={{ paddingLeft: 28, paddingBottom: 16, paddingTop: 16 }}
                checked={this.state.selectUploadIntervalIndex === i}
                backgroundColor={this.state.selectUploadIntervalIndex === i ? null : "white"}
                type={"single"}
                titleStyle={{ fontSize: 16 }}
                subtitleStyle={[{ fontSize: 13 }, this.state.selectUploadIntervalIndex === i ? {} : { color: 'rgba(0,0,0,0.6)' }]}
                itemStyleType={1}
                selectIcon={require("../../Resources/Images/icon_selected_choice.png")}
                onlyChecked={true}
                onValueChange={() => {
                  this.setState({
                    selectUploadIntervalIndex: i
                  });
                }}
              />
            );
          })}
        </View>
      </AbstractDialog>
    );
  }

  _renderToEmpowerDialog() {
    return (
      <AbstractDialog
        visible={this.state.showEmpowerDialog}
        title={LocalizedStrings["cs_invite_empower"]}
        subtitle={LocalizedStrings["cs_invite_empower_desc"]}
        dialogStyle={{
          titleStyle: {
            fontSize: 16.5
          }
        }}
        showSubtitle={false}
        useNewTheme
        canDismiss={false}
        onDismiss={() => {
          this.setState({ showEmpowerDialog: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["cs_not_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
            }
          },
          {
            text: LocalizedStrings["cs_wx_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
              if (this.inviteUser) {
                let params = {
                  // paramType: "requestWxDeviceVoIP",
                  paramType: "shareWxDeviceVoIP",
                  did: Device.deviceID.toString(),
                  userId: `${ this.inviteUser.userid }`,
                  // userId:Service.account.ID.toString(),
                  deviceName: Device.name,
                  model: Device.model,
                  ownerId: Service.account.ID.toString()
                };
                console.log("======", params);
                // 说明是仅通话联系人
                
                if (this.state.shareContactsData?.findIndex((data) => data.userid === this.inviteUser.userid) > -1) {
                  this.goWxInvite("call", LocalizedStrings["wx_invite_only_call"]);
                } else {
                  // Host.ui.requestWxDeviceVoIP(params)
                  Host.ui.shareWxDeviceVoIP(params);
                  // 因为有的手机反应慢，所以需要延迟一下
                  // setTimeout(() => {
                  //     this.props?.navigation?.goBack();
                  // }, 2000);
                  this.setState({ isGoBack: true });
                }
              
              }
            }
          }
        ]}
      >
        <View>
          <Text
            style={{
              fontSize: 16,
              fontWeight: "400",
              marginHorizontal: 28,
              lineHeight: 24
            }}
          >
            {LocalizedStrings["cs_invite_empower_desc"]}
          </Text>

          <View
            style={{
              margin: 28,
              display: "flex",
              flexDirection: "row"
            }}
          >
            <Image
              style={{
                height: 170,
                borderRadius: 12,
                flex: 1
              }}
              source={require("../../Resources/Images/wx_empower.webp")}
            />
          </View>
        </View>
      </AbstractDialog>
    );
  }

  _renderUpdateAppDialog() {
    let myButtons = [
      {
        text: LocalizedStrings["btn_cancel"],
        callback: (_) => {
          this.setState({ showUpdateAppDialog: false });
        }
      },
      {
        text: LocalizedStrings["go_there"],
        callback: (_) => {
          this.setState({ showUpdateAppDialog: false });
          this.openAppStore();
        }
      }
    ];
    let msg = LocalizedStrings['update_warning_ios'];
    if (Platform.OS == "android") {
      myButtons = [
        {
          text: LocalizedStrings["offline_divice_ok"],
          // style: { color: 'lightpink' },
          callback: (_) => {
            this.setState({ showUpdateAppDialog: false });
          }
        }
      ];
      msg = LocalizedStrings['update_warning_android'];
    }
    return (
      <MessageDialog
        visible={this.state.showUpdateAppDialog}
        title={LocalizedStrings['update_warning']}
        message={msg}
        onDismiss={() => {
          this.setState({ showUpdateAppDialog: false });
        }}
        canDismiss={true}
        buttons={myButtons}
      />
    );
  }

  goWxInvite(inviteType, msg = LocalizedStrings["wx_invite_member"]) {
    let params = {
      inviteType: inviteType,
      // inviteType: "homeAdmin",
      did: Device.deviceID.toString(),
      userId: Service.account.ID.toString(),
      deviceName: Device.name,
      model: Device.model,
      wxMessageTitle: msg
    };
    console.log("==============params:", params);
    Host.ui.shareWxForInviteFriends(params);
    this.setState({ isGoBack: true });
    // 因为有的手机反应慢，所以需要延迟一下
    // setTimeout(() => {
    //   this.props?.navigation?.goBack();
    // }, 2000);
  
  }
  _renderContactItem = ({ item, index }) => {
    // let isSelected = item.isSelect;
    // let itemBgColor = isSelected ? "rgba(50, 186, 192, 0.10)" : Util.isDark() ? "#FFFFFFB2" : "#F0F0F0";
    let itemBgColor = "rgba(240, 240, 240, 1)";
    // let disabled =
    //   this.state.usedUidArr.includes(item.userid) ||
    //   this.state.usedUidArr.includes(parseInt(item.userid)) ||
    //   (!item.isSelect && this.state.isLimit) ||
    //   (item.isMax && this.state.isLimit);
    // let opacity = disabled ? { opacity: 0.5 } : { opacity: 1 };
    let nickname = item.nickname ? item.nickname : "";
    const isOwner = item.userid == Service.account.ID;
    if (isOwner) {
      nickname = `${ nickname }${ LocalizedStrings["call_me"] }`;
    }
    return (
      <TouchableOpacity
        style={[
          {
            paddingBottom: 15,
            paddingTop: 15,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            borderRadius: 12,
            marginHorizontal: 12,
            backgroundColor: itemBgColor
          }
        ]}
        // disabled={ disabled }
        onPress={() => {
          this.userPress(item, isOwner);
        }}
      >
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 16, width: "100%" }}>
          <Image
            key={`img_${ index }`}
            style={{ width: 40, height: 40, marginLeft: 16, borderRadius: 20 }}
            source={item.icon ? { uri: item.icon } : require("../../Resources/Images/icon_user.png")}
          />

          <View
            style={{
              display: "flex",
              flexDirection: "row",
              flexGrow: 1,
              paddingLeft: 12,
              paddingRight: 15,
              flex: 1,
              alignItems: "center"
            }}
          >
            <View style={{ display: "flex", flexDirection: "column" }}>
              <Text
                style={[
                  {
                    fontSize: 16,
                    fontWeight: "bold"
                  },
                  { color: "#000000" }
                ]}
              >
                {nickname}
              </Text>
              <Text style={[{ fontSize: 13 }, { color: "rgba(0, 0, 0, 0.60)" }]}>{item.userid ? item.userid : ""}</Text>
            </View>
          </View>

          <Text numberOfLines={3} style={[{ fontSize: 13, marginRight: 12, maxWidth: '35%' }, { color: "rgba(0, 0, 0, 0.40)" }]}>{LocalizedStrings[isOwner ? "cs_wx_empower" : "cs_wx_invite_empower"]}</Text>
          <Image style={{ width: 6, height: 11 }} source={Util.isDark() ? require(`../../Resources/Images/icon_right_dark.png`) : require(`../../Resources/Images/icon_right.png`)}></Image>
        </View>
      </TouchableOpacity>
    );
  };

  userPress(item, isOwner) {
    const isShowInviteView = API_LEVEL >= MIN_SDK;
    if (isOwner) {
      // 本账号点击去授权，后续进入插件不再弹出授权引导
      StorageKeys.WX_WARNING_DIALOG = true;
      StorageKeys.WX_WARNING_LIST_DIALOG = true;
      // 本账号直接授权
      let params = {
        paramType: "requestWxDeviceVoIP",
        did: Device.deviceID.toString(),
        userId: Service.account.ID.toString(),
        // ownerID:Service.account.ID.toString(),
        deviceName: Device.name,
        model: Device.model
      };
      Host.ui.requestWxDeviceVoIP(params);
      // 因为有的手机反应慢，所以需要延迟一下
      // this.props?.navigation?.goBack();
      this.setState({ isGoBack: true });
    
    } else {
      if (Device.isReadonlyShared) {
        Toast.success("cloud_share_hint");
        return;
      }
      if (!isShowInviteView) {
        // Toast.show(LocalizedStrings["place_update_app"]);
        this.setState({ showUpdateAppDialog: true });
        return;
      }
      // 其他账号，弹出邀请授权弹框
      this.inviteUser = item;
      this.setState({ showEmpowerDialog: true });
    }

    // if (item.isAdded) {
    //   console.log("=========");
    //   Toast.show(LocalizedStrings["add_contact_already_choose"]);
    //   return;
    // }
    // if (!item.isSelect && this.state.isLimit) {
    //   Toast.show(LocalizedStrings["add_contact_already_max"]);
    //   return;
    // }
    // // 临时选中
    // item.isSelect = !item.isSelect;

    // // 已添加的联系人数量
    // let count = this.getAlreadyContact(this.state.contactsData, this.state.shareContactsData);

    // console.log("================================count", count);
    // if (count === 10) {
    //   this.setState({
    //     canAdd: count !== 0,
    //     isLimit: count === 10,
    //     contactsData: this.state.contactsData.map((item, idx) => {
    //       if (!item.isSelect) {
    //         return { ...item, isMax: true };
    //       } else {
    //         return item;
    //       }
    //     }),
    //     shareContactsData: this.state.shareContactsData.map((item, idx) => {
    //       if (!item.isSelect) {
    //         return { ...item, isMax: true };
    //       } else {
    //         return item;
    //       }
    //     }),
    //   });
    // } else {
    //   this.setState({
    //     canAdd: count !== 0,
    //     isLimit: count === 10,
    //     contactsData: this.state.contactsData.map((item, idx) => {
    //       if (!item.isSelect) {
    //         return { ...item, isMax: false };
    //       } else {
    //         return item;
    //       }
    //     }),
    //     shareContactsData: this.state.shareContactsData.map((item, idx) => {
    //       if (!item.isSelect) {
    //         return { ...item, isMax: false };
    //       } else {
    //         return item;
    //       }
    //     }),
    //   });
    // }

    // CallUtil.getOpenIdByUid(item.userid).then(openId=>{
    //   this.uploadCallSetting(item,openId);
    // }).catch(error => {
    //   console.log("{{{{{{{{error",error);
    //   this.uploadCallSetting(item)
    // });
  }

  async uploadCallSetting() {
    // console.log("--------",item.userid, typeof (item.userid))
    if (!Device.isOnline) {
      Toast.fail("c_set_fail");
      return;
    }
    let upContactList = [];
    this.state.contactsData.forEach((item) => {
      if (item.isSelect && !item.isAdded) {
        upContactList.push(item);
      }
    });
    this.state.shareContactsData.forEach((item) => {
      if (item.isSelect && !item.isAdded) {
        upContactList.push(item);
      }
    });
    // if (upContactList.length == 0) {
    //   Toast.success('');
    //   return;
    // }
    let uploadData = JSON.parse(JSON.stringify(this.callSettingData));
    if (uploadData == null || JSON.stringify(uploadData) === "{}") {
      uploadData = { switch: { hand: 0, mijia: 1, wx: 1 } };
    }
    // upContactList新增的联系人
    let callIconSetting = {};
    let callIconSettingForUpdate = {};
    for (const item1 of upContactList) {
      console.log("-----------------", item1, uploadData);
      // 需要计算出这个联系人的key
      let key = this.getItemKey(uploadData);
      let indexKey = this.getItemIndex(uploadData);
      await CallUtil.getOpenIdByUid(item1.userid)
        .then((openId) => {
          this.addUploadData(uploadData, item1, openId, key);
          this.addUploadIconData(callIconSetting, callIconSettingForUpdate, item1, key);
        })
        .catch((error) => {
          this.addUploadData(uploadData, item1, "", key);
          this.addUploadIconData(callIconSetting, callIconSettingForUpdate, item1, key);
        });
    }
    // 处理用于排序的index
   // CallUtil.sortByIndex(uploadData);
    // // 需要更新数据
    let uploadDaraArr = [];
    // 基础信息
    let paramsStr = JSON.stringify(uploadData);
    console.log("update device setting base:", paramsStr);
    uploadDaraArr.push({ item: paramsStr, type: 0 });
    // 头像信息
    let iconKeys = Object.keys(callIconSettingForUpdate);
    if (iconKeys.length < 6) {
      let paramsIcon = JSON.stringify(callIconSettingForUpdate);
      console.log("update device setting:", paramsIcon);
      uploadDaraArr.push({ item: paramsIcon, type: 1 });
      // CallUtil.updateSettingToDevice([paramsIcon]);
    } else {
      let groupOne = {};
      iconKeys.slice(0, 5).forEach((key) => {
        groupOne[key] = callIconSettingForUpdate[key];
      });
      console.log("++++++++++++++1", groupOne);
      let groupTwo = {};
      iconKeys.slice(5).forEach((key) => {
        groupTwo[key] = callIconSettingForUpdate[key];
      });
      console.log("++++++++++++++2", groupOne);
      // 分两次下发
      let paramsGroupIconOne = JSON.stringify(groupOne);
      uploadDaraArr.push({ item: paramsGroupIconOne, type: 1 });

      let paramsGroupIconTwo = JSON.stringify(groupTwo);
      uploadDaraArr.push({ item: paramsGroupIconTwo, type: 1 });
    }
    Toast.loading("c_setting");
    Promise.all(
      uploadDaraArr.map((data) => {
        if (data.type === 0) {
          // 基础信息
          return CallUtil.updateSettingToDevice(data.item);
        } else {
          // 头像信息
          return CallUtil.updateIconSettingToDevice(data.item);
        }
      })
    )
      .then((res) => {
        console.log("add to success", res);
        // 更新到设备端成功 若更新到云端失败了
        // delete uploadData['method'];
        console.log("======================callSettingData", uploadData);
        Promise.all([
          DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, JSON.stringify(uploadData)),
          DeviceSettingUtil.setDeviceSettingArray(callIconSetting)
        ])
          .then((res) => {
            console.log(TAG, "服务器更新", res);
            Toast.success("add_contact_success");
            this.props.navigation.getParam("callback")();
            this.props.navigation.goBack();
          })
          .catch((error) => {
            Toast.success("add_contact_success");
            this.props.navigation.getParam("callback")();
            this.props.navigation.goBack();
          });
        // DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, JSON.stringify(uploadData)).then((res) => {
        //   console.log(TAG,"base info update success");
        //   DeviceSettingUtil.setDeviceSettingArray(callIconSetting).then((res) => {
        //     console.log(TAG,"icon info update success");
        //
        //   });
        // });
      })
      .catch((error) => {
        // 设置失败
        Toast.success("c_set_fail");
      });

    // console.log("======================callSettingData",uploadData);
    // DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, JSON.stringify(uploadData)).then((res) => {
    //   // 成功后 基础信息
    //   // 添加头像数据
    //   DeviceSettingUtil.setDeviceSettingArray(callIconSetting).then((res) =>{
    //     // 头像数据也添加成功
    //     // 文案提示改为添加联系人成功
    //     Toast.success('add_contact_success');
    //     uploadData['method'] = "0";
    //     let paramsStr = JSON.stringify(uploadData);
    //     console.log("update device setting base:",paramsStr);
    //     CallUtil.updateSettingToDevice([paramsStr]);
    //     // 下发头像数据
    //     let iconKeys = Object.keys(callIconSettingForUpdate);
    //     if (iconKeys.length < 6) {
    //       callIconSettingForUpdate['method'] = "1";
    //       let paramsIcon = JSON.stringify(callIconSettingForUpdate);
    //       console.log("update device setting:",paramsIcon);
    //       CallUtil.updateSettingToDevice([paramsIcon]);
    //     } else {
    //       //
    //       let groupOne = {};
    //       iconKeys.slice(0, 5).map(key => {
    //         groupOne[key] = callIconSettingForUpdate[key];
    //       });
    //       groupOne['method'] = "1";
    //       console.log("++++++++++++++1",groupOne);
    //       let groupTwo = {}
    //       iconKeys.slice(5).map(key => {
    //         groupTwo[key] = callIconSettingForUpdate[key];
    //       });
    //       groupTwo['method'] = "1";
    //       console.log("++++++++++++++2",groupOne);
    //       // 分两次下发
    //       let paramsGroupIconOne = JSON.stringify(groupOne);
    //       CallUtil.updateSettingToDevice([paramsGroupIconOne]);
    //
    //       let paramsGroupIconTwo = JSON.stringify(groupTwo);
    //       CallUtil.updateSettingToDevice([paramsGroupIconTwo]);
    //     }
    //     this.props.navigation.getParam("callback")();
    //     this.props.navigation.goBack();
    //   }).catch((error) => {
    //     console.log("=========error",error);
    //     Toast.success('c_set_fail');
    //   });
    //   // Toast.success('c_set_success');
    //   // let paramsStr = JSON.stringify(uploadData);
    //   // CallUtil.updateSettingToDevice([paramsStr]);
    //   // this.props.navigation.getParam("callback")();
    //   // this.props.navigation.goBack();
    // }).catch(error => {
    //   Toast.success('c_set_fail');
    // });
  }

  getItemKey(uploadData) {
    let keys = Object.keys(uploadData);
    let keyFree = "key10";
    for (let i = 1; i < 11; i++) {
      let newKey = `key${ i }`;
      let index = keys.findIndex((v) => v == newKey);
      if (index == -1) {
        keyFree = newKey;
        break;
      }
    }
    return keyFree;
  }

  getItemIndex(uploadData) {
    let index = 0;
    let keys = Object.keys(uploadData);
    keys.forEach((item) => {
      if (item.indexOf("key") != -1) {
        index++;
      }
    });
    return index;
  }

  addUploadData(uploadData, item1, openId, key) {
    console.log("======================openId", openId);
    let uid = item1.userid;
    if (typeof item1.userid != "number") {
      uid = parseInt(item1.userid);
    }
    let params = {
      mijia: uid,
      nickname: Util.replaceEmojis(item1.nickname),
      callName: Util.replaceEmojis(item1.nickname),
      // icon: item1.icon ? item1.icon : "",
      wx: openId ? openId : ""
    };
    uploadData[key] = params;
  }

  addUploadIconData(callIconSetting, callIconSettingForUpdate, item, key) {
    let params = {
      icon: item.icon ? item.icon : ""
    };
    callIconSetting[`call_${ key }`] = JSON.stringify(params);
    callIconSettingForUpdate[`call_${ key }`] = params;
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : "white"
  },
  title_group: {
    color: "#8C93B0",
    fontSize: 12,
    paddingVertical: 10,
    marginHorizontal: 27,
    marginBottom: 6
  },
  empty_share_title: {
    color: "rgba(0, 0, 0, 0.40)",
    fontSize: 14,
    marginTop: 12,
    marginHorizontal: 27
  },
  empty_share_subtitle: {
    color: "rgba(0, 0, 0, 0.38)",
    fontSize: 12,
    paddingVertical: 5,
    marginHorizontal: 27
  },
  white_blank: {
    height: 0.5,
    marginTop: 20
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20
  },
  algorithm_title: {
    color: "#999",
    fontSize: 18,
    paddingHorizontal: 24
  },
  algorithm_subtitle: {
    color: "#999",
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 24
  },
  optionsPage: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginTop: 20,
    paddingHorizontal: 24
  },
  optionContainer: {
    minHeight: 80,
    alignItems: "center"
  },
  icon: {
    width: 48,
    height: 48
  },
  optionText: {
    marginTop: 4,
    marginBottom: 10,
    width: 48,
    textAlign: "center",
    fontSize: 12
  },
  inviteMembers: {
    backgroundColor: "rgba(50, 186, 192, 0.1)",
    marginBottom: 15,
    marginLeft: 12,
    marginRight: 12,
    borderRadius: 12,
    paddingLeft: 20,
    // paddingRight: 11,
    paddingTop: 25,
    paddingBottom: 25,
    flexDirection: "row"
  },
  inviteCallMembers: {
    backgroundColor: "rgba(74, 204, 98, 0.1)",
    paddingTop: 19,
    paddingBottom: 19
  },
  callMembersImage: {
    marginRight: 13,
    marginLeft: 36
  },
  inviteMembersTitle: {
    color: "#32BAC0",
    fontWeight: "bold",
    fontSize: 16,
    lineHeight: 22
  },
  descTitle: {
    marginTop: 2,
    color: "rgba(52, 195, 78, 0.8)",
    fontSize: 12,
    lineHeight: 16
  },
  inviteCallColor: {
    color: "#34C34E"
  },
  inviteCallBgColor: {
    backgroundColor: "#34C34E"
  },
  inviteMembersButton: {
    marginTop: 10,
    borderRadius: 73,
    backgroundColor: "#32BAC0",
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 9,
    paddingBottom: 9
  },
  inviteMembersButtonText: {
    fontSize: 14,
    color: "#FFFFFF"
  },
  inviteMembersImage: {
    width: 117,
    height: 112
  },
  inviteCallMembersImage: {
    width: 91,
    height: 103.4
  }
});
