import { Platform, ScrollView, StyleSheet, Text, View, Dimensions, Image, TouchableOpacity, DeviceEventEmitter } from "react-native";
import React, { useEffect, useState, createRef } from "react";

import { Service, Host, Device } from "miot";
import { localStrings as LocalizedStrings, localStrings } from "../MHLocalizableString";
import { NavigationBar, TouchableView } from "mhui-rn";

import Util from "../util2/Util";

import { Styles as SettingStyles } from "miot/resources";
import BaseSettingPage from "../BaseSettingPage";

import FDSUtil from "../util/FDSUtil";

import Toast from "../components/Toast";

import ImagePicker from "react-native-image-picker";
import PermissionUtil from "../util/PermissionUtil";
import { openCropper } from "react-native-image-crop-picker";
import LoadingView from "../ui/LoadingView";
import fs from "react-native-fs";
import CallUtil from "../util/CallUtil";
import AlarmUtilV2, { SIID_THEME_SETTINGS, PIID_THEME, PIID_DEVICE_LOCATION } from "../util/AlarmUtilV2";
import LogUtil from "../util/LogUtil";
import CameraPlayer from "../util/CameraPlayer";
import ThemeFileManager from "../util/ThemeFileManager";

const TAG = "HomeTheme";
const KEY = `${ TAG }${ Device.deviceID }${ Service.account.ID }storage`;
const MaxWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);
const MaxHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width) - 250;
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width); // use this.winPortraitWidth instead
const imageWidth = (kWindowWidth - 24 - 6) / 3; // 24是左右padding(12*2)，6是图片间距(3*2)
const kIsCN = Util.isLanguageCN();

const HomeTheme = ({ props, onStateChange, addPhotoRef, loadingRef }) => {
  const callback = props?.navigation?.state?.params?.callback;
  const location = props?.navigation?.state?.params?.location;
  const [imageList, setImageList] = useState({
    list: [],
    type: -1,
    order: -1
  });
  const [showGlobalLoading, setShowGlobalLoading] = useState(false);
  const [loadSuccess, setLoadSuccess] = useState(false);
  
  // 添加更新图片列表的方法
  const updateImageList = (newList) => {
    setImageList({
      list: newList,
      type: -1,
      order: -1
    });
  };

  // 将状态变化通知父组件
  useEffect(() => {
    if (onStateChange) {
      onStateChange({
        imageList: imageList.list,
        showGlobalLoading,
        loadSuccess,
        type: imageList.type,
        order: imageList.order
      });
    }
  }, [imageList, showGlobalLoading, loadSuccess]);
  
  // 将addPhotoTapped和updateImageList方法暴露给父组件
  useEffect(() => {
    if (addPhotoRef) {
      addPhotoRef.current = {
        addPhotoTapped,
        updateImageList
      };
    }
  }, []);
  
  // 将_renderGlobalLoading方法暴露给父组件
  useEffect(() => {
    if (loadingRef) {
      loadingRef.current = {
        renderGlobalLoading: _renderGlobalLoading,
        showGlobalLoading,
        setShowGlobalLoading
      };
    }
  }, [showGlobalLoading]);
  
  useEffect(() => {
    getMemberList();
  }, []);
  useEffect(() => {
    const newList = imageList.list.map((item) => {
      return {
        order: item.order,
        url: item.url,
      };
    });
    newList.sort((a, b) => a?.order - b?.order);
    if (imageList.type >= 0) { // 只有新增和删除，才给设备发消息 0 删除 1 新增
      CallUtil.updateThemeToDevice(JSON.stringify({ total_num: newList?.length, modify: { type: imageList.type, order: imageList.order }, data: imageList.type === 0 ? [] : [newList[newList.length - 1]] }))
        .then((res) => {
          LogUtil.logOnAll("更新主题到设备成功:", res);
          if (imageList.type === 1) {
            setShowGlobalLoading(false);
            Toast.fail("upload_success");
          }
          const finalList = imageList.list.sort((a, b) => a?.order - b?.order);
          Host.storage.set(KEY, JSON.stringify(finalList));
        })
        .catch((error) => {
          setShowGlobalLoading(false);
          if (imageList.type === 1) {
            Toast.fail("upload_error");
            if (CameraPlayer.getInstance().isConnected()) {
              ThemeFileManager.getInstance().startRequestThemeImgUrls();// 发了一个消息出去 这里会多次请求拉取。
            }
          }
          LogUtil.logOnAll("upload fail 更新主题到设备失败:", error);
        });
    }
  }, [imageList.list?.length]);
  useEffect(() => {
    props.navigation.setParams({
      // title: localStrings.home_theme,
      type: NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            props.navigation.goBack();
          },
        },
      ],
      titleStyle: {
        fontSize: 18,
        color: "#333333",
        fontWeight: 500,
      },
    });
  }, []);

  const deleteListFunction = (deleteOrder) => {
    const newList = imageList.list;

    // 找到并删除指定order的item
    const deleteIndex = newList.findIndex(item => item.order === deleteOrder);
    if (deleteIndex !== -1) {
      //删除本地图片
      Host.file.deleteFile(newList[deleteIndex].fileName);
      newList.splice(deleteIndex, 1);
    }
    newList.sort((a, b) => a?.order - b?.order);
    const finalList = newList.map((item, index) => {
      item.order = index + 1;
      return item;
    });
    Host.storage.set(KEY, JSON.stringify(finalList));
    // type 0: 删除，1：增加
    setImageList({
      list: finalList,
      type: 0,
      order: deleteOrder
    });
  };

  const getMemberList = async () => {
    // 获取微信联系人列表
    Host.storage
      .get(KEY)
      .then((val) => {
        const list = val ? JSON.parse(val) : [];
        setImageList({
          list: list,
          type: -1,
          order: -1
        });
      })
      .finally(() => {
        setLoadSuccess(true);
      });
  };

  const uploadImageByLocal = async (filename, currentTime) => {
    // Host.file.downloadFile("http://..../...png", filename)
    //   .then((res) => {
    //     // const myimg = <Image source={{local:myfile}} />

    //   });

    FDSUtil.uploadAvatarToServer(filename)
      .then((res) => {
        // 先保存到本地
        Host.file
          .downloadFile(res.downloadUrl, res.objName)
          .then((downRes) => {
            // const myimg = <Image source={{local:myfile}} />
            // console.log("保存成功", downRes);

            Host.storage.get(KEY).then((val) => {
              // load val success}
              const list = val ? JSON.parse(val) : [];
              // 每次都要重新排序
              list.push({ localUrl: downRes?.path, url: res.downloadUrl, order: list.length, createTime: currentTime, fileName: res.objName });
              list.sort((a, b) => a?.order - b?.order);
              const newList = list?.map((item, index) => {
                item.order = index + 1;
                return item;
              });
              if (list.length === 1) {
                AlarmUtilV2.setSpecPValue([{ sname: SIID_THEME_SETTINGS, pname: PIID_THEME, value: 5 },
                        { sname: SIID_THEME_SETTINGS, pname: PIID_DEVICE_LOCATION, value: location }]).catch((e)=>{
                          console.log("catchcatch",e);
                        })
                callback&&callback(5);
              }
              setImageList({
                list: newList,
                type: 1,
                order: newList.length
              });
              // 测试阶段保持到本地
              //Host.file.saveImageToPhotosAlbum(filename);
            });
          })
          .catch((e) => {
            Toast.fail("upload_error");
          });
      })
      .catch(() => {
        Toast.fail("upload_error");
        setShowGlobalLoading(false);
      });
  };
  const addPhotoTapped = () => {
    PermissionUtil.checkStoragePermission()
      .then(() => {
        const options = {
          quality: 1.0,
          maxWidth: 1200,
          maxHeight: 1200,
          storageOptions: {
            skipBackup: true,
          },
        };
        setShowGlobalLoading(true);

        ImagePicker.launchImageLibrary(options, (response) => {
          //console.log("response", response);
          if (response.didCancel) {
            console.log("User cancelled photo picker");
            // Toast.fail("bind_error");
            setShowGlobalLoading(false);
            return;
          } else if (response.error) {
            console.log("ImagePicker Error: ", response.error);
            Toast.fail("bind_error");
            setShowGlobalLoading(false);
            return;
          } else if (response.customButton) {
            console.log("User tapped custom button: ", response.customButton);
            Toast.fail("bind_error");
            setShowGlobalLoading(false);
            return;
          } else {

            if (
              !response.fileName ||
              response.fileName.indexOf("webp") != -1 ||
              response.fileName.indexOf("gif") != -1 ||
              (response.type && response.type.indexOf("webp") != -1) ||
              (response.type && response.type.indexOf("gif") != -1)
            ) {
              // 这里需要判断下图片格式，不符合的格式，需要给相应提示
              setShowGlobalLoading(false);
              Toast._showToast(localStrings["picture_not_support"]);
              return;
            }

            let path = response.uri.slice(7);
            //console.log(path, "path");
            //console.log(response.uri, "response.uri");
            // const currentTime = new Date().getTime();
            // const imageName = `${ path }${ Service.account.ID }${ currentTime }`;
            // cropImage(response);
            // setPath(response.uri)

            openCropper({
              path: response.uri,
              width: 320,
              height: 480,
              cropperChooseText: localStrings["csps_right"],
              cropperCancelText: localStrings["irs_left_text"],
              cropperRotateButtonsHidden: true,
              hideBottomControls: true,
            })
              .then((image) => {
                // 上传图片需要把图片放在沙盒里面才能上传
                const currentTime = new Date().getTime();
                const imageName = `${Service.account.ID}${currentTime}`;
                fs.readFile(image.path, "base64")
                  .then((content) => {
                    Host.file
                      .writeFileThroughBase64(imageName, content)
                      .then(() => {
                        uploadImageByLocal(imageName, currentTime);
                      })
                      .catch(() => {
                        setShowGlobalLoading(false);
                      });
                  })
                  .catch(() => {
                    setShowGlobalLoading(false);
                  });
                // uploadImage(image.path);
              })
              .catch(() => {
                setShowGlobalLoading(false);
              });
            // uploadImage(path);
          }
        });
      })
      .catch(() => {
        // 没有读写手机存储权限
        // if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
        //   Toast.fail("upload_error");
        // }
        Toast.fail("upload_error");
      });
  };

  const _renderGlobalLoading = () => {
    if (!showGlobalLoading) {
      return null;
    }
    let loadingViewStyle = {
      zIndex: 0,
      position: "absolute",
      width: "100%",
      height: "100%",
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0,0,0,0.1)"
    };

    return (
      <View
        style={loadingViewStyle}
        pointerEvents={"none"}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
          color={"#ffffff"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  };

  const remainder = imageList.list?.length % 3;
  return (
    <View styles={styles.container}>
      <ScrollView bounces={Platform.OS === "ios" ? false : true} style={{ width: "100%", height: "100%" }}>
        <View style={styles.imageTable}>
          {imageList.list?.length ? (
            imageList.list
              .sort((a, b) => b?.order - a?.order)
              .map((item, index) => {
                const uri = `file://${ Host.file.storageBasePath }/${ item.fileName }`;
                return (
                  <TouchableView
                    key={index}
                    viewStyle={styles.imageItem}
                    onPress={() => {
                      if (showGlobalLoading) {
                        return;
                      }
                      props.navigation.navigate("HomeThemeReview", {
                        imageList: imageList.list,
                        index,
                        callback: (data) => {
                          // 处理新的回调数据结构
                          console.log("删除的图片order:", data.deleteOrder);
                          deleteListFunction(data.deleteOrder);
                        }
                      });
                    }}
                  >
                    <Image
                      onError={({ nativeEvent: e }) => {
                        console.log("onError1111", e);
                      }}
                      style={styles.image}
                      source={{ uri }}
                    />
                  </TouchableView>
                );
              })
          ) : (
            <View style={styles.noImageTable}>
              <Image style={{ width: 92, height: 60 }} source={require("../../Resources/Images/noImage.png")}></Image>
              <Text style={{ color: "#999999", fontSize: 15, marginTop: 18 }}>{localStrings["no_image"]}</Text>
            </View>
          )}
          {remainder === 2 ? <View style={styles.image}></View> : null}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : "#F6F6F6",
    flex: 1,
    marginRight: 12,
    marginLeft: 12,
    position: 'relative'
  },
  imageTable: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: 12,
  },
  imageItem: {
    position: "relative",
    marginBottom: 3,
    width: imageWidth,
  },
  deleteIcon: {
    position: "absolute",
    bottom: 10,
    right: 10,
  },
  image: {
    width: imageWidth,
    height: (imageWidth / 2) * 3,
  },
  titleContainer: {
    height: 32,
    backgroundColor: Util.isDark() ? "#xm000000" : "white",
    justifyContent: "center",
    paddingLeft: SettingStyles.common.padding,
  },
  title: {
    fontSize: 12,
    color: "#8c93b0",
    lineHeight: 14,
  },
  separatorTable: {
    height: 40,
    justifyContent: "center",
  },
  freeCall: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginLeft: SettingStyles.common.padding,
    marginRight: SettingStyles.common.padding,
    marginBottom: 16,
    marginTop: 16,
  },
  freeCallDesc: {
    flex: 1,
    marginRight: 20,
  },
  freeCallTitle: {
    fontSize: 16,
    color: "#000000",
    fontWeight: "bold",
  },
  freeCallSubtitleTitle: {
    fontSize: 13,
    color: "rgba(0, 0, 0, 0.6)",
  },
  leftAvatarStyle: {
    width: 40,
    height: 40,
    borderRadius: 22,
  },
  noImageTable: {
    alignItems: "center",
    justifyContent: "center",
    height: MaxHeight,
    width: kWindowWidth,
  },
});

export default class WXCallSettingMore extends BaseSettingPage {
  constructor(props, context) {
    super(props, context);
    this.props = props;
    this.state = {
      ...this.state,
      homeThemeState: {
        imageList: [],
        showGlobalLoading: false,
        loadSuccess: false
      }
    };
    // 创建一个ref来引用HomeTheme组件的方法
    this.homeThemeRef = createRef();
    // 创建一个ref来引用HomeTheme组件的loading方法
    this.loadingRef = createRef();
  }

  componentDidMount() {
    // 添加主题图片下载完成的事件监听
    this.themeDownloadListener = DeviceEventEmitter.addListener(
      'themeImagesDownloadComplete',
      (data) => {
        console.log('主题图片下载完成22:', data);
        if (data.success) {
          // 刷新图片列表
          Host.storage.get(KEY).then((val) => {
            const list = val ? JSON.parse(val) : [];
            // 更新父组件状态
            this.setState({
              homeThemeState: {
                ...this.state.homeThemeState,
                imageList: list
              }
            });
            // 直接调用子组件的更新方法
            if (this.homeThemeRef.current) {
              this.homeThemeRef.current.updateImageList(list);
            }
          });
        }
      }
    );
  }

  componentWillUnmount() {
    // 组件卸载时移除监听器
    if (this.themeDownloadListener) {
      this.themeDownloadListener.remove();
    }
  }

  getTitle() {
    return localStrings["home_theme"];
  }
  renderSettingContent() {
    return <HomeTheme 
      props={this.props} 
      onStateChange={(state) => {
        this.setState({
          homeThemeState: state
        });
      }}
      addPhotoRef={this.homeThemeRef}
      loadingRef={this.loadingRef}
    />;
  }

  renderSettingBottomContent() {
    const { homeThemeState } = this.state;
    const { imageList, showGlobalLoading } = homeThemeState;
    
    if (!Device.isOwner) {
      // 共享用户不显示添加按钮
      return null;
    }

    const disabled = imageList?.length > 8;
    return (
      <TouchableOpacity
        style={{ position: "absolute", right: 0, bottom: 42 }}
        pointerEvents="none"
        onPress={() => {
          if (showGlobalLoading) {
            return;
          }
          if (disabled) {
            Toast.show("photos_maximum_limit");
            return;
          }
          // 使用ref调用HomeTheme组件的addPhotoTapped方法
          if (this.homeThemeRef.current && this.homeThemeRef.current.addPhotoTapped) {
            this.homeThemeRef.current.addPhotoTapped();
          }
        }}
      >
        <Image
          style={{ width: 128, height: 128 }}
          source={disabled ? require("../../Resources/Images/icon_add_timer_new_dis.webp") : require("../../Resources/Images/icon_add_timer_new.webp")}
        />
      </TouchableOpacity>
    );
  }
  
  renderGlobalLoading() {
    // 使用ref调用HomeTheme组件的renderGlobalLoading方法
    if (this.loadingRef.current && this.loadingRef.current.renderGlobalLoading) {
      return this.loadingRef.current.renderGlobalLoading();
    }
    return null;
  }
  
  render() {
    return (
      <View style={{ flex: 1 }}>
        {super.render()}
        {this.renderGlobalLoading()}
      </View>
    );
  }
}
