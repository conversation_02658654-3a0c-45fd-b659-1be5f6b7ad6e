'use strict';

import { ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text } from 'react-native';
import CameraConfig from '../util/CameraConfig';
import { DescriptionConstants } from '../Constants';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import API from '../API';
import Toast from '../components/Toast';
import StorageKeys from '../StorageKeys';
import VersionUtil from '../util/VersionUtil';
import { Device } from 'miot';
import AlarmUtil from '../util/AlarmUtil';
import ImageButton from "miot/ui/ImageButton";
import { NavigationBar } from 'mhui-rn';
import TrackUtil from '../util/TrackUtil';
import BaseSettingPage from "../BaseSettingPage";

export default class NotificationTypeSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      aiPush: false, // TODO：智能
      areaPush: false,
      babyCrySwitch: false,
      babyPush: false,
      peoplePush: false,
      facePush: false,
      petPush: false,
      LouderSound: false,
      isVip: false
    };
  }
  getTitle(): string {
    return  LocalizedStrings['nts_title'];
  }

  buildPushList() {
    let list = [];
    list.push({ title: "nts_person_detected_simplified", value: this.state.peoplePush, type: 2 });

    if (CameraConfig.displayLOUDER_SOUND_Timeline(Device.model)) {
      list.push({ title: "event_type_loud_push", value: this.state.LouderSound, type: 100, key: "LouderSound" });
    }

    list.push({ title: 'event_type_obj_motion_push', value: this.state.areaPush, type: 1 });


    if (!VersionUtil.judgeIsV1(Device.model) && CameraConfig.CloudBabyCry()) {
      list.push({ title: 'nts_babycry_detected', value: this.state.babyPush, type: 3 });
    }
    if (CameraConfig.intelligentScenePush(Device.model) || !VersionUtil.judgeIsV1(Device.model)) {
      list.push({ title: 'nts_smart_scene', value: this.state.aiPush, type: 4 });
    }

    // if (this.showEvent(Event.Pet)) {
    //   list.push({ title: CameraConfig.isNewPetDesc() ? "event_type_pet_detection_push" : 'event_type_pet_push', value: this.state.petPush, type: 5 });
    // }

    // if (this.showEvent(Event.FenceIn)) {
    //   list.push({ title: "ele_fence_alarm_push", value: this.state.FenceIn, type: 100, key: "FenceIn" })
    // }

    return list;
  }


  renderSettingContent() {
    return (
      <View style={styles.container}>
          <View style={styles.featureSetting}
            key={101}
          >
            {
              // (((this.forceBabyCry || this.state.isVip || VersionUtil.isAiCameraModel(Device.model)) && !VersionUtil.judgeIsV1(Device.model))// 是vip且不是v1
              //   ? [
              //     { title: 'event_type_obj_motion_push', accessibilityLabel: DescriptionConstants.sz_5_51, accessibilityLabel_1: DescriptionConstants.sz_5_51_1, value: this.state.areaPush, type: 1 },
              //     { title: 'nts_person_detected', accessibilityLabel: DescriptionConstants.sz_5_53, accessibilityLabel_1: DescriptionConstants.sz_5_53_1, value: this.state.peoplePush, type: 2 },
              //     CameraConfig.CloudBabyCry()?{ title: 'nts_babycry_detected', value: this.state.babyPush, type: 3 } : null,
              //     AlarmUtil.sceneSize > 0 ? { title: 'nts_smart_scene', accessibilityLabel: DescriptionConstants.sz_5_55, accessibilityLabel_1: DescriptionConstants.sz_5_55_1, value: this.state.aiPush, type: 4 } : null,
              //     CameraConfig.isSupportPet(Device.model) ? { title: 'event_type_pet_push', value: this.state.petPush, type: 5 } : null
              //   ]
              //   : [
              //     { title: 'event_type_obj_motion_push', value: this.state.areaPush, type: 1 },
              //     { title: 'nts_person_detected', value: this.state.peoplePush, type: 2 },
              //     CameraConfig.intelligentScenePush(Device.model) && AlarmUtil.sceneSize > 0 ? { title: 'nts_smart_scene', value: this.state.aiPush, type: 4 } : null// 039a04不支持智能场景推送
              //   ])
                this.buildPushList().map((item, i) => {
                  if (item == null) return null;
                  return (
                    <ListItemWithSwitch
                      key={i}
                      titleNumberOfLines={3}
                      unlimitedHeightEnable={true}
                      showSeparator={false}
                      title={LocalizedStrings[item.title]}
                      value={item.value}
                      onValueChange={(val) => this._onSwitchValue(val, item.type, item.key)}
                      accessibilitySwitch={{
                        accessibilityLabel: LocalizedStrings[item.title]
                      }}
                    />
                  );
                })
            }

          </View>
      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    // this.props.navigation.setParams({
    //   // show:true
    //   title: LocalizedStrings['nts_title'],
    //   titleNumberOfLines: 2,
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => { this.props.navigation.goBack(); }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });

    StorageKeys.IS_VIP_STATUS.then((res) => {
      this.setState({ isVip: res });
    }).catch((err) => {
      Toast.fail('c_get_fail', err);
    });

    // API.get('/miot/camera/app/v1/get/alarmSwitch', 'business.smartcamera').then((res) => {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code == 0) {
      } else {
        console.log('request fail');
        return;
      }
      console.log('res.data.aiPush', res.data.aiPush, res)
      this.setState({
        aiPush: res.data.aiPush,
        petPush: res.data.petPushSwitch,
        areaPush: res.data.areaPush,
        babyCrySwitch: res.data.babyCrySwitch,
        babyPush: res.data.babyPush,
        peoplePush: res.data.pedestrianDetectionPushSwitch,
        LouderSound:res.data.genericSwitchMap.hasOwnProperty("LouderSound")?res.data.genericSwitchMap.LouderSound:false,
        facePush: res.data.facePush
      });
    }).catch((err) => {
      Toast.fail('c_get_fail', err);
    });

    this.forceBabyCry = CameraConfig.isSupportNonVipBabyCry(Device.model);
  }

  _onSwitchValue(value, type, eventType) {
    console.log(value, type, 'value, type');
    switch (type) {
      case 1:
        value ?
          TrackUtil.reportResultEvent("MonitoringSetting_AreaMoved_Status", "type", 1)
          :
          TrackUtil.reportResultEvent("MonitoringSetting_AreaMoved_Status", "type", 2);
        break;
      case 2:
        value ?
          TrackUtil.reportResultEvent("MonitoringSetting_PeopleMoved_Status", "type", 1)
          :
          TrackUtil.reportResultEvent("MonitoringSetting_PeopleMoved_Status", "type", 2);
        break;
      case 3:
        value ?
          TrackUtil.reportResultEvent("MonitoringSetting_BabyCry_Status", "type", 1)
          :
          TrackUtil.reportResultEvent("MonitoringSetting_BabyCry_Status", "type", 2);
        break;
      case 4:
        value ?
          TrackUtil.reportResultEvent("MonitoringSetting_Automation_Status", "type", 1)
          :
          TrackUtil.reportResultEvent("MonitoringSetting_Automation_Status", "type", 2);
        break;
    }
    if (type == 100) { // 自定义推送事件类型。
      API.post("/miot/camera/app/v1/put/genericEventPushSwitch", 'business.smartcamera', { eventType: eventType, open: value})
        .then((res) => {
          if (res.code == 0) {
            this.setState({ [eventType]: value }); // 取变量值
            Toast.success('c_set_success');
          } else {
            Toast.fail('c_set_fail');
            this.setState({ [eventType]: !value });

          }
        })
        .catch((err) => {
          Toast.fail('c_set_fail', err);
          this.setState({ [eventType]: !value });
        });
    } else {
      API.post(`/miot/camera/app/v1/put/${this._path(type)}`, 'business.smartcamera', {
        open: value
      }).then((res) => {
        this._updateState(type, res.code == 0 ? value : !value);
        if (res.code != 0) {
          Toast.fail('c_set_fail');
        } else {
          Toast.success('c_set_success');
        }
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    }

  }

  _path(type) {
    switch (type) {
      case 1: return 'areaChangePushSwitch';
      case 2: return 'pedestrianDetectionPushSwitch';
      case 3: return 'babyCryPushSwitch';
      case 4: return 'aiPushSwitch';
      case 5: return 'petPushSwitch';
      default: return '';
    }
  }

  _updateState(type, value) {
    switch (type) {
      case 1:
        this.setState({ areaPush: value });
        break;
      case 2:
        this.setState({ peoplePush: value });
        break;
      case 3:
        this.setState({ babyPush: value });
        break;
      case 4:
        this.setState({ aiPush: value });
        break;
      case 5:
        this.setState({ petPush: value });
        break;
    }
  }
}
