"use strick";
import React from "react";
import { ScrollView, Image, StyleSheet, View, Dimensions, Text, DeviceEventEmitter } from "react-native";
import BaseSettingPage from "../BaseSettingPage";
import { styles } from "./SettingStyles";
import { AbstractDialog, ListItem, MessageDialog } from "mhui-rn";
import { ChoiceDialog } from "miot/ui/Dialog";
import { TouchableWithoutFeedback } from "react-native";
import I18n from "miot/native/common/i18n";
import ParsedText from "react-native-parsed-text";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { API_LEVEL, Device, Host } from "miot";
import Toast from "../components/Toast";
import AlarmUtilV2, { SIID_THEME_SETTINGS, PIID_THEME, PIID_TIME_SHOW, PIID_CALENDER_SETTINGS, PIID_DEVICE_LOCATION } from "../util/AlarmUtilV2";
import { Permissions } from "miot/system/permission";
import Service from "miot/Service";
import LogUtil from "../util/LogUtil";
import Util from "../util2/Util";
import base64js from "base64-js";
import NumberUtil from "../util/NumberUtil";
import CameraPlayer from "../util/CameraPlayer";
import { MISSCommand } from "miot/service/miotcamera";
import ThemeFileManager from "../util/ThemeFileManager";
import XiaoaiTTSUtil from "../util/XiaoaiTTSUtil";


const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width); // use this.winPortraitWidth instead
const imageWidth = (kWindowWidth - 60) / 2.3;
const imageHeight = (imageWidth / 2) * 3;
const Home_Theme_KEY = `HomeTheme${Device.deviceID}${Service.account.ID}storage`;
const kRDTDataReceiveCallBackName = "rdtDataReceiveCallBack";
export default class ThemeSetting extends BaseSettingPage {
  constructor(props, context) {
    super(props, context);
    this.themeCallback = this.props.navigation.getParam("callback");

    this.state = {
      timeFormat: 0,
      calendarFormat: 0,
      selectedWhichTheme: 0,
      timeFormatVisible: false,
      calendarFormatVisable: false,
      weatherPermissionVisable: false,
      homeThemeList: [],
    };
    this.selectedWeatherIndex = 0;
    this.hadFamilyAddr = false; // 是否设置家庭地址
    this.longitude = "0";
    this.latitude = "0";
    // require('../../Resources/Images/theme_clock.png')
    this.digitClockTheme = [
      [require("../../Resources/Images/theme_number_one_solar.png"), require("../../Resources/Images/theme_number_one_lunar.png")],
      [require("../../Resources/Images/theme_number_two_solar.png"), require("../../Resources/Images/theme_number_two_lunar.png")],
    ];
    this.weatherTheme = [require("../../Resources/Images/theme_weather.png")];
    this.pointerClockTheme = [require("../../Resources/Images/theme_clock.png")];
 
    // 添加主题图片下载完成的事件监听
    this.themeDownloadListener = DeviceEventEmitter.addListener(
      'themeImagesDownloadComplete',
      (data) => {
        console.log('主题图片下载完成:', data);
        // 这里可以添加你的处理逻辑
        if (data.success) {
          // 刷新UI或执行其他操作
          Host.storage.get(Home_Theme_KEY).then((val) => {
            const list = val ? JSON.parse(val) : [];
            if (list[list.length - 1]) {
              this.setState({ homeThemeList: [`file://${Host.file.storageBasePath}/${list[list.length - 1].fileName}`] });
            } else {
              this.setState({ homeThemeList: [] });
            }
          });
          this.themeCallback({ theme: this.state.selectedWhichTheme });
          // this.setState({
          //   // 更新相关状态
          // });
        }
      }
    );
  }

  getTitle() {
    return LocalizedStrings["theme_setting"];
  }
  getTitleBackgroundColor() {
    //return Util.isDark() ? 'rgba(26, 26, 26)' : '';
   return Util.isDark() ? 'xm#1A1A1A' : null;
  }
  getGlobalBackgroundColor(){
    // return Util.isDark() ? 'rgba(26, 26, 26)' : '';
    return Util.isDark() ? "xm#1A1A1A" : null;
  }
  /**
   * 根据不同的设置修改对应的state
   * @param {Number} index 选中了哪个主题
   * @param {Number} selectWhichSetting 对应的哪个设置
   */
  _changeImgSelect(index, selectWhichSetting) {
    console.log("index=========", index, selectWhichSetting);

    switch (selectWhichSetting) {
      case 0:
        index = index + 1;
        console.log("数字时钟选中", index);
        break;
      // case 1:
      //   index = index + 1 + this.digitClockTheme.length;
      //   console.log('天气选中', index);
      //   break;
      case 2:
        index = index + 1 + this.digitClockTheme.length + this.weatherTheme.length;
        console.log("指针时钟选中", index);
        break;
      case 5:
        index = 5;
      default:
        console.log("该设置不存在");
    }

    if (this.state.selectedWhichTheme == index) {
      return;
    }
    this.setAllSpecValue("theme", index);
  }

  /**
   * 渲染图片的横向滚动列表
   * @param {Array} imgsData 传入的主题数组
   * @param {Number} imgSelected 选中的哪个主题
   * @param {Number} selectWhichSetting 选择了哪个设置[数字时钟，天气，指针时钟]
   * @returns 横向滚动的图片列表
   */
  _renderImages(imgsData, imgSelected, selectWhichSetting, isDiffByCal = false) {
    const imgNotSelectStyle = {
      alignItems: "center",
      justifyContent: "center",
      width: imageWidth + 15,
      height: imageHeight + 15,
      marginHorizontal: 3,
    };
    const styles = StyleSheet.create({
      imgNotSelectStyle: imgNotSelectStyle,
      imgSelectStyle: {
        ...imgNotSelectStyle,
        borderWidth: 3,
        borderRadius: 14,
        borderColor: "#32BAC0",
      },
    });
    return (
      <View style={{ marginHorizontal: 21, marginTop: 20, marginBottom: 20 }}>
        <ScrollView 
          horizontal={true} 
          contentContainerStyle={styles.contentContainer} 
          showsHorizontalScrollIndicator={false}
          scrollEnabled={false}
        >
          {imgsData !== 0
            ? imgsData.map((item, index) => {
                // require 不支持携带参数
                let source;
                if (isDiffByCal) {
                  source = this.state.calendarFormat == 2 ? item[1] : item[0];
                } else {
                  source = item;
                }
                return (
                  <TouchableWithoutFeedback
                    key={index}
                    onPress={() => {
                      // 由于天气需要位置授权，所以做一层判断
                      if (selectWhichSetting === 1) {
                        if (this.state.selectedWhichTheme == 3) {
                          // 已经是天气主题
                          return;
                        }
                        LogUtil.logOnAll("ThemeSetting", API_LEVEL);

                        this.selectedWeatherIndex = index + 1 + this.digitClockTheme.length;
                        this.setState({ weatherPermissionVisable: true });
                        // 只有主账号能取到家庭位置 未设置的家庭位置经纬度都为0
                      } else {
                        this._changeImgSelect(index, selectWhichSetting);
                      }
                    }}
                  >
                    <View style={index === imgSelected ? styles.imgSelectStyle : styles.imgNotSelectStyle}>
                      <Image style={{ width: imageWidth, borderRadius: 12, height: imageHeight }} key={index} source={source} />
                    </View>
                  </TouchableWithoutFeedback>
                );
              })
            : null}
        </ScrollView>
      </View>
    );
  }

  _renderImagesNew(imgsData, imgSelected, selectWhichSetting, isDiffByCal = false) {
    console.log("yaoyonghui  version:" +(XiaoaiTTSUtil.CURRENT_VERSION < XiaoaiTTSUtil.MIN_CONTACTS_TTS_FW_VERSION))
    if (Host.isMiuiChannel || XiaoaiTTSUtil.CURRENT_VERSION < XiaoaiTTSUtil.MIN_CONTACTS_TTS_FW_VERSION) {// Miui隐藏家庭相册
      return null;
    }
    console.log(imgsData);

    const imgNotSelectStyle = {
      alignItems: "center",
      justifyContent: "center",
      width: imageWidth + 15,
      height: imageHeight + 15,
      marginHorizontal: 3,

      borderRadius: 10,
    };
    const styles = StyleSheet.create({
      imgNotSelectStyle: imgNotSelectStyle,
      imgSelectStyle: {
        ...imgNotSelectStyle,
        borderWidth: 3,
        borderRadius: 14,
        borderColor: "#32BAC0",
      },
      time928_select: {
        position: "absolute",
        bottom: 12,
        left: 12,
        width: 70,
        height: 34,
      },
      time928_noSelect: {
        position: "absolute",
        bottom: 15,
        left: 15,
        width: 70,
        height: 34,
      },
    });
    return (
      <View style={{ marginHorizontal: 21, marginTop: 20, marginBottom: 20 }}>
        <ScrollView horizontal={true} scrollEnabled={false} contentContainerStyle={styles.contentContainer} showsHorizontalScrollIndicator={false}>
          {imgsData.length !== 0 ? (
            imgsData.map((item, index) => {
              // require 不支持携带参数
              console.log(item, "不支持携带参数");

              return (
                <TouchableWithoutFeedback
                  key={index}
                  onPress={() => {
                    //this.props.navigation.navigate('HomeTheme');
                    if (5 === imgSelected) {
                      this.props.navigation.navigate("HomeTheme");
                    } else {
                      this._changeImgSelect(5, selectWhichSetting);
                    }
                  }}
                >
                  <View style={[5 === imgSelected ? styles.imgSelectStyle : styles.imgNotSelectStyle, { position: "relative" }]}>
                    <Image style={{ width: imageWidth, borderRadius: 12, height: imageHeight }} key={index} source={{ uri: item }} />
                    <Image style={5 === imgSelected ? styles.time928_select : styles.time928_noSelect} source={require("../../Resources/Images/time928.png")} />
                  </View>
                </TouchableWithoutFeedback>
              );
            })
          ) : (
            <TouchableWithoutFeedback
              onPress={() => {
                this.props.navigation.navigate("HomeTheme", { callback: (result) => this.setThemeCallback(result), location: `${this.longitude},${this.latitude}` });
              }}
            >
              <View style={[5 === imgSelected ? styles.imgSelectStyle : styles.imgNotSelectStyle]}>
                <View style={{ width: imageWidth, borderRadius: 12, height: imageHeight, backgroundColor: "#F3F3F3", alignItems: "center", justifyContent: "center" }}>
                  <Image style={{ width: 20, height: 20 }} source={require("../../Resources/Images/preset_position_add.png")} />
                  <Text style={{ marginTop: 10, color: "rgba(0, 0, 0, 0.4)", fontSize: 13, lineHeight: 17 }}>{LocalizedStrings["add_photo"]}</Text>
                </View>

              </View>
            </TouchableWithoutFeedback>
          )}
        </ScrollView>
      </View>
    );
  }

  _onDismiss(selectedSetting) {
    switch (selectedSetting) {
      case "time":
        this.setState({ timeFormatVisible: false });
        break;
      case "calendar":
        this.setState({ calendarFormatVisable: false });
        break;
      default:
        return;
    }
  }
  _renderChoiceDialog(selectedSetting) {
    if (this.state.selectWhichDigitClock === -1) {
      // 如果当前选中的不是数字时钟，要做适当的处理
      return;
    }
    const options =
      selectedSetting === "time"
        ? [{ title: LocalizedStrings["time_display_2424"] }, { title: LocalizedStrings["time_display_12"] }]
        : [{ title: LocalizedStrings["solar_calendar"] }, { title: LocalizedStrings["lunar_calendar"] }];
    return (
      <ChoiceDialog
        type={ChoiceDialog.TYPE.SINGLE}
        style={{ width: 100 }}
        dialogStyle={{ titleStyle: { fontWeight: "bold" } }}
        useNewType={true}
        visible={selectedSetting === "time" ? this.state.timeFormatVisible : this.state.calendarFormatVisable}
        // itemStyleType可以设置选中项的样式，1代表标题左侧向右箭头，2代表整体的右侧的对勾
        itemStyleType={2}
        title={selectedSetting === "time" ? LocalizedStrings["time_display"] : LocalizedStrings["calendar_setting"]}
        options={options}
        selectedIndexArray={selectedSetting === "time" ? [this.state.timeFormat - 1] : [this.state.calendarFormat - 1]}
        onDismiss={() => {
          this._onDismiss(selectedSetting);
        }}
        buttons={[
          {
            text: I18n.cancle,
            callback: () => {
              console.log("cancle");
              this._onDismiss(selectedSetting);
            },
          },
          {
            text: I18n.ok,
            // 点击确定按钮的回调
            callback: (result) => {
              console.log(`选中的选项`, result);
              this._onDismiss(selectedSetting);
              this.setAllSpecValue(selectedSetting, result[0] + 1);
            },
          },
        ]}
      />
    );
  }

  renderSettingContent() {
    return (
      <View style={[{ flex: 1 }]}>
        <View style={[{}]}>
          <ScrollView scrollEnabled={false}>
            <ListItem
              title={LocalizedStrings["number_clock"]}
              // value={this.digitClockTheme.length}
              containerStyle={{ backgroundColor: Util.isDark() ? "#xm1A1A1A" : null }}
              showSeparator={false}
              hideArrow={true}
               titleStyle={{  fontWeight: "bold",fontSize: 16 ,color: "#000000",fontFamily: null}}
            />
            {this._renderImages(this.digitClockTheme, this.state.selectedWhichTheme - 1, 0, true)}

            {/* 24小时制 or 12小时制 */}
            <ListItem
              title={LocalizedStrings["time_display"]}
              showSeparator={false}
             titleStyle={{  fontWeight: "bold",fontSize: 16 ,color: "#000000",fontFamily: null}}
              containerStyle={{ backgroundColor: Util.isDark() ? "#xm1A1A1A" : null }}
              value={this.state.timeFormat === 1 ? LocalizedStrings["time_display_2424"] : LocalizedStrings["time_display_12"]}
              onPress={() => {
                this.setState({
                  timeFormatVisible: true,
                });
                console.log(this.state.timeFormatVisible);
              }}
            />
            {/* 利用ChoiceDialog实现单选弹窗 */}
            {this._renderChoiceDialog("time")}

            {/* 阳历 or 阴历 */}
            <ListItem
              title={LocalizedStrings["calendar_setting"]}
              showSeparator={false}
              containerStyle={{ backgroundColor: Util.isDark() ? "#xm1A1A1A" : null }}
              titleStyle={{  fontWeight: "bold",fontSize: 16 ,color: "#000000",fontFamily: null}}
              value={this.state.calendarFormat === 1 ? LocalizedStrings["solar_calendar"] : LocalizedStrings["lunar_calendar"]}
              onPress={() => {
                this.setState({
                  calendarFormatVisable: true,
                });
              }}
            />
            {(!Host.isMiuiChannel && XiaoaiTTSUtil.CURRENT_VERSION >= XiaoaiTTSUtil.MIN_CONTACTS_TTS_FW_VERSION) && <View style={[styles.whiteblank]} />}

            {this._renderChoiceDialog("calendar")}
          </ScrollView>
          {(!Host.isMiuiChannel && XiaoaiTTSUtil.CURRENT_VERSION >= XiaoaiTTSUtil.MIN_CONTACTS_TTS_FW_VERSION) && <ListItem
            title={LocalizedStrings['home_theme']}
            // value={this.digitClockTheme.length}
            containerStyle={{ backgroundColor: Util.isDark() ? "#xm1A1A1A" : null }}
            showSeparator={false}
            hideArrow={true}
              titleStyle={{  fontWeight: "bold",fontSize: 16 ,color: "#000000",fontFamily: null}}
            
          />}
          {this._renderImagesNew(this.state.homeThemeList, this.state.selectedWhichTheme, 5)}

          <View style={[styles.whiteblank]} />
          <ScrollView scrollEnabled={false}  style={[{ opacity: 1 }]}>
            <ListItem
              title={LocalizedStrings["display_weather"]}
              showSeparator={false}
              titleStyle={{  fontWeight: "bold",fontSize: 16 ,color: "#000000",fontFamily: null}}
              containerStyle={{ backgroundColor: Util.isDark() ? "#xm1A1A1A" : null }}
              hideArrow={true}
              unlimitedHeightEnable={true}
              subtitle={LocalizedStrings["display_weather_subtitle"]}
              subtitleNumberOfLines={5}
            />
            {this._renderImages(this.weatherTheme, this.state.selectedWhichTheme - (1 + this.digitClockTheme.length), 1)}
            {/* <MessageDialog */}
            {/*  visible={this.state.weatherPermissionVisable} */}
            {/*  message={!this.hadFamilyAddr ? LocalizedStrings['weather_dialog_without_familyaddr'] : LocalizedStrings['weather_dialog_familyaddr']} */}
            {/*  onDismiss={() => { */}
            {/*    this.setState({ */}
            {/*      weatherPermissionVisable: false */}
            {/*    }); */}
            {/*  }} */}
            {/*  buttons={[ */}
            {/*    { */}
            {/*      text: I18n.cancle, */}
            {/*      callback: () => { */}
            {/*        console.log('cancle'); */}
            {/*        this.setState({ */}
            {/*          weatherPermissionVisable: false */}
            {/*        }); */}
            {/*      } */}
            {/*    }, { */}
            {/*      text: LocalizedStrings["agree_choose"], */}
            {/*      // 点击确定按钮的回调 */}
            {/*      callback: (result) => { */}
            {/*        console.log(`选中的选项`, result, this.selectedWeatherIndex); */}
            {/*        this.setAllSpecValue('theme', this.selectedWeatherIndex) */}
            {/*        this.setState({ */}
            {/*          weatherPermissionVisable: false, */}
            {/*        }); */}
            {/*      } */}
            {/*    } */}
            {/*  ]} */}
            {/* /> */}

            <ListItem
              title={LocalizedStrings["display_clock_point"]}
              showSeparator={false}
              titleStyle={{  fontWeight: "bold",fontSize: 16 ,color: "#000000",fontFamily: null}}
              hideArrow={true}
              containerStyle={{ backgroundColor: Util.isDark() ? "#xm1A1A1A" : null }}
            />
            {this._renderImages(this.pointerClockTheme, this.state.selectedWhichTheme - (1 + this.digitClockTheme.length + this.weatherTheme.length), 2)}
          </ScrollView>
          {this._renderWeatherDialog()}
        </View>
      </View>
    );
  }

  _renderWeatherDialog() {
    return (
      <AbstractDialog
        visible={this.state.weatherPermissionVisable}
        showTitle={false}
        useNewTheme={true}
        onDismiss={() => {
          this.setState({
            weatherPermissionVisable: false,
          });
        }}
        buttons={[
          {
            text: I18n.cancle,
            callback: () => {
              console.log("cancle");
              this.setState({
                weatherPermissionVisable: false,
              });
            },
          },
          {
            text: LocalizedStrings["agree_choose"],
            // 点击确定按钮的回调
            callback: (result) => {
              console.log(`选中的选项`, this.selectedWeatherIndex);
              if (!__DEV__ && API_LEVEL < 10098) {
                // 小于10099 不去调用方法获取
                this.hadFamilyAddr = false;
                this.longitude = "0";
                this.latitude = "0";
                this.setAllSpecValue("theme", this.selectedWeatherIndex);
                this.setState({
                  weatherPermissionVisable: false,
                });
                return;
              }
              // this.setAllSpecValue('theme', this.selectedWeatherIndex)
              Device.getRoomLocation(Device.deviceID, "1")
                .then((res) => {
                  // Device.getRoomInfoForCurrentHome(Device.deviceID).then((res) => {
                  console.log("===============getRoomInfoForCurrentHome", res, typeof res);

                  if (res.code == 0 && res.data && res.data.hasOwnProperty("latitude")) {
                    this.longitude = res.data.longitude;
                    this.latitude = res.data.latitude;
                    this.hadFamilyAddr = !(this.longitude == "0" && this.latitude == "0"); // 是否设置家庭地址
                    // this.setState({ weatherPermissionVisable: true });
                    // this.selectedWeatherIndex = index + 1 + this.digitClockTheme.length;
                    this.setAllSpecValue("theme", this.selectedWeatherIndex);
                  } else {
                    Toast.fail("c_set_fail");
                  }
                })
                .catch((err) => {
                  console.log("+================", err);
                  Toast.fail("c_set_fail");
                });
              this.setState({
                weatherPermissionVisable: false,
              });

              // let message = `经度：${this.longitude}\n纬度：${this.latitude}`
              // Alert.alert("家庭位置",message);
            },
          },
        ]}
      >
        <ParsedText
          style={{ fontSize: 16, color: "rgba(0, 0, 0, 0.8)", margin: 28, lineHeight: 22 }}
          parse={[{ pattern: /\[1(.+?)\]/g, style: { fontSize: 16, color: "#32bac0" }, renderText: this.renderText }]}
          childrenProps={{ allowFontScaling: false }}
        >
          {/* {!this.hadFamilyAddr ? LocalizedStrings['weather_dialog_without_familyaddr'] : LocalizedStrings['weather_dialog_familyaddr']} */}
          {LocalizedStrings["weather_dialog_without_familyaddr_v2"]}
        </ParsedText>
      </AbstractDialog>
    );
  }

  renderText(matchingString, matches) {
    let find = "\\[|\\]|1|2";
    let re = new RegExp(find, "g");
    return matchingString.replace(re, "");
  }
  componentWillUnmount() {
    this.didFocusListener && this.didFocusListener?.remove();
    // 移除事件监听
    if (this.themeDownloadListener) {
      this.themeDownloadListener.remove();
    }
  }
  componentDidMount() {
    super.componentDidMount();

    const params = [
      { sname: SIID_THEME_SETTINGS, pname: PIID_THEME },
      { sname: SIID_THEME_SETTINGS, pname: PIID_TIME_SHOW },
      { sname: SIID_THEME_SETTINGS, pname: PIID_CALENDER_SETTINGS },
      { sname: SIID_THEME_SETTINGS, pname: PIID_DEVICE_LOCATION },
    ];
    AlarmUtilV2.getSpecPValue(params)
      .then((res) => {
        console.log("[[[[[[[[[[[[[[[[[[[", res);
        if (res[0].code == 0) {
          console.log("get ThemeSettings OK=========", res, res[3].value);
          this.setState({
            selectedWhichTheme: res[0].value,
            timeFormat: res[1].value,
            calendarFormat: res[2].value,
          });
        } else {
          Toast.fail("c_get_fail");
        }
      })
      .catch((err) => {
        console.log("=========correct", err);
        Toast.fail("c_get_fail", err);
      });
    this.didFocusListener = this.props.navigation.addListener(
      // 回到当前页面 或者第一次进来
      "didFocus",
      () => {
        Host.storage.get(Home_Theme_KEY).then((val) => {
          const list = val ? JSON.parse(val) : [];
          if (list[list.length - 1]) {
            this.setState({ homeThemeList: [`file://${Host.file.storageBasePath}/${list[list.length - 1].fileName}`] });
          } else {
            this.setState({ homeThemeList: [] });
          }
        });
        this.themeCallback({ theme: this.state.selectedWhichTheme });
      }
    );

    console.log("this.connectionListener", CameraPlayer.getInstance().isConnected());
    if (CameraPlayer.getInstance().isConnected() && !Host.isMiuiChannel && XiaoaiTTSUtil.CURRENT_VERSION >= XiaoaiTTSUtil.MIN_CONTACTS_TTS_FW_VERSION) {
      ThemeFileManager.getInstance().startRequestThemeImgUrls();// 发了一个消息出去 这里会多次请求拉取。
    }
  
  }

  componentWillUnmount() {
    this.connectionListener && this.connectionListener.remove();
    this.rdtListener && this.rdtListener.remove();
  }

  setAllSpecValue(selectedSetting, result) {
    const setParamsValue = [];
    if (selectedSetting === "time") {
      setParamsValue.push({ sname: SIID_THEME_SETTINGS, pname: PIID_TIME_SHOW, value: result });
    } else if (selectedSetting === "calendar") {
      setParamsValue.push({ sname: SIID_THEME_SETTINGS, pname: PIID_CALENDER_SETTINGS, value: result });
    } else if (selectedSetting === "theme") {
      let location = `${this.longitude},${this.latitude}`;
      setParamsValue.push({ sname: SIID_THEME_SETTINGS, pname: PIID_THEME, value: result }, { sname: SIID_THEME_SETTINGS, pname: PIID_DEVICE_LOCATION, value: location });
    } else {
      selectedSetting = [];
      return;
    }
    console.log("result==========", result);

    AlarmUtilV2.setSpecPValue(setParamsValue)
      .then((res) => {
        if (res[0].code === 0) {
          console.log("set OK ===========", res);
          Toast.success("c_set_success");
          if (selectedSetting === "time") {
            this.setState({
              timeFormatVisible: false,
              timeFormat: result,
            });
          } else if (selectedSetting === "calendar") {
            this.setState({
              calendarFormatVisable: false,
              calendarFormat: result,
            });
          } else if (selectedSetting === "theme") {
            this.setState({
              selectedWhichTheme: result,
            });
            this.themeCallback({ theme: result });
          } else {
            Toast.fail("c_set_fail");
          }
        } else {
          Toast.fail("c_set_fail");
        }
      })
      .catch((err) => {
        Toast.fail("c_set_fail", err);
      });
  }

  setThemeCallback(result) {
    this.setState({
      selectedWhichTheme: result,
    });
  }

  setDeviceLocaton(longitude, latitude) {
    let specValue = `116.397026,39.918058`;
    let params = [{ sname: SIID_THEME_SETTINGS, pname: PIID_DEVICE_LOCATION, value: specValue }];
    AlarmUtilV2.setSpecPValue(params)
      .then((res) => {
        if (res[0].code === 0) {
          console.log("set location OK =========", res);
          Toast.success("c_set_device_location_success");
        } else {
          Toast.fail("c_set_device_location_fail");
        }
      })
      .catch((err) => {
        Toast.fail("c_set_device_location_fail");
      });
  }
}
