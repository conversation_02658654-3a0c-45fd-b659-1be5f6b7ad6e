import { StyleSheet, ScrollView, Image, Text, View, Dimensions, StatusBar, TouchableOpacity } from 'react-native';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles as settingStyles } from './SettingStyles';
import { Device, Host } from "miot";
import StorageKeys from '../StorageKeys';
import StackNavigationInstance from '../StackNavigationInstance';
import CameraConfig from "../util/CameraConfig";
import VersionUtil from '../util/VersionUtil';
import Service from 'miot/Service';


export default class NoSdcardPage extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      showBuyButton: false
    };
    this.isCloudServer = CameraConfig.getIsCloudServer();
    this.supportLargeSdcard = CameraConfig.supportLargeSdcard(Device.model);
    this.disableCloudSetting = !VersionUtil.isFirmwareSupportCloud(Device.model);
  }

  componentDidMount() {
    this.getLocalStatus();

  }

  async getLocalStatus() {
    if (this.disableCloudSetting) { // 跳过去吧 009 不支持云存的设备
      return;
    }
    try {
      let isVip = await StorageKeys.IS_VIP_STATUS;
      let isSupportCloud = CameraConfig.isSupportCloud();
      if (isVip || !isSupportCloud || !Device.isOwner) {
        this.setState({ showBuyButton: false });
      } else {
        this.setState({ showBuyButton: true });
      }
    } catch (exception) {

    }
  }

  render() {
    console.log('Device.model ', Device.model)
    let showBuyText = this.state.showBuyButton && this.props.showBuyButton;
    let width = Dimensions.get("window").width - 34;
    let videoTimeList;
    let videoDescribe = ['sds_buy_tips', 'sds_buy_tips1', 'sds_buy_tips2', 'sds_buy_tips3']
    if (this.supportLargeSdcard) { // 真是乱   c01a01 c01a02支持大卡，但是分辨率还是保持2k   039 049支持大分辨率
      videoTimeList = [
        ['sds_grid00', CameraConfig.isSupport2K(Device.model) ? "camera_quality_fhd2k" : (CameraConfig.isSupport25K(Device.model) ? 'sds_grid03' : 'sds_grid02')],
        ['sds_grid10', 'sds_grid12'],
        ['sds_grid20', 'sds_grid22'],
        ['sds_grid30', 'sds_grid32'],
        ['sds_grid40', 'sds_grid42'],
        ['sds_grid50', 'sds_grid52']
      ];
      if (CameraConfig.isSupportSuperDefinition(Device.model)) {
        videoTimeList = [
          ['sds_grid00', 'sds_grid04'],
          ['sds_grid10', 'sds_grid23'],
          ['sds_grid20', 'sds_grid22'],
          ['sds_grid30', 'sds_grid21'],
          ['sds_grid40', 'sds_grid24'],
          ['sds_grid50', 'sds_grid25']
        ];
      }
      videoDescribe = ['sds_buy_tips', 'sds_buy_tips1', 'sds_buy_tips2', CameraConfig.supportSDCardV2(Device.model) ? null : 'sds_buy_tips21', 'sds_buy_tips3'];
    } else {
      videoTimeList = [
        ['sds_grid00', CameraConfig.isSupport2K(Device.model) ? "camera_quality_fhd2k" : (CameraConfig.isSupport25K(Device.model) ? 'sds_grid03' : 'sds_grid02')],
        ['sds_grid10', 'sds_grid12'],
        ['sds_grid20', 'sds_grid22']
      ];
    }
    if (CameraConfig.isSupportOpus(Device.model)){
      videoDescribe = ['sds_buy_tips', 'sds_buy_tips1', 'sds_buy_tips2', CameraConfig.supportSDCardV2(Device.model) ? null : 'sds_buy_tips21', 'sds_buy_tips4','sds_buy_tips3']
    }
    let tdWidth = Number(100 / videoTimeList[0].length) + "%"
    console.log('tdwidth', typeof tdWidth, tdWidth)
    return (
      <ScrollView 
        style={{ width: "100%", flex: 1, position: "relative", backgroundColor: '#ffffff' }}
        contentContainerStyle={{ alignItems: "center", justifyContent: "space-around" }}
        key={10000} >
        {/* <Separator /> */}
        <View style={styles.noTopBack}>
          <Image style={styles.noTopImage}
            source={require('../../Resources/Images/icon_no_sdcard.png')}
          />
          <Text style={styles.noText}>{LocalizedStrings['sds_nosd']}</Text>
          <View style={{ height: 20 }}></View>
        </View>
        <View style={styles.noDetailBack}>
          {
            videoDescribe.map((item, i) => {
              if (!item) {
                return null;
              }
              return (
                <Text style={styles.noTextLeft}
                  key={11000 + i}
                >{(LocalizedStrings[item].indexOf("32") != -1 && i != 3 && videoTimeList.length == 6) ? LocalizedStrings[item].replace("32", "256") : LocalizedStrings[item]}</Text>

              );
            })
          }
          <View style={{ height: 8 }}></View>
          <View style={styles.noGridContainer}>
            {
              videoTimeList.map((items, i) => {
                return (
                  <View style={{ width: '100%', flexDirection: 'row' }}
                    key={i + 1000}
                  >
                    {
                      items.map((item, i) => {
                        return (
                          <View 
                            style={{
                              width: tdWidth,
                              height: 40,
                              borderWidth: 0.3,
                              borderColor: 'rgba(0,0,0,0.1)',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                            key={i + 100}
                          >
                            <Text style={styles.noText}> {LocalizedStrings[item]} </Text>
                          </View>
                        );
                      })
                    }
                  </View>
                );
              })
            }
          </View>
        </View>
        <View style={{ height: 30 }}></View>
        {
          showBuyText ?
            <TouchableOpacity style={{ position: "relative", bottom: 10, backgroundColor: "#00b377", borderRadius: 4, width: width, marginHorizontal: 17, paddingVertical: 9, display: "flex", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                if (this.props.onButtonClick != null) {
                  this.props.onButtonClick();
                  return;
                }

                let mSupportCloudCountry = CameraConfig.isSupportCloud();
                let isInternationalServer = CameraConfig.getInternationalServerStatus();
                if (isInternationalServer && mSupportCloudCountry) {// 海外云存 跳购买页
                  Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "sdmgt_button" });// 直接跳过去了
                } else {
                  StackNavigationInstance.jumpToStackNavigationPage("CloudIntroPage");
                }
              }}
            >
              <Text style={{ color: "#ffffff", fontSize: 14, textAlign: "center" }}>
                {this.isCloudServer ? LocalizedStrings["eu_buy_cloud_video_tips"] : LocalizedStrings["buy_cloud_video_tips"]}
              </Text>
            </TouchableOpacity>
            : null
        }
      </ScrollView>
    );
  }
}

const storageStyles = StyleSheet.create({
  stateContainer: {
    width: '100%',
    backgroundColor: 'white',
    alignItems: 'center'
  },
  stageBack: {
    width: 217,
    height: 217,
    display: "flex",
    alignItems: "center"
  },
  stateImage: {
    width: '100%',
    height: '100%'
  },
  stateCover: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    display: "flex",
    alignItems: 'center'
  },
  stateCoverTitle: {
    marginTop: 66,
    fontSize: 22,
    color: 'white',
    textAlign: "center"
  },
  stateCoverSeprate: {
    position: "absolute",
    top: 124,
    backgroundColor: 'rgba(255,255,255,0.5)',
    height: 1,
    width: '80%'
  },
  stateCoverDetail: {
    position: "absolute",
    bottom: 56,
    fontSize: 15,
    color: 'white'
  },
  totalText: {
    marginTop: 20,
    marginBottom: 30,
    fontSize: 16,
    color: 'rgba(0,0,0,0.6)'
  },
  tipsBack: {
    paddingTop: 3,
    paddingLeft: 24,
    paddingRight: 24
  },
  tips: {
    marginTop: 5,
    fontSize: 14,
    color: 'rgba(0,0,0,0.5)'
  },
  noTopBack: {
    backgroundColor: 'white',
    width: '100%',
    alignItems: 'center'
  },
  noTopImage: {
    resizeMode: 'stretch',
    marginTop: 25,
    width: 81,
    height: 98
  },
  noDetailBack: {
    paddingTop: 3,
    paddingLeft: 24,
    paddingRight: 24,
    paddingBottom: 23
  },
  noGridContainer: {
    borderWidth: 1.5,
    borderColor: 'rgba(0,0,0,0.1)'
  },
  noText: {
    marginTop: 5,
    fontSize: 14,
    color: 'rgba(0,0,0,0.5)',
    textAlign: "center",
    paddingHorizontal: 10
  },
  noTextLeft: {
    marginTop: 5,
    fontSize: 14,
    color: 'rgba(0,0,0,0.5)'
  }
});

const styles = { ...settingStyles, ...storageStyles };