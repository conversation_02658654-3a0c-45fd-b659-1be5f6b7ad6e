
import React from 'react';
import { ScrollView, View, Text, Platform, Image, TouchableOpacity } from 'react-native';
import Separator from 'miot/ui/Separator';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import { Service, Host, Device } from 'miot';
import NavigationBar from "miot/ui/NavigationBar";
import CameraConfig from '../util/CameraConfig';
import LogUtil from '../util/LogUtil';

export default class CloudIntroPage extends React.Component {

  state = {
    isNoneChinaLand: false
  }
  _renderTitleView() {
    if (this.state.fullScreen) {
      return null;
    }
    const titleProps = {
      
    };
    return (
      <NavigationBar {...titleProps} />
    );
   
  }
  render() {
    return (
      <View style={{ width: "100%", height: "100%", backgroundColor: "#ffffff" }}>
        <Separator />
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={{ width: "100%", height: 72, display: "flex", alignItems: "center", paddingHorizontal: 5 }}>
            <Image style={{ width: "100%", height: "100%" }}
              source={this.state.isNoneChinaLand ? require("../../Resources/Images/cloud_video_intro_title_en.png") : require("../../Resources/Images/cloud_video_intro_title.png")}
              resizeMode={"contain"}
            />
          </View>

          <View style={{ height: 50, display: "flex", flexDirection: "row", marginTop: 10, marginHorizontal: 26, alignItems: "center", backgroundColor: "#e7f6f2" }}>
            <Text style={{ flexGrow: 1, textAlign: "center", color: "#05906d", fontSize: 14 }}>
              {this.isEuropeServer ? LocalizedStrings["eu_camera_cloud"] : LocalizedStrings["camera_cloud"]}
            </Text>
            <Text style={{ flexGrow: 1, textAlign: "center", color: "#05906d", fontSize: 14 }}>
              {LocalizedStrings["cloud_video_intro_title_sdcard"]}
            </Text>
          </View>
          <View style={{ display: "flex", flexDirection: "row", flexWrap: "wrap", marginHorizontal: 26, backgroundColor: "#d1efe5" }}>
            <Text style={{ width: "50%", paddingHorizontal: 20, paddingVertical: 30, backgroundColor: "#ffffff", color: "black", fontSize: 12, padding: 1, borderRadius: 1, borderColor: "#00b377", borderWidth: 0.5, textAlign: "center" }}>
              {LocalizedStrings["cloud_video_tips_1"]}
            </Text>
            <Text style={{ width: "50%", paddingHorizontal: 20, paddingVertical: 30, backgroundColor: "#ffffff", color: "black", fontSize: 12, padding: 1, borderRadius: 1, borderColor: "#00b377", borderWidth: 0.5, textAlign: "center" }}>
              {LocalizedStrings["sdcard_tips_1"]}
            </Text>
            <Text style={{ width: "50%", paddingHorizontal: 20, paddingVertical: 30, backgroundColor: "#ffffff", color: "black", fontSize: 12, padding: 1, borderRadius: 1, borderColor: "#00b377", borderWidth: 0.5, textAlign: "center" }}>
              {LocalizedStrings["cloud_video_tips_2"]}
            </Text>
            <Text style={{ width: "50%", paddingHorizontal: 20, paddingVertical: 30, backgroundColor: "#ffffff", color: "black", fontSize: 12, padding: 1, borderRadius: 1, borderColor: "#00b377", borderWidth: 0.5, textAlign: "center" }}>
              {LocalizedStrings["sdcard_tips_2"]}
            </Text>
            <Text style={{ width: "50%", paddingHorizontal: 20, paddingVertical: 30, backgroundColor: "#ffffff", color: "black", fontSize: 12, padding: 1, borderRadius: 1, borderColor: "#00b377", borderWidth: 0.5, textAlign: "center" }}>
              {LocalizedStrings["cloud_video_tips_3"]}
            </Text>
            <Text style={{ width: "50%", paddingHorizontal: 20, paddingVertical: 30, backgroundColor: "#ffffff", color: "black", fontSize: 12, padding: 1, borderRadius: 1, borderColor: "#00b377", borderWidth: 0.5, textAlign: "center" }}>
              {LocalizedStrings["sdcard_tips_3"]}
            </Text>
          </View>

          <View
            style={{ display: "flex", flexDirection: "row", marginHorizontal: 26, justifyContent: 'center', alignItems: 'flex-end'}}
          >
            <View style={{paddingRight: 30 }}>
              <Image style={{ width: 120, height: 200 }}
                source={this.state.isNoneChinaLand ? require("../../Resources/Images/cloud_video_intro_img_1_en.jpg") : require("../../Resources/Images/cloud_video_intro_img_1.jpg")}
              />
            </View>

            <View style={{paddingLeft: 30 }}>
              <Image style={{ width: 120, height: 200 }}
                source={this.state.isNoneChinaLand ? require("../../Resources/Images/cloud_video_intro_img_2_en.png") : require("../../Resources/Images/cloud_video_intro_img_2.jpg")}
              />
            </View>
          </View>

          <View style={{ width: "100%", display: "flex", alignItems: "center" }}>
            <TouchableOpacity style={{ width: 150, height: 44, display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", marginBottom: 30, backgroundColor: "#00b377", borderRadius: 4 }}
              onPress={() => {

                LogUtil.logOnAll("CloudIntroPage", "云存储打开了云存储设置");
                Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "sdmgt_button" });// 直接跳过去了
                CameraConfig.isToUpdateVipStatue = true;
              }}
            >
              <Text style={{ color: "#ffffff", fontSize: 18 }}>
                {LocalizedStrings["buy_now"]}
              </Text>
            </TouchableOpacity>
          </View>

        </ScrollView>
      </View>
    );
  }

  componentDidMount() {
    this.isEuropeServer = CameraConfig.getIsEuropeServer();
    this.props.navigation.setParams({
      title: LocalizedStrings['sds_title'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

    let language = Host.locale.language || "en";
    let isNoneChinaLand = language != "zh" && language != "tw" && language != "hk";
    this.setState({ isNoneChinaLand: isNoneChinaLand });

  }
}