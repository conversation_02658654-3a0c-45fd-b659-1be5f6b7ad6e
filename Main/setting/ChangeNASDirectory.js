'use strict';
import { Device, DarkMode } from "miot";
import React from 'react';
import {
  Image,
  ActivityIndicator,
  StyleSheet,
  ScrollView,
  View,
  Text,
  TextInput,
  Button,
  TouchableOpacity
} from 'react-native';
import RPC from "../util/RPC";
import Toast from '../components/Toast';

import { ListItemWithSwitch, ListItem } from 'miot/ui/ListItem';
import { ChoiceDialog, MessageDialog, LoadingDialog } from 'miot/ui/Dialog';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import NavigationBar from "miot/ui/NavigationBar";
import LogUtil from "../util/LogUtil";
import LoadingView from "../ui/LoadingView";
export default class ChangeNASDirectory extends React.Component {

  static navigationOptions = ({navigation}) => {
    return {
      header: (
          <NavigationBar
              type={"dark" == DarkMode.getColorScheme() ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT}
              backgroundColor={"dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff'}
              left={navigation.getParam('left')}
              right={navigation.getParam('right')}
              title={navigation.getParam("navTitle")}
          />
      )
    };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      showFailDialog: false,
      selectOldVideo: 0,
      isPickerOldVideo: false,
      storageLocation: '1',
      showLoading: true,
      darkMode: false,
      canSave: false,
      showSaveDialog:false
    };

  }
  render() {
    return (
      <View style={styles.container} >
        <ScrollView showsVerticalScrollIndicator={false}
                    scrollEventThrottle={1}
                    onScroll={this.scrollViewScroll}>

          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={'0'}>
            <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23,fontFamily:'MI-LANTING--GBK1-Light' }}>
              {this.props.navigation.state.params.title}</Text>
          </View>
          {this._renderSelectView()}
        </ScrollView>
        {this._renderLoadingView()}

        {this._renderSetFailDialog()}
        {this._renderBackDialog()}
      </View>
    );
  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ navTitle: LocalizedStrings['change_nas_location'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ navTitle: "" });
    }
  };

  componentDidMount() {
   /* this.props.navigation.setParams({
      title: '',
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });*/
    this.refreshNavigationBar();
    //刷新的时候
    this.willFocusSubscription = this.props.navigation.addListener(
      'didFocus', () => {
        this._getDirectory();
      }
    );
  }

  refreshNavigationBar() {

    this.props.navigation.setParams({
      backgroundColor: "#F6F6F6",
      title: LocalizedStrings['change_nas_location'],
      type: "dark" == DarkMode.getColorScheme() ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({showSaveDialog: true});
              return;
            }
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key:  NavigationBar.ICON.COMPLETE,
          disable: !this.state.canSave,
          onPress: () => {
            this.setState({
              isPickerOldVideo: true //老逻辑，询问用户是保留还是删除所选目录下的文件
            });
           // this.props.navigation.goBack();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

  }

  componentWillUnmount() {
    this.willFocusSubscription.remove()
  }
  _getDirectory() {
    let { deviceMessage } = this.props.navigation.state.params
    // console.log(deviceMessage,'deviceMessage')
    this.setState({
      lastDir: deviceMessage.share.dir
    });
    this.orignDirectory = deviceMessage.share.dir;

    let obj = {};
    obj.type = deviceMessage.share.type;
    obj.group = deviceMessage.share.group;
    obj.addr = deviceMessage.share.addr;
    obj.name = deviceMessage.share.name;
    obj.dir = ''
    // obj.dir = deviceMessage.name == null ? deviceMessage.dir : (deviceMessage.addr + "/" + this.state.deviceName)
    obj.user = deviceMessage.share.user
    obj.pass = deviceMessage.share.pass
    let param = {};
    param.share = obj;
    RPC.callMethod("nas_list_dir", param).then((res) => {
      LogUtil.logOnAll("ChangeNASDirectory res:" + JSON.stringify(res));

      if (res.code == 0 && res.result.length !== 0) {
        this.setState({
          showLoading: false,
          locationList: res.result,
          showSelectView: true
        })
      }
      else {
        //这里出错原因 ：修改存储位置但是u盘不在路由器

        Toast.fail('c_get_fail');
        this.props.navigation.goBack()
      }


    }).catch((err) => {
      LogUtil.logOnAll("ChangeNASDirectory", "nas_list_dir failed" + JSON.stringify(err));

      this.props.navigation.goBack()
      // console.log(err,'errr')
      Toast.fail('c_get_fail', err);
    });

  }
  _renderLoadingView() {
    if (this.state.showLoading) {
      let isDark = DarkMode.getColorScheme() == "dark";
      return (
          <View
              style={{ position:"absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
          >
            <LoadingView
                type={isDark ? LoadingView.TYPE.LIGHT : LoadingView.TYPE.DARK}
                style={{ width: 54, height: 54 }}
            />
            <Text
                style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
              {LocalizedStrings["camera_loading"]}
            </Text>
          </View>
      );

    }

  }
  _renderSelectView() { //this.state.lastDir == item
    if (this.state.showSelectView) {
      return (
          <ScrollView style={styles.container}>
            <View style={{height: 72, paddingHorizontal: 28, display: 'flex', flexDirection: 'row',
              justifyContent: "flex-start", alignItems: "center"}} key={'0'}>
              <View>
                <Image style={{width: 50, height: 50}}
                       source={require('../../Resources/Images/icon_nas_disk.png')}></Image>
              </View>

              <View style={{flex: 1}}>
                <Text style={{fontSize: 16, color: "black", marginLeft: 14}}>
                  {this.props.navigation.state.params.deviceMessage.share.name}</Text>
              </View>
            </View>

            <View style={styles.whiteBlank} />
          {
            this.state.locationList.map((item, i) =>
                (
                    <TouchableOpacity key={i} onPress={() => {
                      this._onSelectedItem(item)
                    }}>

                      <View style={{height: 72, paddingHorizontal: 28, display: 'flex', flexDirection: 'row',
                        justifyContent: "flex-start", alignItems: "center"}} key={i}>
                        <View>
                          <Image style={{width: 50, height: 50}}
                                 source={require('../../Resources/Images/icon_nas_file.png')}></Image>
                        </View>

                        <View style={{flex: 1}}>
                          <Text style={{fontSize: 16, color: "black", marginLeft: 14}}>
                            {item}</Text>
                        </View>

                        <Image style={{width:22,height:22}} source={this.state.lastDir == item?require("../../Resources/Images/icon_single_checked.png"):require("../../Resources/Images/icon_single_unchecked.png")}/>
                      </View>
                    </TouchableOpacity>

                )
            )
          }
          {this.renderPickerOldVideo()}
        </ScrollView>
      )
    }

  }
  _onSelectedItem(value) {
    if (this.state.lastDir !== value) {
      this.setState({
        lastDir:value,
        storageLocationName: value,
        canSave:this.orignDirectory!=value
      },()=>this.refreshNavigationBar());

      // 先弹那个老位置的框
      //这里是选择了保留还是不保留之后的操作 这里就要设置新的存储器了
    }
  }
  renderPickerOldVideo() {
    let selectedIndex = this.state.selectOldVideo
    this.selectedIndexArray = [selectedIndex];
    return (
      <ChoiceDialog
        style={{ width: 100 }}
        dialogStyle={{ itemSubtitleNumberOfLines: 1 }}
        useNewType={true}
        itemStyleType={2}
        visible={this.state.isPickerOldVideo}
        title={LocalizedStrings["nas_location_tip"]}
        options={[
          { title: LocalizedStrings["nas_location_tip1"] },
          { title: LocalizedStrings["nas_location_tip2"] },
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ isPickerOldVideo: false })}
       /* onSelect={(result) => {
          this.selectedIndexArray = result;
          this._onPickerOldVideoChanged(result);
          this.setState({ isPickerOldVideo: false });
          this.props.navigation.goBack();
        }}*/
        buttons={[{
          text: LocalizedStrings.action_cancle,
          callback: () => this.setState({ isPickerOldVideo: false })
        }, {
          text: LocalizedStrings.action_confirm,
          callback: (result) => {
            this.selectedIndexArray = result;
            this._onPickerOldVideoChanged(result);
            this.setState({ isPickerOldVideo: false });
          }
        }]}

      />
    )
  }
  _renderSetFailDialog() {
    return (
      <MessageDialog
        visible={this.state.showFailDialog}
        title={LocalizedStrings['c_set_fail']}
        message={LocalizedStrings["nas_set_fail_tips"]}
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              this.setState({ showFailDialog: false });
            }
          }
        ]}
      />
    );
  }
  _onPickerOldVideoChanged(result) {
    let { deviceMessage } = this.props.navigation.state.params
    deviceMessage.share.dir = this.state.storageLocationName
    Toast.loading('c_setting');
    if (result == 0) {
      RPC.callMethod("nas_set_config", deviceMessage).then((res) => {
        if (res.result[0] == 'OK') {
          Toast.success('save_success')
          this.props.navigation.goBack()
        }
        else {
          // Toast.fail('c_set_fail');
          this.setState({ showFailDialog: true });
        }
      }).catch((err) => {
        LogUtil.logOnAll("ChangeNASDirectory", "nas_set_config failed" + JSON.stringify(err));

        // Toast.fail('c_set_fail', err);
        this.setState({ showFailDialog: true });
      });
    } else {
      RPC.callMethod("nas_clear_dir", {}).then((res) => {
        if (res.result == 'OK') {
          RPC.callMethod("nas_set_config", deviceMessage).then((res) => {
            if (res.result[0] == 'OK') {
              Toast.success('save_success')
              this.props.navigation.goBack();
            }
            else {
              // Toast.fail('c_set_fail');
              this.setState({ showFailDialog: true });
            }
          }).catch((err) => {
            LogUtil.logOnAll("ChangeNASDirectory", "nas_set_config failed" + JSON.stringify(err));

            // Toast.fail('c_set_fail', err);
            this.setState({ showFailDialog: true });
          });
        }

      }).catch((err) => {
        LogUtil.logOnAll("ChangeNASDirectory", "nas_clear_dir failed" + JSON.stringify(err));

        // this.setState({ showLoading: false });
        Toast.fail('c_set_fail', err);
      });
    }
  }

  _renderBackDialog() {
    return (
        <MessageDialog
            visible={this.state.showSaveDialog}
            message={LocalizedStrings['exit_change_disappear']}
            messageStyle={{textAlign: "center"}}
            canDismiss={false}
            buttons={[
              {
                text: LocalizedStrings["action_cancle"],
                callback: (_) => {
                  this.setState({showSaveDialog: false});
                }
              },
              {
                text: LocalizedStrings["btn_confirm"],
                callback: (_) => {
                  this.setState({showSaveDialog: false});
                  this.props.navigation.goBack();
                }
              }
            ]}
        />
    )
  }

}
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
  },
  whiteBlank: {
    height: 0.5,
    marginHorizontal: 28,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginTop: 20,
    marginBottom: 20
  },

});
