import { ScrollView, StyleSheet, Text, BackHandler, View, Image, PermissionsAndroid, Platform, TouchableHighlight, I18nManager } from "react-native";
import React, { useEffect, useState } from "react";
import { LocalizedStrings, TouchableView } from "miot/ui";
import { Device, Service, Host } from "miot";
import { localStrings } from "../MHLocalizableString";
import { AbstractDialog, ListItem, NavigationBar, MessageDialog, InputDialog, ImageButton } from "mhui-rn";
import Util from "../util2/Util";

import { Styles as SettingStyles } from "miot/resources";
import BaseSettingPage from "../BaseSettingPage";

import FDSUtil from "../util/FDSUtil";
import LogUtil from "../util/LogUtil";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import ChoiceItem from "../widget/ChoiceItem";
import MIChoiceItem from "mhui-rn/dist/components/listItem/ChoiceItem";
import ParsedText from "react-native-parsed-text";

import CallUtil from "../util/CallUtil";
import PermissionUtil from "../util/PermissionUtil";
import ImagePicker from "react-native-image-picker";
import Toast from "../components/Toast";
import { RkButton } from 'react-native-ui-kitten';
import LoadingView from "../ui/LoadingView";
import XiaoaiTTSUtil from "../util/XiaoaiTTSUtil";

const ContactsSettingRender = ({ props, customGoBack }) => {
  let callSettingData = props?.navigation?.state?.params?.callSettingData;
  const callback = props?.navigation?.state?.params?.callback;
  const initIndex = props?.navigation?.state?.params?.index;
  const [userItem, setUseItem] = useState(props?.navigation?.state?.params?.item);
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [renameError, setRenameError] = useState({ commentErr: null, isErrorName: null });
  const [showDelDialog, setShowDelDialog] = useState(false);
  const [showAvatarIntroDialog, setShowAvatarIntroDialog] = useState(false);
  const [showGlobal, setShowGlobal] = useState(false);


  console.log(callSettingData, "ContactsSettingRender");
  
 
  const goBackFunction = () => {
    if (!showGlobal) {
      props?.navigation?.goBack();
      return true;
    } else {
      return true;
    }
  };
  useEffect(() => {
    
    if (!showGlobal) {
  
      customGoBack(props?.navigation?.goBack);
    } else {
      customGoBack(null);
    }
    BackHandler.addEventListener('hardwareBackPress', goBackFunction);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', goBackFunction);
    };
  }, [showGlobal]);
  const updateAvatar = (data) => {
    // 更新联系人信息
    let needUpData = {};

    if (userItem && userItem.key) {
      needUpData.icon = userItem.icon ? userItem.icon : "";
      needUpData.callNameTts = userItem.callNameTts ? userItem.callNameTts : "";
      if (data.downloadUrl) {
        needUpData.fdsUrl = data.downloadUrl;
      } else {
        delete needUpData.fdsUrl;
      }
      needUpData.objName = data.objName;
    }
    Toast.loading("c_setting");
    let paramsStr = JSON.stringify(needUpData);
    // 更新头像
    // 改为先更新到设备，再设置到云端
    let contactKey = `call_${ userItem.key }`;
    // let updateObj = {method: "1", [contactKey]: needUpData};
    let updateObj = { [contactKey]: needUpData };
    let updateParams = JSON.stringify(updateObj);
    // CallUtil.updateSettingToDevice([updateParams]).then((res) => {
    CallUtil.updateIconSettingToDevice(updateParams)
      .then((res) => {
        DeviceSettingUtil.setDeviceSetting(contactKey, paramsStr)
          .then((res) => {
            Toast.success("c_set_success");
            // 删除之前的缓存
            if (userItem.objName) {
              FDSUtil.deleteLocalImgCache(userItem.objName).then(() => {});
            }

            // 更新修改后的头像
            // 已fds图片来存到本地
            FDSUtil.getLocalImgUrl(data.objName, data.downloadUrl).then((local) => {
              userItem.localFileName = local;
              userItem.objName = data.objName;
              userItem.fdsUrl = data.downloadUrl;
              callback(initIndex, JSON.stringify(userItem));
              setUseItem({ ...userItem });
            });
          })
          .catch((error) => {
            Toast.fail("c_set_fail");
          });
      })
      .catch((error) => {
        Toast.fail("c_set_fail");
      });
  };
  const controlAvatarDialog = (visible) => {
    setShowAvatarIntroDialog(visible);
  };
  // 修改来电名字
  const renderRenameDialog = () => {
    return (
      <InputDialog
        visible={showRenameDialog}
        title={localStrings["ai_call_name"]}
        inputs={[
          {
            placeholder: localStrings["please_enter_name"],
            defaultValue: userItem?.callName,
            textInputProps: {
              autoFocus: true
            },
            onChangeText: (text) => {
              if (renameError?.commentErr != null) {
                renameError.commentErr = null;
                setRenameError({ ...renameError });
              }
              let isEmoji = Util.containsEmoji(text);
              let length = text.length;
              
              // let isCommon = this.isTextcommon(result);
              if (isEmoji) {
                setRenameError({ isErrorName: true, commentErr: localStrings["Special_symbol_input_not_supported_temporarily"] });
              } else if (length > 10) {
                setRenameError({ isErrorName: true, commentErr: localStrings["input_name_too_long2"] });
              } else if (length <= 0 || text.trim().length == 0) {
                setRenameError({ isErrorName: true, commentErr: localStrings["add_feature_empty_tips"] });
              } else {
                setRenameError({ isErrorName: false, commentErr: "error" });
              }
            },
            type: "DELETE",
            isCorrect: !renameError.isErrorName
          }
        ]}
        inputWarnText={renameError.commentErr}
        noInputDisButton={true}
        buttons={[
          {
            callback: () => {
              setShowRenameDialog(false);
              setRenameError({ isErrorName: false, commentErr: null });
            }
          },
          { 
            callback: (result) => {
              console.log(`结果`, result.textInputArray[0]);
              if (renameError.isErrorName) {
                return;
              }
              let text = result.textInputArray[0].trim();
              if (text.length > 0 && !Util.containsEmoji(text)) {
                setRenameError({ commentErr: null });
                if (!Device.isOnline) {
                  Toast.success("c_set_fail");
                  return;
                }
                if (XiaoaiTTSUtil.isSupportedContactsTts()) {
                  // 1. 先获取TTS
                  Service.xiaoai.callXiaoaiTTS({ text: text })
                    .then((res) => {
                      console.log(`getXiaoaiTTS callname success ${JSON.stringify(res)}`);
                      let { data: { url } } = res;

                      // 2. 准备更新数据
                      let needUpData = JSON.parse(JSON.stringify(callSettingData));
                      LogUtil.logOnAll("KeyCallSetting", needUpData.hasOwnProperty("key1"), needUpData.hasOwnProperty("key2"), needUpData.hasOwnProperty("key3"));
                      if (userItem && needUpData[userItem.key]) {
                        needUpData[userItem.key].callName = text;
                      }
                      let iconUpData = {};

                      iconUpData.callName = text || "";
                      iconUpData.icon = userItem.icon || "";
                      if (userItem.fdsUrl) {
                        iconUpData.fdsUrl = userItem.fdsUrl;
                      }
                      iconUpData.objName = userItem.objName || "";
                      iconUpData.callNameTts = url;  // 确保TTS URL也被保存到头像数据中

                      // 3. 准备头像数据
                      let contactKey = `call_${userItem.key}`;

                      let updateIconObj = { [contactKey]: iconUpData };

                      // 4. 按顺序更新
                      let nameParamsStr = JSON.stringify(needUpData);
                      let updateIconParams = JSON.stringify(updateIconObj);

                      // 先更新基础设置
                      CallUtil.updateSettingToDevice(nameParamsStr)
                        .then(() => {
                          // 再更新头像设置
                          return CallUtil.updateIconSettingToDevice(updateIconParams);
                        })
                        .then(() => {
                          // 更新云端基础用户信息设置
                          return DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, nameParamsStr);
                        })
                        .then(() => {
                          // 更新云端头像设置
                          return DeviceSettingUtil.setDeviceSetting(contactKey, JSON.stringify(iconUpData));
                        })
                        .then(() => {
                          // 更新本地状态
                          callSettingData = needUpData;
                          userItem.callName = text;
                          userItem.callNameTts = url;

                          // 更新UI和回调
                          callback(initIndex, JSON.stringify(userItem));
                          setUseItem({ ...userItem });
                          setRenameError({ isErrorName: false, commentErr: null });
                          setShowRenameDialog(false);
                          Toast.success("c_set_success");
                        })
                        .catch((error) => {
                          Toast.fail("c_set_fail");
                        });
                    })
                    .catch((err) => {
                      console.log(`getXiaoaiTTS callname fail ${err}`);
                      Toast.fail("c_set_fail");
                    });
                } else {
                  let needUpData = JSON.parse(JSON.stringify(callSettingData));
                  LogUtil.logOnAll("KeyCallSetting", needUpData.hasOwnProperty("key1"), needUpData.hasOwnProperty("key2"), needUpData.hasOwnProperty("key3"));
                  if (userItem && needUpData[userItem.key]) {
                    needUpData[userItem.key].callName = text;
                  }
                  let paramsStr = JSON.stringify(needUpData);
                  // 先更新设备端，再更新云端
                  // needUpData['method'] = "0";
                  let updateParams = JSON.stringify(needUpData);
                  CallUtil.updateSettingToDevice(updateParams)
                    .then((res) => {
                      DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr)
                        .then((res) => {
                          // delete needUpData['method'];
                          callSettingData = needUpData;
                          Toast.success("c_set_success");
                          userItem.callName = text;
                          console.log(userItem?.index, JSON.stringify(userItem), "JSON.stringify(userItem))");

                          callback(initIndex, JSON.stringify(userItem));
                          setUseItem(userItem);
                          setRenameError({ isErrorName: false, commentErr: null });
                          setShowRenameDialog(false);
                        })
                        .catch((error) => {
                          Toast.fail("c_set_fail");
                        });
                    })
                    .catch((err) => {
                      Toast.fail("c_set_fail");
                    });
                }
              } else {
                if (Util.containsEmoji(text)) {
                  renameError.commentErr = localStrings["Special_symbol_input_not_supported_temporarily"];
                  setRenameError({ ...renameError });
                }
              }
            }
          }
        ]}
        onDismiss={() => {
          setShowRenameDialog(false);
          setRenameError({ commentErr: null, isErrorName: false });
        }}
      />
    );
  };

  const deleteClickContact = () => {
    if (!Device.isOnline) {
      Toast.success("c_set_fail");
      return;
    }
    let needUpData = JSON.parse(JSON.stringify(callSettingData));
    
    const delUid = needUpData[userItem.key]?.mijia;
    const onlyWx = needUpData[userItem.key]?.only_wx;
    if (userItem.key && needUpData[userItem.key]) {
      delete needUpData[userItem.key];
    }
    setShowGlobal(true);
    console.log(onlyWx, "apiResapiRes111");
    if (onlyWx) {
      CallUtil.delWxCallOnlyContact({
        uid: delUid, // 联系人的uid
        did: Device.deviceID,
        owner_uid: Service.account.ID // 设备owner的uid
      }).then((apiRes) => {
       
        
        if (apiRes.succ === true) {
          CallUtil.sortByIndexNew(needUpData);
          console.log(needUpData, "apiResapiRes");
          delContact(needUpData);
        }
      }).catch((e) => {
        console.log(e, "失败了111");
        setShowGlobal(false);
      });
    } else {
      CallUtil.sortByIndexNew(needUpData);
      let paramsStr = JSON.stringify(needUpData);
      // needUpData['method'] = "0";
      let updateParams = JSON.stringify(needUpData);
      CallUtil.updateSettingToDevice(updateParams)
        .then((res) => {
          DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr)
            .then((res) => {
              delContact(needUpData);
              // props?.navigation?.goBack();
            })
            .catch((error) => {
              setShowGlobal(false);
              Toast.fail("c_set_fail");
            });
        })
        .catch((err) => {
          setShowGlobal(false);
          Toast.fail("c_set_fail");
        });
    }
    const delContact = (needUpData) => {
      // 成功后
      // delete needUpData['method'];
      callSettingData = needUpData;
      Toast.success("delete_contact_success");
      callback(initIndex, null, true);
      // 删除setting中的头像数据
      let deleteKey = `call_${ userItem.key }`;
      console.log(deleteKey, "deleteKey");
      DeviceSettingUtil.delDeviceSetting([deleteKey]).then((res) => {});
      // 删除本地缓存图片
      if (userItem.objName && userItem.objName != "") {
        let fileName = userItem?.objName?.substring(userItem.objName.lastIndexOf("/") + 1);
        Host.file.deleteFile(fileName).then(() => {});
      }
   
      // 删除联系人后也要把onekeycalluser删除掉
      if (callSettingData?.onekeycalluser === userItem?.key && callSettingData.onekeycalluserid === `${ userItem?.mijia }`) {
     console.log('进来了11111111111');
     
        callSettingData.onekeycalluser = '';
        callSettingData.onekeycalluserid = '';
        DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(callSettingData))
          .then((res) => {
            CallUtil.updateSettingToDevice(JSON.stringify(callSettingData))
              .then((res) => {
              }).catch((erroe) => {
                setShowGlobal(false);
              }).finally(() => {
                setShowGlobal(false);
                 props?.navigation?.goBack();
        
              });
          }).catch((error) => {
            setShowGlobal(false);
          });
      } else {
        setShowGlobal(false);
        props?.navigation?.goBack();
      }
   
     
    };
  };
  const editAvatar = () => {
  
    if (!Device.isOnline) {
    // 设备已经离线了
      Toast.success("c_set_fail");
      return;
    }
    controlAvatarDialog(true);
  
  };

  // 取消微信授权弹窗和message
  const removeContactMsg = () => {
    return (

      <View style={{ flexDirection: 'row' }}
        key={11}
      >
        <RkButton
          style={{ margin: SettingStyles.common.padding, flexGrow: 1, height: 46, borderRadius: 23, backgroundColor: '#f5f5f5', display: 'flex', marginBottom: 0 }}
          onPress={() => {
            if (showGlobal) {
              return; 
            }
            setShowDelDialog(true);
          }}
          activeOpacity={0.8}
        >
          <Text style={{ color: '#F43F31', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
            {localStrings["remove_contact"]}
          </Text>
        </RkButton>
        <MessageDialog
          visible={showDelDialog}
          message={
            <ParsedText
              style={[styles.desc_subtitle, { fontWeight: "400", paddingHorizontal: 28, marginTop: 20 }]}
              parse={[{ pattern: /\%(.+?)\%/g, style: { }, renderText: () => userItem.callName }]}
              childrenProps={{ allowFontScaling: false }}
            >
              {localStrings["wx_confirm_delete_contact_V2"]}
            </ParsedText>
          }
          messageStyle={{ textAlign: "center" }}
          canDismiss={true}
          onDismiss={() => setShowDelDialog(false)}
          buttons={[
            {
              text: localStrings["btn_cancel"],
              callback: (_) => {
                setShowDelDialog(false);
              }
            },
            {
              text: localStrings["remove_button"],
              callback: (_) => {
                setShowDelDialog(false);
                // 删除按键联系人
                deleteClickContact();
              }
            }
          ]}
        />
      </View>


    /*  <View
        style={{
          flex: 1,
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          top: "100%",
          left: 28,
          right: 28,
          position: "absolute",
          height: 46
        }}
      >
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => {
            setShowDelDialog(true);
          }}
        >
          <Text
            style={{
              color: "#F43F31",
              fontSize: 16,
              fontWeight: "bold",
              textAlign: "center",
              // paddingHorizontal: 100
              width: '100%'
            }}
          >
            {localStrings["remove_contact"]}
          </Text>
        </TouchableOpacity>

        
      </View> */
    );
  };

  let nickname = userItem.callName ? userItem.callName : "";
  // if (userItem.mijia == Service.account.ID) {
  //   nickname = `${nickname}${localStrings["call_me"]}`;
  // }
  let icon = null;
  if (userItem.objName && userItem.localFileName) {
    icon = `file://${ Host.file.storageBasePath }/${ userItem.localFileName }`;
  } else if (userItem.icon?.indexOf("http") < 0) {
    icon = null;
  } else if (userItem.icon) {
    icon = userItem.icon;
  }
    
  return (
    <View style={styles.container}>
      <ScrollView bounces={Platform.OS === "ios" ? false : true} style={{ paddingBottom: 40, flex: 1 }}>
        <ListItem
          titleStyle={{ fontSize: 16, fontWeight: "bold" }}
          containerStyle={{ height: 60, opacity: 1 }}
          showSeparator={false}
          title={localStrings["mi_ID"]}
          value={userItem?.mijia}
          onPress={() => {}}
          hideArrow={true}
        />
        <View style={{ height: 60, position: 'relative' }}>
          <ListItem
            titleStyle={{ fontSize: 16, fontWeight: "bold" }}
            containerStyle={{ height: 60, opacity: 1 }}
            showSeparator={false}
            title={localStrings["call_Avatar"]}
            onPress={editAvatar }
           
          />
          <ImageButton onPress={editAvatar} style={{ position: 'absolute', width: 40, height: 40, borderRadius: 20, top: 10, bottom: 10, right: 47 }} source={icon != null ? { uri: icon } : require("../../Resources/Images/icon_user.png")} />
  
        </View>
       
        <ListItem
          titleStyle={{ fontSize: 16, fontWeight: "bold" }}
          containerStyle={{ height: 60 }}
          showSeparator={false}
          title={localStrings["ai_call_name"]}
          value={nickname}
          onPress={() => {
            setShowRenameDialog(true);
          }}
          valueMaxWidth={"40%"}
        />
      </ScrollView>
      {removeContactMsg()}
      {renderRenameDialog()}
      <RenderAvatarDialog showAvatarIntroDialog={showAvatarIntroDialog} updateVisible={controlAvatarDialog} updateAvatar={updateAvatar} props={props} icon={icon} />
     
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : 'white',
    flex: 1,
    height: '100%',
    position: 'relative'
  },
  titleContainer: {
    height: 32,
    backgroundColor: Util.isDark() ? "#xm000000" : "white",
    justifyContent: "center",
    paddingLeft: SettingStyles.common.padding
  },
  title: {
    fontSize: 12,
    color: "#8c93b0",
    lineHeight: 14
  },
  separatorTable: {
    height: 40,
    justifyContent: "center"
  },
  freeCall: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginLeft: SettingStyles.common.padding,
    marginRight: SettingStyles.common.padding,
    marginBottom: 16,
    marginTop: 16
  },
  freeCallDesc: {
    flex: 1,
    marginRight: 20
  },
  freeCallTitle: {
    fontSize: 16,
    color: "#000000"
  },
  freeCallSubtitleTitle: {
    fontSize: 13,
    color: "rgba(0, 0, 0, 0.6)"
  },
  removeButton: {
    borderRadius: 86,
    paddingBottom: 12,
    paddingTop: 12,
    backgroundColor: "rgba(0, 0, 0, 0.04)",
    width: '100%'
  }
});

export default class ContactsSetting extends BaseSettingPage {
  constructor(props, context) {
    super(props, context);
    this.props = props;
    this.state = {
      goBack: null
    };
  }
  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings.contacts_info,
      type: NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            // this.props.navigation.goBack();
            this.state.goBack && this.state.goBack();
          }
        }
      ],

      titleStyle: {
        fontSize: 18,
        color: "#333333",
        fontWeight: 500
      }
    });
  }
  getTitle() {
    return localStrings["contacts_info"];
  }
  customSetGoBack(goBack) {
    this.setState({ goBack });
  }
  renderSettingContent() {
    return <ContactsSettingRender props={this.props} customGoBack={this.customSetGoBack.bind(this)}/>;
  }
}

/* const RenderGlobalLoading = ({ showGlobalLoading }) => {
  if (!showGlobalLoading) {
    return null;
  }
  return (
    <View style={{ backgroundColor: 'rgba(255,255,255,0.2)', zIndex: 4, position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}>
      <LoadingView style={{ width: 100, height: 100 }} />
    </View>
  );

}; */

// 修改头像组件
const RenderAvatarDialog = ({ showAvatarIntroDialog, updateVisible, props, updateAvatar, icon }) => {
  const [showAvatarContactDialog, setShowAvatarContactDialog] = useState(false);
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [permissionRequestState, setPermissionRequestState] = useState(0);
  const takePicture = () => {
    PermissionUtil.checkCameraPermission()
      .then((res) => {
        console.log(res);
        props.navigation.navigate("AvatarDisplay", {
          type: "camera",
          callback: (data) => {
            if (data) {
              updateAvatar(data);
            }
          }
        });
      })
      .catch((err) => {
        // 没有相机权限
        if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          setShowPermissionDialog(true);
          setPermissionRequestState(0);
        }
      });
  };

  const selectPicture = () => {
    PermissionUtil.checkCameraPermission()
      .then((res) => {
        PermissionUtil.checkStoragePermission()
          .then((res) => {
            console.log(res);
            selectPhotoTapped();
          })
          .catch((err) => {
            // 没有读写手机存储权限
            if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
              setShowPermissionDialog(true);
              setPermissionRequestState(0);
            }
          });
      })
      .catch((err) => {
        // 没有相机权限
        if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          setShowPermissionDialog(true);
          setPermissionRequestState(1);
        }
      });
  };

  const renderPermissionDialog = () => {
    if (!showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 相机
    //
    let message = "";
    if (permissionRequestState == 0) {
      message = localStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? localStrings["permission_name_storage"] : localStrings["s_photo_album"]);
    } else {
      message = localStrings["permission_tips_denied_msg"].replace("%s", localStrings["permission_name_camera"]);
    }
    return (
      <MessageDialog
        title={localStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14
        }}
        buttons={[
          {
            text: localStrings["action_cancle"],
            callback: () => {
              setShowPermissionDialog(false);
            }
          },
          {
            text: localStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              setShowPermissionDialog(false);
            }
          }
        ]}
        onDismiss={() => {
          setShowPermissionDialog(false);
        }}
        visible={showPermissionDialog}
      />
    );
  };

  const selectPhotoTapped = () => {
    const options = {
      quality: 1.0,
      maxWidth: 1200,
      maxHeight: 1200,
      storageOptions: {
        skipBackup: true
      }
    };
    setTimeout(() => {
      ImagePicker.launchImageLibrary(options, (response) => {
        console.log("response", response);
        if (response.didCancel) {
          console.log("User cancelled photo picker");
          // Toast.fail("bind_error");
          return;
        } else if (response.error) {
          console.log("ImagePicker Error: ", response.error);
          Toast.fail("bind_error");
          return;
        } else if (response.customButton) {
          console.log("User tapped custom button: ", response.customButton);
          Toast.fail("bind_error");
          return;
        } else {
          // Toast.loading('c_setting');
          if (
            !response.fileName ||
            response.fileName.indexOf("webp") != -1 ||
            response.fileName.indexOf("gif") != -1 ||
            (response.type && response.type.indexOf("webp") != -1) ||
            (response.type && response.type.indexOf("gif") != -1)
          ) {
            // 这里需要判断下图片格式，不符合的格式，需要给相应提示
            Toast._showToast(localStrings["picture_not_support"]);
          } else {
            let path = response.uri.slice(7);
            console.log(path, "path");

            cropImage(response);
          }
        }
      });
    }, 100);
  };

  const cropImage = async(data) => {
    try {
      let width = data.width;
      let height = data.height;
      let targetName = `avatar/crop_avatar.jpg`;
      let tempTargetName = `avatar/crop_avatar_temp.jpg`;
      // -1的原因是，某些特殊图片，精度问题会导致裁剪的宽、高大于实际值
      width = width - 1;
      height = height - 1;
      let imgMin = Math.min(width, height);
      let cropSize = imgMin;
      let imgMax = Math.max(width, height);
      let x = 0,
        y = 0;
      if (width >= height) {
        x = Math.floor((imgMax - imgMin) / 2);
      } else {
        y = Math.floor((imgMax - imgMin) / 2);
      }
      Host.file
        .deleteFile(targetName)
        .then((res) => {
          console.log("================", res);
          doCropImage(data, targetName, tempTargetName, cropSize, x, y);
        })
        .catch((error) => {
          console.log("================error", error);
          doCropImage(data, targetName, tempTargetName, cropSize, x, y);
        });
    } catch (e) {
      console.log("crop avatar error2", e);
      Toast.fail("c_set_fail");
    }
  };

  const doCropImage = (data, targetName, tempTargetName, cropSize, x, y) => {
    Host.file
      .writeFileThroughBase64(tempTargetName, data.data)
      .then(async(result) => {
        console.log("write file success", result);
        let params = {
          offset: { x: x, y: y },
          size: { width: cropSize, height: cropSize },
          displaySize: { width: 238, height: 238 }
        };
        console.log("crop params", params);
        Host.file
          .cropImage(targetName, tempTargetName, params)
          .then((res) => {
            console.log("is success", res);
            let filepath = `${ Host.file.storageBasePath }/${ targetName }`;

            props.navigation.navigate("AvatarDisplay", {
              type: "photo",
              // data: { filename: targetName },
              data: { filename: targetName, originFilename: tempTargetName },
              callback: (data) => {
                if (data) {
                  if (data.action == "cancel") {
                    selectPhotoTapped();
                  } else {
                    updateAvatar(data);
                  }
                }
              }
            });
          })
          .catch((error) => {
            console.log("crop avatar error1", error);
          });
      })
      .catch((err) => {
        console.log("write file error", err);
        Toast.fail("c_set_fail");
      });
  };

  return (
    <View>
      <AbstractDialog
        visible={showAvatarIntroDialog}
        useNewTheme={true}
        title={localStrings["edit_contact_avatar"]}
        dialogStyle={{
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        }}
        onDismiss={(_) => updateVisible(false)}
        buttons={[
          {
            text: localStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              updateVisible(false);
            }
          }
        ]}
      >
        <View
          style={{
            marginBottom: 16,
            flexDirection: "column",
            alignItems: "center"
          }}
        >
          <Image style={{ width: 72, height: 72, borderRadius: 36 }} source={icon != null ? { uri: icon } : require("../../Resources/Images/icon_user.png")} />
          <Text style={{ fontSize: 12, color: "rgba(0, 0, 0, 0.6)", marginTop: 6 }}>{localStrings["edit_contact_avatar_desc"]}</Text>
          <View style={{ width: "100%", marginTop: 26 }}>
            <TouchableHighlight
              style={{ marginHorizontal: 27, backgroundColor: "#32BAC0", justifyContent: "center", alignItems: "center", borderRadius: 23, height: 46 }}
              underlayColor={"#32BAC0CC"}
              onPress={() => {
                setShowAvatarContactDialog(true);
                updateVisible(false);
              }}
            >
              <Text style={{ fontSize: 16, color: "#FFFFFF", textAlign: "center" }}>{localStrings["upload_avatar"]}</Text>
            </TouchableHighlight>
          </View>
        </View>
      </AbstractDialog>
      <AbstractDialog
        visible={showAvatarContactDialog}
        useNewTheme={true}
        title={localStrings["edit_contact_avatar"]}
        dialogStyle={{
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        }}
        onDismiss={(_) => setShowAvatarContactDialog(false)}
        buttons={[
          {
            text: localStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              setShowAvatarContactDialog(false);
            }
          }
        ]}
      >
        <View
          style={{
            marginBottom: 16
          }}
        >
          {[
            {
              title: localStrings["takePhoto"],
              onPress: () => {
                setShowAvatarContactDialog(false);
                takePicture();
              }
            },
            {
              title: localStrings["select_dialog_album_v2"],
              redText: true,
              onPress: () => {
                setShowAvatarContactDialog(false);
                selectPicture();
              }
            }
          ].map((option, index) => (
            <View key={(option.title || "") + index}>
              <MIChoiceItem
                type={MIChoiceItem.TYPE.SINGLE}
                titleStyle={{
                  fontSize: 16,
                  fontWeight: "bold"
                }}
                title={option.title || ""}
                onPress={() => option.onPress()}
              />
            </View>
          ))}
        </View>
      </AbstractDialog>
      {renderPermissionDialog()}
    </View>
  );
};
