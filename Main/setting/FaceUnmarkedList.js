'use strict';
import React from 'react';
import { Dimensions, Image, ActivityIndicator, ScrollView, View, Text, TouchableOpacity, FlatList } from 'react-native';
import { Device, Service, DarkMode } from 'miot';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import NavigationBar from "miot/ui/NavigationBar";
import Util from "../util2/Util";
import InputDlgEx from '../widget/InputDlgEx';
import { ChangeType, buildChange, applyChange } from "../widget/EventList";
import Toast from '../components/Toast';
const WindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const WindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
import LogUtil from '../util/LogUtil';
export default class FaceUnmarkedList extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      showLoading: false,
      unmarkFacesImgList: [],
      commentDlg: false,
      isRefreshing: false,
      isNameError: false,
    };

  }
  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings["face_unmarked"],
      type: DarkMode.getColorScheme() == 'dark' ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack() }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.setState({
          isRefreshing: false
        })
        this.isPageForeGround = true;
        this._onGetData();
      }
    );
  }
  _onGetData() {
    let endTime = new Date().getTime()
    Util.getAllUnmarkFaces(endTime).then((res) => {
      
      this.setState({
        unmarkFacesImgList: res.faceList,
        showUnMarkedFaceListView: true,
        showLoading: false,
        isRefreshing: false,
        endTime: res.nextTime
      }, () => {
        this.getAllUnFaceurl(this.state.unmarkFacesImgList);
      })
      console.log('未备注的人脸', res[res.length - 1])
    }).catch((err) => {
      LogUtil.logOnAll("FaceUnmarkedList", "getAllUnmarkFaces failed:" + JSON.stringify(err));
      
      Toast.fail("c_get_fail", err);
    })
    //已备注的人脸 
    Util.getAllFigure().then((ret) => {
      this.mAllFigureInf = ret;
      this.setState({
        mAllFigureInf: ret
      }
      )
      // console.log(ret, 'ret')
    })
      .catch((err) => {
        LogUtil.logOnAll("FaceUnmarkedList", "getAllFigure failed: " + JSON.stringify(err));

        Toast.fail("c_get_fail", err);
      });

  }

  _onGetMoreData() {
    //要是一次最多拿50个 我就直接加在后面
    this.setState({
      showMoreLoading: true
    })
    Util.getAllUnmarkFaces(this.state.endTime).then((res) => {
      let list = this.state.unmarkFacesImgList
      let newList = list.concat(res.faceList.slice(0, -1))
      //当没有新的人脸时
      if (res.faceList.length == 0) {
        this.setState({
          showMoreLoading: false
        });
        return;
      }
      this.setState({
        unmarkFacesImgList: newList,
        endTime: res.faceList[res.faceList.length - 1].endTime,
        showUnMarkedFaceListView: true,
        showLoading: false,
        isRefreshing: false,
        showMoreLoading: false
      }, () => {
        this.getAllUnFaceurl(this.state.unmarkFacesImgList)
      })
    }).catch((err) => {
      LogUtil.logOnAll("FaceUnmarkedList", "getAllUnmarkFaces failed2" + JSON.stringify(err));
      this.setState({
        showMoreLoading: false
      });
      // Toast.fail("c_get_fail", err);
    })
  }
  getAllUnFaceurl(unmarkFacesImgList) {
    let lastNotifyTime = Date.now();
    for (let i = 0; i < unmarkFacesImgList.length; i++) {
      Util.getFaceImgUrl(unmarkFacesImgList[i].faceId).then((res) => {
        let path = res;
        unmarkFacesImgList[i].faceUrl = path;
        this.setState({
          unmarkFacesImgList: unmarkFacesImgList
        });

      }).catch((err) => {
        console.log('err', err);
      });
      if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
        continue;
      }
      lastNotifyTime = Date.now();
    }
    this.setState({
      unmarkFacesImgList: unmarkFacesImgList
    });
  }
  _onGetunmarkFaces(data) {
    data.map((item, index) => {
      Service.miotcamera.getFaceImgWithDid(item.faceId).
        then((res) => {
          let obj = {}
          obj.uri = res
          obj.faceId = item.faceId
          obj.updateTime = item.updateTime
          let list = this.state.unmarkFacesImgList
          //这里是加载人脸以及添加按钮 
          this.setState({
            unmarkFacesImgList: [...list, obj],
            showUnMarkedFaceListView: true,
            showLoading: false,

          })
        }).catch((err) => {
          Toast.fail("c_get_fail", err);
        })
    })
  }
  render() {
    return (
      <View
        style={{ width: '100%', flex: 1, backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff', }}
      >
        {this._renderUnmarkFacesImgListView()}
        {this._renderLoadingView()}
        {this.state.commentDlg ? this._renderCommentDlg() : null}
        {this._renderLoadMoreView()}                                
      </View>

    );
  }

  _renderUnmarkFacesImgListView() {
    if (this.state.showUnMarkedFaceListView) {
      return (

        <FlatList
          // Viewport={200}
          style={{ marginLeft: 16, flex: 1 }}
          data={this.state.unmarkFacesImgList}
          renderItem={({ item, index }) => this._renderUnmarkFacesView(item, index)}
          // initialNumToRender={10}
          numColumns={1}
          keyExtractor={(item, index) => index}
          // ListFooterComponent={<View style={{ height: 20 }}></View>}
          //设置一个尾部组件 就是最下面的那个留个空白
          //下拉刷新
          refreshing={this.state.isRefreshing}
          onRefresh={() => {
            this.setState({
              isRefreshing: true
            })
            this._onGetData(); //下拉刷新加载数据
          }}
          onEndReached={() => {
            this._onGetMoreData()
          }}
          onEndReachedThreshold={0.1}
          onScrollBeginDrag={() => {
            // console.log('onScrollBeginDrag');
          }}

          // maxToRenderPerBatch={10} // 增量渲染最大数量
          getItemLayout={(data, index) => (
            { length: WindowHeight / 9, offset: WindowHeight / 9 * index, index }
          )}
        />

      )
    }
  }
  _renderUnmarkFacesView(item, index) {

    return (
      <TouchableOpacity
        style={{ height: WindowHeight / 9, display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 20, }}
        //这里注意将marginbottom去掉 要不然会抖动
        onPress={() => {
          this._showCommentdlg(item)
        }}
        key={index}
      >
        <View style={{ display: "flex", flexDirection: 'row', justifyContent: 'flex-start', alignItems: "center" }}>

          <Image
            style={{ width: 50, height: 50, borderRadius: 25, marginRight: 10 }}
            source={item.faceUrl}
          >
          </Image>

        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: "center" }}>
          <TouchableOpacity
            style={{ width: 76, height: 28, borderRadius: 14, backgroundColor: '#f5f5f5', alignItems: 'center', justifyContent: 'center' }}
            onPress={() => { this._showCommentdlg(item) }}
          >
            <Text style={{ color: '#666666', fontSize: 12, }}>
              {LocalizedStrings["add_notes"]}
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    )
  }
  _renderLoadingView() {
    let isDark = DarkMode.getColorScheme() == "dark";
    if (this.state.showLoading) {
      return (
        <View
          style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <ActivityIndicator
            style={{ width: 54, height: 54 }}
            color={isDark ? "xm#ffffff" : "#000000"}
            size={"large"}

          />
          <Text
            style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
            {LocalizedStrings["camera_loading"]}
          </Text>
        </View>
      );
    }



  }
  _renderLoadMoreView() {
    if (this.state.showMoreLoading) {
      return (
        <View style={{width: "100%", flexDirection: "row", alignItems: "center", justifyContent: "center"}}>
          <ActivityIndicator
            style={styles.indicator}
            size={"small"}
            color={"#000000"}
            animating={true}
          />
          <Text>{LocalizedStrings["loading_more"]}</Text>
        </View>
      )
    }

  }


  _showCommentdlg(item) {
    this.setState({
      commentDlg: true,
      faceUrl: item.faceUrl.uri,
      faceId: item.fInf.faceId
    })

  }
  _renderCommentDlg() {
    let obj = {}
    obj.uri = this.state.faceUrl
    if (this.state.commentDlg)
      return (
        <InputDlgEx
          title={LocalizedStrings["cloud_comment_dlg_title"]}
          visible={this.state.commentDlg}
          icon={obj}
          listData={this.state.mAllFigureInf}
          onPressed={(aDat) => {
            this.setState({ defVal: aDat.name, isNameError: false });
          }}
          onDismiss={(_) => {
            this.renameItem = null;
            this.setState({ commentDlg: false, isRename: false, defVal: '', isNameError: false });
          }}
          inputWarnText={this.state.commentErr}
          noInputDisButton={ true }
          inputs={[{
            // onChangeText: (text) => {
            //   if (this.state.commentErr != null) {
            //     this.setState({ commentErr: null });
            //   }
            // },
            onChangeText: (result) => {
              let isEmoji = Util.containsEmoji(result);
              let length = result.length;
              // let isCommon = this.isTextcommon(result);
              if (isEmoji) {
                this.setState({ isNameError: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
              } else if (length > 8) {
                this.setState({ isNameError: true, commentErr: LocalizedStrings["input_name_too_long3"] });
              } else if (length <= 0 || result.trim().length == 0) {
                this.setState({ isNameError: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
              } else {
                this.setState({ isNameError: false, commentErr: "error" });
              }
            },
            textInputProps: {
              // maxLength: 8,
              // returnKeyType: "done",
              autoFocus: Util.isHeightPt() ? true : false
            },
            defaultValue: this.state.defVal,
            type: 'DELETE',
            isCorrect: !this.state.isNameError
          }]}
          buttons={[
            {
              text: LocalizedStrings["action_cancle"],
              callback: (_) => {
                this.renameItem = null;
                this.setState({ commentDlg: false, isRename: false, defVal: '', isNameError: false});
              }
              // ignore
            },
            {
              text: LocalizedStrings["csps_right"],
              callback: (result) => {
                //10个人物上限
                // if (this.state.mAllFigureInf.length == 10) {
                //   this.setState({
                //     commentDlg: false,
                //     defVal: ''
                //   }, () => {
                //     Toast.fail("figure_max_tips", '', true)
                //   });

                //   return
                // }
                if (this.state.isNameError) {
                  console.log("name input is not good!")
                  return;
                }
                let text = result.textInputArray[0].trim();

                if (text.length > 0 && !this.containsEmoji(text) ) {
                  let cmd = null;
                  //这个人有名字？

                  cmd = Util.commentFace(text, this.state.faceId);

                  if (cmd) {
                    cmd.then((aRet) => {
                      this._onGetData();
                      Toast.success('save_success');
                      this.setState({ commentDlg: false });
                      const { state } = this.props.navigation;
                      state.params.callback(
                        {
                          data: 'mark'
                        }
                      );
                      this.props.navigation.goBack()

                    })
                      .catch((aErr) => {
                        this.setState({ commentDlg: false });
                        let errCode = aErr.code;
                        // 400302 人物上限
                        let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                        let err = errMap[errCode] || "action_fail";
                        Toast.fail(err, false, true);
                        console.log(this.tag, "comment failed", aErr);
                      });
                  } else {
                    console.log(this.tag, "nothing changed");
                  }

                } else {
                  // Alert.alert(
                  //   LocalizedStrings["lowpower_leave_msg_cant_null"],
                  //   null,
                  //   [{ text: LocalizedStrings["owldiff_wifi_ok"] }]
                  // );
                  if (this.containsEmoji(text)) {
                    this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
                  }
                  else {
                    this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
                  }
                }
              }
            }
          ]}
        />);
  }
  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
      (codePoint == 0x9) ||
      (codePoint == 0xA) ||
      (codePoint == 0xD) ||
      ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
      ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
      ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }
  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      }
    }
    return false;
  }

}


