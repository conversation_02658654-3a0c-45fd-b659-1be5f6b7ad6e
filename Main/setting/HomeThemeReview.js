import { Platform, ScrollView, StyleSheet, Text, View, Dimensions, Image, TouchableOpacity, ActivityIndicator, SafeAreaView } from "react-native";
import React, { useEffect, useState } from "react";

import { DarkMode, Host, Service } from "miot";
import { localStrings as LocalizedStrings, localStrings } from "../MHLocalizableString";
import { NavigationBar} from "mhui-rn";
import { MessageDialog } from 'miot/ui/Dialog';

import Util from "../util2/Util";

import Toast from "../components/Toast";
import ImageViewer from "react-native-image-zoom-viewer";

const TAG = "HomeTheme";
const MaxWidth = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const MaxWidthIos = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width); // use this.winPortraitWidth instead

const kIsCN = Util.isLanguageCN();
const HomeThemeReview = ({ props }) => {
  const list = props.navigation.state.params.imageList;
  const callback = props.navigation.state.params.callback;
  const [imageShowList, setShowImageList] = useState(
    list?.map((element) => {
      return {
        ...element,
        url: `file://${Host.file.storageBasePath}/${element.fileName}`,
        width: kWindowWidth,
        height: kWindowWidth,
      };
    })
  );
  const [index, setIndex] = useState(props.navigation.state.params.index);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  useEffect(() => {
    props.navigation.setParams({
      // title: localStrings.home_theme,
      type: NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            props.navigation.goBack();
          },
        },
      ],
      title: imageShowList[index]?.fileName,
      titleStyle: {
        fontSize: 18,
        color: "#333333",
        fontWeight: 500,
      },
      backgroundColor: "#ffffff",
    });
  }, [index]);

  const imageChange = (index) => {
    setIndex(index);
  };
  const renderImage = (prop) => {
    return (
      <Image
        onError={({ nativeEvent: e }) => {
          console.log("onError1111", e);
        }}
        key={1}
        style={styles.image}
        resizeMode="cover"
        source={{ uri: prop.source.uri }}
      />
    );
  };

  const renderIndicator = (index, count) => {
    return null
    // return (
    //   <View style={styles.countStyle}>
    //     <Text style={styles.countTextStyle}>
    //       {index}/{count}
    //     </Text>
    //   </View>
    // );
  };

  const renderDialog = () => {
    return (
      <MessageDialog
        visible={deleteDialogVisible}
        message={LocalizedStrings['delete_title']}

        messageStyle={{ textAlign: 'center' }}
        canDismiss={true}
        onDismiss={() => {
          console.log("onDismiss");
          setDeleteDialogVisible(false);
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              console.log("onCancel");
              setDeleteDialogVisible(false);
            }
          },
          {
            text: LocalizedStrings["delete_confirm"],
            callback: (_) => {
              console.log("imageList[index].order",imageShowList[index].order);
              const deleteIndex = index;
              const deleteOrder = imageShowList[index].order;
              const temp = imageShowList;

              temp.splice(index, 1);

              if (temp.length > 0) {
                temp.sort((a, b) => b.order - a.order);
                const newList = temp?.map((item, index) => {
                  item.order = temp.length - index;
                  return item;
                });
                // 更新 imageShowList，确保包含正确的 url
                const newShowList = newList.map((element) => ({
                  ...element,
                  url: `file://${Host.file.storageBasePath}/${element.fileName}`,
                  width: kWindowWidth,
                  height: kWindowWidth
                }));
                newShowList.sort((a, b) => b.order - a.order);
                setShowImageList(newShowList);
                // 智能设置新的index
                let newIndex;
                if (deleteIndex === 0) {
                  // 如果删除第一张，，切换到最后一张
                  newIndex = imageShowList.length - 1;
                } else if (deleteIndex === imageShowList.length) {
                  // 如果删除最后一张，切换到新的最后一张
                  newIndex = imageShowList.length - 1;
                } else {
                  // 如果删除中间的，切换到删除位置的前一张
                  newIndex = deleteIndex - 1;
                }
                setIndex(newIndex);
                // 修改回调，传递更多信息
                callback({
                  deleteIndex: deleteIndex,
                  deleteOrder: deleteOrder
                });
              } else {
                callback({
                  deleteIndex: deleteIndex,
                  deleteOrder: deleteOrder
                });
                props.navigation.goBack();
              }
              setDeleteDialogVisible(false);
              Toast.fail("delete_success");
            }
          }
        ]}
      />
    );
  };
const isDark= DarkMode.getColorScheme() == "dark";
const bgColor = isDark ? "#000000" : '#ffffff';
  return (
    <View style={[styles.container, { backgroundColor: bgColor, paddingBottom: 40 }]}>
      {/* 顶部页码显示 */}
      <View style={styles.countStyle}>
        <Text style={styles.countTextStyle}>
          {index + 1}/{imageShowList.length}
        </Text>
      </View>

      {/* 图片预览区域 */}
      <View style={styles.imageContainer}>
        <ImageViewer
          style={{width: kWindowWidth, height: kWindowWidth}}
          backgroundColor={bgColor}
          imageUrls={imageShowList.sort((a, b) => b?.order - a?.order)}
          saveToLocalByLongPress={false}
          loadingRender={() => <ActivityIndicator size="large" color={bgColor} />}
          onChange={imageChange}
          index={index}
          renderImage={renderImage}
          renderIndicator={renderIndicator}
        />
      </View>

      {/* 底部删除按钮 */}
      <View style={[styles.delButton]}>
        <TouchableOpacity
          onPress={() => {
            setDeleteDialogVisible(true);
          }}
        >
          <Image style={{ width: 25, height: 25, alignSelf: 'center' }} source={!Util.isDark() ? require("../../Resources/Images/camera_icon_loc_pic_delete.png") : require("../../Resources/Images/camera_icon_loc_pic_delete_white.png")} />
          <Text style={{fontSize: 11, alignSelf: 'center', color: Util.isDark() ? "#ffffff" : "#333333"}}>{localStrings["delete_files"]}</Text>
        </TouchableOpacity>
      </View>

      {renderDialog()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  imageContainer: {
    width: kWindowWidth,
    height: kWindowWidth,
    alignSelf: 'center',
    marginTop: 'auto',
    marginBottom: 'auto',
  },
  image: {
    width: kWindowWidth,
    height: kWindowWidth,
  },

  countTextStyle: {
    color: "black",
    fontSize: kIsCN ? 16 : 14,
    backgroundColor: "transparent",
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: {
      width: 0,
      height: 0.5,
    },
    textShadowRadius: 0,
  },
  countStyle: {
    // position: 'absolute',
    // left: 0,
    // right: 0,
    // top: 38,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "transparent",
    backgroundColor: "#fff",
  },
  delButton: {
    alignItems: "center",
    zIndex: 11,
    justifyContent: "center"
  },
});

export default class HomeThemeReviewRender extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.props = props;
  }

  render() {
    return <HomeThemeReview props={this.props} />;
  }
}
