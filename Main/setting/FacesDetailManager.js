'use strict';
import React from 'react';
import { <PERSON><PERSON>, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, ActivityIndicator, Platform, PermissionsAndroid } from 'react-native';
import { Device, Service, DarkMode, System, Host } from 'miot';
import { InputDialog, MessageDialog } from "mhui-rn";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';
import Toast from '../components/Toast';
import { AbstractDialog, ChoiceDialog } from "miot/ui/Dialog";
import Util from "../util2/Util";
import ImagePicker, { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import StorageKeys from '../StorageKeys';
import { resolveObjectURL } from 'buffer';
import LogUtil from '../util/LogUtil';
import { NavigationBar } from 'mhui-rn';
import TabRouter from 'react-navigation/src/routers/TabRouter';
import CommonMsgDialog from "../ui/CommonMsgDialog";
import VersionUtil from '../util/VersionUtil';
export default class FacesDetailManager extends React.Component {
	state = {
		isSelectMode: false,
		index: 0,
		isEmpty: true,
		isCurrentDayEmpty: false,
		calendarDays: [],
		dialogVisible: false,
		albumFiles: [],
		showLoading: true,
		coverFaceInfosList: [],
		faceList: [],
		addMarkedFaceDialogVisible: false,
		isNameError: false,
		// figureInfos: []
		faceListLength:" "
	};
	constructor(props) {
		super(props);
		this.isAllDelete = false;
		this.dateTime = new Date();
		this.figureInfos = [];
		this.coverFaceInfosList = [];
	}

	dimensionListener(args) {
		if (Platform.OS === "ios") {
			console.log('why!, setDimensionsIos000: ', args);
			console.log('why!, Dimensions', Dimensions.get('window'));
			if (args && args.screen && args.window) {
				if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
					setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
					console.log('纠正========');
				}
			}
		}
	}
	//头部导航
	setNavigation(isSelectAll, isSelectMode, isDisableSelect, title) {
		if (Device.isReadonlyShared) {
			isDisableSelect = true;
		}
		this.props.navigation.setParams({
			title: isSelectMode ? title : LocalizedStrings["lowpower_face_manager"],
			left: [
				{
					key: isSelectMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
					onPress: () => {
						if (this.state.isSelectMode) {// 如果已经是选择模式，点左边 应该退出选择模式
							this.onSelectAllChanged(false);// 将isSelected重置
							this.setNavigation(false, false, false,);
							this.setState({ isSelectMode: false });
						} else { // 不是选择模式 就退出吧
							this.props.navigation.goBack();
						}
					}
				}
			],
			right: [
				{
					key: !isSelectMode ? NavigationBar.ICON.EDIT : (isSelectAll ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL),
					onPress: () => {
						if (!this.state.isSelectMode) { //进入选择模式
							this.setNavigation(false, true, false, LocalizedStrings["action_select"]);
							this.setState({ isSelectMode: true });
						} else if (this.state.isSelectAll) { //退出全选模式
							this.onSelectAllChanged(false);
						} else { //进入全选模式
							this.onSelectAllChanged(true);
						}
					}
				}
			],
			titleStyle: {
				fontSize: 18,
				color: '#333333',
				fontWeight: 500
			},
		});
	}

	componentDidMount() { // 第一次进来的时候这样子设计。
		self.windowWidth = Dimensions.get("window").width;
		self.windowHeight = Dimensions.get("window").height;
		if (self.windowHeight < self.windowWidth) {
			let sw = self.windowWidth;
			self.windowWidth = self.windowHeight;
			self.windowHeight = sw;
		}

		this.setNavigation(false, false, true);
		this.setState({ index: 1 });
		//这里是要判断返回的时候 是不是选择模式
		if (Platform.OS === "android") {
			BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
		}
		this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
			'didFocus',
			() => {
				this.isPageForeGround = true;
				this._onGetData();
			}
		);

		this.didBlurListener = this.props.navigation.addListener(
			'didBlur',
			() => {
				this.isPageForeGround = false;
			}
		);
		if (Platform.OS == "ios") {
			this._onGetData();
		}
	}
	//获取数据
	_onGetData() {
		let headerInfo = this.props.navigation.state.params.figureInfo;
		if (headerInfo == null){
			Toast.fail("c_get_fail");
			this.props.navigation.goBack();
			return ;
		}
		this.myCallback = this.props.navigation.state.params.callback
		console.log(this.props.navigation.state.params.figureInfo, "我是传过来的数据");

		this.setState({
			headerInfo: headerInfo,
			showFaceInfoMetaView: true
		})

		Util.getFaceAllFigure().then((ret) => {
			this.setState({
				mAllFigureInf: ret,
				showLoading: false,
			}, () => {
				this.getHeaderImgUri(this.state.mAllFigureInf, this.state.headerInfo);

			})
		})
			.catch((err) => {
				LogUtil.logOnAll("FacesDetailManager", "getAllFigure failed" + JSON.stringify(err));
				Toast.fail("c_get_fail", err);
			});
		Util.getExistFigure(headerInfo.name).then((res) => {
			console.log(res,"根据名字获取人物的figureid");
			this.setState({
				figureId: res
			})
			//获取回来figureid下的所有的人脸
			Util.getFigureFaces(res).then((res) => { 
			 console.log(res,"获取回来figureid下的所有的人脸");
				this.setState({
					faceList: [...res, { faceUrl: false }],
					showLoading: false,
					faceListLength: res.length
				}, () => {
					this.getAllFaceurl(this.state.faceList)
				})
				this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);
			}).catch((err) => {
				LogUtil.logOnAll("FacesDetailManager", "getFigureFaces failed" + JSON.stringify(err));
				Toast.fail("face_recognition_fail_tips", err);
			})
		}).catch((err) => {
			LogUtil.logOnAll("FacesDetailManager", "getExistFigure failed" + JSON.stringify(err));
			Toast.fail("face_recognition_fail_tips", err);
		})
	}
	getHeaderImgUri(mAllFigureInf, headerInfo) {
		let headerImgUri = mAllFigureInf.filter((item) => {
			return item.name == headerInfo.name
		})
		if (!headerImgUri || headerImgUri.length <= 0) {
			Toast._showToast("人物不存在或已被删除");
			this.props.navigation.navigate('FaceManager2');
			return;
		}
		Util.getFaceImgUrl(headerImgUri[0].faceId).then((res) => {
			let path = res;
			this.setState({
				headerImgUri: path
			})
		}).catch((err) => {
			console.log('err', err);
		});

	}
	getAllFaceurl(faceList) {
		let lastNotifyTime = Date.now();
		for (let i = 0; i < faceList.length - 1; i++) {
			Util.getFaceImgUrl(faceList[i].faceId).then((res) => {
				let path = res;
				faceList[i].faceUrl = path;
				this.setState({
					faceList: faceList
				});

			}).catch((err) => {
				console.log('err', err);
			});
			if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
				continue;
			}
			lastNotifyTime = Date.now();
		}
		this.setState({
			faceList: faceList
		});
	}
	componentWillUnmount() {
		// Dimensions.removeEventListener('change', this.dimensionListener);
		this.didFocusListener.remove();
		this.didBlurListener.remove();
		if (Platform.OS === "android") {
			BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
		}
	}
	//渲染
	render() {
		return (
			<View style={styles.container}>
				{/* {this._renderLoadingView()} */}
				{this._renderFaceInfoMetaView()}
				{this._renderBottomSelectView()}
				{this._renderDialog()}
				{this._renderCommentDlg()}
				{this._renderChoiceAddMarkedFaceWayDialog()}
				{this._renderTipsDialogView()}
				{this._renderPermissionDialog()}
			</View>
		);
	}
	//头部列表
	_renderFaceInfoMetaView() {
		if (this.state.showFaceInfoMetaView) {
			return (
				<View style={{ width: '100%', flex: 1 }}>
					<View style={{ display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 26, marginBottom: 10 }}>
						<View style={{ display: "flex", flexDirection: 'row', justifyContent: 'flex-start', alignItems: "center", marginTop: 10 }}>
							<View>
								<Image
									style={{ width: 50, height: 50, borderRadius: 25, marginRight: 10, backgroundColor: '#EEEEEE' }}
									source={this.state.headerImgUri}
								>
								</Image>
							</View>
							<View style={{ display: "flex", flexDirection: 'column', justifyContent: 'flex-start' }}>
								<Text style={{ fontSize: 16 }}>
									{this.state.headerInfo.name}
								</Text>
								<Text style={{ fontSize: 12, color: '#999999' }}>
									{this.state.faceList.length > 1 ? LocalizedStrings["figure_face_count_tips"].replace("%d", this.state.faceList.length - 1):''}
									
								</Text>
							</View>
						</View>
						<TouchableOpacity
							style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: "center" }}
							onPress={() => {
								this.setState({
									commentDlg: true
								})
							}}
						>
							<Text style={{ fontSize: 12, color: '#999999' }}>
								{LocalizedStrings["modify_notes"]}
							</Text>
							<Image
								style={{ width: 20, height: 40 }}
								source={require('../../Resources/Images/button_next_nor.png')}
							/>
						</TouchableOpacity>
					</View>

					<View style={styles.whiteblank}>

					</View>
					{this._renderFacesInfoListView()}
				</View>
			)
		}
	}
	//下部列表渲染
	_renderFacesInfoListView() {
		return (
			<View style={{ flex: 1, marginHorizontal: 16 }}>
				<FlatList
					style={{}}
					data={this.state.faceList}
					renderItem={({ item, index }) => this._renderFacesInfoView(item, index)}
					numColumns={3}
					keyExtractor={(item, index) => index}
					ListFooterComponent={<View style={{ height: 20 }}></View>}
				//设置一个尾部组件 就是最下面的那个留个空白
				/>
			</View>
		)
	}
	_renderFacesInfoView(item, index) {
		let path = null
		path = item.faceUrl;
		let containerWidth = Dimensions.get("window").width / 3 - 10
		return (
			<View >
				<TouchableOpacity
					style={{ width: containerWidth, height: containerWidth, paddingBottom: 10, alignItems: "center", justifyContent: "center" }}
					onPress={() => this._onPressFaceImg(index)}
					onLongPress={() => {
						this._onPressFaceImg(index, true)
					}}
				>
					{/* 这个小图片是选中的时候*/}
					{index !== this.state.faceList.length - 1 ? 
            <Image style={{ width: 94, height: 94, borderRadius: 10, backgroundColor: '#EEEEEE' }}
              source={path ? path : null}/>
            :
            <View style={{ width: 94, height: 94, borderRadius: 10, backgroundColor: '#EEEEEE', alignItems: "center", justifyContent: "center" }}>
              <Image style={{ width: 40, height: 40, borderRadius: 10, backgroundColor: '#EEEEEE' }}
                source={require('../../Resources/Images/home_icon_add2_pres.png')}/>
            </View>
          }

					{
						this.state.isSelectMode && index !== this.state.faceList.length - 1 ?
							<Image
								style={{ width: 20, height: 20, position: "absolute", bottom: 25, right: (containerWidth - 94) / 2 + 5 }}
								source={item.isSelected ? require("../../Resources/Images/icon_selected.png") : require("../../Resources/Images/icon_unselected.png")}
							/> :
							null
					}
				</TouchableOpacity>
			</View>
		)
	}
	
	_onPressFaceImg(index, isLongPress) {
		console.log(index, 'index')

		if (isLongPress) {
			this.setState({
				isSelectMode: true
			})
		}
		if (index == 10) {
			Toast.fail("face_max_tips", '', true)
			return
		}
		if (index == this.state.faceList.length - 1) {
			for (let file of this.state.faceList) {
				file.isSelected = false
			}
			this.setState({
				isSelectMode: false,
				dialogVisible: false,
				addMarkedFaceDialogVisible: true

			})
			this.setNavigation(false, false, false);

			return

		}
		//这里是点一下就刷新一下
		//这里是没有点击选择 就直接跳到相册详情页
		if (!this.state.isSelectMode) {

			if (index == this.state.faceList.length - 1) {
				this.setNavigation(false, false, false);
				this.setState({
					addMarkedFaceDialogVisible: true
				})
			}
		}
		else {

			let faceList = this.state.faceList[index];
			faceList.isSelected = !faceList.isSelected;
			let selectedCount = 0;
			for (let file of this.state.faceList) {
				if (file.isSelected) {
					selectedCount++;
				}
			}

			// 在这里重新设置标题栏 
			this.setNavigation(false, true, false, LocalizedStrings["selected_count"].replace("%1$d", selectedCount))
			if (selectedCount == 0) {
				this.onSelectAllChanged(false);
			} else if (selectedCount == this.state.faceList.length - 1) {
				this.onSelectAllChanged(true);
			} else {
				this.setState({ faceList: this.state.faceList });// 刷新页面 状态不要保留在ui控件里
			}
		}


	}

	_renderDialog() {
		let message = this.isallDelete ? LocalizedStrings["face_delete_all_faces_message"] : LocalizedStrings["delete_title"];
		let title = LocalizedStrings["face_delete_dialog_title"]
		let btn = LocalizedStrings["csps_right"];
		return (
			<AbstractDialog
				visible={this.state.dialogVisible}
				cancelable={true}
				title={message}
				// message={message}
        showSubtitle={false}
        useNewTheme
        canDismiss={true}
				buttons={[
					{
						text: LocalizedStrings["action_cancle"],
						callback: () => {
							this.setState({ dialogVisible: false });
						}
					},
					{
						text: btn,
						callback: () => {
							this.onConfirmDelete();
							this.setState({ dialogVisible: false });
						}
					}
				]}
				onDismiss={() => {
					this.setState({ dialogVisible: false });
				}}
      ><View></View></AbstractDialog>
		);
	}
	//加载效果
	_renderLoadingView() {
		let isDark = DarkMode.getColorScheme() == "dark";
		if (this.state.showLoading) {
			return (
				<View
					style={{ width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
				>
					<ActivityIndicator
						style={{ width: 54, height: 54 }}
						color={isDark ? "xm#ffffff" : "#000000"}
						size={"large"}

					/>
					<Text
						style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
						{LocalizedStrings["camera_loading"]}
					</Text>
				</View>
			);
		}
	}
	//点击备注后的弹窗
	_renderCommentDlg() {

		return (
			<InputDialog
				title={LocalizedStrings["face_mark_dialog_title"]} //"face_mark_dialog_title": "备注名称",
				singleLine={true}
				onDismiss={() => {
					this.renameItem = null;
					this.setState({ commentDlg: false, isRename: false, isNameError: false });
				}}
				visible={this.state.commentDlg}

				inputs={[{
					// onChangeText: (text) => {
					// 	if (this.state.commentErr != null) {
					// 		this.setState({ commentErr: null });
					// 	}
					// },
					textInputProps: {
						// maxLength: 8,
						// returnKeyType: "done",
						// autoFocus: Util.isHeightPt() ? true : false
						autoFocus: true
					},
					onChangeText: (result) => {
						let isEmoji = Util.containsEmoji(result);
						let length = result.length;
						// let isCommon = this.isTextcommon(result);
						if (isEmoji) {
							this.setState({ isNameError: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
						} else if (length > 8) {
							this.setState({ isNameError: true, commentErr: LocalizedStrings["input_name_too_long3"] });
						} else if (length <= 0 || result.trim().length == 0) {
							this.setState({ isNameError: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
						} else {
							this.setState({ isNameError: false, commentErr: "error" });
						}
					},
					defaultValue: this.state.headerInfo && this.state.headerInfo.name ? this.state.headerInfo.name : '' ,
					type: 'DELETE',
					isCorrect: !this.state.isNameError
				}]}

				buttons={[
					{
						text: LocalizedStrings["action_cancle"],
						callback: (_) => {
							this.renameItem = null;
							this.setState({ commentDlg: false, isRename: false, isNameError: false });
						}
					},
					{
						text: LocalizedStrings["csps_right"],
						callback: (result) => {
							if (this.state.isNameError) {
								console.log("name input is not good!")
								return;
							}
							let text = result.textInputArray[0].trim();
							if (text.length > 0 && !this.containsEmoji(text)) {
								let cmd = null;
								if (this.state.headerInfo.name != null) {
									if (this.state.headerInfo.name != text) {
										//这里要修改所有照片的备注
										cmd = Util.modifyAllFaceComment(this.state.faceList[0].fInf.figureId, text);
									}
								}

								if (cmd) {
									cmd.then(() => {
										//这里这样没有更新所有
										let headerInfo = this.state.headerInfo
										headerInfo.name = text
										this.setState({
											commentDlg: false,
											headerInfo: headerInfo
										})
										//成功了之后刷新
										this.myCallback({...this.state.headerInfo, forceUpdate: true})
									})
										.catch((aErr) => {
											LogUtil.logOnAll("FacesDetailManager", "modifyAllFaceComment failed" + JSON.stringify(aErr));
											// this.setState({ commentDlg: false });
											let errCode = aErr.code;
											// 400302 人物上限
											// 400302 人物上限
											//400303 人物名称已经存在
											let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips", 400303: "add_modify_feature_exist" };
											let err = errMap[errCode] || "face_recognition_fail_tips";
											// Toast.fail(err, err);
											this.setState({ commentErr: LocalizedStrings["add_modify_feature_exist"] })
											console.log(this.tag, "comment failed", aErr);
										});
								} else {
									console.log(this.tag, "nothing changed");
									this.setState({ commentErr:LocalizedStrings["add_modify_feature_exist"]});
								}
							} else {
								if (this.containsEmoji(text)) {
									this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
								}
								else {
									this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
								}
							}
						}
					}
				]}
				inputWarnText={this.state.commentErr}
				noInputDisButton={ true }
			/>
		);

	}
	//底部删除弹框
	_renderBottomSelectView() {
		if (!this.state.isSelectMode) {
			return;
		}
		return (
			<View style={{
				width: "100%",
				height: 69,
				bottom: 10,
				borderBottomColor: DarkMode.getColorScheme() == 'dark' ? '#000' : '#ffffff',
				borderTopColor: "#e5e5e5",
				borderTopWidth: 1,
				borderBottomWidth: 1,
				display: "flex",
				flexDirection: "row",
				justifyContent: "center",
				alignItems: "center",
				backgroundColor: DarkMode.getColorScheme() == 'dark' ? '#000' : '#ffffff'
			}}>

				<TouchableOpacity
					style={{ width: 50, display: "flex", flex: 1, alignItems: "center", }}
					onPress={() => { this.onPressDelete(); }}
				>
					<Image
						style={{ width: 25, height: 25 }}
						source={Util.isDark() ? require("../../resources2/images/icon_videorecord_delete_w.png") : require("../../resources2/images/icon_videorecord_delete_b.png")}
						 />
					<Text
						style={{ color: "#000000", fontSize: 11 }}
					>
						{LocalizedStrings["delete_files"]}
					</Text>
				</TouchableOpacity>
			</View>
		);
	}
	_renderTipsDialogView() {

		return (
			<AbstractDialog
				visible={this.state.showTips}
				useNewTheme
				onDismiss={() => { this.setState({ showTips: false, }) }}
				buttons={[
					{
						text: LocalizedStrings["csps_right"],
						style: { color: '#f0ac3d' },
						callback: (_) => {
							this.setState({
								showTips: false,
							}, () => [
								this.selectPhotoTapped()
							]);

							StorageKeys.IS_AI_FACE_OPEN_TOAST = true
							StorageKeys.IS_AI_FACE_OPEN_TOAST = true
						}
					}
				]}
			>
				<View
					style={{
						flex: 1,
						flexDirection: "column",
						// height: 200,
						alignItems: 'center',
						justifyContent: 'center',
					}}
				>
					<View>
						<Image style={{ width: 280, height: 200 }} source={require('../../Resources/Images/photo_placeholder.webp')}>
						</Image>
					</View>
					<View style={{ marginVertical: 5, textAlign: 'center', marginHorizontal: (Dimensions.get("window").width - 280) / 2 }}>
						<Text style={{ fontSize: 12, color: '#000000' }}>
							{LocalizedStrings["pick_album_tips"]}
						</Text>
					</View>

				</View>
			</AbstractDialog>
		)
	}
	// android返回键处理
	onBackHandler = () => {
		if (!this.isPageForeGround) {
			return false;
		}
		if (this.state.isSelectMode) {
			this.onSelectAllChanged(false);
			this.setNavigation(false, false, false);
			this.setState({ isSelectMode: false });
			return true;
		} else {
			return false;// 不接管
		}
	}


	//选择了所有
	onSelectAllChanged(isSelectAll) {

		this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
		this.setState({ index: isSelectAll ? 0 : 1 });
		for (let timeHourItem of this.state.faceList) {
			timeHourItem.faceUrl ? timeHourItem.isSelected = isSelectAll ? true : false : timeHourItem.isSelected = false

		}
		this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty, isSelectAll ? LocalizedStrings["selected_count"].replace("%1$d", this.state.faceList.length - 1) : LocalizedStrings["action_select"]);
		this.setState({ faceList: this.state.faceList, isSelectAll: isSelectAll });
	}


	onPressDelete() {
		this.isallDelete = false
		let ids = [];
		for (let faceId of this.state.faceList) { // 遍历所有的正在展示的内容
			if (faceId.isSelected) {
				ids.push(faceId.fInf.faceId);
			}
		}

		if (ids.length == this.state.faceListLength || this.state.faceList.length == 2 ) {
			this.isallDelete = true;
		}
		if (ids.length == 0) {
			Toast._showToast(LocalizedStrings["bottom_action_tip"]); //"请选择需要操作的文件",
			return;
		}
		this.setState({ dialogVisible: true });

		// if (Platform.OS == "android") {
		//     //这里是判断一张都没有选择
		// } else {
		//     //这里要注意苹果的删除
		//     this.setState({ dialogVisible: true });

		//     // this.onConfirmDelete();
		// }
	}
	onConfirmDelete() {
		//这里判断是删除一张还是所有 所有的话还要把这个人物删除
		let ids = [];
		for (let faceId of this.state.faceList) { // 遍历所有的正在展示的内容
			if (faceId.isSelected) {
				ids.push(faceId.fInf.faceId);
			}
		}
		Util.delFaces(ids, this.state.faceList[0].fInf.figureId)
			.then((res) => {
				if ("ok" == res.result.toLowerCase()) {
					this.setState((state) => {
						return {
							isSelectMode: false,
							isSelectAll: false
						};
					}, () => {
						//删除了所有之后返回
						if (ids.length == this.state.faceListLength || this.state.faceList.length == 2) {
							console.log(Device.model,"这是相机的型号哟");
							if (VersionUtil.isAiCameraModel(Device.model)) {
								const { state } = this.props.navigation;
						        this.myCallback({name: false, forceUpdate: true})
							    this.props.navigation.navigate('FaceManager2')
							} else {
								this.props.navigation.goBack()
							}
						}
						else {
							this._onGetData()
							this.myCallback({...this.state.headerInfo, forceUpdate: true})
						}

					});
				}

				Toast.success("delete_success");
			})
			.catch((err) => {
				LogUtil.logOnAll("FacesDetailManager", "delFaces failed" + JSON.stringify(err));
				console.log('err', err)
				Toast.fail("delete_failed", err);
			});

	}
	_renderChoiceAddMarkedFaceWayDialog() {

		return (
			<ChoiceDialog
				style={{ width: 100 }}
				dialogStyle={{ itemTitleNumberOfLines: 3 }}
				useNewType={false}
				visible={this.state.addMarkedFaceDialogVisible}
				title={LocalizedStrings["select_dialog_title"]}
				options={[
					{ title: LocalizedStrings["select_dialog_camera"] },
					{ title: LocalizedStrings["select_dialog_album"] },
					{ title: LocalizedStrings["action_cancle"] }
				]}
				selectedIndexArray={[0]}
				onDismiss={(_) => this.setState({ addMarkedFaceDialogVisible: false })}
				onSelect={(result) => {
					this.selectedIndexArray = result;
					this._choiceAddMarkedFaceWay(result);
					// this.setState({ addMarkedFaceDialogVisible: false });
				}}
				buttons={[
				]}
			/>
		);
	}
	_choiceAddMarkedFaceWay(result) {
		if (result == 0) {
			this._startSelectPhoto(result);
		}
		else if (result == 1) {
			this._startSelectPhoto(result);
		}
		else if (result == 2) {
			this.setState({
				addMarkedFaceDialogVisible: false
			})
		}
		//这里的逻辑是我给服务器上传了一个人脸照片  然后把这个照片加到这个人上 所以在face'camera就要调用添加人脸到人的这个操作
		//从手机相册选择也是这种原理
		//这里还有一个路由管理哦 拍照录入人脸的时候是 就是把人的信息传到facecamera这一页然后直接加进去 返回来就是刷新数据

		//0 是拍照录入 1 是从手机相册选择  2 是取消 取消这个应该不用弄

	}
	_startSelectPhoto(result) {
		let permissionAndoid = null, permissionResult = result, permissionIOS
		if (result == 0) {
			permissionAndoid = PermissionsAndroid.PERMISSIONS.CAMERA;
			permissionIOS = 'camera';
		}
		else if (result == 1) {
			permissionAndoid = PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE;
			permissionIOS = 'photos';
		}
		if (Platform.OS === "android") {
			console.log('Platform.OS === "android"')
			this.isCheckingPermission = true;
			PermissionsAndroid.request(permissionAndoid, null)
				.then((granted) => {
					this.isCheckingPermission = false;
					console.log("granted", granted);
					if (granted === PermissionsAndroid.RESULTS.GRANTED) {
						if (permissionResult == 0) {
							this.props.navigation.navigate('FaceCamera'
								, {
									figureId: this.state.figureId,
									callback: (data) => {
										// console.log('datashuaxxin')
										//这里刷新 但是头部没有刷新
										this._onGetData()
									}
								}
							)
						}
						else if (permissionResult == 1) {
							StorageKeys.IS_AI_FACE_OPEN_TOAST
								.then((result) => {
									// let ret = `$(JSON.stringify(ret))`
									// 当前的设备和用户和是否开启
									if (result == true) {
										this.setState({ showTips: false });
										this.selectPhotoTapped()
									}
									else {
										this.setState({
											showTips: true
										})
										return;
									}

								})
								.catch(err => {
									console.log('errr', err)
									this.setState({ showTips: true, });
								})
						}
					} else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
						// Toast.success("camera_no_write_permission");
						this.setState({ showPermissionDialog: true, permissionRequestState: permissionResult, });
					} else {
						Toast.success("please_open_camera");
					}
				}).catch((error) => {
					this.isCheckingPermission = false;
					Toast.success("action_failed");
				});
		} else {
			// no ios's photos const use hardcode

			System.permission.request(permissionIOS).then((res) => {
				console.log('res', res)
				if (permissionResult == 0) {
					this.props.navigation.navigate('FaceCamera'
						, {
							figureId: this.state.figureId,
							callback: (data) => {
								// console.log('datashuaxxin')
								//这里刷新 但是头部没有刷新
								this._onGetData()
							}
						}
					)
				}
				else if (permissionResult == 1) {
					StorageKeys.IS_AI_FACE_OPEN_TOAST
						.then((result) => {
							// let ret = `$(JSON.stringify(ret))`
							console.log('result', result)
							// 当前的设备和用户和是否开启
							if (result == true) {
								this.setState({ showTips: false });
								this.selectPhotoTapped();
							}
							else {
								this.setState({
									showTips: true
								})
								return;
							}
						})
						.catch(err => {
							console.log('errr', err)
							this.setState({ showTips: true, });
						})
				}

			}).catch((error) => {
				console.log('error', error)
				// Toast.success("camera_no_write_permission");
				this.setState({ showPermissionDialog: true, permissionRequestState: permissionResult });
			});

		}
	}
	selectPhotoTapped() {
		const options = {
			quality: 1.0,
			maxWidth: 500,
			maxHeight: 500,
			storageOptions: {
				skipBackup: true
			}
		};


		setTimeout(() => {
			ImagePicker.launchImageLibrary(options, (response) => {
				// this.setState({
				//     showLoading: true
				// })
				// console.log(response, 'response')
				if (response.didCancel) {
					console.log('User cancelled photo picker');
					// Toast.fail("bind_error")
					return;
				}
				else if (response.error) {
					console.log('ImagePicker Error: ', response.error);
					Toast.fail("bind_error")
					return;
				}
				else if (response.customButton) {
					console.log('User tapped custom button: ', response.customButton);
					Toast.fail("bind_error")
					return;
				}
				else {

					Toast.loading('c_setting');
					Service.miotcamera.uploadImageToCameraServer(response.uri.slice(7))
						.then((result) => {
							let res = JSON.parse(result);
							LogUtil.logOnAll("FacesDetailManager", "uploadImageToCameraServer success" + JSON.stringify(res));
							//{"code": 0, "data": {"faceCount": 0, "faceInfoMetas": []}, "description": "成功", "result": "ok", "retriable": false, "ts": 1639896147542}
							if (res.result == "ok") {
								let data = res.data;
								if (data != null && data.faceInfoMetas != null && data.faceInfoMetas[0] != null) {
									//  这里就是给这个图片添加备注 然后这个备注我也知道了,没有用到添加人脸到人物
									console.log('data', data.faceInfoMetas[0].faceId)

									let cmd = Util.commentFace(this.state.headerInfo.name, data.faceInfoMetas[0].faceId);
									if (cmd) {
										cmd.then(() => {
											this._onGetData()
										})
											.catch((aErr) => {
												console.log('aerrrrr', aErr)
												this.setState({ commentDlg: false });
												let errCode = aErr.code;
												// 400302 人物上限
												let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
												let err = errMap[errCode] || "action_failed";
												Toast.fail(err, err, true);
											});
									} else {
										Toast.fail("face_recognition_fail_tips")
									}

								}
								else {
									Toast.fail("face_recognition_fail_tips")
								}

							}
							else {
								Toast.fail("face_recognition_fail_tips")
							}

						}).catch((err) => {
							LogUtil.logOnAll("FacesDetailManager", "uploadImageToCameraServer failed" + JSON.stringify(err));
							Toast.fail("face_recognition_fail_tips", err)
							console.log('err', err)
						})

				}

			})
		}, 100)


	}

	isEmojiCharacterV2(codePoint) {
		return !((codePoint == 0x0) ||
			(codePoint == 0x9) ||
			(codePoint == 0xA) ||
			(codePoint == 0xD) ||
			((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
			((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
			((codePoint >= 0x10000))) ||
			(codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
				codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
				codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
			|| ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
			|| ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
			|| ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
	}
	containsEmoji(str) {
		let length = str.length;
		for (let i = 0; i < length; ++i) {
			let c = str.charCodeAt(i);
			if (this.isEmojiCharacterV2(c)) {
				return true;
			}
		}
		return false;
	}
	_renderPermissionDialog() {
		if (!this.state.showPermissionDialog) {
			return null;
		}
		let message = null;
		if (this.state.permissionRequestState == 1) {
			message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
		} else {
			message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
		}
		return (
			// <AbstractDialog

			<MessageDialog
				title={LocalizedStrings["tips"]}
				message={message}
				messageStyle={{
					fontSize: 14,
			}}
				buttons={[
					{
						text: LocalizedStrings["action_cancle"],
						callback: () => {
							this.setState({ showPermissionDialog: false });
						}
					},
					{
						text: LocalizedStrings["setting"],
						callback: () => {
							Host.ui.openTerminalDeviceSettingPage(1);
							this.setState({ showPermissionDialog: false });
						}
					}
				]}
				onDismiss={() => {
					this.setState({ showPermissionDialog: false });
				}}
				visible={this.state.showPermissionDialog} />
		);

	}
}

export const styles = StyleSheet.create({
	container: {
		backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : 'xm#fff',
		width: '100%',
		height: '100%',
		display: "flex",
		flexWrap: "nowrap"
	},
	whiteblank: {
		height: 0.5,
		marginHorizontal: 24,
		backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#ffffff15" : "xm#e5e5e5",
		marginBottom: 30,
		marginTop: 20,
	}
});
