import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity, Dimensions
} from 'react-native';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import { Device } from 'miot/device';
import { AbstractDialog, MessageDialog } from 'miot/ui/Dialog';
import Util from "../util2/Util";
import AlarmUtilV2, {
  KEY_ALARM_REMIND_LIST_PIID,
  KEY_ALARM_REMIND_SIID
} from "../util/AlarmUtilV2";
import StatusBarUtil from "../util/StatusBarUtil";
import dayjs from "dayjs";
import { DeviceEvent, Service } from "miot";
import RPC from "../util/RPC";
import LogUtil from "../util/LogUtil";
import CameraPlayer from '../util/CameraPlayer';
import XiaoaiTTSUtil from "../util/XiaoaiTTSUtil";

const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
export default class ClockAlarmPage extends React.Component {
  static navigationOptions = (navigation) => {
    return {// 不要导航条
      headerTransparent: true,
      header:
        null
    };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      listDatas: [],
      alarmValues: {},
      showDelDialog: false,
      editMode: false,
      showBarTitle: false,
      isAllSelect: false,
      showEmpty: false,
      showMoreDlg: false,
      moreIconY: -1,
      disabledAdd: false
    };
    this.statusBarTop = StatusBarUtil._getInset("top");
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.isPageForeGround = true;
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
    this.keys = [];
    this.LONG_TIME_KEY_PREFIX = "prop.s_chuangmi_clocks";
    this.selectCount = 0;
    this.deleteArray = [];
    this.deleteAlarmKeys = {};
    this.titleHeight = 38;
    this.mDate = new Date();
  }

  renderTitleBar() {
    let titleStr = this.state.editMode ? LocalizedStrings['edit'] : LocalizedStrings['clock_alarm'];
    // let rightKey = (!this.state.listDatas || this.state.listDatas.length === 0) ?
    //   null : this.state.editMode ?
    //     this.state.isAllSelect ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL
    //     : NavigationBar.ICON.MORE;
    let rightKey = this.state.editMode ?
      this.state.isAllSelect ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL
      : NavigationBar.ICON.MORE;
    let titleBarContent = {
      title: this.state.showBarTitle ? titleStr : " ",
      type: this.state.darkMode ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: this.state.editMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.editMode) {
              this.state.listDatas.forEach((item) => item.select = false);
              this.selectCount = 0;
              this.setState({ editMode: false, isAllSelect: false });
            } else {
              this.props.navigation.goBack();
            }
          }
        }
      ],
      right: [
        {
          // key: (!this.state.listDatas || this.state.listDatas.length === 0) ? null : this.state.editMode ? null : NavigationBar.ICON.EDIT,
          key: rightKey,
          onPress: () => {
            // if (this.selectCount >= this.state.listDatas.length) {
            //   this.state.listDatas.forEach((item) => item.select = false);
            //   this.selectCount = 0;
            // } else {
            //   this.state.listDatas.forEach((item) => item.select = true);
            //   this.selectCount = this.state.listDatas.length;
            // }

            // this.setState({ editMode: !this.state.editMode });

            if (this.state.editMode) {
              if (this.selectCount >= this.state.listDatas.length) {
                // this.state.listDatas.forEach((item) => item.select = false);
                this.selectCount = 0;
                this.setState({
                  listDatas: this.state.listDatas.map((item, _index) => {
                    return { ...item, select: false };
                  }),
                  isAllSelect: false
                });
              } else {
                // 弹出弹框
                this.selectCount = this.state.listDatas.length;
                this.setState({
                  listDatas: this.state.listDatas.map((item, _index) => {
                    return { ...item, select: true };
                  }),
                  isAllSelect: true
                });
              }
            } else {
              // this.selectCount = 0;
              // this.setState({ editMode: true });
              this.setState({ showMoreDlg: true });

            }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        // fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    // 设置tts鉴权信息
    this.setXiaoaiTTS();
    this.loadData();
    CameraPlayer.getInstance().bindAlarmPropCallback(this._alarmUpdate);
    // this._deviceStatusListener = DeviceEvent.deviceReceivedMessages
    //   .addListener(
    //     (device, map, res) => {
    //       if (res) {
    //         let key = res[0].key;
    //         let value = res[0].value[0];
    //         if (key == "prop.27.1") {
    //           console.log("alarmCallback", this.alarmCallback);
    //           this._alarmUpdate(value)
    //         }
    //       }
    //     });
  }

  componentWillUnmount() {
    //this._deviceStatusListener && this._deviceStatusListener.remove();
      CameraPlayer.getInstance().bindAlarmPropCallback(null);
  }
  // 监听别的手机是否增加闹铃
  _alarmUpdate=(value)=> {
    
    if (typeof (value) != "undefined" && value != "") {
       let values = JSON.parse(value);


      this.setState({ listDatas: values }, () => {
      
        // 判断那些数据没有audio_id ,补传音频数据
        let needUpload = false;
        let array = [];
        let needAudioArray = [];
        this.state.listDatas.forEach((item) => {
          if (item.audio_id == null && XiaoaiTTSUtil.isSupportedAlarmTts()) {
            needUpload = true;
            if (item.noteType === 0) {
              item.audio_id = -1;
            } else {
              item.audio_id = this.getUniqueAudioId();
              needAudioArray.push(item);
            }
          }
          array.push({
            enable: item.enable,
            time: item.time,
            type: item.type,
            repeat: item.repeat,
            noteType: item.noteType,
            notes: item.notes,
            audio_id: item.audio_id
          });
        });
        if (needUpload) {
          let arrayStr = JSON.stringify(array);
          console.log(`needUpload ${arrayStr}`);

          this.putClockList(arrayStr).then((res) => {
            if (res[0].code === 0) {
              // 补传音频
              needAudioArray.forEach((data) => {
                let note = this.getNotesStr(data);
                console.log(`正在补传tts ${note}`);
                this.uploadXiaoaiTTS(note, data.audio_id);
              });

            } else {

            }
          }).catch((err) => {

          });
        }
      });
    }
  }

  onItemLongClick(item) {
    item.select = true;
    this.selectCount = 1;
    this.setState({ editMode: true, isAllSelect: this.state.listDatas.length === this.selectCount });
  }

  renderItemView(item, index) {
    // {\"start\":\"07:00\",\"end\":\"09:00\",\"repeat\":127,\"enable\":false,\"clock_idx\":0,\"name\":\"早上无人出现\"}
    console.log("[[[[[[[[[[[[[[[[[[[[[", item);
    return (
      <View style={{ display: "flex", flexDirection: "column" }} key={item}>
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20 }}>
          <TouchableOpacity
            style={{ display: "flex", flexDirection: "column", flexGrow: 2, padding: 20, width: "80%" }}
            onLongPress={() => this.onItemLongClick(item)}
            disabled={this.state.disabledAdd}
            onPress={() => {
              if (this.state.editMode) {
                item.select = !item.select;
                item.select ? this.selectCount++ : this.selectCount--;
                this.setState({ isAllSelect: this.state.listDatas.length === this.selectCount });
              } else {
                this.props.navigation.navigate('ClockAlarmSet',
                  {
                    item: JSON.parse(JSON.stringify(item)),
                    callback: (data) => {
                      if (data.noteType === 0) {
                        data.audio_id = -1;
                      } else if (data.audio_id == null || data.audio_id < 0) {
                        data.audio_id = this.getUniqueAudioId();
                      }
                      Object.assign(item, data);
                      this.onItemCheckChanged(true, item);
                      this.forceUpdate();
                    }
                  });
              }
            }}>
            <Text style={{ fontSize: 30, color: '#000000', fontWeight: 'bold' }}>{item.time}</Text>
            <View style={{ display: "flex", flexDirection: "row", alignItems: "center", }}>
            <Text style={{ fontSize: 14, color: 'rgba(0,0,0,0.6)' }}> {this.getRepeatStr(item)} </Text>
            <View style={{ width:1,height:8,borderRadius:16,backgroundColor:'rgba(0, 0, 0, 0.1)'}}/>
            <Text style={{ fontSize: 14, color: 'rgba(0,0,0,0.6)' }}> {this.getNotesStr(item)}</Text>
            </View>
          </TouchableOpacity>
          {this.state.editMode ? <Checkbox
            style={{ width: 20, height: 20, borderRadius: 20 }}
            checked={item.select}
            onValueChange={(checked) => {
              item.select = checked;
              item.select ? this.selectCount++ : this.selectCount--;
              this.setState({ isAllSelect: this.state.listDatas.length === this.selectCount });
            }}
          /> : <View style={{ display: "flex", flexDirection: "row", alignItems: 'center' }}>
            <View style={{ width: 0.5, height: 26, backgroundColor: 'rgba(0, 0, 0, 0.2)', marginRight: 16.5 }}></View>
            <Switch
              value={item.enable}
              disabled={false}
              onValueChange={(checked) => {
                item.enable = checked;
                let needChange = false;
                if (checked && item.audio_id == null) {
                  needChange = true;
                }
                this.onItemCheckChanged(needChange, item);
              }}
            />
          </View>}
        </View>
      </View>
    );
  }

  getRepeatStr(item) {
    if (item.type == 0) {
      this.mDate.setTime(item.repeat * 1000);
      return dayjs(this.mDate).format(LocalizedStrings["yyyymmdd"]);
    }
    if (item.type == 1) {
      return LocalizedStrings['plug_timer_everyday'];
    }
    if (item.type == 2) {
      return LocalizedStrings['clock_alarm_repeat_work'];
    }
    if (item.type == 3) {
      return LocalizedStrings['clock_alarm_repeat_rest'];
    }
    return Util.getRepeatString(item.repeat);
  }

  getNotesStr(item) {
    if (item.noteType == 0) {
      return LocalizedStrings['backup_none'];
    }
    if (item.noteType == 1) {
      return LocalizedStrings['take_medicine'];
    }
    if (item.noteType == 2) {
      return LocalizedStrings['eat_food'];
    }
    if (item.noteType == 3) {
      return LocalizedStrings['do_homework'];
    }
    if (item.noteType == 4) {
      return LocalizedStrings['wakeup'];
    }
    return item.notes;
  }

  getNotesStr2(item) {
    if (item.noteType == 0) {
      return "";
    }
    if (item.noteType == 1) {
      return LocalizedStrings['take_medicine'];
    }
    if (item.noteType == 2) {
      return LocalizedStrings['eat_food'];
    }
    if (item.noteType == 3) {
      return LocalizedStrings['do_homework'];
    }
    if (item.noteType == 4) {
      return LocalizedStrings['wakeup'];
    }
    return item.notes;
  }

  getDurationText(start, end) {
    let text = `${start} - ${end}`;
    let startValue = parseInt(start.split(":")[0]) * 60 + parseInt(start.split(":")[1]);
    let endValue = parseInt(end.split(":")[0]) * 60 + parseInt(end.split(":")[1]);
    if (startValue > endValue) {
      text = `${start} - ${LocalizedStrings.setting_monitor_next_day} ${end}`;
    }
    return text;
  }
  // 渲染底部提示
  renderFooter() {
    return (
      <View style={{ backgroundColor: "#FFFFFF", paddingLeft: 20, paddingTop: 20 }}>
        <Text>{LocalizedStrings['ss_long_time_nobody_tips']}</Text>
      </View>
    );
  }

  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    // let flag = y > 28;
    let flag = y > this.titleHeight;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.setState({ showBarTitle: true });
    } else {
      this.showTitle = false;
      this.setState({ showBarTitle: false });
    }
  };
  //color: "rgba(0, 0, 0, 0.80)",
  render() {
    let isDark = DarkMode.getColorScheme() == "dark";
    let titleStr = this.state.editMode ? LocalizedStrings['edit'] : LocalizedStrings['clock_alarm'];
    return (<View style={{
      display: "flex",
      height: "100%",
      width: "100%",
      flex: 1,
      flexDirection: "column",
      alignItems: "center"
    }}>
      {this.renderTitleBar()}
      <ScrollView scrollEventThrottle={16} onScroll={this.scrollViewScroll} style={{ width: "100%", backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF' }} contentContainerStyle={{ flexGrow: 1 }}>


        <View style={{ flex: 1, backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF' }}>

          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0} onLayout={({ nativeEvent: { layout: { height } } }) => {
            this.titleHeight = height - 28;
          }}>
            <Text style={{fontFamily:'MI-LANTING--GBK1-Light',fontWeight:'300',  fontSize: 30, color: "rgba(0, 0, 0, 0.80)", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23 }}>
              {titleStr}
            </Text>
          </View>


          <FlatList
            style={{ width: "100%", flex: 1, marginBottom: 100 }}
            data={this.state.listDatas}
            keyExtractor={(item, index) => `key_${index}`}
            renderItem={(data) => this.renderItemView(data.item, data.index)}
            ListEmptyComponent={() => this._renderEmptyList()}
            contentContainerStyle={[{ flexGrow: 1, paddingHorizontal: 12 }]}
            refreshing={this.state.isLoading}>

          </FlatList>
        </View>
      </ScrollView>
      {this.state.editMode ?
        <View style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "100%",
          position: 'absolute',
          bottom: 15
        }}>
          <TouchableOpacity
            style={{
              flexDirection: "column",
              alignItems: "center"
            }}
            onPress={() => {
              this.deleteArray = [];
              this.deleteAlarmKeys = {};
              let needDelete = false;
              this.state.listDatas.forEach((item) => {
                if (!item.select) {
                  this.deleteArray.push({
                    enable: item.enable,
                    noteType: item.noteType,
                    notes: item.notes,
                    repeat: item.repeat,
                    time: item.time,
                    type: item.type,
                    audio_id: item.audio_id
                  });
                } else {
                  needDelete = true;
                }
              });
              if (needDelete == false) {
                console.log(`onDeleteItem no item to delete`);
                Toast.success('idm_empty_tv_device_tips');
                return;
              }
              this.setState({ showDelDialog: true });
            }}>
            <Image
              style={{ width: 25, height: 25 }}
              source={Util.isDark() ? require("../../Resources/Images/icon_delete_white.png") : require("../../Resources/Images/icon_delete_normal1.png")}
              tintColor={isDark ? IMG_DARKMODE_TINT : null}
            />
            <Text style={{ color: "#000000", fontSize: 11 }}>
              {LocalizedStrings["delete_files"]}
            </Text>
          </TouchableOpacity>

        </View>
        :
        <TouchableOpacity
          style={{ position: 'absolute', right: 0, bottom: 0 }}
          disabled={this.state.listDatas.length >= 10 || this.state.disabledAdd}
          onPress={() => {
            this.onAddItem();
          }}>
          <Image style={{ width: 120, height: 120 }}
            source={this.state.listDatas.length >= 10 || this.state.disabledAdd ? require("../../Resources/Images/icon_add_timer_new_dis.webp") : require("../../Resources/Images/icon_add_timer_new.webp")} />
        </TouchableOpacity>
      }
      {this.renderDeleteDialog()}
      {this._renderMoreItemDialog()}
    </View>);
  }
  _renderMoreItemDialog() {

    let left = kWindowWidth - 220;
    let modalStyle = {
      width: 200,
      top: this.statusBarTop + 46,
      // left: left,
      right: 20,
      height: 54 * 2 + 20,
      alignSelf: 'center',
      borderRadius: 16,
      paddingVertical: 10
    };
    return (
      <AbstractDialog
        style={modalStyle}
        showTitle={false}
        visible={this.state.showMoreDlg}
        showButton={false}
        onDismiss={() => {
          this.setState({ showMoreDlg: false });
        }}
        canDismiss={true}
        useNewTheme={true}
      >
        <View style={{ flexGrow: 1 }}>
          <TouchableOpacity
            style={{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 54
            }}
            onPress={() => {
              this.setState({ showMoreDlg: false });
              this.props.navigation.navigate('ClockAlarmSetting');
            }}>
            <Text style={{
              fontSize: 16,
              color: "#000000",
              fontWeight: "400",
              textAlignVertical: "center",
              paddingHorizontal: 28
            }}>{LocalizedStrings['more_setting']}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 54
            }}
            disabled={!this.state.listDatas || this.state.listDatas.length === 0}
            onPress={() => {
              // 删除，进入编辑模式
              if (!this.state.listDatas || this.state.listDatas.length === 0) {
                return;
              }
              this.selectCount = 0;
              this.setState({ editMode: true, showMoreDlg: false });
            }}>
            <Text style={[{
              fontSize: 16,
              color: "#000000",
              fontWeight: "400",
              textAlignVertical: "center",
              paddingHorizontal: 28
            }, (!this.state.listDatas || this.state.listDatas.length === 0) ? { color: "rgba(0,0,0,0.3)" } : null]}>{LocalizedStrings['f_delete']}</Text>
          </TouchableOpacity>
        </View>

      </AbstractDialog>
    );
  }
  _renderEmptyList() {
    if (this.state.isLoading) {
      return null;
    }
    return (
      <View style={{ width: "100%", display: 'flex', flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Image style={{ width: 92, height: 60 }}
          source={DarkMode.getColorScheme() == "dark" ? require("../../Resources/Images/icon_timer_empty_dark.png") : require("../../Resources/Images/icon_timer_empty.webp")}></Image>
        <Text style={{
          color: "#999999",
          fontSize: 15,
          textAlign: 'center',
          marginTop: 5,
          paddingBottom: 10
        }}>{LocalizedStrings['clock_set_timer_empty']}</Text>
      </View>
    );
  }

  renderDeleteDialog() {
    console.log("========================", this.deleteArray);
    return (
      <MessageDialog
        visible={this.state.showDelDialog}
        // title={ LocalizedStrings['confirm_delete_clock'] }
        message={LocalizedStrings['confirm_delete_clock']}

        messageStyle={{ textAlign: 'center' }}
        canDismiss={true}
        onDismiss={() => this.setState({ showDelDialog: false })}
        buttons={[
          {
            text: LocalizedStrings["btn_cancel"],
            // style: { color: 'lightpink' },
            callback: (_) => {
              this.setState({ showDelDialog: false });
            }
          },
          {
            text: LocalizedStrings["delete_files"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              this.onDeleteItem();
              this.setState({ showDelDialog: false });
            }
          }
        ]}
      />
    );
  }

  onDeleteItem() {
    // let arrayStr = JSON.stringify({ values: array });
    let arrayStr = JSON.stringify(this.deleteArray);
    console.log(`onDeleteItem ${arrayStr}`);
    this.putClockList(arrayStr).then(() => {
      this.setState({ editMode: false });
      this.selectCount = 0;
      this.loadData();
    }).catch((err) => {
      console.log(`onDeleteItem error=${JSON.stringify(err)}`);
    });
  }
  // {"idx":2,"clock":[{"start":"08:00","end":"09:00","repeat":127,"enable":true,"clock_idx":0}, {"start":"11:00","end":"13:00","repeat":127,"enable":true,"clock_idx":1}]}
  onAddItem() {
    this.props.navigation.navigate('ClockAlarmSet', {
      callback: (data) => {
        console.log(`alarmDataResultListener=${JSON.stringify(data)}`);
        let array = [];

        this.state.listDatas.forEach((item, index) => {
          array.push({
            enable: item.enable,
            time: item.time,
            type: item.type,
            repeat: item.repeat,
            noteType: item.noteType,
            notes: item.notes,
            audio_id: item.audio_id
          });
        });
        if (XiaoaiTTSUtil.isSupportedAlarmTts()) {
          if (data.noteType === 0) {
            data.audio_id = -1;
          } else {
            data.audio_id = this.getUniqueAudioId();
          }
        }
        array.push(data);
        if (XiaoaiTTSUtil.isSupportedAlarmTts()) {
          if (!data.audio_id) {
            Toast.fail("c_set_fail");
            this.setState({ disabledAdd: false });
            return;
          }
        } else {
          if (!data.time) {
            Toast.fail("c_set_fail");
            this.setState({ disabledAdd: false });
            return;
          }
        }

        let arrayStr = JSON.stringify(array);
        console.log(`onAddItem ${arrayStr}`);
        this.setState({ disabledAdd: true });
        this.putClockList(arrayStr).then((res) => {
          if (res[0].code == 0) {
            Toast.success("c_set_success");
            let note = this.getNotesStr(data);
            this.uploadXiaoaiTTS(note, data.audio_id);
            this.loadData();
          } else {
            Toast.fail("c_set_fail");
            this.setState({ disabledAdd: false });
          }
        }).catch((err) => {
          Toast.fail("c_set_fail");
          this.setState({ disabledAdd: false });
        });
      }
    });
  }

  onResume() {

  }

  onItemCheckChanged(noteChanged = false, item = null) {
    let array = [];
    this.state.listDatas.forEach((item) => {
      array.push({
        enable: item.enable,
        time: item.time,
        type: item.type,
        repeat: item.repeat,
        noteType: item.noteType,
        notes: item.notes,
        audio_id: item.audio_id
      });
    });
    let data = JSON.stringify(array);
    console.log(`onItemCheckChanged ${data}`);
    this.putClockList(data).then((res) => {
      // 需要传音频数据
      Toast.success("c_set_success");
      console.log(`Changed success  ${noteChanged} 内容 ${item.notes}`);
      if (noteChanged && item !== null) {
        let note = this.getNotesStr(item);
        this.uploadXiaoaiTTS(note, item.audio_id);
      }
    }).catch((err) => {
      Toast.fail("c_set_fail");
    });
  }

  setAlarmKey(alarmKeys) {
    let alarmKeysStr = JSON.stringify(alarmKeys);
    console.log(alarmKeysStr);
    if (alarmKeysStr == "{}") {
      console.log("alarmKeys is {}");
      return;
    }
    let data = { did: Device.deviceID, props: alarmKeys };
    AlarmUtil.setProps(data).then((res) => {
      console.log(JSON.stringify(res));
      // this.loadData();
    }).catch((err) => {
      console.log(JSON.stringify(err));
    });
  }

  putClockList(data) {
    // 如果 data 是字符串，先解析成对象
    if (typeof data === 'string') {
      try {
        data = JSON.parse(data);
      } catch (e) {
        console.log("解析数据失败:", e);
        return Promise.reject(e);
      }
    }
    
    // Process the data array to add notes field where needed
    if (data && data.length > 0) {
      data = data.map((item) => {
        if (item.noteType >= 0 && item.noteType < 5) {
          const noteStr = this.getNotesStr2(item);
          return {
            ...item,
            notes: noteStr
          };
        }
        return item;
      });
    }
    return new Promise((resolve, reject) => {
      // 确保参数格式正确
      let params = [{ 
        sname: KEY_ALARM_REMIND_SIID, 
        pname: KEY_ALARM_REMIND_LIST_PIID, 
        value: JSON.stringify(data) // 需要将数组转回字符串
      }];
      AlarmUtilV2.setSpecPValue(params).then((res) => {
        resolve(res);
      }).catch((err) => {
        console.log(`putClockList err=${JSON.stringify(err)}`);
        this.loadData();
        reject(err);
      });
    });
  }


  loadData() {
    this.setState({
      isLoading: true
    });

    let params = [{ sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_LIST_PIID }];
    AlarmUtilV2.getSpecPValue(params).then((result) => {
      console.log("[[[[[[[[[1111", typeof (result), result);
      this.setState({ isLoading: false });
      if (result[0].code === 0) {
        let value = result[0].value;
        if (typeof (value) != "undefined" && value != "") {
          let values = JSON.parse(value);
          this.setState({ listDatas: values }, () => {
            // 判断那些数据没有audio_id ,补传音频数据
            let needUpload = false;
            let array = [];
            let needAudioArray = [];
            this.state.listDatas.forEach((item) => {
              if (item.audio_id == null && XiaoaiTTSUtil.isSupportedAlarmTts()) {
                needUpload = true;
                if (item.noteType === 0) {
                  item.audio_id = -1;
                } else {
                  item.audio_id = this.getUniqueAudioId();
                  needAudioArray.push(item);
                }
              }
              array.push({
                enable: item.enable,
                time: item.time,
                type: item.type,
                repeat: item.repeat,
                noteType: item.noteType,
                notes: item.notes,
                audio_id: item.audio_id
              });
            });
            if (needUpload) {
              let arrayStr = JSON.stringify(array);
              console.log(`needUpload ${arrayStr}`);

              this.putClockList(arrayStr).then((res) => {
                if (res[0].code === 0) {
                  // 补传音频
                  needAudioArray.forEach((data) => {
                    let note = this.getNotesStr(data);
                    console.log(`正在补传tts ${note}`);
                    this.uploadXiaoaiTTS(note, data.audio_id);
                  });

                } else {

                }
              }).catch((err) => {

              });
            }
          });
        }
      }
    }).catch((err) => {
      this.setState({ isLoading: false });
      console.log("[[[[[[[[[[[[", err);
      Toast.fail("c_get_fail");
    }).finally(() => {
      this.setState({ disabledAdd: false });
    });

  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    if (this.state.editMode) {
      this.state.listDatas.forEach((item) => item.select = false);
      this.setState({ editMode: false });
      this.selectCount = 0;
      return true;
    } else {
      return false;
    }
  };

  setXiaoaiTTS() {
    if (!XiaoaiTTSUtil.isSupportedAlarmTts()) {
      return;
    }
    let params = {
      clientId: "1211754106612352000",
      signSecret: "myGg_tWhGviwrMct_seT9BsD9HOOkzluC5Q3CjYZpzR8OPdQUmKwudCUTtdzcTsow1jOZmmBHX29043GvHR-9A",
      ApiKeyName: "prod-com.xiaomi.smarthome",
      ApiKey: "y6lQMN615P_aY89Tnlhbykf9uMq2ueQqXaQPXo7j0O8",
      clientSecret: "Q1Z2ITixpkHksbTsUxeioeBNLpppF-5HtW4-_f3tqJ0YBO_qsHKlIkzNwILh2lREiBUJb6Ik6JjD2B2uvKTnOQ",
      CertMD5: "d5:2e:03:3c:39:b6:f4:7a:02:48:b2:50:5a:2d:6a:91",
      CertSHA256: "b0:31:fe:98:a4:db:b0:d4:d8:26:61:78:7f:25:de:64:31:82:b3:78:e9:ef:63:2d:8a:de:a7:5a:ab:58:f2:d8"
    };
    Service.xiaoai.setXiaoaiTTSAuth(params).then((res) => {
      LogUtil.logOnAll("ClockAlarmPage", `setXiaoaiTTSAuth success `);
    }).catch((err) => {
      LogUtil.logOnAll(`setXiaoaiTTSAuth fail`);
    });
  }

  uploadXiaoaiTTS(notes, audio_id) {
    if (!XiaoaiTTSUtil.isSupportedAlarmTts()) {
      return;
    }
    LogUtil.logOnAll("ClockAlarmPage", `getXiaoaiTTS begin notes:${notes}  audio_id:${audio_id}`);
    if (audio_id === -1) { // 无备注 不需要上传音频
      LogUtil.logOnAll("ClockAlarmPage", `无备注 不需要上传音频`);
      return;
    }
    let params = { text: notes };
    LogUtil.logOnAll("ClockAlarmPage", `getXiaoaiTTS params :`);
    Service.xiaoai.callXiaoaiTTS(params).then((res) => {
      LogUtil.logOnAll("ClockAlarmPage", `getXiaoaiTTS success `);
      let { data: { url } } = res;
      let param = {
        "audio_id": audio_id,
        "url": url
      };
     
      LogUtil.logOnAll("ClockAlarmPage", `_sync.alarm_audio_set param============`);
      RPC.callMethod("_sync.alarm_audio_set", param).then(() => {
      
        LogUtil.logOnAll("ClockAlarmPage", `_sync.alarm_audio_set res============`);
      }).catch((err) => {
        Toast.fail("c_set_fail");
        LogUtil.logOnAll("ClockAlarmPage", `_sync.alarm_audio_set err============`);
      });
    }).catch((err) => {
      LogUtil.logOnAll("ClockAlarmPage", `getXiaoaiTTS fail `);
    });
  }

  getUniqueAudioId() {
    if (!XiaoaiTTSUtil.isSupportedAlarmTts()) {
      return null;
    }
    let audio_ids = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    this.state.listDatas.forEach((item) => {
      let index = audio_ids.indexOf(item.audio_id);
      if (index !== -1) {
        audio_ids.splice(index, 1);
      }
    });
    return audio_ids[0];
  }

}

