'use strict';

import React from 'react';
import { TouchableOpacity, StyleSheet, ScrollView, View, Text, TextInput, Button, TouchableOpacityBase } from 'react-native';
import RPC from "../util/RPC";
import Toast from '../components/Toast';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { DarkMode } from 'miot'
// import { styles } from './SettingStyles';

import NavigationBar from "miot/ui/NavigationBar";

import LogUtil from '../util/LogUtil';
import Host from 'miot/Host';
import InputView from '../widget/InputView'
import RoundedButtonView from "../widget/RoundedButtonView";
export default class AddNASSetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      deviceName: null,
      userName: null,
      userPassword: null,
      isInputNameInvalid: false,
      inputNameErrorHint: null,
      inputNameBottomHint:LocalizedStrings.nas_input_character_hint,
      isInputPsdInvalid: false,
      inputPsdErrorHint: null,
      inputPsdBottomHint: LocalizedStrings.nas_input_character_hint,
      darkMode : DarkMode.getColorScheme() == "dark"
    };

  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ title: LocalizedStrings['nas_storage'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };

  render() {
    return (
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}
                    scrollEventThrottle={1}
                    onScroll={this.scrollViewScroll}>

          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={'0'}>
            <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23,fontFamily:'MI-LANTING--GBK1-Light' }}>
              { LocalizedStrings['nas_storage']}</Text>
          </View>

        {this._renderAddStorageView()}

        </ScrollView>

        <RoundedButtonView buttonText={LocalizedStrings.nas_storage_fin}
                           disabled={this.state.isInputNameInvalid || this.state.isInputPsdInvalid}
                           buttonStyle={{marginHorizontal: 27, marginBottom: Host.isIphoneXSeries ? 35 : 27, height: 45, backgroundColor: 'rgba(50, 186, 192, 1)'}}
                           onPress={() => {
                             this._onComplete()
                           }}/>
      </View>
    );
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: '',
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.willFocusSubscription = this.props.navigation.addListener(
      'didFocus', () => {
        this._renderAddStorageView()
      }
    );

  }
  _renderAddStorageView() {
    let { deviceMessage } = this.props.navigation.state.params
    return (
      <View>

        <Text style={styles.inputTitleStyle}>
          {LocalizedStrings['nas_storage_name']}
        </Text>
        <View style={[styles.deviceNameContainer,this.state.darkMode?{backgroundColor:"#333333AA"}:null]}>
          <Text style={{fontSize: 17, color:  "rgba(0,0,0,0.4)"}}>
            {deviceMessage.name}</Text>
        </View>
        {/*<InputView
            type={InputView.TYPE.NONE}
            isCorrect={true}
            style={{marginHorizontal: 24,marginTop:4,marginBottom:17}}
            placeholder={deviceMessage.name}
            defaultValue={""}
            textInputProps={{autoFocus: false}}
        />
*/}
        <Text style={styles.inputTitleStyle}>
          {LocalizedStrings['nas_user_setting']}
        </Text>
        <InputView
            type={InputView.TYPE.DELETE}
            style={{marginHorizontal: 24,marginVertical:4}}
            placeholder={LocalizedStrings['nas_storage_user']}
            defaultValue={""}
            onChangeText={text => this._onChangeUserNameText(text)}
            textInputProps={{autoFocus: false}}
            isCorrect={!this.state.isInputNameInvalid}
            errorHint={this.state.inputNameErrorHint}
            inputBottomHint={this.state.inputNameBottomHint}
        />

        <InputView
            type={InputView.TYPE.DELETE}
            style={{marginHorizontal: 24,marginVertical:4}}
            placeholder={LocalizedStrings['nas_storage_pas']}
            defaultValue={""}
            onChangeText={text => this._onChangeUserPasswordText(text)}
            textInputProps={{autoFocus: false}}
            isCorrect={!this.state.isInputPsdInvalid}
            errorHint={this.state.inputPsdErrorHint}
            inputBottomHint={this.state.inputPsdBottomHint}
        />



        <View>
          <Text style={styles.tipsBack}>
            {LocalizedStrings['nas_storage_tip']}
          </Text>
        </View>


      </View>
    )
  }
  _onChangeUserNameText(result) {
    this.setState({
      isInputNameInvalid: false,
      inputNameErrorHint: null,
      // inputNameBottomHint: test ? null: LocalizedStrings.nas_input_character_hint
      inputNameBottomHint: null,
      userName: result
    });
  }
  _onChangeUserPasswordText(result) {
    this.setState({
      isInputPsdInvalid: false,
      inputPsdErrorHint: null,
      // inputPsdBottomHint: test ? null : LocalizedStrings.nas_input_character_hint
      inputPsdBottomHint:  null,
      userPassword: result
    });
  }
  _onChangeDeviceNameText(text) {
    this.setState({
      deviceName: text
    })
  }

  _onComplete() {
    //这里要判断是修改还是添加还要看java中是怎么区分修改还是添加
    //这里要重新获取整体的数据 然后改一下名字
    Toast.loading('c_setting');
    let { deviceMessage } = this.props.navigation.state.params
    let obj = {};
    obj.type = deviceMessage.type;
    obj.group = deviceMessage.group;
    obj.addr = deviceMessage.addr;
    obj.name = this.state.deviceName == null ? deviceMessage.name : this.state.deviceName;
    obj.dir = ''
    // obj.dir = deviceMessage.name == null ? deviceMessage.dir : (deviceMessage.addr + "/" + this.state.deviceName)
    obj.user = this.state.userName ? this.state.userName : '';
    obj.pass = this.state.userPassword ? this.state.userPassword : '';
    let param = {};
    param.share = obj;
    //这里一进来是要获取之前的存储位置的 然后将它选择上
    RPC.callMethod("nas_list_dir", param).then((res) => {
      console.log("nas_list_dir", res)
      // setTimeout(() => {
      //     this.setState({ showLoading: false });
      // }, 300)
      //这里跳过去有点慢 应该弹一个等待框
      // this.props.navigation.navigate('SelectNASLocation', { locationList: res.result, deviceMessage: param })
      if (!Object.prototype.hasOwnProperty.call(res, 'error')) {
        this.props.navigation.replace('SelectNASLocation', { locationList: res.result, deviceMessage: param })
      }
      else {
        Toast.fail('nas_user_name_psd_error');
        LogUtil.logOnAll("AddNASSetting", "nas_list_dir failed res" + JSON.stringify(res.error));
      }

    }).catch((err) => {
      LogUtil.logOnAll("AddNASSetting", "nas_list_dir failed" + JSON.stringify(err));

      // this.setState({ showLoading: false });
      Toast.fail('nas_user_name_psd_error', err);
    });

  }


  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
        (codePoint == 0x9) ||
        (codePoint == 0xA) ||
        (codePoint == 0xD) ||
        ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
        ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
        ((codePoint >= 0x10000))) ||
        (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
            codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
            codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
        || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
        || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
        || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }
  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      } else if (str.match(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]/)) {
        return true;
      }
      // else if(this.isTextcommon(str)){
      //   return true
      // }
    }
    return false;
  }

}
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
  },
  inputStyle: {
    width: '100%',
    // height: 40,
    borderBottomColor: '#e5e5e5',
    borderBottomWidth: 1,
    paddingTop: Host.isIOS ? 15 : 0,
    paddingBottom: Host.isIOS ? 15 : 0,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  inputTitleStyle: {
    marginHorizontal:27,
    marginVertical:9,
    fontSize:12,
    color:"#8C93B0",
    fontWeight:'400'
  },
  deviceNameContainer: {
    height: 53,
    borderRadius: 18,
    backgroundColor: "rgba(0,0,0,0.04)",
    marginHorizontal: 28,
    marginTop: 4,
    marginBottom: 17,
    paddingHorizontal: 16,
    justifyContent: "center"
  },
  tipsBack: {
    paddingLeft:45,
    paddingRight:35,
    color: 'rgba(0,0,0,0.4)',
    fontSize: 12,
    marginTop: 5
  }
});
