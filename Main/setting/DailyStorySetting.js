import React from 'react';
import { ScrollView, View, BackHandler, Platform, Text } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import { styles } from './SettingStyles';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { NavigationBar, Separator } from 'mhui-rn';
import BaseSettingPage from "../BaseSettingPage";

export default class DailyStorySetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      dailyStorySwitch: false
    };
  }
  getTitle() {
    return LocalizedStrings['ss_daily_story'];
  }

  renderSettingContent() {
    return (
      <View style={styles.container}>
        <ListItemWithSwitch
          containerStyle={{marginBottom: 20}}
          title={LocalizedStrings['ss_daily_story']}
          showSeparator={false}
          value={this.state.dailyStorySwitch}
          onValueChange={(value) => this._onEnableValueChange(value)}
        />
        <Separator style={{ marginHorizontal: 27 }}/>
        <Text style={{ paddingLeft: 28, paddingRight: 28, marginTop: 20, fontSize: 14, color: 'rgba(0,0,0,0.6)' }}>
          {LocalizedStrings["ss_daily_story_setting_tips"]}
        </Text>

      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    // this.mCategory = this.props.navigation.state.params.category;
    // this.mRoutename = this.props.navigation.state.params.routeName;
    // this.mCallback = this.props.navigation.state.params.callback;

    // this.mParams = this.props.navigation;
    console.log("this.props.navigation.routeName:", this.mRoutename);
    // this.props.navigation.setParams({
    //   title: LocalizedStrings['ss_daily_story'],
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => {
    //           this.props.navigation.goBack();
    //       }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
  }

  componentWillUnmount() {

    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.willFocusSubscription.remove()
  }

  _getSetting() {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code != 0) {
        console.log("getdailyStorySwitch:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }

      this.setState({
        dailyStorySwitch: res.data.dailyStorySwitch,
      });
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
    });
  }

  _onEnableValueChange(value) {
    console.log(`_onEnableValueChange ${value}`);
    AlarmUtil.putDailyStorySwitch({
      open: value,
    }).then((res) => {
      this.setState({ dailyStorySwitch: res.code == 0 ? value : !value });
      if (res.code == 0) {
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      this.setState({ detectionSwitch: !value });
      Toast.fail('c_set_fail', err);
    });
  }

  // android返回键处理
  onBackHandler = () => {
    if (this.props.navigation.state.params.onGoBack) {
      // this.mCallback(this.mCategory);
      this.props.navigation.state.params.onGoBack();
      setTimeout(() => {
        this.props.navigation.popToTop();
      }, 300);
    } else {
      // this.mCallback(this.mCategory);
      this.props.navigation.goBack();
    }
    return true;
  }
}