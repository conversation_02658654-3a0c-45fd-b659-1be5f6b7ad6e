'use strict';

import React from 'react';
import {BackHandler, Dimensions, Platform, ScrollView, Text, View} from 'react-native';
import ChoiceItem from "../widget/ChoiceItem";
import {localStrings as LocalizedStrings} from '../MHLocalizableString';

const {width: screenWidth} = Dimensions.get("screen");
import NavigationBar from "miot/ui/NavigationBar";
import MHDatePicker from "miot/ui/MHDatePicker";
import { ListItem, MessageDialog} from 'mhui-rn';
import {DarkMode} from "miot";

export default class NasUploadIntervalSetting extends React.Component {

    static navigationOptions = ({navigation}) => {
        return {
            header: (
                <NavigationBar
                    type={NavigationBar.TYPE.LIGHT}
                    backgroundColor={"#F6F6F6"}
                    left={navigation.getParam('left')}
                    right={navigation.getParam('right')}
                    title={navigation.getParam("navTitle")}
                />
            )
        };
    };

    constructor(props, context) {
        super(props, context);
        this.state = {
            selectUploadIntervalIndex:this.props.navigation.state.params ? this.props.navigation.state.params.selectUploadIntervalIndex :0,
            syncInterval:this.props.navigation.state.params ? this.props.navigation.state.params.syncInterval :300,
            showTimeDialog: false,
            showSaveDialog: false,

            timeSelectedStr: "00:00",
            canSave: false
        };
        if (this.props.navigation.state.params) {
            this.commitCallback = this.props.navigation.state.params.callBack;
        }
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: "#F6F6F6"}}>
                <ScrollView showsVerticalScrollIndicator={false}
                            scrollEventThrottle={1}
                            onScroll={this.scrollViewScroll}
                >

                    <View style={{flexDirection: "row", flexWrap: "wrap"}} key={6}>
                        <Text style={{
                            fontSize: 30,
                            color:  "rgba(0, 0, 0, 0.80)",
                            position: "relative",
                            fontWeight:"300",
                            marginLeft: 25,
                            marginTop: 5,
                            marginBottom: 22,
                            fontFamily:'MI-LANTING--GBK1-Light'
                        }}>
                            {this.props.navigation.state.params.title}
                        </Text>
                    </View>


                    <View style={{backgroundColor: "#F6F6F6"}}
                          key={2}
                    >
                        {
                            [
                                {name: 'nas_interval_instant', interval: 300},
                                {name: 'nas_interval_hourly',  interval: 3600},
                            ].map((item, i) => {
                                return (
                                    <ChoiceItem title={LocalizedStrings[item.name]}
                                                subtitle={LocalizedStrings[item.subtitle]}
                                                key={i + 100}
                                                keyString={i + 100}
                                                containerStyle={{minHeight:74,marginHorizontal: 22, marginVertical: 6, borderRadius:16}}
                                                checked={this.state.selectUploadIntervalIndex == i}
                                                backgroundColor={this.darkMode ? 'dark' : 'white'}
                                                titleColor={'rgba(0, 0, 0, 1)'}
                                                subtitleColor={'rgba(0, 0, 0, 0.6)'}
                                                selectIcon={require("../../Resources/Images/icon_single_checked.png")}
                                                unselectIcon={require("../../Resources/Images/icon_single_unchecked.png")}
                                                onlyChecked={true}
                                                onValueChange={(value) => {
                                                    value && this._onSelectedItem(i,item.interval);
                                                }}
                                    />
                                );
                            })
                        }

                        <ChoiceItem title={LocalizedStrings.nas_interval_daily} keyString={"key_2"}
                                    containerStyle={{
                                        minHeight:74,
                                        marginHorizontal: 22,
                                        marginVertical: 6,
                                        borderRadius:16,
                                        // borderBottomLeftRadius: this.state.selectUploadIntervalIndex==2 ? 0 : 16,
                                        // borderBottomRightRadius: this.state.selectUploadIntervalIndex==2 ? 0 : 16
                                    }}
                                    checked={this.state.selectUploadIntervalIndex==2}
                                    backgroundColor={this.darkMode ? 'dark' : 'white'}
                                    titleColor={'rgba(0, 0, 0, 1)'}
                                    selectIcon={require("../../Resources/Images/icon_single_checked.png")}
                                    unselectIcon={require("../../Resources/Images/icon_single_unchecked.png")}
                                    onlyChecked={true}
                                    onValueChange={(value) => {
                                        value && this._onSelectedItem(2,86400);
                                    }}
                        />

                        {/*m300暂不支持指定转存时间*/}
                        {/*<ListItem key={"key_3"}*/}
                        {/*          allowFontScaling={false}*/}
                        {/*          containerStyle={{*/}
                        {/*              width: screenWidth - 44,*/}
                        {/*              height: 60,*/}
                        {/*              marginHorizontal: 22,*/}
                        {/*              paddingVertical: 20,*/}
                        {/*              paddingHorizontal:20,*/}
                        {/*              backgroundColor: this.darkMode ? 'dark' : 'white',*/}
                        {/*              borderBottomLeftRadius: 16,*/}
                        {/*              borderBottomRightRadius: 16,*/}
                        {/*              display: this.state.selectUploadIntervalIndex == 2 ? "flex" : "none"*/}
                        {/*          }}*/}
                        {/*          titleStyle={{fontSize: 16, color: "#000000"}}*/}
                        {/*          valueStyle={{fontSize: 13, color: "#999999"}}*/}
                        {/*          title={LocalizedStrings.nas_interval_select_time}*/}
                        {/*          value={this.state.timeSelectedStr}*/}
                        {/*          valueNumberOfLines={3}*/}
                        {/*          onPress={(_) => {*/}
                        {/*              this.setState({ showTimeDialog: true});*/}
                        {/*          }}*/}
                        {/*          showSeparator={false}/>*/}



                    </View>

                </ScrollView>

                {this._renderTimeDialog()}

                {this._renderBackDialog()}
            </View>
        );
    }

    scrollViewScroll = (event) => {
        const y = event.nativeEvent.contentOffset.y;
        // 当页面滚动的距离等于标题栏的高度时，其透明度变为1
        let flag = y > 28;
        if (this.showTitle == flag) {
            return;
        }
        if (flag) {
            this.showTitle = true;
            this.props.navigation.setParams({navTitle: LocalizedStrings.nas_store_interval_setting});
        } else {
            this.showTitle = false;
            this.props.navigation.setParams({navTitle: ""});
        }
    };

    componentDidMount() {
        if (Platform.OS === "android") {
            BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
        }
        this.refreshNavigationBar();
        this._getSelectedTimeStr();
    }

    onBackHandler = () => {
        if (this.state.canSave) {
            this.setState({showSaveDialog: true});
            return true;
        }
        return false;
    };

    refreshNavigationBar() {

        this.props.navigation.setParams({
            backgroundColor: "#F6F6F6",
            title: LocalizedStrings['nas_store_interval_setting'],
            type: this.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
            left: [
                {
                    key: (this.state.canSave) ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
                    onPress: () => {
                        if (this.state.canSave) {
                            this.setState({showSaveDialog: true});
                            return;
                        }
                        this.props.navigation.goBack();
                    }
                }
            ],
            right: [
                {
                    key: (this.state.canSave) ? NavigationBar.ICON.COMPLETE : null,
                    onPress: () => {
                        this.props.navigation.getParam('callBack')(this.state.syncInterval,this.state.selectUploadIntervalIndex);
                        this.props.navigation.goBack();
                    }
                }
            ],
            titleStyle: {
                fontSize: 18,
                color: '#333333',
                fontWeight: 500
            }
        });

    }

    componentWillUnmount() {

    }


    _getSelectedTimeStr() {
        let syncInterval = this.state.syncInterval;
        if (syncInterval == 86400 || syncInterval == 0) {
            this.setState({timeSelectedStr: "00:00"});
            return;
        }
        let h = (Math.floor(syncInterval / 3600)).toString().padStart(2, '0');
        let m = (syncInterval % 3600 / 60).toString().padStart(2, '0');
        this.setState({timeSelectedStr: `${h}:${m}`});
    }



    _onSelectedItem(index,interval) {
        this.setState({
            selectUploadIntervalIndex: index,
            syncInterval: interval,
            canSave: true
        }, () => {
            index==2&&this._getSelectedTimeStr();
            this.refreshNavigationBar()
        });
    }

    _renderBackDialog() {
        return (
            <MessageDialog
                visible={this.state.showSaveDialog}
                message={LocalizedStrings['exit_change_disappear']}
                messageStyle={{textAlign: "center"}}
                canDismiss={false}
                buttons={[
                    {
                        text: LocalizedStrings["action_cancle"],
                        callback: (_) => {
                            this.setState({showSaveDialog: false});
                        }
                    },
                    {
                        text: LocalizedStrings["action_confirm"],
                        callback: (_) => {
                            this.setState({showSaveDialog: false});
                            this.props.navigation.goBack();
                        }
                    }
                ]}
            />
        )
    }

    _renderTimeDialog() {
        return (
            <MHDatePicker
                visible={this.state.showTimeDialog}
                title={LocalizedStrings['nas_interval_select_time']}
                showSubtitle={false} //subtitle默认为当前选中的时间
                type={MHDatePicker.TYPE.TIME24}
                onDismiss={() => this.setState({showTimeDialog: false})}
                datePickerStyle={{
                    rightButtonStyle: {color: "#FFFFFF"},
                    rightButtonBgStyle: {bgColorNormal: "#32BAC0", bgColorPressed: "#32BAC099"},
                    pickerInnerStyle:{selectFontSize:26,unitTextColor:"#32BAC0"}
                }}

                onSelect={(res) => {
                    let str = `${res.rawArray[0]}:${res.rawArray[1]}`;
                    this.setState({
                        syncInterval: parseInt(res.rawArray[0]) * 3600 + parseInt(res.rawArray[1]) * 60,
                        timeSelectedStr: `${str}`,
                        canSave: true
                    }, () => this.refreshNavigationBar());
                }}
                current={(this.state.timeSelectedStr ? this.state.timeSelectedStr.split(':') : new Date())}
            />
        );
    }

}
