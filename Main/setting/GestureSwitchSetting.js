import React from 'react';
import { Service, Device } from "miot";
import { ScrollView, ART, View, BackHandler, Dimensions, Platform, Text, Button, StyleSheet, Image, TouchableOpacity, TouchableWithoutFeedback, ImageBackground } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Switch from 'miot/ui/Switch';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import RPC from '../util/RPC';
import Host from 'miot/Host';
import {LoadingDialog, MessageDialog} from 'miot/ui/Dialog';
import VersionUtil from '../util/VersionUtil';
import AICard from '../components/AICard';

export default class GestureSwitchSetting extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      gestureSwitch: false,
      faceSwitch:false,
      showHintDialog:false
    };
    this.gestureInvalid = false; //when gestureSwitch opened && faceSwitch closed , gesture call maybe does't work
  }

  componentDidMount() {
    this.fromSelecteScene = this.props.navigation.getParam("fromSelecteScene");
    this.props.navigation.setParams({
      title: LocalizedStrings['gesture_switch_txt'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });


    AlarmUtil.getAiFrameSwitch022().then((res) => {
      console.log("get gesture switch=", JSON.stringify(res));
      this.setState({ gestureSwitch: res[0].value });
      
      this._getFaceSwitch().then(()=>{}).catch(()=>{
        this._getFaceSwitch().then(()=>{}).catch(()=>{
          console.log('get spec failed twice,to do nothing temporarily');
        });
      });
    }).catch((err) => {
      console.log("get gesture switch err=", JSON.stringify(err));
    });
  }

  render() {
    let switchItem = (
      <ListItemWithSwitch
        title={LocalizedStrings['gesture_switch_txt']}
        showSeparator={false}
        // subtitle={LocalizedStrings['settings_alarm_baby_sleep_sub']}
        value={this.state.gestureSwitch}
        onValueChange={(value) => {
          if (value && !this.state.faceSwitch) {
            this.setState({showHintDialog: true});
            return;
          }
          this._onSwitchChange(value);
        }}
        titleStyle={{ fontWeight: 'bold' }}
      />
    );
    const bottomBtn = (
      this.state.gestureSwitch && !this.fromSelecteScene && false ? // 暂时屏蔽
        <TouchableOpacity 
          style={{ height: 46, width: "90%", backgroundColor: "#32BAC0", borderRadius: 23, marginLeft: 20, marginBottom: 35 }}
          onPress={() => {
            Service.scene.openIftttAutoPage();
          }}>
          <Text style={{ textAlign: "center", fontSize: 15, lineHeight: 46, color: "#ffffff" }}>
            {LocalizedStrings["targetPushTitle_subtitle1"]}
          </Text>
        </TouchableOpacity> : null
    );
    return (
      <View>
        <AICard
          img={require('../../Resources/Images/faceRecognition/pic_gesture_talk.png')}
          desc={LocalizedStrings['gesture_tips_v2']}
          switchButton={switchItem}
          bottomBtn={bottomBtn}
        />
        {this._renderHintDialog()}
      </View>
    );
  }

  _onSwitchChange(value) {
    Toast.loading('c_setting');
    AlarmUtil.putAiFrameSwitch022(value).then((res) => {
      console.log("put gesture switch=", JSON.stringify(res));
      this.setState({ gestureSwitch: value });
      Toast.success('c_set_success');
    }).catch((err) => {
      console.log("put gesture switch err=", JSON.stringify(err));
      this.setState({ gestureSwitch: !value });
      Toast.fail('c_set_fail');
    });
  }

  _renderHintDialog() {
    return (
      <MessageDialog
        visible={this.state.showHintDialog}
        title={LocalizedStrings['gesture_switch_tips']}
        message={LocalizedStrings['face_service_tips_message']}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["license_negative_btn_face"],
            callback: (_) => {
              this.setState({showHintDialog: false});
              if (this.gestureInvalid) { //user disagrees，close gestureSwitch automatically
                this._onSwitchChange(false);
              }
            }
          },
          {
            text: LocalizedStrings["license_positive_btn_face"],
            callback: (_) => {
              let param = {
                privacyVersion: "",
                type: AlarmUtil.FACE_ACCEPT,
                privacyType: 3,
                pluginPrivacyId: 302
              };

              Toast.loading('c_setting');
              //when this.gestureInvalid is true && open faceSwitch failed，close gestureSwitch automatically
              //to avoid gesture call do not work
              Device.setPrivacyConfirmation(param).then(() => {
                AlarmUtil.putFaceSwitch022(true).then((result) => {
                  if (result[0].code == 0) {
                    this.setState({faceSwitch: true});
                    this._onSwitchChange(true);
                  } else {
                    if (this.gestureInvalid) {
                      this._onSwitchChange(false);
                      return;
                    }
                    Toast.fail('c_set_fail');
                  }
                }).catch((_) => {
                  if (this.gestureInvalid) {
                    this._onSwitchChange(false);
                    return;
                  }
                  Toast.fail('c_set_fail');
                });
              }).catch((err) => {
                if (this.gestureInvalid) {
                  this._onSwitchChange(false);
                  return;
                }
                Toast.fail('c_set_fail');
              });
              this.setState({showHintDialog: false});
            }
          }
        ]}
        onDismiss={(_) => this.setState({showHintDialog: false})}
      />
    );
  }

  _getFaceSwitch() {
    return new Promise((resolve, reject) => {
      AlarmUtil.getAiSwitch022(2).then((res) => {
        console.log("getAiSwitch022 res=", JSON.stringify(res));
        if(res[0].code == 0) {
          this.setState({
            faceSwitch: res[0].value
          }, () => {
            if (this.state.gestureSwitch && !this.state.faceSwitch) {
              this.gestureInvalid = true;
              this.setState({showHintDialog: true});
            }
            resolve();
          });
        }else{ //get spec failed
          reject();
        }
      }).catch((err) => { //get spec failed
        console.log("getAiSwitch022 err=", JSON.stringify(err));
        reject();
      });
    });
  }

}