import React from 'react';
import { ScrollView, View, BackHandler, Platform, Text} from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import { styles } from './SettingStyles';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { InputDialog, NavigationBar } from 'mhui-rn';
import { Styles } from 'miot/resources';
import { Device} from 'miot';

export default class TalkForPushSettings extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      talkSwitch: false,
      talkForPushSwitch: false,
      inputNumDialog: false,
      commentErr:null
    };
    this.phoneNum = "";
  }

  render() {
    return (
      <View style={styles.container}>
        <ListItemWithSwitch
          title={LocalizedStrings['talk_for_push']}
          subtitle={LocalizedStrings['talk_for_push_tips']}
          showSeparator={false}
          unlimitedHeightEnable={true}
          subtitleNumberOfLines={0}
          value={this.state.talkSwitch}
          onValueChange={(value) => this._onTalkSwitchValueChange(value)}
        />
        <ListItemWithSwitch
          title={LocalizedStrings['talk_for_push_server']}
          subtitle={LocalizedStrings["one_key_call_tips"]}
          showSeparator={false}
          unlimitedHeightEnable={true}
          subtitleNumberOfLines={0}
          value={this.state.talkForPushSwitch}
          onValueChange={(value) => this._onEnableValueChange(value)}
        />
        {/* <Text style={{ justifyContent: 'center', paddingLeft: Styles.common.padding, paddingEnd: 20 }}>
          {LocalizedStrings["one_key_call_tips"]}
        </Text> */}
        <ListItem
          title={LocalizedStrings['talk_for_push_phone_num']}
          showSeparator={false}
          disabled={!this.state.talkForPushSwitch}
          value={this.phoneNum == "" ? LocalizedStrings.plug_timer_no_set : this.phoneNum}
          onPress={(_) => this.phoneNumPress()}
        />
        {this.renderInputPhoneNumDialog()}
      </View>
    );
  }

  _onTalkSwitchValueChange(value) {
    AlarmUtil.putOneKeyCallSwitch(value).then((res) => {
      console.log("putOneKeyCallSwitch res=", JSON.stringify(res));
      this.setState({
        talkSwitch: value
      });
      if (res[0].code == 0) {
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      console.log("putOneKeyCallSwitch err=", JSON.stringify(err));
      this.setState({
        talkSwitch: !value
      });
    });
  }

  phoneNumPress() {
    if(Device.isOwner === false){
      Toast.show(LocalizedStrings['share_user_permission_hint'])
    }else{
      this.setState({ inputNumDialog: true });
    }
    console.log(Device.isOwner,"是否为共享设备")
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings['talk_for_push'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
  }

  renderInputPhoneNumDialog() {
    return (
      <InputDialog
        visible={this.state.inputNumDialog}
        title={LocalizedStrings["talk_for_push_phone_num_input"]}
        inputs={[{
          placeholder: LocalizedStrings["talk_for_push_phone_num_input"],
          defaultValue: this.phoneNum,
          textInputProps: {
            autoFocus: true
          },
          onChangeText: (text) => {
            if (this.state.commentErr != null) {
              this.setState({ commentErr: null });
            }
          },
          isCorrect: this.state.commentErr == null
        }]}
        inputWarnText={this.state.commentErr}
        buttons={[
          {
            // style: { color: 'lightpink' },
            callback: () => this.setState({ inputNumDialog: false, commentErr: null })
          },
          {
            text: LocalizedStrings["save_files"],
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`结果`, result.textInputArray[0]);
              let isPhoneNumber = result.textInputArray[0].match(/^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/)
              if(isPhoneNumber || result.textInputArray[0] == ""){
                this._setPhoneNum(result.textInputArray[0]);
                this.setState({ inputNumDialog: false, commentErr: null });
              } else {
                this.setState({ commentErr: LocalizedStrings.talk_for_push_right_phone_num })
              }
             
              // this.setState({ inputNumDialog: false });
              // this._setPhoneNum(result.textInputArray[0]);
            }
          }
        ]}
        onDismiss={() => this.setState({ inputNumDialog: false, commentErr: null })}
      />
    );
  }

  _setPhoneNum(num) {
    AlarmUtil.setThirdAlarmInfo(num).then((res) => {
      console.log("setThirdAlarmInfo res=", JSON.stringify(res));
      this.phoneNum = num;
      this.forceUpdate();
    }).catch((err) => {
      console.log("setThirdAlarmInfo err=", JSON.stringify(err));
    });
  }

  componentWillUnmount() {

  }

  _getSetting() {
    AlarmUtil.getThirdAlarmInfo().then((res) => {
      console.log("getThirdAlarmInfo res=", JSON.stringify(res));
      this.phoneNum = res.phone_number;
      this.setState({
        talkForPushSwitch: res.switch
      });
    }).catch((err) => {
      console.log("getThirdAlarmInfo err=", JSON.stringify(err));
    });
    AlarmUtil.getOneKeyCallSwitch().then((res) => {
      console.log("getOneKeyCallSwitch res=", JSON.stringify(res));
      this.setState({ talkSwitch: res[0].value });
    }).catch((err) => {
      console.log("getOneKeyCallSwitch err=", JSON.stringify(err));
      this.setState({
        talkSwitch: false
      });
    });
  }

  _onEnableValueChange(value) {
    console.log(`_onEnableValueChange ${ value }`);
    AlarmUtil.setPhoneServiceSwitch(value).then((res) => {
      console.log("setPhoneServiceSwitch res=", JSON.stringify(res));
      this.setState({ talkForPushSwitch: value });
      // if (res.code == 0) {
      Toast.success('c_set_success');
      // } else {
      //   Toast.fail('c_set_fail');
      // }
    }).catch((err) => {
      console.log("setPhoneServiceSwitch err=", JSON.stringify(err));
      this.setState({ talkForPushSwitch: !value });
      Toast.fail('c_set_fail', err);
    });
  }

  // android返回键处理
    onBackHandler = () => {
      if (this.props.navigation.state.params.onGoBack) {
        this.props.navigation.state.params.onGoBack();
        setTimeout(() => {
          this.props.navigation.popToTop();
        }, 300);
      } else {
        this.props.navigation.goBack();
      }
      return true;
    }
}