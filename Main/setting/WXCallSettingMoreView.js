import { Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View, Dimensions } from "react-native";
import React, { useEffect, useState } from "react";
import { LocalizedStrings } from "miot/ui";
import { Service, Host, Device } from "miot";
import { localStrings } from "../MHLocalizableString";
import { ListItem, ListItemWithSwitch, NavigationBar, Separator } from "mhui-rn";
import AbstractDialog from "miot/ui/Dialog/AbstractDialog";
import Util from "../util2/Util";

import { Styles as SettingStyles } from "miot/resources";



import { strings as I18n } from "miot/resources";
import FDSUtil from "../util/FDSUtil";
import LogUtil from "../util/LogUtil";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import ChoiceItem from "../widget/ChoiceItem";
import CallUtil from "../util/CallUtil";
import Toast from "../components/Toast";
import XiaoaiTTSUtil from "../util/XiaoaiTTSUtil";


const TAG = "WXCallSettingMore";

const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead

const WXCallSettingMoreView = ({ props ,updateSetting}) => {
  const [showCancelEmpowerDialog, setShowCancelEmpowerDialog] = useState(false);
  const [onCallDialogVisible, setOnCallDialogVisible] = useState(false);
  const [memberList, setMemberList] = useState([]);
  const [selectMemberIndex, setSelectMemberIndex] = useState(-1);
  const [settingData, setSettingData] = useState({
   
  });
  useEffect(() => {
  
   
    const willFocusSubscription = props.navigation.addListener("didFocus", () => {
    console.log("jinlaile");
   
      getMemberList();
    });
    return () => {
      willFocusSubscription?.remove && willFocusSubscription?.remove();
    };
  }, []);
  const getMemberList = async() => {
    // 获取微信联系人列表
    
    const data = await queryMemberList().finally(() => {
      Toast._hideLastToast();
    });
    if (data) {
     
      const call_setting = data?.settingsData?.call_setting;
      // console.log(call_setting, "data?.contacts");
      const switchData = call_setting ? JSON.parse(call_setting)?.switch : {};
      const initSwitch = {
        hand: switchData?.hand ?? 0,
        mijia: switchData?.mijia ?? 1,
        wx: switchData?.wx ?? 1,
        autoanswer: switchData?.autoanswer, 
        onekeycall: switchData?.onekeycall,
        callnametts: call_setting ? (switchData?.callnametts ?? 0) : 1
      };
      // if (switchData?.autoanswer === undefined) {
      //   initSwitch.autoanswer = 0;
      // }
      // if (switchData?.onekeycall === undefined) {
      //   initSwitch.onekeycall = 0;
      // }
      if (call_setting) {
        data.settingsData.call_setting = JSON.parse(call_setting);
        data.settingsData.call_setting.switch = initSwitch;
        console.log(data.settingsData.call_setting, "resresres");
      }
      const findIndex = data?.contacts?.findIndex((item) => {
        if (data?.settingsData?.call_setting?.onekeycalluserid) {
          return item.key === (data?.settingsData?.call_setting?.onekeycalluser) && 
          `${ item.mijia }` === (data?.settingsData?.call_setting?.onekeycalluserid);
        }
        if (data?.settingsData?.call_setting?.onekeycalluser) {
          return item.key === (data?.settingsData?.call_setting?.onekeycalluser); 
        }
        
        return false;
      });
      
      if (findIndex >= 0) {
        setSelectMemberIndex(findIndex);
      } else {
        setSelectMemberIndex(-1);
      }
      console.log("data?.settingsData",data?.settingsData);
      
      setSettingData(data?.settingsData);
      setMemberList(data?.contacts);
    }
  };

  // 取消微信授权弹窗和message
  const wxCancelMsg = () => {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          // top: "100%",
          // left: 0,
          // right: 0,
          // position: "absolute",
          marginTop: 104
        }}
      >
        <View key={12}>
          <TouchableOpacity
            onPress={() => {
              setShowCancelEmpowerDialog(true);
            }}
          >
            <Text
              style={{
                color: "#32BAC0",
                fontSize: 14,
                fontWeight: "400",
                textAlign: "center",
                textDecorationLine: "underline",
                paddingTop: 10,
                paddingHorizontal: 80
              }}
            >
              {localStrings["cs_wx_cancel_invite"]}
            </Text>
          </TouchableOpacity>
        </View>
        <AbstractDialog
          visible={showCancelEmpowerDialog}
          title={localStrings["cs_wx_cancel_invite_title"]}
          dialogStyle={{
            titleStyle: {
              fontSize: 16.5
            }
          }}
          showSubtitle={false}
          useNewTheme
          canDismiss={true}
          onDismiss={() => {
            setShowCancelEmpowerDialog(false);
          }}
          buttons={[
            {
              text: localStrings["offline_divice_ok"],
              colorType: "grayLayerBlack",
              callback: (_) => {
                setShowCancelEmpowerDialog(false);
              }
            }
          ]}
        >
          <View>
            <Text
              style={{
                fontSize: 16,
                fontWeight: "400",
                marginHorizontal: 28,
                marginBottom: 26,
                lineHeight: 24
              }}
            >
              {localStrings["cs_wx_cancel_invite_msg"]}
            </Text>
          </View>
        </AbstractDialog>
      </View>
    );
  };

  const setSwitch = (key, value) => {
    if (!settingData?.call_setting?.switch) {
      return;
    }
    settingData.call_setting.switch[key] = value ? 1 : 0;
    
    // if (key === "onekeycall" && !settingData.call_setting?.onekeycalluser) {
    //   settingData.call_setting.onekeycalluser = memberList[0]?.key;
    // }
    let upData = JSON.parse(JSON.stringify(settingData))?.call_setting;
    // 先走rtc，再走云端消息
    CallUtil.updateSettingToDevice(JSON.stringify(upData))
      .then((res) => {
    
        DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData))
          .then((res) => {
            console.log(res, "resres");
            Toast._showToast(localStrings["c_set_success"]);
            setSettingData({ ...settingData });
            updateSetting && updateSetting()
          })
          .catch((error) => {
            settingData.switch[key] = !value ? 1 : 0;
            setSettingData({ ...settingData });
            Toast.fail("c_set_fail");
          });
      })
      .catch((error) => {
        settingData.switch[key] = !value ? 1 : 0;
        setSettingData({ ...settingData });
        Toast.fail("c_set_fail");
      });
    
    // DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData))
    //   .then((res) => {
    //     CallUtil.updateSettingToDevice(JSON.stringify(upData))
    //       .then((res) => {
    //         console.log(res, "resres");
    //         Toast._showToast(localStrings["c_set_success"]);
    //         setSettingData({ ...settingData });
    //       })
    //       .catch((error) => {
    //         settingData.switch[key] = !value ? 1 : 0;
    //         setSettingData({ ...settingData });
    //         Toast.fail("c_set_fail");
    //       });
    //   })
    //   .catch((error) => {
    //     settingData.switch[key] = !value ? 1 : 0;
    //     setSettingData({ ...settingData });
    //     Toast.fail("c_set_fail");
    //   });
  };
  const setOneCall = (index, initIndex) => {
     
    settingData.call_setting.onekeycalluser = memberList[index]?.key;
    settingData.call_setting.onekeycalluserid = `${ memberList[index]?.mijia }`;
    let upData = JSON.parse(JSON.stringify(settingData))?.call_setting;
    // 先走rtc，再走云端消息
    setSelectMemberIndex(index);
    CallUtil.updateSettingToDevice(JSON.stringify(upData))
      .then((res) => {
        DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData))
          .then((res) => {
            console.log(res, "resres");
            console.log(settingData, "settingData");
            setSettingData({ ...settingData });
            updateSetting && updateSetting()
            Toast._showToast(localStrings["c_set_success"]);
          })
          .catch((error) => {
            setSelectMemberIndex(initIndex);
            Toast.fail("choose_again");
          }).finally(() => {
            setOnCallDialogVisible(false);
          });
      })
      .catch((error) => {
        setOnCallDialogVisible(false);
        setSelectMemberIndex(initIndex);
        Toast.fail("choose_again");
      });
    // DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData))
    //   .then((res) => {
    //     CallUtil.updateSettingToDevice(JSON.stringify(upData))
    //       .then((res) => {
    //         console.log(res, "resres");
    //         console.log(settingData, "settingData");
    //         setSettingData({ ...settingData });
    //         Toast._showToast(localStrings["c_set_success"]);
    //       })
    //       .catch((error) => {
    //         setSelectMemberIndex(initIndex);
    //         Toast.fail("choose_again");
    //       }).finally(() => {
          
    //         setOnCallDialogVisible(false);
    //       });
    //   })
    //   .catch((error) => {
    //     setSelectMemberIndex(initIndex);
    //     setOnCallDialogVisible(false);
    //     Toast.fail("choose_again");
    //   });
  };
  // 选择一键呼叫联系人
  const onCallDialog = () => {
    return (
      <AbstractDialog
        
        visible={onCallDialogVisible}
        title={localStrings["wx_choose_contact"]}
        useNewTheme
        dialogStyle={{
          allowFontScaling: true,
          unlimitedHeightEnable: false,
          titleStyle: {
            fontSize: 18,
            fontWeight: "bold"
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
         
        }}
        buttons={[
          {
            text: I18n.cancel,
            colorType: "grayLayerBlack",
            callback: () => {
              setOnCallDialogVisible(false);
            }
          }
        ]}
        onDismiss={() => {
          setOnCallDialogVisible(false);
        }}
        itemStyleType={2}
        selectedIndexArray={[0]}
      >
        <ScrollView style={{ marginBottom: 12, maxHeight: Host.isPad ? kWindowWidth * 0.6 : Dimensions.get('window').height * 0.6 }}>
          {memberList.map((item, i) => {
            let icon = null;
            if (item.objName && item.localFileName) {
              icon = `file://${ Host.file.storageBasePath }/${ item.localFileName }`;
            } else if (item.icon?.indexOf("http") < 0) {
              icon = null;
            } else if (item.icon) {
              icon = item.icon;
            } 
            return (
              <ChoiceItem
                leftAvatar={icon ? { uri: icon } : require("../../Resources/Images/icon_user.png")}
                leftAvatarStyle={styles.leftAvatarStyle}
                title={item?.callName || item?.nickname}
                key={i + 100}
                keyString={i + 100}
                containerStyle={{ paddingLeft: 28, paddingBottom: 16, paddingTop: 16, borderRadius: 0 }}
                checked={selectMemberIndex === i}
                backgroundColor={selectMemberIndex === i ? null : "white"}
                type={"single"}
                titleStyle={{ fontSize: 16 }}
                subtitleStyle={{ fontSize: 13 }}
                itemStyleType={1}
                selectIcon={require("../../Resources/Images/icon_selected_choice.png")}
                onlyChecked={true}
                onValueChange={() => setOneCall(i, selectMemberIndex)}
              />
            );
          })}
        </ScrollView>
      </AbstractDialog>
    );
  };

  const getSwitchColor = () => { 
   
    return memberList?.length > 0; 
  };

 
  return (
    <View style={styles.container}>
      <ListItemWithSwitch
        title={localStrings["wx_device_free_call"]}
        subtitle={localStrings["wx_device_free_call_desc_v2"]}
        subtitleNumberOfLines={5}
        onPress={(_) => {
          
          if (!getSwitchColor()) {
            return;
          }
        }}
        value={!!settingData?.call_setting?.switch?.autoanswer}
        onValueChange={(value) => {
          if (!getSwitchColor()) {
            setSettingData({ ...settingData });
            return;
          }
          setSwitch('autoanswer', value);
        }}
        titleStyle={[styles.freeCallTitle, getSwitchColor() ? {} : { color: "#999" }]}
        subtitleStyle={[styles.freeCallSubtitleTitle, getSwitchColor() ? {} : { color: "#999" }]}
        showSeparator={false}
        unlimitedHeightEnable={true}
        onTintColor={getSwitchColor() ? '' : "rgba(50, 186, 192, 0.3)" }
      />

      <View style={styles.separatorTable}>
        <Separator style={{ height: 0.5, marginLeft: SettingStyles.common.padding, marginRight: SettingStyles.common.padding }} />
      </View>
      <ListItemWithSwitch
        title={localStrings["wx_one_key_call"]}
        subtitle={localStrings["wx_one_key_call_desc"]}
        subtitleNumberOfLines={4}
        unlimitedHeightEnable={true}
        onPress={(_) => {
          if (!getSwitchColor()) {
            return;
          }
        }}
        value={!!settingData?.call_setting?.switch?.onekeycall}
        onValueChange={(value) => {
          if (!getSwitchColor()) {
            setSettingData({ ...settingData });
            return;
          }
             
          setSwitch('onekeycall', value);
        }}
        titleStyle={[styles.freeCallTitle, getSwitchColor() ? {} : { color: "#999" }]}
        subtitleStyle={[styles.freeCallSubtitleTitle, getSwitchColor() ? {} : { color: "#999" }]}
        showSeparator={false}
        onTintColor={getSwitchColor() ? '' : "rgba(50, 186, 192, 0.3)" }
      />

      {getSwitchColor() && settingData?.call_setting?.switch?.onekeycall ? (
        <View>
          <ListItem
            titleStyle={styles.freeCallTitle}
            containerStyle={{ height: 60 }}
            showSeparator={false}
            title={localStrings["contacts"]}
            value={memberList[selectMemberIndex]?.callName || memberList[selectMemberIndex]?.nickname || localStrings['choose_again']}
            onPress={() => {
              setOnCallDialogVisible(true);
            }}
          />
        </View>
      ) : null}

      {XiaoaiTTSUtil.isSupportedContactsTts() && <View style={styles.separatorTable}>
        <Separator style={{ height: 0.5, marginLeft: SettingStyles.common.padding, marginRight: SettingStyles.common.padding }} />
      </View>}
      {XiaoaiTTSUtil.isSupportedContactsTts() && <ListItemWithSwitch
        title={localStrings["wx_play_contacts_name"]}
        subtitle={localStrings["wx_play_contacts_name_desc"]}
        subtitleNumberOfLines={4}
        unlimitedHeightEnable={true}
        onPress={(_) => {
          if (!getSwitchColor()) {
            return;
          }
        }}
        value={!!settingData?.call_setting?.switch?.callnametts}
        onValueChange={(value) => {
          if (!getSwitchColor()) {
            setSettingData({ ...settingData });
            return;
          }

          setSwitch('callnametts', value);
        }}
        titleStyle={[styles.freeCallTitle, getSwitchColor() ? {} : { color: "#999" }]}
        subtitleStyle={[styles.freeCallSubtitleTitle, getSwitchColor() ? {} : { color: "#999" }]}
        showSeparator={false}
        onTintColor={getSwitchColor() ? '' : "rgba(50, 186, 192, 0.3)" }
      />}
  
      {wxCancelMsg()}
      {onCallDialog()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : "rgba(255, 255, 255, 1)",
    flex: 1
  },
  titleContainer: {
    height: 32,
    backgroundColor: Util.isDark() ? "#xm000000" : "white",
    justifyContent: "center",
    paddingLeft: SettingStyles.common.padding
  },
  title: {
    fontSize: 12,
    color: "#8c93b0",
    lineHeight: 14
  },
  separatorTable: {
    height: 40,
    justifyContent: "center"
  },
  freeCall: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginLeft: SettingStyles.common.padding,
    marginRight: SettingStyles.common.padding,
    marginBottom: 16,
    marginTop: 16
  },
  freeCallDesc: {
    flex: 1,
    marginRight: 20
  },
  freeCallTitle: {
    fontSize: 16,
    color: "#000000",
    fontWeight: "bold"
  },
  freeCallSubtitleTitle: {
    fontSize: 13,
    color: "rgba(0, 0, 0, 0.6)"
  },
  leftAvatarStyle: {
    width: 40,
    height: 40,
    borderRadius: 22
  }
});



const queryMemberList = () => {
  return new Promise((resolve, reject) => {
    DeviceSettingUtil.getDeviceSettingByPrefix(DeviceSettingUtil.callPrefixKey)
      .then((res) => {
        if (res.code == 0) {
          console.log(res, "res.result");
       
          let settingsData = res.result.settings;
          if (settingsData && settingsData.call_setting) {
            let data = JSON.parse(settingsData.call_setting);
             
            let contacts = [];
            Object.keys(data).forEach((key) => {
              if (key.indexOf("key") != -1 && key !== 'onekeycalluser' && key !== 'onekeycalluserid') {
                let item = data[key];
                item.key = key;
                item.index = item.index || 0;
                contacts.push(item);
              }
            });
            // 取头像
            Object.keys(settingsData).forEach((key) => {
              if (key.indexOf("call_key") != -1) {
                // 图片后缀key,需要匹配到联系人中去
                let iconSuffixKey = key.substring(key.lastIndexOf("_") + 1);
                let iconData = JSON.parse(settingsData[key]);

                contacts.map((item) => {
                  if (item.key === iconSuffixKey) {
                    item.icon = iconData.icon;
                    item.fdsUrl = iconData.fdsUrl ? iconData.fdsUrl : "";
                    item.objName = iconData.objName ? iconData.objName : "";
                    return item;
                  }
                  return item;
                });
              }
            });

            // 先去重
            let hash = {};
            contacts = contacts.reduce((item, next) => {
              // hash[next['mijia']] ? '' : ((hash[next['mijia']] = true) && item.push(next));
              if (!hash[next["mijia"]]) {
                hash[next["mijia"]] = true;
                item.push(next);
              }
              return item;
            }, []);

            // 按index排序 升序
            contacts.sort((a, b) => a.index - b.index);
            contacts.forEach((item, index) => {
              if (item.mijia == Service.account.ID) {
                contacts.unshift(contacts.splice(index, 1)[0]);
              }
            });
            downloadImage(contacts).then((contactsData) => {
              resolve({ settingsData: settingsData, contacts: contactsData || [] });
            });
          } else {
            resolve({ settingsData: settingsData, contacts: [] });
          }
        }
      })
      .catch((error) => {
        console.log("======= error: ", error);
        reject({});
      });
  });
};

const downloadImage = async(contacts) => {
  if (contacts.length === 0) {
    return;
  }
  for (let i = 0; i < contacts.length; ++i) {
    try {
      let item = contacts[i];
      if (item.objName) {
        if (XiaoaiTTSUtil.CURRENT_VERSION >= XiaoaiTTSUtil.MIN_CONTACTS_AVATAR_FW_VERSION) {
          item.localFileName = await FDSUtil.getLocalImgUrlNew(item.objName);
        } else {
          item.localFileName = await FDSUtil.getLocalImgUrl(item.objName);
        }
      }
    } catch (err) {
      console.log("download fds error", err);
      LogUtil.logOnAll(TAG, `download fds Imag ${ JSON.stringify(err) }`);
    }
  }
  return contacts;
};


export default WXCallSettingMoreView;