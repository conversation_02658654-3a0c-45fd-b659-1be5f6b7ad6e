import { DarkMode } from 'miot';

export const EVENT_TYPE = {
  EVENT_TYPE_DEFAULT: 1 << 0,
  EVENT_TYPE_AREA_MOTION: 1 << 1,
  EVENT_TYPE_PEOPLE_MOTION: 1 << 2,
  EVENT_TYPE_LOUDER_SOUND: 1 << 3,
  EVENT_TYPE_FACE: 1 << 4,
  EVENT_TYPE_BABY_CRY: 1 << 5,
  EVENT_TYPE_PET_MOTION: 1 << 6,
  EVENT_TYPE_AI: 1 << 7,
  EVENT_TYPE_CAMERA_CALLING: 1 << 8
};

Object.freeze(EVENT_TYPE);
let isDark = DarkMode.getColorScheme() == "dark";

export const EVENT_TYPE_COLOR = {
  motionSelectedColor: "#FDC541",
  unselectedColor: isDark ? "#474747" : "#F7F7F7",
  peopleSelectedColor: "#44CECA",
  loudSoundSelectedColor: "#F29E60",
  babyCrySelectedColor: "#9B91FF",
  cameraCallingSelectedColor: "#6395FF",
  faceSelectedColor: "#2DB0FF",
  aiSelectedColor: "#7DA6E0",
  petSelectedColor: "#75DF6E"
};

Object.freeze(EVENT_TYPE_COLOR);

const SD_AI_TYPE = 50; // 云存会用到，sd卡无
const SD_Emotion = 41;
const SD_WokeUp = 40;
const SD_Asleep = 39;
const SD_CoveredFace = 38;
const SD_LOUDER_SOUND = 13;
const SD_PeopleCough = 24;
const SD_FencePass = 10;
const SD_FenceIn = 20;
const SD_FenceOut = 21;
const SD_ABNORMAL_SOUND = 9; // 异响
const SD_CAMERA_CALLING = 8;
const SD_BABY_CRY = 7;
const SD_KNOWN_FACE = 5;
const SD_FACE = 4;
const SD_PEOPLE_MOTION = 1;
const SD_OBJECT_MOTION = 0;
const SD_DEFAULT = -1;
const SD_CAT = 2;
const SD_DOG = 3;
export const SD_PRIORITY = {
  [SD_DEFAULT]: 0,
  [SD_OBJECT_MOTION]: 1,
  [SD_Emotion]: 2,
  [SD_ABNORMAL_SOUND]: 3,
  [SD_LOUDER_SOUND]: 3,
  [SD_PEOPLE_MOTION]: 4,
  [SD_Asleep]: 5,
  [SD_WokeUp]: 6,
  [SD_FencePass]: 7,
  [SD_FenceOut]: 8,
  [SD_FenceIn]: 9,
  [SD_FACE]: 10,
  [SD_KNOWN_FACE]: 11,
  [SD_DOG]: 12,
  [SD_CAT]: 12,
  [SD_PeopleCough]: 13,
  [SD_BABY_CRY]: 14,
  [SD_CoveredFace]: 15,
  // [SD_ELDER_FALL]: 12,
  [SD_AI_TYPE]: 49,
  [SD_CAMERA_CALLING]: 50
};
// 报警视频列表里的事件优先级排列， 可能有多个，需要逆序排列。
export const ALARM_EVENT_TYPE = {
  "Pet": 7, // 宠物是最后的
  "AI": 6,
  "Face": 4,
  "PeopleMotion": 2,
  "KnownFace": 3,
  "BabyCry": 5,
  "ObjectMotion": 1
};

Object.freeze(ALARM_EVENT_TYPE);

// 固件里的事件类型定义
export const TIMELINE_EVENT_TYPE = {
  "ObjectMotion": SD_OBJECT_MOTION,
  "PeopleMotion": SD_PEOPLE_MOTION,
  // cat 2  dog 3 skip;
  "Face": SD_FACE,
  "KnownFace": SD_KNOWN_FACE,
  // old man 6 skip
  "Pet": SD_DOG, // any one presents for pets is ok.
  "BabyCry": SD_BABY_CRY,
  "AI": SD_AI_TYPE, // 固件里并没有该类型，强制塞的一个
  "LouderSound": SD_LOUDER_SOUND, // 固件里并没有该类型，强制塞的一个
  "CameraCalling": SD_CAMERA_CALLING // 固件里并没有该类型，强制塞的一个
};

Object.freeze(TIMELINE_EVENT_TYPE);

export default { SD_CAMERA_CALLING, SD_AI_TYPE, SD_LOUDER_SOUND, SD_BABY_CRY, SD_KNOWN_FACE, SD_FACE, SD_PEOPLE_MOTION, SD_OBJECT_MOTION, SD_DEFAULT, SD_CAT, SD_DOG };