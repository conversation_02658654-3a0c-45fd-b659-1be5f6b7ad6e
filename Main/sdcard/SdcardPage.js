'use strict';

import React from 'react';
import { ActivityIndicator, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, Platform, PermissionsAndroid, SafeAreaView } from 'react-native';

import { Device, Host, System } from 'miot';


import Toast from '../components/Toast';
import { MessageDialog } from "mhui-rn";

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';

import SdFileManager, { DownloadVideoState } from './util/SdFileManager';
import EventTypeConfig from './util/EventTypeConfig';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import CancelableProgressDialog from '../ui/CancelableProgressDialog';
import TrackUtil from '../util/TrackUtil';
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import DldMgr from '../framework/DldMgr';
import AlbumHelper from '../util/AlbumHelper';
import { DldStatus } from '../framework/EventLoaderInf';
import CommonMsgDialog from '../ui/CommonMsgDialog';
import { DarkMode } from 'miot/Device';
import Util from "../util2/Util";
import { DescriptionConstants } from '../Constants';
import { NavigationBar } from 'mhui-rn';

export default class SdcardPage extends React.Component {

  state = {
    pstate: -1,
    error: -1,
    isSelectMode: false,
    index: 0,
    selectedDayIndex: -1,
    isEmpty: true,
    isCurrentDayEmpty: true,
    calendarDays: [],
    sdcardFiles: [],
    dialogVisible: false,
    loading: false,
    isSelectAll: false,
    showLoading: true,

    permissionRequestState: 0,
    showPermissionDialog: false
  };

  constructor(props) {
    super(props);
    this.isDelete = false;
    this.isSupportNewStorage = CameraConfig.shouldDisplayNewStorageManage(Device.model);
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
        this.setState({ index: 1 });
        this._onGetData();

        // 视频下载回调。
        if (this.isSupportNewStorage) {
          this.sdcardVideoDownload = DldMgr.addListener((status) => {
            // console.log("download status:test", status);
            this.videoFileListener();
          });
        } else {
          // 老的sdcard视频下载走单独的逻辑。
        }

        this.fileListListener = SdcardEventLoader.getInstance().addListener(() => {
          this._onGetData();
        });

        self.enterPlaybackTime = new Date().getTime();
      }
    );
    this.didBlur = this.props.navigation.addListener(
      'willBlur',
      () => {
        this.sdcardVideoDownload && this.sdcardVideoDownload.remove();
        this.sdcardVideoDownload = null;
        this.fileListListener && this.fileListListener.remove();
        this.fileListListener = null;

        this.isPageForeGround = false;
        let playbackTime = (new Date().getTime() - self.enterPlaybackTime) / 1000;
        TrackUtil.reportResultEvent("Camera_Playback_Time", "Time", playbackTime); // Camera_Playback_Time
      }
    );
    this.dayList = null;

    this.isSupportDownload = CameraConfig.shouldDownloadSdcardFile(Device.model);

    this.downloadingTimestamps = [];
    this.shouldDownload = true;
    this.destroyed = false;
    this.selectCount = 0;

  }


  componentWillUnmount() {
    this.destroyed = true;
    clearTimeout(this.scrollTimeout);// 避免退出页面了 还操作ui。
    this.didFocusListener.remove();
    this.didBlur.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.fileListListener && this.fileListListener.remove();
    this.fileListListener = null;
    this.sdcardVideoDownload && this.sdcardVideoDownload.remove();
    this.sdcardVideoDownload = null;
    this.shouldDownload = false;
  }

  setNavigation(isSelectAll, isSelectMode, isDisableSelect) {
    // console.log("cheng" + "setNavigation");
    if (Device.isReadonlyShared) {
      isDisableSelect = true;
    }

    let title = isSelectMode ? (this.selectCount <= 0 ? LocalizedStrings["action_select"] : LocalizedStrings["selected_count"].replace("%1$d", this.selectCount)) : LocalizedStrings["sdcard_manage"];
    this.props.navigation.setParams({

      title: title,

      left: [
        {
          key: isSelectMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.isSelectMode) {// 如果已经是选择模式，点左边 应该退出选择模式
              this.onSelectAllChanged(false);// 将isSelected重置
              this.setNavigation(false, false, false);
              this.setState({ isSelectMode: false });
            } else { // 不是选择模式 就退出吧
              this.props.navigation.goBack();
            }
          }
        }
      ],
      right: !isDisableSelect ?
        [
          {
            key: !isSelectMode ? NavigationBar.ICON.EDIT : (isSelectAll ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL),
            onPress: () => {
              if (!this.state.isSelectMode) { //进入选择模式
                this.setState(() => { return { isSelectMode: true }; }, () => { this.setNavigation(false, true, false); });
              } else if (this.state.isSelectAll) { //退出全选模式
                this.onSelectAllChanged(false);
              } else { //进入全选模式
                this.onSelectAllChanged(true);
              }
            }
          }
        ] : [],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      },

      // titleProps: {
      //   title: title,
      //   isSelectAll: isSelectAll,
      //   isSelectMode: isSelectMode,
      //   disableSelect: isDisableSelect,
      //   onEnterSelectMode: () => {
      //     this.setState(() => { return { isSelectMode: true }; }, () => { this.setNavigation(false, true, false); });
      //   },
      //   onExitSelectMode: () => {
      //     this.onSelectAllChanged(false);// 将isSelected重置
      //     this.setNavigation(false, false, false);
      //     this.setState({ isSelectMode: false });
      //   },
      //   onSelectAll: () => {
      //     this.onSelectAllChanged(true);
      //   },
      //   onBackPress: () => {
      //     this.props.navigation.goBack();
      //   },
      //   onUnselectAll: () => {
      //     // console.log("选择0"); this.setNavigation(false); this.setState({ index: 1 }); alert("none")
      //     this.onSelectAllChanged(false);
      //   }
      // }
    });
    // this.props.navigation.setParams({
    //   titleProps: {
    //     // left: [
    //     //   {
    //     //     key: NavigationBar.ICON.BACK,
    //     //     onPress: _ => this.props.navigation.goBack()
    //     //   }
    //     // ],
    //     // title: '常用导航栏',
    //   }
    // });
  }

  render() {
    return (

      <SafeAreaView style={styles.container}>

        {this._renderHeader()}
        {this._renderDayFiles()}
        {this._renderEmptyLayout()}
        {this._renderLoadingView()}
        {this._renderBottomSelectView()}
        {this._renderDialog()}
        {this._renderLoadingDialog()}
        {this._renderPermissionDialog()}
      </SafeAreaView>
    );
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
      }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  _renderDialog() {
    let title = this.isDelete ? LocalizedStrings["delete_title"] : LocalizedStrings["save_title"];
    let btn = this.isDelete ? LocalizedStrings["delete_confirm"] : LocalizedStrings["action_confirm"];
    return (
      <MessageDialog 
        message={title}
        // title={LocalizedStrings["tips"]}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              console.log('onCancel');
              this.isDelete = false;
              this.setState({ dialogVisible: false });
            }
          },
          {
            text: btn,
            callback: () => {
              if (this.isDelete) {
                this.onConfirmDelete();
              } else {
                this.onConfirmSave();
              }
              this.isDelete = false;
              this.setState({ dialogVisible: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ dialogVisible: false });
        }}
        cancelable={true}
        visible={this.state.dialogVisible} />
    );
  }

  _renderLoadingDialog() {
    return (
      <CancelableProgressDialog
        ref={(ref) => {
          this.cancelableProgressDialog = ref;
        }}
        visible={this.state.loading}
        onCancelPress={() => {
          this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
          this.shouldDownload = false;
        }}
        cancelText={LocalizedStrings["action_cancel"]}
        loadingText={this.isDelete ? LocalizedStrings["c_setting"] : LocalizedStrings["action_downloading"]}
        disableOutsideCancel={true}


      />
    );
  }

  _renderBottomSelectView() {
    if (!this.state.isSelectMode) {
      return;
    }

    return (
      <View style={{ width: "100%", height: 40, borderTopColor: "#e5e5e5", borderTopWidth: 1, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" }}>
        <TouchableOpacity
          style={{ width: 90, display: "flex", alignItems: "center" }}
          onPress={() => { this.onPressSave(); }}
          accessible={true}
          accessibilityLabel={DescriptionConstants.hk_1_12}
        >
          <Image
            style={{ width: 25, height: 25 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_download_w.png") : require("../../resources2/images/icon_videorecord_download_b.png")} />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["save_files"]}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{ marginLeft: 30, width: 90, display: "flex", alignItems: "center" }}
          onPress={() => { this.onPressDelete(); }}
          accessible={true}
          accessibilityLabel={DescriptionConstants.hk_1_13}
        >
          <Image
            style={{ width: 25, height: 25 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_delete_w.png") : require("../../resources2/images/icon_videorecord_delete_b.png")} />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["delete_files"]}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  _renderHeader() {
    if (this.state.isEmpty) {
      return null;
    }
    return (
      <View style={{ width: "100%", borderColor: "#e5e5e5", borderTopWidth: 1, borderBottomWidth: 0.5, paddingHorizontal: 10, paddingVertical: 15 }}>
        <FlatList
          ref={(ref) => { this.dayList = ref; }}
          data={this.state.calendarDays}
          renderItem={({ item, index }) => this._renderHeaderItem(item, index)}
          horizontal={true}
          getItemLayout={this._onItemLayout}
          keyExtractor={(item, index) => index.toString()}
          onScrollToIndexFailed={()=>{

          }}
        />
      </View>

    );
  }

  _renderDayFiles() {
    if (this.state.isEmpty || this.state.isCurrentDayEmpty) {
      return null;
    }
    return (
      // <View style={{ flexGrow: 1, marginTop: 22, marginHorizontal: 16 }}>
      <FlatList
        style={{ flexGrow: 1, marginTop: 22, marginHorizontal: 16 }}
        data={this.state.sdcardFiles}
        renderItem={({ item, index }) => this._renderDayFile(item, index)}
        numColumns={3}
        keyExtractor={(item, index) => index.toString()}
        ListFooterComponent={<View style={{ height: 20 }}></View>}
      />
      // </View>

    );

  }

  _renderHeaderItem(item, index) {
    let screenWidth = self.windowWidth;
    let widthItem = (screenWidth - 20) / 7;
    let isSelected = index == this.state.selectedDayIndex;
    let containerStyle = { position: "relative", width: widthItem, height: widthItem };
    let textContainerStyles = { position: "absolute", width: widthItem, height: widthItem };
    let textContainerInnerStyle = { width: "100%", height: "100%", display: "flex", alignItems: "center", justifyContent: "center" };
    let backgroundStyles = { position: "absolute", backgroundColor: "#32BAC0", width: widthItem, height: widthItem, borderRadius: widthItem / 2 };
    let textStyle = { fontSize: 12, color: isSelected ? "#ffffff" : "black" };
    return (
      <TouchableOpacity
        style={containerStyle}
        onPress={() => {
          this.setState({ selectedDayIndex: index });
          // 切换日期 退出选择模式。
          this.setState(() => { return { isSelectMode: false }; }, () => {
            this.onSelectAllChanged(false);// 将isSelected重置
            this.setNavigation(false, false, false);
            this.updateSdcardFiles(index);
          });
        }}
      >
        {
          isSelected ?
            <View style={backgroundStyles} />
            : null
        }
        <View
          style={{ textContainerStyles }}
          accessible={true}
          accessibilityState={{
            selected:isSelected
          }}
          >
          <View
            style={textContainerInnerStyle}
            accessible={true}
          >
            <Text
              style={textStyle}>
              {(item.day < 10 ? "0" : "") + item.day}
            </Text>

            <Text
              style={[textStyle, { marginTop: 2 }]}
            >
              {LocalizedStrings[`month_${item.month}`]}
            </Text>
          </View>

        </View>

      </TouchableOpacity>
    );
  }

  _renderDayFile(item, index) {
    let marginHorizontal = 3.5;
    let screenWidth = self.windowWidth;
    let containerWidth = (screenWidth - 33 - marginHorizontal * 6) / 3;
    let path = SdFileManager.getInstance().getImageFilePath(item.startTime);
    let source = (path == null) ? require("../../Resources/Images/icon_camera_file_loading.png") : ({ uri: `file://${Host.file.storageBasePath}${path}` });

    return (
      <TouchableOpacity
        style={{ width: containerWidth, height: 95, paddingBottom: 10, marginLeft: 3.5, marginRight: 3.5 }}
        onPress={() => this._onPressVideoFileView(index)}
        accessible={true}
        accessibilityLabel={DescriptionConstants.hk_1_8}
      >

        <View style={{ width: "100%", height: 60, marginBottom: 3, position: "relative" }} >
          <Image style={{ width: "100%", height: "100%", borderRadius: 5, resizeMode: "stretch" }}
            source={source}
          >
          </Image>
          {
            this.state.isSelectMode ?
              <Image
                accessible={true}
                accessibilityLabel={DescriptionConstants.hk_1_9}
                accessibilityState={{
                  selected:item.isSelected
                }}
                style={{ width: 20, height: 20, position: "absolute", bottom: 4, right: 4 }}
                source={item.isSelected ? require("../../Resources/Images/icon_selected.png") : require("../../Resources/Images/icon_unselected.png")}
              /> :
              null
          }

          {
            item.save == 1 ?
              <Image
                style={{ width: 16, height: 16, position: "absolute", bottom: 4, left: 4 }}
                source={require("../../Resources/Images/icon_lock.png")}
              />
              :
              null
          }
        </View>
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
          {this._renderFileFlagIcons(item)}

          <Text
             accessible={true}
             accessibilityLabel={DescriptionConstants.hk_1_10}
            style={{ marginLeft: 4, marginRight: 4, fontSize: 11, color: "#000000" }}
          >
            {`${item.hour > 9 ? `${item.hour}` : `0${item.hour}`}:00`}
          </Text>

        </View>


      </TouchableOpacity>
    );
  }

  _renderFileFlagIcons(item) {
    let eventTypes = item.eventTypes;

    return (
      eventTypes.map((eventType, index) => {
        if (eventType < EventTypeConfig.SD_OBJECT_MOTION) {
          return;
        }

        let source = null;
        if (eventType == EventTypeConfig.SD_PEOPLE_MOTION) {
          source = require("../../Resources/Images/icon_event_type_people_run.png");
        } else if (eventType == EventTypeConfig.SD_BABY_CRY) {
          source = require("../../Resources/Images/icon_event_type_baby_cry.png");
        } else if (eventType == EventTypeConfig.SD_DOG || eventType == EventTypeConfig.SD_CAT) {
          source = require("../../Resources/Images/icon_event_type_pet.png");
        } else if (eventType == EventTypeConfig.SD_FACE) {
          source = require("../../Resources/Images/icon_event_type_unknown_people.png");
        } else if (eventType == EventTypeConfig.SD_KNOWN_FACE) {
          source = require("../../Resources/Images/icon_event_type_known_people.png");
        } else if (eventType == EventTypeConfig.SD_CAMERA_CALLING) {
          source = require("../../Resources/Images/icon_event_type_camera_calling.png");
        } else if (eventType == EventTypeConfig.SD_LOUDER_SOUND) {
          source = require("../../Resources/Images/icon_event_type_louder.png");
        } else if (eventType == EventTypeConfig.SD_OBJECT_MOTION) {
          source = require("../../Resources/Images/icon_event_type_object_motion.png");
        }
        if (source == null) {
          return null;
        }
        return (
          <Image 
            accessible={true}
            accessibilityLabel={DescriptionConstants.hk_1_11}
            style={{ width: 14, height: 14, marginLeft: 5 }}
            source={source} 
            key = {index}
          />
        );
      })

    );
  }
  _renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View

        style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );

  }
  _renderEmptyLayout() {
    if ((this.state.isEmpty || this.state.isCurrentDayEmpty) && !this.state.showLoading) {
      return (
        <View
          style={{ width: "100%", flexGrow: 1, display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            accessible={true}
            accessibilityLabel={DescriptionConstants.hk_1_18}
            source={require("../../Resources/Images/icon_camera_empty_files.png")}
            style={{ width: 79, height: 79 }}
          />
          <Text
            accessible={true}
            accessibilityLabel={DescriptionConstants.hk_1_19}
            style={{ fontSize: 14, color: "#808080" }}
          >
            {LocalizedStrings["no_files"]}
          </Text>
        </View>
      );
    } else {
      return null;
    }

  }

  _onItemLayout = (data, index) => {
    let screenWidth = self.windowWidth;
    let widthItem = (screenWidth - 20) / 7;

    return {
      length: (widthItem),
      offset: (widthItem * index),
      index
    };
  }

  componentDidMount() {
    self.windowWidth = Dimensions.get("window").width;
    self.windowHeight = Dimensions.get("window").height;
    if (self.windowHeight < self.windowWidth) {
      let sw = self.windowWidth;
      self.windowWidth = self.windowHeight;
      self.windowHeight = sw;
    }
    this.setNavigation(false, false, false);
    this.setState({ index: 1 });

    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  _bindFilesHandler = (status) => {
    this._onGetData();
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      return false;// 不接管
    }
  }

  _onPressVideoFileView(index) {
    if (!this.state.isSelectMode) {

      let hour = this.state.sdcardFiles[index].hour;
      let tag = this.state.calendarDays[this.state.selectedDayIndex].tag;
      let dayStartTime = this.state.calendarDays[this.state.selectedDayIndex].timeStamp;
      this.props.navigation.navigate('SdcardHourPage', { tag: tag, hour: hour, dayBeginTime: dayStartTime });
    } else {
      let sdcardFile = this.state.sdcardFiles[index];
      sdcardFile.isSelected = !sdcardFile.isSelected;
      let selectedCount = 0;
      for (let file of this.state.sdcardFiles) {
        if (file.isSelected) {
          selectedCount++;
        }
      }
      if (selectedCount == 0) {
        this.onSelectAllChanged(false);
      } else if (selectedCount == this.state.sdcardFiles.length) {
        this.onSelectAllChanged(true);
      } else {
        this.setState({ sdcardFiles: this.state.sdcardFiles });// 刷新页面 状态不要保留在ui控件里      
        this.selectCount = selectedCount;
        this.setState(() => { return { isSelectAll: false }}, () => { 
          this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);
        });
      }
    }
  }


  onSelectAllChanged(isSelectAll) {
    this.selectCount = isSelectAll ? this.state.sdcardFiles.length : 0;
    this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.setState({ index: isSelectAll ? 0 : 1 });
    for (let timeHourItem of this.state.sdcardFiles) {
      timeHourItem.isSelected = isSelectAll ? true : false;
    }
    this.setState({ sdcardFiles: this.state.sdcardFiles, isSelectAll: isSelectAll });
  }

  _onGetData() {
    this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.updateHeaderDatas();
    // 当前选中的是哪个日期
    // this.updateSdcardFiles(this.state.selectedDayIndex);
  }

  updateSdcardFiles(index) {
    let tag = this.state.calendarDays[index].tag;
    // console.log(`cheng gettimeitemday()${ tag }`);

    if (tag == null) {
      this.setNavigation(false, false, true);
      this.setState({ isCurrentDayEmpty: true, isSelectMode: false, isSelectAll: false });
      this.setState({ index: 0 });
      return;
    }
    let timeItemDay = SdFileManager.getInstance().getTimeItemDay(tag);
    if (timeItemDay == null || timeItemDay.timeItemHourList == null || timeItemDay.timeItemHourList.length == 0) { // 当前选中日期没有数据
      //
      this.setNavigation(false, false, true);
      this.setState({ isCurrentDayEmpty: true, isSelectMode: false, isSelectAll: false });

      this.setState({ index: 0 });
      return;
    }

    // 选中日期有数据
    let timeHourItems = [];
    let timestampList = [];
    for (let j = 0; j < timeItemDay.timeItemHourList.length; j++) {
      let timeItemHour = timeItemDay.timeItemHourList[j];
      for (let timeHourItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
        if (timeHourItem.hour === timeItemHour.hour) {
          timeItemHour.isSelected = timeHourItem.isSelected;
          break;
        }
      }
      if (timeItemHour != null && timeItemHour.timeItemList != null && timeItemHour.timeItemList.length > 0) {
        let k = 0;
        let eventTypes = [];
        for (k = 0; k < timeItemHour.timeItemList.length; k++) {
          // console.log(k);
          let timeItem = timeItemHour.timeItemList[k];
          if (timeItem.save == 1) {
            timeItemHour.save = 1;
          }
          let eventType = timeItem.eventType;
          let shouldAdd = true;
          for (let ii = 0; ii < eventTypes.length; ii++) {
            let value = eventTypes[ii];
            if (eventType == value) {
              shouldAdd = false;
              break;
            }
          }
          if (shouldAdd) {
            eventTypes.push(timeItem.eventType);
          }
        }
        eventTypes.sort((a, b) => {
          return b - a;
        });
        let index = -1;
        for (let i = 0; i < eventTypes.length; i++) {
          if (eventTypes[i] <= EventTypeConfig.SD_OBJECT_MOTION) {
            index = i;
            break;
          }
        }
        if (index >= 2) {
          eventTypes = eventTypes.slice(0, 2);
        } else if (index == 1) {
          eventTypes = eventTypes.slice(0, 1);
        } else if (index == 0) {
          // 第一个等于0 置空
          eventTypes = [];
        } else { // -1 说明没有object_motion   如果多余2个，就截取2，如果小于等于2，就不改变了
          eventTypes = eventTypes.length > 2 ? eventTypes.slice(0, 2) : eventTypes;
        }
        timeItemHour.eventTypes = eventTypes;

        timeHourItems.push(timeItemHour);
        let path = SdFileManager.getInstance().getImageFilePath(timeItemHour.startTime);
        if (path == null) {
          timestampList.push(timeItemHour.startTime);
        }
      }
    }
    if (!this.state.isSelectMode) {
      this.onSelectAllChanged(false);// 去除全选
    }
    this.setState({ sdcardFiles: timeHourItems, isEmpty: false, showLoading:false,  isCurrentDayEmpty: false, isSelectMode: this.state.isSelectMode, isSelectAll: this.state.isSelectAll });// 数据刷新了
    this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);// 走到这里 数据肯定不是空的了。直接传false。
    this.setState({ index: 0 });
    this.downloadThumbs(timestampList);
  }

  async downloadThumbs(timestampList) {
    let lastNotifyTime = Date.now();
    if (timestampList.length > 0) {
      for (let i = 0; i < timestampList.length; i++) {
        try {
          await SdcardEventLoader.getInstance().getThumb({ imgStoreId: timestampList[i] });
          if (Date.now() - lastNotifyTime < 1000) {
            continue;
          }
          lastNotifyTime = Date.now();
          // if (i % 5 == 0) { // 下载成功 通知一下。
            this.onReceiveFile(timestampList[i]);
          // }
        } catch (err) {

        }
      }
      this.onReceiveFile(timestampList[0]);// 下载完毕后，通知刷新。
    }
  }


  onReceiveFile = (timestamp, status) => {
    // checkTimestampRange
    let files = this.state.sdcardFiles;
    if (files == null || files.length == 0) {
      return;
    }
    if (files[0].startTime <= timestamp && files[files.length - 1].startTime >= timestamp) { // 只有在这中间的才刷新
      this.setState({ index: (this.state.index > 100 ? 0 : this.state.index + 1) });// 刷新页面
    }
  }

  updateHeaderDatas() {
    let timeItemDays = SdFileManager.getInstance().getTimeItemDays();// 强制刷新一遍
    if (timeItemDays == null || timeItemDays.length <= 0) {
      this.setState({ isEmpty: true });
      return;
    }
    // if (this.state.calendarDays.length > 0) { // 已经加载过头部了
    //   return;
    // }

    let topDayDatas = [];// 顶部的日期
    if (timeItemDays.length < 7) { // 补全
      let startTime = timeItemDays[0].startTime;
      let tmpDate = new Date();
      for (let i = 7 - timeItemDays.length; i > 0; i--) {
        let topDayData = {};
        let timeStamp = startTime - i * (24 * 60 * 60 * 1000);
        tmpDate.setTime(timeStamp);
        let month = tmpDate.getMonth() + 1;
        let day = tmpDate.getDate();
        topDayData.month = month;
        topDayData.day = day;
        topDayData.timeStamp = 0;
        topDayData.tag = 0;
        topDayDatas.push(topDayData);
      }
    }
    for (let i = 0; i < timeItemDays.length; i++) {
      let timeItemDay = timeItemDays[i];
      let topDayData = {};
      topDayData.month = timeItemDay.month;
      topDayData.day = timeItemDay.day;
      topDayData.timeStamp = timeItemDay.startTime;
      topDayData.tag = timeItemDay.tag;
      topDayDatas.push(topDayData);
    }

    let calendarDays = topDayDatas;
    let selectedDayIndex = this.state.selectedDayIndex < 0 ? topDayDatas.length - 1 : (this.state.selectedDayIndex > topDayDatas.length - 1 ? topDayDatas.length - 1 : this.state.selectedDayIndex);
    let isEmpty = false;

    this.setState((state) => {
      return {
        calendarDays: calendarDays,
        selectedDayIndex: selectedDayIndex,
        isEmpty: isEmpty
      };
    }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
      this.updateSdcardFiles(selectedDayIndex);
      clearTimeout(this.scrollTimeout);
      this.scrollTimeout = setTimeout(() => {
        if (this.state.selectedDayIndex < 0) {
          return;
        }
        if (this.dayList == null || this.destroyed) {
          return;
        }
        this.dayList.scrollToIndex({
          animated: true,
          index: this.state.selectedDayIndex
        });
      }, 500);
    });
  }

  onPressSave() {
    let timeItems = [];
    for (let timeHourItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeHourItem.isSelected) {
        timeItems = timeItems.concat(timeHourItem.timeItemList);
      }
    }
    if (timeItems == null || timeItems.length == 0) {
      Toast.success("bottom_action_tip");
      return;
    }

    if (this.isSupportDownload) {
      this._startDownload(timeItems);
      return;
    }

    for (let i = timeItems.length - 1; i >= 0; i--) {
      if (timeItems[i].save == 1) {
        timeItems.splice(i, 1);
      }
    }
    if (timeItems.length == 0) {
      Toast.success("bottom_save_tip");
      return;
    }
    this.isDelete = false;
    this.setState({ dialogVisible: true });


  }

  onConfirmDelete() {
    let timeItems = [];
    for (let timeHourItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeHourItem.isSelected) {
        timeItems = timeItems.concat(timeHourItem.timeItemList);
      }
    }
    
    this.onSelectAllChanged(false);//退出全选
    this.setState({
      isSelectMode: false,
    });

    SdFileManager.getInstance().startDeleteFiles(timeItems)
      .then(() => {
        this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
        this._onGetData();
        Toast.success("delete_success");
        this.isDelete = false;
      })
      .catch((err) => {
        this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
        Toast.fail("delete_failed", err);
        this.isDelete = false;
      });

  }

  _startDownload(selectedTimeItems) {

    this.setState((state) => {
      return {
        isSelectMode: false
      };
    }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
      this.onSelectAllChanged(false);
    });

    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          // console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartDownloadFiles(selectedTimeItems);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartDownloadFiles(selectedTimeItems);
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    }

  }

  _realStartDownloadFiles(timeItems) {

    let timeList = timeItems;

    if (this.isSupportNewStorage) {
      let times = timeList.map((item) => {
        let startTime = item.startTime;
        let localUrls = SdcardEventLoader.getInstance().getThumbUrlByImgStoreId(startTime);
        return { fileId: startTime, mediaType: "sdcard", createTime: startTime, imgStoreUrl: localUrls.thumbUrl, videoUrl: localUrls.videoUrl, playCfg: { loader: SdcardEventLoader.getInstance() } };// 缩略图，下载路径，创建时间，fileId
      });
      DldMgr.addDld(times, SdcardEventLoader.getInstance());
    } else {
      let times = timeList.map((item) => {
        let startTime = item.startTime;
        let localUrls = SdcardEventLoader.getInstance().getThumbUrlByImgStoreId(startTime);
        return startTime;// 缩略图，下载路径，创建时间，fileId
      });
      this.downloadingTimestamps = times;
      this.cancelableProgressDialog && this.cancelableProgressDialog.show();
      this.shouldDownload = true;
      this.tryDownloadWithDirectWay();
    }

  }

  async tryDownloadWithDirectWay() {

    for (let i = 0; i < this.downloadingTimestamps.length && this.shouldDownload; i++) {
      let timestamp = this.downloadingTimestamps[i];
      try {
        await new Promise((resolve, reject) => {
          SdcardEventLoader.getInstance()
            .download({ fileId: timestamp }, null, {
              onDldProgress: (state) => {
                if (state == DldStatus.Complete) {
                  resolve();
                } else if (state == DldStatus.Err) {
                  reject();
                }
              }
            });
        });
        let { videoUrl } = SdcardEventLoader.getInstance().getThumbUrlByImgStoreId(timestamp);
        if (!this.shouldDownload) {
          break;
        }
        await AlbumHelper.saveToAlbum(videoUrl, true);

      } catch (err) {
        // console.log(`download video and save error:${ timestamp }`);
        Toast.fail("save_faild", err);
        this.shouldDownload = false;
        this.cancelableProgressDialog.hide();
        break;
      }

    }
    if (this.shouldDownload) {
      Toast.success("save_success");
      this.shouldDownload = false;
    }
    this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
  }

  videoFileListener = (timestamp, state) => {
    // console.log("all video downloaded, begin To hide dialog");

    // if (state == DownloadVideoState.DOWNLOADED) {
    //   let index = this.downloadingTimestamps.indexOf(timestamp);
    //   if (index >= 0) {
    //     this.downloadingTimestamps.slice(index, 1);
    //   }
    //   if (this.downloadingTimestamps.length == 0) {
    //     this.cancelableProgressDialog.hide();
    //     Toast.success("save_success");
    //   }
    // // } else if (state == DownloadVideoState.FAILED) {
    //   let index = this.downloadingTimestamps.indexOf(timestamp);
    //   if (index >= 0) {
    //     if (index >= 0) {
    //       this.downloadingTimestamps.slice(index, 1);
    //     }
    //     if (this.downloadingTimestamps.length == 0) {
    //       this.cancelableProgressDialog.hide();
    //       // Toast.success("save_success");
    //     }
    //     // this.downloadingTimestamps.slice(index, 1);
    //     // Toast.fail("save_fail");
    //   }

    // }
  }


  onPressDelete() {
    let timeItems = [];
    for (let timeHourItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeHourItem.isSelected) {
        timeItems = timeItems.concat(timeHourItem.timeItemList);
      }
    }
    if (timeItems == null || timeItems.length == 0) {
      Toast.success("bottom_action_tip");
      return;
    }
    this.isDelete = true;
    this.setState({ dialogVisible: true });
  }

  onConfirmSave() {
    let timeItems = [];
    for (let timeHourItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeHourItem.isSelected) {
        timeItems = timeItems.concat(timeHourItem.timeItemList);
      }
    }
    if (timeItems == null || timeItems.length == 0) {

      return;
    }
    for (let i = timeItems.length - 1; i >= 0; i--) {
      if (timeItems[i].save == 1) {
        timeItems.splice(i, 1);
      }
    }
    if (timeItems.length == 0) {
      return;
    }

    SdFileManager.getInstance().startSaveFiles(timeItems)
      .then(() => {
        this.setState((state) => {
          return {
            isSelectMode: false
          };
        }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
          this.onSelectAllChanged(false);
          this._onGetData();
        });
        Toast.success("save_success");
      })
      .catch((err) => {
        Toast.fail("save_faild", err);
      });

  }
}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  }
});
