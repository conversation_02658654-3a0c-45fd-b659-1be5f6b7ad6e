import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList } from 'react-native';
import Util from '../../util2/Util';
import { BaseStyles } from "../../BasePage";
import { localStrings as LocalizedStrings } from "../../MHLocalizableString";
import { DescriptionConstants } from '../../Constants';
import Toast from "../../components/Toast";
import { DarkMode } from 'miot'

const Padding = 12;
const NumColumns = 7;
const itemSpace = 4;
const TAG = "DateFilterViewV2";
export default class DateFilterViewV2 extends React.Component {
  flatListRef = null
  beginOffset = 0;
  endOffset = 0;
  constructor(props, context) {
    super(props, context);

    this.isDark = DarkMode.getColorScheme() === "dark";
  }

  componentDidUpdate(prevProps) {
    
    if (prevProps.currentStopIndex != this.props.currentStopIndex) {
      if (this.props.currentStopIndex > -1 && this.flatListRef && this.flatListRef.scrollToIndex) {
        // 因为7天为一屏显示，所以只需要计算当前index是7的几倍就可以了
        // const remainder = (this.props.currentStopIndex+1) % 7;
        // const index = remainder > 0 ? Math.floor(this.props.currentStopIndex / 7) : Math.floor(this.props.currentStopIndex / 7) - 1  ;
        // 0-6
        // 7-13
        // 14-20
        // 21-27
        const index = Math.floor(this.props.currentStopIndex / 7);
        setTimeout(() => {
          this.flatListRef.scrollToIndex({ index })
        }, 50);
      }
    }
  }

  render() {
    let itemWidth = (this.props.totalWidth - 2 * Padding - itemSpace * (NumColumns + 1)) / NumColumns;
    // console.log(TAG, this.props.dataSource.length, this.props.dataSource);
    if (this.props.dataSource && this.props.dataSource.length > 0) {
      const chunkedData = [];
      const dataSource = this.props.dataSource;
      for (let i = 0; i < dataSource.length; i += 7) {
        const temp = dataSource.slice(i, i + 7);
        chunkedData.push(temp)
      }
      // console.log("� ~ file: DateFilterViewV2.js ~ line 18 ~ DateFilterViewV2 ~ render ~ dataSource", chunkedData)
      let now = (new Date()).getTime();
      let date = null;
      let fmt = LocalizedStrings["mmdd"];
      
      
      for (let item of this.props.dataSource) {
        if (item.selected) {
          date = item.ts;
          break;
        }
      }
      let dayM = Util.getMoment(date / 1000);
      let nowM = Util.getMoment(now / 1000);
      let extra = dayM.dayOfYear() == nowM.dayOfYear() ? ` ${ LocalizedStrings["date_filter_today"] }` : "";
      // console.log("🚀 ~ file: DateFilterViewV2.js ~ line 32 ~ DateFilterViewV2 ~ render ~ extra", extra)
      let dStr = dayM.format(fmt) + extra;
      return (
        <View style={[BaseStyles.column, { alignItems: "flex-start", paddingTop: 10 }]} onLayout={this.props.onLayout}>
          {/* add addtional view to fix ios lack textAlignVertical */}
          <View
            style={{ height: 34, marginLeft: 28, justifyContent: "center" }}
          >
            <Text style={[BaseStyles.text12, { color: "#8C93B0", fontWeight: "bold" }]}>{dStr}</Text>
          </View>
          {
            // if this.props.dataSource change too fast can cause FlatList only render partial
            this.props.dataSource == null || 0 == this.props.dataSource.length ? null :
              <FlatList
                onLayout={() => {this.flatListRef.scrollToOffset({animated: false, offset: this.props.totalWidth * 4})}}
                showsHorizontalScrollIndicator={false}
                style={{ marginBottom: 13, width: "100%" }}
                ListFooterComponent={() => this.mFooter(itemWidth / 3)}
                // data={this.props.dataSource}  使用下面的，7天一组
                data={chunkedData}
                renderItem={(item) => this.renderDate(itemWidth, item)}
                horizontal={true}
                keyExtractor={(item, index) => index.toString()}
                // initialScrollIndex={5}
                /* 这个代码去掉可能会有问题，以后要注意一下 
                getItemLayout={(data, index) =>
                  ({ length: itemWidth, offset: (itemWidth + itemSpace) * index, index })
                } */
                pagingEnabled={true}
                ref={(ref) => { this.flatListRef = ref; }}
                onScrollBeginDrag={(e) => {
                  // 记录起始偏移量
                  this.beginOffset = e.nativeEvent.contentOffset.x;
                  console.log('beginOffset:', this.beginOffset);
                }}
             
                // // onMomentumScrollEnd在滚动动画结束触发，这样避免了使用onScrollEndDrag时用户抬手时仍滚动的场景带来的endOffset=0的问题（这会导致停在中间）
                // // onScrollEndDrag={}
                onMomentumScrollEnd={(e) => {
                  // 记录抬手时的偏移量
                  this.endOffset = e.nativeEvent.contentOffset.x;
                  console.log('endOffset:', this.endOffset);
                  this.props.scrollModel ? this.scrollByWeek(itemWidth) : null;
                }}
                onScrollToIndexFailed={()=>{

                }}
              
              />
          }
          {this.props.extraHeader ? this.props.extraHeader() : null}
        </View>
      );
    } else {
      return null;
    }
  }

  renderDate = (aItmW, wkDat) => {
    // console.log(TAG, "render item", wkDat.index, wkDat.item);
    let dateStyles = [[styles.cell, { width: aItmW, height: aItmW }],
    [styles.cell, { backgroundColor: '#33ACFF', borderRadius: aItmW, width: aItmW, height: aItmW }]];
    let textStyle = [{ color: this.isDark ? "xm#FFFFFF80" : "rgba(153,153,153,0.4)" }, { color: "#000000" }, { color: this.isDark ? "xm#FFFFFF66" : "rgba(153,153,153,0.4)" }, { color: "#00000066" }, { color: "#FFFFFF" }];

    const wkDaysCpn = wkDat.item.map((aDat, index) => {
      let item = aDat;
      let fmt = LocalizedStrings["yyyymmdd"];
      let date = item.ts || 0;
      let dayM = Util.getMoment(date / 1000);
      let dStr = dayM.format(fmt);
      return (
        <TouchableOpacity
          key={index}
          // disabled={!item.enabled}
          style={[dateStyles[item.selected ? 1 : 0], (wkDat.index === 0 && index === 0) ? {marginLeft:12} : null, { paddingTop: 4, paddingBottom: 4 }]}
          onPress={() => {
            if (!item.enabled) {
              Toast.success('call_date_no_note');
              return;
            }
            this.props.dayButtonPressed(item);
          }}
          accessible={true}
          accessibilityState={{
          selected:item.selected
          }}
        >
          <Text
            style={[BaseStyles.text12, item.selected ? textStyle[4] : item.enabled ? textStyle[0] : textStyle[2]]}
          accessibilityLabel={DescriptionConstants.kj_1_5+item.wkDay}
          >
            {item.wkDay}
          </Text>
          <Text
            accessibilityLabel={dStr}
            style={[BaseStyles.text16, item.selected ? textStyle[4] : item.enabled ? textStyle[1] : textStyle[3], { fontWeight: "bold", paddingTop: 3, paddingBottom: 3 }]}>
            {item.date}
          </Text>
        </TouchableOpacity>
      )
    })
    // console.log(wkDaysCpn)
    return (
      <View style={{flexDirection: 'row', width: this.props.totalWidth, marginLeft:2}}>
        {wkDaysCpn}
      </View>
    )
  }

  mFooter = (itemWidth) => {
    return (<View style={{ width: itemWidth }}>
      <Text> </Text>
    </View>);
  }

  scrollByWeek = (itemWidth) => {
    if (this.endOffset >= this.props.totalWidth * 3.5) {
      // 滑到最后时，再触底不换页
      return
    }

    let coefficient = 0;
    // 滑动超过屏幕宽度2/7时才切换，利用coefficient做系数判断向左还是向右
    if (Math.abs(this.endOffset - this.beginOffset) > (itemWidth + itemSpace) * 2) {
      this.endOffset > this.beginOffset ? coefficient = -1 : coefficient = 1;
    } else {
      coefficient = 0;
    }
    // let finalOffset = coefficient === 0 ? this.beginOffset : this.beginOffset - coefficient * (itemWidth * 7 + itemSpace * 7)
    // this.flatListRef.scrollToOffset({ offset: finalOffset })
    if(this.flatListRef){
      this.flatListRef.scrollToIndex({ animated:false, index: NumColumns * coefficient });
    }
   
  }
}


var styles = StyleSheet.create({
  cell: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: itemSpace / 2,
    marginBottom: itemSpace
  }
});
