import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList } from 'react-native';
import Util from '../../util2/Util';
import { BaseStyles } from "../../BasePage";
import { localStrings as LocalizedStrings } from "../../MHLocalizableString";
import { DescriptionConstants } from '../../Constants';

const Padding = 12;
const NumColumns = 7;
const itemSpace = 4;
const TAG = "DateFilterView";
export default class DateFilterView extends React.Component {

  render() {
    let itemWidth = (this.props.totalWidth - 2 * Padding - itemSpace * (NumColumns + 1)) / NumColumns;
    // console.log(TAG, this.props.dataSource.length, this.props.dataSource);
    if (this.props.dataSource && this.props.dataSource.length > 0) {
      // console.log("🚀 ~ file: DateFilterView.js ~ line 18 ~ DateFilterView ~ render ~ dataSource", this.props.dataSource)
      let now = (new Date()).getTime();
      let date = null;
      let fmt = LocalizedStrings["mmdd"];

      for (let item of this.props.dataSource) {
        if (item.selected) {
          date = item.ts;
          break;
        }
      }
      let dayM = Util.getMoment(date / 1000);
      let nowM = Util.getMoment(now / 1000);
      let extra = dayM.dayOfYear() == nowM.dayOfYear() ? ` ${ LocalizedStrings["date_filter_today"] }` : "";
      // console.log("🚀 ~ file: DateFilterView.js ~ line 32 ~ DateFilterView ~ render ~ extra", extra)
      let dStr = dayM.format(fmt) + extra;
      return (
        <View style={[BaseStyles.column, { alignItems: "flex-start", paddingTop: 10 }]} onLayout={this.props.onLayout}>
          {/* add addtional view to fix ios lack textAlignVertical */}
          <View 
            style={{ height: 34, marginLeft: 28, justifyContent: "center" }} 
          >
            <Text style={[BaseStyles.text12, { color: "#8C93B0", fontWeight: "bold" }]}>{dStr}</Text>
          </View>
          {
            // if this.props.dataSource change too fast can cause FlatList only render partial
            this.props.dataSource == null || 0 == this.props.dataSource.length ? null :
              <FlatList
                showsHorizontalScrollIndicator={false}
                style={{ marginBottom: 13, width: "100%" }}
                ListFooterComponent={() => this.mFooter(itemWidth / 3)}
                data={this.props.dataSource}
                renderItem={(item) => this.renderDate(itemWidth, item)}
                horizontal={true}
                keyExtractor={(item, index) => index.toString()}
                initialScrollIndex={this.props.dataSource.length - 7}
                getItemLayout={(data, index) => (
                  { length: itemWidth, offset: (itemWidth + itemSpace) * index, index }
                )}/>
          }
          {this.props.extraHeader ? this.props.extraHeader() : null}
        </View>
      );
    } else {
      return null;
    }
  }

  renderDate = (aItmW, aDat) => {
    // console.log(TAG, "render item", aDat.index, aDat.item);
    let item = aDat.item;
    let fmt = LocalizedStrings["yyyymmdd"];
    let date =item.ts||0;
    let dayM = Util.getMoment(date / 1000);
    let dStr=dayM.format(fmt)
    // console.log("🚀 ~ file: DateFilterView.js ~ line 68 ~ DateFilterView ~ item", item)
    let dateStyles = [[styles.cell, { width: aItmW, height: aItmW }], 
      [styles.cell, { backgroundColor: '#EAEAEA', borderRadius: aItmW, width: aItmW, height: aItmW }]];
    let textStyle = [{ color: "#999999" }, {}];
    return (
      <TouchableOpacity 
        disabled={!item.enabled} 
        style={[dateStyles[item.selected ? 1 : 0], { paddingTop: 4 }]}
        onPress={() => this.props.dayButtonPressed(item)}
        accessible={true}
        accessibilityState={{
          selected:item.selected
        }}
      >
        <Text 
          style={[BaseStyles.text12, textStyle[0]]}
          accessibilityLabel={DescriptionConstants.kj_1_5+item.wkDay}
        >
          {item.wkDay}
        </Text>
        <Text 
          accessibilityLabel={dStr}
          style={[BaseStyles.text16, textStyle[1], { fontWeight: "bold", paddingTop: 3, paddingBottom: 3 }]}>
            {item.date}
        </Text>
      </TouchableOpacity>
    );
  }

  mFooter = (itemWidth) => {
    return (<View style={{ width: itemWidth }}>
      <Text> </Text>
    </View>);
  }
}


var styles = StyleSheet.create({
  cell: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: itemSpace / 2,
    marginBottom: itemSpace
  }
});
