import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList
} from 'react-native';
import Service from 'miot/Service';

export default class EventFilterView extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      filterIndex: 0
    };
  }

  render() {
    return (
      <View style = {styles.container}>
        <FlatList
          style = {styles.list}
          data={this.props.dataSource}
          renderItem={({ item }) => this._renderRow(item)}
          horizontal={false}
          numColumns={2}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
    );
  }

  _handleEventFilterPressed(item) {
    if (this.props.eventFilterPressed) {
      this.props.eventFilterPressed(item);
    }
  }

  _renderRow = (item) => {
    return (
      <TouchableOpacity style = {styles.cell} onPress = {() => {
        this._handleEventFilterPressed(item);
        let key = 0;
        switch (item['key']) {
          case 'Default': key = 1; break;
          case 'Stay': key = 2; break;
          case 'Pass': key = 3; break;
          case 'Bell': key = 4; break;
          case 'Demolition': key = 5; break;
          case 'BellVideo': key = 6; break;
          default: key = 0;
        }
        // 打点
        // Service.smarthome.reportEvent('home_casetype_ClickNum', { 'owl.p01.but1.a07': key });
      }
      }>
        <Text style={styles.text}>{item.name}</Text>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    height: 174,
    paddingTop: 20,
    paddingBottom: 20,
    paddingLeft: 20,
    paddingRight: 20,
    backgroundColor: 'white'
  },
  list: {
    flex: 1
  },
  cell: {
    flex: 1,
    height: 32,
    fontSize: 16,
    margin: 6,
    // paddingVertical: 10,
    backgroundColor: '#f7f7f7',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5
  },
  text: {
    color: 'black'
  }
});
