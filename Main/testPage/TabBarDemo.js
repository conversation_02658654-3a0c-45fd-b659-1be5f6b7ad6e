import React from 'react';
import { createMaterialTopTabNavigator } from 'react-navigation';
import { Text, View, SafeAreaView, StatusBar, Platform, TouchableOpacity, Image, StyleSheet, Dimensions } from "react-native";
import FirstPage from './FirstPage';
import SecondPage from './SecondPage';
import TabBarComponent from '../ui/TabBarComponent';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import CameraConfig from '../util/CameraConfig';


function createTopNavigator(initPageName) {
  return createMaterialTopTabNavigator(
    {
      topPage1: {
        screen: FirstPage,
        navigationOptions: {
          tabBarLabel: this.isEuropeServer?LocalizedStrings["eu_mi_cloud"]:LocalizedStrings["mi_cloud"]
        }
      },
      topPage2: {
        screen: SecondPage,
        navigationOptions: ({ navigation }) => ({
          tabBarLabel: LocalizedStrings["sd_title"]
        })
      }
    },
    {
      backBehavior: "none",
      animationEnabled: false,
      initialRouteName: initPageName,
      swipeEnabled: false,
      tabBarComponent: TabBarComponent,
      lazy: true,
      initialLayout: { width: Dimensions.get("window").width },
      tabBarOptions:
      {
        showLabel: true,
        // 是否显示标签栏
        activeTintColor: '#333333', // 标签栏激活字体颜色
        inactiveTintColor: '#32BAC0', // 标签栏未激活字体颜色
        style: { backgroundColor: "#ffffff" } // 设置整个tabbar样式(背景颜色等)
  
  
      }
  
    }
  );
  
}

export default class TabBarDemo extends React.Component {


  constructor(props) {
    super(props);
    // here get tab name and index 
    this.isEuropeServer=CameraConfig.getIsEuropeServer();
    this.pageName = "topPage1";
  }

  componentDidMount() {

  }

  render() {
    let RootTabs = createTopNavigator(this.pageName);

    return (
    // // <View style={{ width: "100%", height: "100%" }}>

      //   {/* <TouchableOpacity style={{ position: "absolute", height: 50 , width: 63, paddingLeft: 9, paddingRight: 14, display: "flex", justifyContent: "center", top: statusBarHeight, left: 0 }}
      //     onPress={() => {
      //       console.log("on press back button");
      //       this.props.navigation.goBack();
      //       //here not work in fact  but why?  currentPage not work at all;
      //     }}
      //   > 
      //     <Image
      //       style={{ width: 40, height: 40 }}
      //       source={require("../../Resources/Images/icon_back_black.png")}
      //     >
      //     </Image>
      //   </TouchableOpacity> */}
      // // </View>
      <RootTabs/>

    );

  }

}

