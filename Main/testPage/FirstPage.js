import React, { Component } from 'react';
import { BackHandler, Platform, TouchableOpacity } from 'react-native';
import {
  StyleSheet,
  Text,
  View
} from 'react-native';
import Orientation from 'react-native-orientation';

export default class FirstPage extends Component {
  static navigationOptions = ({ navigation }) => {
    // if (true) {//不要导航条
    //   return null;
    // }
    
    let tabBarVisible = true;
    let param = navigation.state.params || {};
    if (param.isFullScreen) {
      tabBarVisible = false;
    }
    return {
      tabBarVisible
    };
  }

  

  constructor(props) {
    super(props);
    this.state = {
      isFullScreen: false
    };
    this.mOri = "PORTRAIT";

  }

  componentDidMount() {
    console.log("first page did mount");
    BackHandler.addEventListener("hardwareBackPress", this._onbackHandler);
    this.toPortrait();
    Orientation.addOrientationListener(this._orientationListener);

  }

  _onbackHandler = () => {
    if (this.state.isFullScreen) {
      this.toPortrait();
      return true;
    }

    // this._onPause();
    return false;// 不接管
  }
  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    console.log("test", `device orientation changed :${ orientation } want ${ this.mOri }`);
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this._setNavigation(true);
      } else {
        // do something with portrait layout
        this._setNavigation(false);

      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };

  restoreOri() {
    console.log("test", "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait() {
    console.log("toPortrait");
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
  }

  toLandscape() {
    console.log("toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
  }

  componentWillUnmount() {
    console.log("first page will unmount");
    BackHandler.removeEventListener("hardwareBackPress", this._onbackHandler);
    Orientation.removeOrientationListener(this._orientationListener);
  }

  render() {
    return (
      <View style={styles.container}
        onLayout={
          (event) => {
            console.log("first Page", event.nativeEvent.layout);
          }
        }
      >
        <Text style={styles.welcome}>
          This is First Page!
        </Text>
        <TouchableOpacity
          onPress={() => {
            // this.props.navigation.navigate("TestPage");
            if (this.state.isFullScreen) {
              this.toPortrait();
            } else {
              this.toLandscape();
            }
          }}
        >
          <Text style={{ padding: 5, backgroundColor: "pink" }}>
            {"aaa"}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  _setNavigation(isFull) {
    this.props.navigation.setParams({ isFullScreen: isFull });
    this.setState({ isFullScreen: isFull });
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  welcome: {
    fontSize: 20,
    textAlign: 'center',
    color: '#000000'
  }
});