import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import AlarmCard from './AlarmCard';
import Util from '../../../util/Util';
const TAG = "AlarmList";
const PageSZ = 32;
export default class AlarmList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      eventItems: [],
      curItem: props.curItem,
      initScrollIdx: 0
    };
    this.mEv = [];
    this.mEvT = null;
    this.mScrollTgt = -1;
    this.mLoader = props.eventLoader;
  }

  deleteItem(dItem) {
    return new Promise((resolve, reject) => {
      let items = this.state.eventItems;
      let idx = -1;
      for (let i = 0; i < items.length; i++) {
        if (items[i].fileId == dItem.fileId) {
          idx = i;
          break;
        }
      }
      if (idx != -1) {
        if (items.length == 1) {
          items.splice(idx, 1);
          reject(0); // 已删光
        } else {
          items.splice(idx, 1);
          let newItem;
          if (idx == items.length) {
            newItem = items[idx - 1];
          } else {
            newItem = items[idx];
          }
          newItem.selected = true;
          resolve(newItem);
        }
        this.setState({
          eventItems: items
        });
      } else {
        reject(-1); // 未找到
      }
    });
  }

  handleItemPressed(item) {
    for (let aItem of this.state.eventItems) {
      aItem.selected = false;
    }
    item.selected = true;
    this.setState({
      curItem: item
    });
    if (this.props.itemPressed) {
      this.props.itemPressed(item);
    }
  }

  componentDidMount() {
    let date = this.props.date ? this.props.date : new Date();
    this.mEvT = this.props.event ? this.props.event : 'Default';
    // this._getEventList(date, event, false);
    this.mActive = true;
    this.loadAllEvent(date);
  }

  componentWillUnmount() {
    this.mActive = false;
  }

  mEvGetter = (aEvLst) => {
    console.log(TAG, "evloaded", aEvLst.items.length, aEvLst.hasMore, aEvLst.nextTime);
    for (let i = 0; i < aEvLst.items.length && -1 === this.mScrollTgt; ++i) {
      let item = aEvLst.items[i];
      if (item.fileId == this.state.curItem.fileId) {
        item.selected = true;
        this.mScrollTgt = this.mEv.length + i;
        break;
      }
    }
    this.downloadFileThump(aEvLst.items);
    this.mEv = this.mEv.concat(aEvLst.items);
    if (aEvLst.hasMore) {
      Util.getEventList(aEvLst.nextTime, this.mEvT, true, PageSZ)
        .then(this.mEvGetter)
        .catch(this.mErrH);
    } else {
      console.log(TAG, "update ui");
      this.setState({
        eventItems: this.mEv,
        initScrollIdx: Math.max(this.mScrollTgt, 0)
      });
    }
  }

  mErrH = (aErr) => {
    console.log(aErr);
    if (this.mScrollTgt !== -1) {
      this.setState({
        eventItems: this.mEv,
        initScrollIdx: Math.max(this.mScrollTgt, 0)
      });
    }
  }

  loadAllEvent(aDat) {
    this.mLoader.getEventList(aDat, this.mEvT, false, PageSZ)
      .then(this.mEvGetter)
      .catch(this.mErrH);
    /*
    Util.getEventList(aDat, this.mEvT, false, PageSZ)
      .then(this.mEvGetter)
      .catch(this.mErrH);
*/
  }




  async downloadFileThump(items) {
    for (let i = items.length - 1; i >= 0 && this.mActive; --i) {
      try {
        let item = items[i];
        item.imgStoreUrl = await this.mLoader.getThumb(item);
        // 3 thumb per refresh
        if (i % 3 == 0) {
          this.setState({});
        }
      } catch (err) {
        console.log(TAG, "getthumb error", err);
      }
    }
  }

  render() {
    return <View style = {[styles.container, this.props.style]}>
      {
        this.state.eventItems.length > 0 ?
          <FlatList
            style = {styles.list}
            data={this.state.eventItems}
            horizontal={true}
            initialScrollIndex={this.state.initScrollIdx}
            getItemLayout={(al, idx) => ({ length: 130, offset: 130 * idx, index: idx })
            }
            renderItem={
              ({ item }) =>
                <AlarmCard item={item} cardPressed={() => {
                  this.handleItemPressed(item);
                }}/>
            }
            keyExtractor={(item, index) => index.toString()}
          />
          : null
      }

    </View>;
  }
}

const styles = StyleSheet.create({
  container: {
    height: 120,
    backgroundColor: 'white'
  },
  list: {
    backgroundColor: 'white',
    paddingTop: 10,
    paddingLeft: 10,
    marginRight: 10
  }
});
