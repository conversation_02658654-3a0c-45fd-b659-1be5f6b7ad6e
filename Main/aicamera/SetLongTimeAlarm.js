import React from 'react';
import { ScrollView, View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import { styles } from '../setting/SettingStyles';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { InputDialog, NavigationBar } from 'mhui-rn';
import { Device } from 'miot/device';
import ItemLongTimeAlarm from '../ui/ItemLongTimeAlarm';
import {
  AbstractDialog, ActionSheet, ChoiceDialog,
  LoadingDialog, MessageDialog, PinCodeDialog, ProgressDialog, ShareDialog
} from 'miot/ui/Dialog';
import MHDatePicker from 'miot/ui/MHDatePicker';

export default class SetLongTimeAlarm extends React.Component {
  static navigationOptions = (navigation) => {
    return {// 不要导航条
      headerTransparent: true,
      header:
        null
    };
  };
  constructor(props, context) {
    super(props, context);
    this.state = {
      showNameDialog: false,
      showRepeatModeDialog: false,
      showRepeatWeekDialog: false,
      showTimeDialog: false,
      setTime: 0 // 0 开始时间，1 结束时间
    };
    this.data = {
      selectedIndexArray: [-1],
      multiIndexArray: []
    };
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.item = this.props.navigation.getParam('item');
    if (this.item) {
      this.setWeekByRepeat();
    } else {
      this.item = { repeat: 0 };
    }
    console.log("item==", this.item);
    // {"alarmValue": "白天无人出现", "enable": false, "key": 3, "repeat": 127, "select": false, "time_end": "20:00", "time_start": "06:00"}
    this.pageCallback = this.props.navigation.getParam('callback');
  }

  setWeekByRepeat() {
    let flag = 0b00000001;
    let i = 0;
    this.data.multiIndexArray = [];
    for (i = 0; i < 7; i++) {
      if ((this.item.repeat & flag) != 0) {
        this.data.multiIndexArray.push(i);
      }
      flag = flag << 1;
    }
    if (0b00000000 == this.item.repeat) {
      this.data.selectedIndexArray = [0];
    } else if (0b01111111 == this.item.repeat) {
      this.data.selectedIndexArray = [1];
    } else {
      this.data.selectedIndexArray = [2];
    }
  }

  onResume() {

  }

  checkAndSubmit() {
    if (!this.item.alarmValue || this.item.alarmValue.trim() == "") {
      Toast.fail("add_feature_empty_tips");
      return;
    }
    if (!this.item.time_start || this.item.time_start.trim() == "" || !this.item.time_end || this.item.time_end.trim() == "") {
      Toast.fail("plug_timer_unset");
      return;
    }
    if (this.item.time_start && this.item.time_end && this.item.time_start == this.item.time_end) {
      Toast.fail("plug_timer_offtime_illegal");
      return;
    }
    this.item.enable = true;
    this.pageCallback(this.item);
    this.props.navigation.goBack();
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['long_time_type_period'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => { 
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            this.checkAndSubmit();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  renderNameDialog() {
    return (
      <InputDialog
        visible={this.state.showNameDialog}
        title={LocalizedStrings['long_time_name_dialogue_title']}
        subTitle="qwe"
        onDismiss={(_) => {
          this.setState({ showNameDialog: false });
        }}
        buttons={[
          {
            callback: (_) => this.setState({ showNameDialog: false })
          },
          {
            callback: (result) => {
              // {"checked": false, "hasPressUnderlineText": false, "textInputArray": ["value"]}
              let textInputArray = result.textInputArray;
              console.log(`textInputArray`, textInputArray[0]);
              this.item.alarmValue = textInputArray[0].trim();
              this.setState({ showNameDialog: false });
            }
          }
        ]}
        inputs={[
          {
            placeholder: LocalizedStrings['long_time_name'],
            defaultValue: this.item.alarmValue ? this.item.alarmValue : '',
            type: 'DELETE'
          }
        ]}
      />
    );
  }

  repeatModeDialog() {
    console.log("this.data.selectedIndexArray == ", this.data.selectedIndexArray);
    return (
      <ChoiceDialog
        visible={this.state.showRepeatModeDialog}
        title={LocalizedStrings['plug_timer_repeat_selection']}
        options={[
          {
            title: LocalizedStrings['plug_timer_onetime']
          },
          {
            title: LocalizedStrings['plug_timer_everyday']
          },
          {
            title: LocalizedStrings['plug_timer_sef_define']
          }
        ]}
        selectedIndexArray={(this.data.selectedIndexArray[0] && this.data.selectedIndexArray[0] == -1) ? [0] : this.data.selectedIndexArray}
        onDismiss={() => this.setState({ showRepeatModeDialog: false })}
        onSelect={(result) => {
          console.log(`selected:`, result);
          if (result == 2) {
            this.setState({ showRepeatModeDialog: false });
            this.setState({ showRepeatWeekDialog: true });
          } else if (result == 0) {
            this.item.repeat = 0b00000000;
            this.setWeekByRepeat();
          } else {
            this.item.repeat = 0b01111111;
            this.setWeekByRepeat();
          }
        }}
      />);
  }

  repeatWeekDialog() {
    return (
      <ChoiceDialog
        type={ChoiceDialog.TYPE.MULTIPLE}
        visible={this.state.showRepeatWeekDialog}
        title={LocalizedStrings['plug_timer_custom_repeat']}
        options={[
          {
            title: LocalizedStrings['monday1']
          },
          {
            title: LocalizedStrings['tuesday1']
          },
          {
            title: LocalizedStrings['wednesday1']
          },
          {
            title: LocalizedStrings['thursday1']
          },
          {
            title: LocalizedStrings['friday1']
          },
          {
            title: LocalizedStrings['saturday1']
          },
          {
            title: LocalizedStrings['sunday1']
          }
        ]}
        selectedIndexArray={this.data.multiIndexArray}
        color="#32BAC0"
        buttons={[
          {
            // style: { color: 'lightblue' },
          },
          {
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`selected:`, result);
              if (result && result.length <= 0) {
                Toast.fail("smarthome_span_error");
              }
              this.data.multiIndexArray = result;
              this.setState({
                showRepeatWeekDialog: false
              });
              this.item.repeat = 0b00000000;
              this.data.multiIndexArray.forEach((item) => {
                this.item.repeat = this.item.repeat | (0b00000001 << item);
              });
              this.setWeekByRepeat();
            }
          }
        ]}
        onDismiss={() => this.setState({ showRepeatWeekDialog: false })}
      />);
  }

  renderTimeDialog() {
    return (
      <MHDatePicker
        visible={this.state.showTimeDialog}
        title={this.state.setTime == 0 ? LocalizedStrings['csps_start'] : LocalizedStrings['csps_end']}
        type={MHDatePicker.TYPE.TIME24}
        onDismiss={() => this.setState({ showTimeDialog: false })}
        onSelect={(res) => {
          console.log(JSON.stringify(res));
          let str = `${ res.rawArray[0] }:${ res.rawArray[1] }`;
          this.state.setTime == 0 ? this.item.time_start = str : this.item.time_end = str;
        }}
        current={this.state.setTime == 0 ? (this.item.time_start ? this.item.time_start.split(':') : new Date()) : 
          (this.item.time_end ? this.item.time_end.split(':') : new Date())}
      />
    );
  }

  render() {
    return (<View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#FFFFFF", alignItems: "center" }}>
      { this.renderTitleBar() }
      <ItemLongTimeAlarm
        title={ LocalizedStrings['long_time_name']}
        sub_title={ this.item.alarmValue ? this.item.alarmValue : LocalizedStrings['plug_timer_no_set']}
        onPress={() => {
          this.setState({ showNameDialog: true });
        }}
      />
      <ItemLongTimeAlarm
        title={LocalizedStrings['plug_timer_repeat']}
        sub_title={ this.item ? AlarmUtil.getLongTimeRepeatStr(this.item.repeat) : LocalizedStrings['plug_timer_onetime']}
        onPress={() => {
          this.setState({ showRepeatModeDialog: true });
        }}
      />
      <ItemLongTimeAlarm
        title={LocalizedStrings['csps_start']}
        sub_title={ this.item.time_start ? this.item.time_start : LocalizedStrings['plug_timer_no_set']}
        onPress={() => {
          this.setState({ setTime: 0, showTimeDialog: true });
        }}
      />
      <ItemLongTimeAlarm
        title={LocalizedStrings['csps_end']}
        sub_title={ this.item.time_end ? this.item.time_end : LocalizedStrings['plug_timer_no_set']}
        onPress={() => {
          this.setState({ setTime: 1, showTimeDialog: true });
        }}
      />
      { this.renderNameDialog() }
      { this.repeatModeDialog() }
      { this.repeatWeekDialog() }
      { this.renderTimeDialog() }
    </View>);
  }
}