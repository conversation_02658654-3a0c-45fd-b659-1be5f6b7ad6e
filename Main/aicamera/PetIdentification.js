/**
 * @name liuxu
 * @desc ~/code/xiaomi/rn-plugin/miot-workspace/projects/com.xiaomi.cardcamera/Main/aicamera/BabyCrying.js
 * <AUTHOR>
 * @time 2022年06月27日 15:18:47 星期一
 * @param {Object} {}
 * @return  {*}
 */
import React from 'react';
import AIcard from '../components/AICard.js';
import { localStrings as LocalizedStrings } from '../../Main/MHLocalizableString';
import { NavigationBar } from 'mhui-rn';
import { ListItemWithSwitch } from 'miot/ui/ListItem';
import Toast from '../components/Toast';
import { Device } from 'miot';
import CameraConfig from '../util/CameraConfig';
import VersionUtil from '../util/VersionUtil';
import AlarmUtil from '../util/AlarmUtil';
  
class PetIdentification extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      babyCrySwitch: false,
      isVip: false,
      petSwitch: false
 
    };
    this.is051 = CameraConfig.Model_chuangmi_051a01 == Device.model;
    this.isBabyCrySpec = /** Device.model === CameraConfig.Model_chuangmi_022 || CameraConfig.isCamera039(Device.model)* */ CameraConfig.isXiaomiCamera(Device.model) || this.is051;// 022走云端，039、049识别成功率低 改成了云端；目前只有c01a02的宝宝哭声开关走spec，051也走spec
    this.shouldDisableBabyCry = false;
    this.isBabyCrySpec = /** Device.model === CameraConfig.Model_chuangmi_022 || CameraConfig.isCamera039(Device.model)* */ CameraConfig.isXiaomiCamera(Device.model) || this.is051;// 022走云端，039、049识别成功率低 改成了云端；目前只有c01a02的宝宝哭声开关走spec，051也走spec
  }
  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['pet_detection'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
        
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    if (VersionUtil.isAiCameraModel(Device.model)) {
      AlarmUtil.getAiSwitch022(2).then((res) => {
        console.log("getAiSwitch022 res=", JSON.stringify(res));
        this.setState({
          petSwitch: res[1].value
        });
      }).catch((err) => {
        console.log("getAiSwitch022 err=", JSON.stringify(err));
      });
    }
  }
  componentWillMount() {
          
  }
  _onPetSwitchChange(value) {
    Toast.loading('c_setting');
    AlarmUtil.putPetSwitch022(value).then(() => {
      Toast.success('c_set_success');
      this.setState({ petSwitch: value });
    }).catch(() => {
      Toast.fail('c_set_fail');
      this.setState({ petSwitch: !value });
    });
  }
  render() {
    let petSwitchItem = (
      <ListItemWithSwitch
        showSeparator={false}
        title={LocalizedStrings['pet_detection']}
        value={this.state.petSwitch}
        onValueChange={(value) => this._onPetSwitchChange(value)}
        titleStyle={{ fontWeight: 'bold' }}
 
      />
    );
    return (
      <AIcard
        img={require('../../Resources/Images/faceRecognition/petIdentification.webp')}
        desc={LocalizedStrings['pet_identification_desc']}
        switchButton={petSwitchItem}/>
    );
  }
  
}
  
export default PetIdentification;