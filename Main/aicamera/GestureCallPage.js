import React from 'react';
import {
  <PERSON><PERSON>View,
  View,
  <PERSON>H<PERSON>ler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { AbstractDialog, ChoiceDialog, ImageButton, InputDialog, MessageDialog, NavigationBar } from 'mhui-rn';
import AlarmUtil from '../util/AlarmUtil';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import CameraConfig from '../util/CameraConfig';
import { Service, Host, Device, Entrance } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import API from '../API';
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import { strings as I18n } from "miot/resources";
import { styles } from "../setting/SettingStyles";
import AlarmUtilV2, {
  PIID_COUGH_SENSITIVITY,
  PIID_COUGH_SWITCH,
  PIID_CRY_SENSITIVITY,
  PIID_CRY_SWITCH,
  PIID_MOVE_SENSITIVITY,
  PIID_MOVE_SWITCH,
  PIID_PEOPLE_SWITCH,
  PIID_SOUND_SENSITIVITY,
  PIID_SOUND_SWITCH, SIID_AI_DETECTION, SIID_AI_SENSITIVITY
} from "../util/AlarmUtilV2";
import { Event } from '../config/base/CfgConst';
import Util from "../util2/Util";
import ListItemWithIcon from "../widget/ListItemWithIcon";
import ChoiceItem from "mhui-rn/dist/components/listItem/ChoiceItem";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import CallUtil, { CALL_TYPE } from "../util/CallUtil";
import BaseSettingPage from "../BaseSettingPage";

const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

const TAG = "MotionDetectionPage";
const sensitiveOptions = [
  { title: LocalizedStrings['alarm_level_high_title'] },
  { title: LocalizedStrings['alarm_level_middle_title'] },
  { title: LocalizedStrings['alarm_level_low_title'] }
];

/**
 * @Author: byh
 * @Date: 2023/10/26
 * @explanation:
 * 手势通话
 *********************************************************/
export default class GestureCallPage extends BaseSettingPage {
  // static navigationOptions = (navigation) => {
  //   return { headerTransparent: true, header: null };
  // };

  constructor(props, context) {
    super(props, context);
    this.pageType = this.props.navigation.getParam("type");
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      showCloseDialog: false,
      tempName: '',
      showRenameDialog: false,
      showChoiceContactDialog: false,
      gestureSet: null
    };
    this.changeUid = undefined;
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.titleStr = LocalizedStrings['detect_move'];
    this.detectionDesc = LocalizedStrings['ai_move_desc'];
    this.attentionDesc = LocalizedStrings['ai_note_attention'];
    this.topImageSrc = require("../../Resources/Images/faceRecognition/pic_gesture_talk.png");
  }
  getTitle(): string {
    return LocalizedStrings['gesture_call'];
  }

  getTitleBackgroundColor(): string {
    return "#f6f6f6";
  }

  componentDidMount() {
    super.componentDidMount();
    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });

    this._getSetting();
  }

  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  _getSetting() {
    DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(res => {
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = data;
          let stateProps = {};
          if (data.hasOwnProperty("switch") && data.switch.hasOwnProperty('hand')) {
            stateProps.switchValue = data.switch.hand;
          }

          if (data.hasOwnProperty("hand1")) {
            let key4Data = data.hand1;
            stateProps.gestureSet = key4Data;
            this.queryLastAccount('gestureSet','hand1', key4Data);
          }

          this.setState(stateProps);
        }
      }
    }).catch(error => {
      console.log("======= error: ", error);
    });
  }

  queryLastAccount(key,settingKey, data) {
    CallUtil.getAccountInfoById(`${data.mijia}`).then((res) => {
      let icon = this.callSettingData[settingKey].icon;
      let nickname = this.callSettingData[settingKey].nickname;
      if (nickname != res.nickName || icon != res.avatarUrl) {
        // 有不一致才更新
        data.icon = res.avatarURL;
        data.nickname = res.nickName;
        this.callSettingData[settingKey] = data;
        this.setState({ [key]: data });
      }
    }).catch(error => {
      // 失败了就不做其他处理
    });
  }

  _getSpecParams() {
    let params = [
      { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
      { "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY }];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY }
        ];
        break;
      case Event.PeopleMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH }];
        break;
      case Event.LouderSound:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_SOUND_SENSITIVITY }
        ];
        break;
      case Event.BabyCry:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_CRY_SENSITIVITY }
        ];
        break;
      case Event.PeopleCough:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_COUGH_SENSITIVITY }
        ];
        break;
    }
    return params;
  }

  _renderSensitivityDialog() {
    return (
      <ChoiceDialog
        visible={ this.state.sensitivityVisible }
        title={ LocalizedStrings['sensitivity_settings'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: true,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        } }
        buttons={ [{
          text: I18n.cancel
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("ssssss", res);
            const newPluginType = res?.[0];
            if (newPluginType === this.state.sensitiveIndex) {
              this.setState({ sensitivityVisible: false });
              return;
            }
            //设置灵敏度
            this.setSensitivity(newPluginType);
          }
        }] }
        options={ sensitiveOptions }
        selectedIndexArray={ [this.state.sensitiveIndex] }
      />
    );
  }

  setSensitivity(value) {
    let params = this.getSetSensitivityParams(value);
    AlarmUtilV2.setSpecPValue(params, TAG).then((res) => {
      console.log("setSensitivity success", res);
      if (res[0].code != 0) {
        Toast.fail("c_set_fail");
      } else {
        this.setState({ sensitiveIndex: value, sensitivityVisible: false });
      }
    }).catch((err) => {
      console.log("setSensitivity error", err);
      Toast.fail("c_set_fail");
      this.setState({ sensitivityVisible: false });
    });

  }

  _onSwitchValue(value) {
    if (!value) {
      //关闭的时候弹框提示
      this.setState({ showCloseDialog: true });
      return;
    }
    this.updateCallSettingSwitch(value);
  }

  updateCallSettingSwitch(value) {
    if (!Device.isOnline) {
      Toast.success("device_offline");
      return;
    }
    let upData = JSON.parse(JSON.stringify(this.callSettingData));
    upData.switch.hand = value ? 1 : 0;
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData)).then((res) => {
      this.callSettingData = upData;
      CallUtil.updateSettingToDevice([JSON.stringify(upData)]);
      this.setState({ switchValue: value });
    }).catch((error) => {
      this.setState({ switchValue: !value });
      Toast.fail("c_set_fail");
    });
  }

  getSetParams(value) {
    let params = [];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH, value: value }];
        break;
      case Event.PeopleMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH, value: value }];
        break;
      case Event.LouderSound:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH, value: value }];
        break;
      case Event.BabyCry:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH, value: value }];
        break;
      case Event.PeopleCough:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH, value: value }];
        break;
    }
    return params;
  }

  getSetSensitivityParams(value) {
    value = value == 0 ? 2 : value == 2 ? 0 : 1;
    let params = [];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY, value: value }];
        break;
      case Event.LouderSound:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_SOUND_SENSITIVITY, value: value }];
        break;
      case Event.BabyCry:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_CRY_SENSITIVITY, value: value }];
        break;
      case Event.PeopleCough:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_COUGH_SENSITIVITY, value: value }];
        break;
    }
    return params;
  }

  renderSettingContent() {

    let containerStyleAllRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleTopRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleBottomRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    return (
      <View style={ {
        display: "flex",
        height: "100%",
        flex: 1,
        flexDirection: "column",
        backgroundColor: Util.isDark() ? "#xm000000" : '#F6F6F6',
        alignItems: "center"
      } }>
          <View key={ 102 }>

            <View style={ { alignItems: "center", marginHorizontal: 20, marginTop: 20 } }>
              <Image
                style={ { width: '100%', height: 200, borderRadius: 9 } }
                source={ this.topImageSrc }/>
            </View>

            <View style={ stylesDetection.white_blank }/>
            <Text style={ styles.desc_subtitle }>{ LocalizedStrings['ai_gesture_call_desc'] }</Text>

            <View style={ styles.whiteblank }/>

            <ListItemWithSwitch
              titleNumberOfLines={ 3 }
              unlimitedHeightEnable={ true }
              showSeparator={ false }
              title={ LocalizedStrings['gesture_call'] }
              value={ this.state.switchValue }
              onValueChange={ (val) => this._onSwitchValue(val) }
              containerStyle={ { backgroundColor: Util.isDark() ? "#xm000000" : '#F6F6F6' } }
              titleStyle={ { fontWeight: 'bold' } }
              accessibilitySwitch={ {
                accessibilityLabel: LocalizedStrings['care_screen_close_show_protect']
              } }
            />
            {
              <View style={ { marginTop: 10 } }>
                {
                  this.state.gestureSet ?
                    <View>
                      <View style={ [containerStyleTopRadius, {
                        flex: 1,
                        flexDirection: "row",
                        minHeight: 70,
                        alignItems: "center"
                      }] }>
                        <Text style={ {
                          fontSize: 16,
                          fontWeight: 'bold',
                          flex: 1,
                          marginRight: 10
                        } }>{ LocalizedStrings['ai_gesture_call'] }</Text>
                        <ImageButton
                          style={ { width: 32, height: 32, marginRight: 20 } }
                          source={ Util.isDark() ? require("../../Resources/Images/icon_call_edit_dark.png") : require("../../Resources/Images/icon_call_edit.png") }
                          onPress={ () => {
                            console.log("=======");
                            this.changeUid = this.state.gestureSet.mijia;
                            this.selectUser(1,this.changeUid);
                            // this.setState({ showChoiceContactDialog: true });
                          } }/>
                      </View>
                      <ListItemWithIcon
                        icon={ this.state.gestureSet.icon ? { uri: this.state.gestureSet.icon } : require("../../Resources/Images/icon_user.png") }
                        iconStyle={{width: 28, height: 28, borderRadius: 14}}
                        style={ {
                          backgroundColor: "#ffffff", marginLeft: 12,
                          marginRight: 12, width: screenWidth - 24, alignSelf: "center"
                        } }
                        hideArrow={ true }
                        title={ Service.account.ID == this.state.gestureSet.mijia ? `${ this.state.gestureSet.nickname }${ LocalizedStrings['call_me'] }` : this.state.gestureSet.nickname }
                        sub_title={ `${this.state.gestureSet.mijia}` }
                      />

                      <ListItem
                        allowFontScaling={ false }
                        containerStyle={ containerStyleBottomRadius }
                        titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                        valueStyle={ { fontSize: 13, color: "#979797" } }
                        title={ LocalizedStrings['ai_call_name'] }
                        value={ this.state.gestureSet.callName }
                        onPress={ (_) => this.setState({ showRenameDialog: true, tempName: this.state.gestureSet ? this.state.gestureSet.callName : "" }) }
                        showSeparator={ false }/>
                    </View>
                    :
                    <ListItem
                      allowFontScaling={ false }
                      containerStyle={ containerStyleAllRadius }
                      titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                      valueStyle={ { fontSize: 13, color: "#979797" } }
                      title={ LocalizedStrings['ai_gesture_call'] }
                      value={ this.state.subtitleStr }
                      onPress={ (_) => this.selectUser() }
                      showSeparator={ false }/>
                }

              </View>
            }

            <View style={ { height: 30 } }/>

          </View>

        { this._renderCloseDialog() }
        { this._renderRenameDialog() }
        { this._renderChoiceContactDialog() }
      </View>
    );
  }

  _renderCloseDialog() {
    return (
      <MessageDialog
        visible={ this.state.showCloseDialog }
        // message={ LocalizedStrings['ai_contact_call_close_desc'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: true,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        } }
        buttons={ [{
          text: LocalizedStrings['still_close'],
          callback: () => {
            this.setState({ showCloseDialog: false });
            this.updateCallSettingSwitch(false);
          }
        }, {
          text: LocalizedStrings['not_close'],
          callback: (res) => {
            console.log("ssssss", res);
            this.setState({ showCloseDialog: false });
          }
        }] }
      />
    );
  }

  _renderRenameDialog() {
    return (
      <InputDialog
        visible={ this.state.showRenameDialog }
        title={ LocalizedStrings["ai_call_name"] }
        inputs={ [{
          placeholder: LocalizedStrings["please_enter_name"],
          defaultValue: this.state.tempName,
          textInputProps: {
            autoFocus: true
          },
          onChangeText: (text) => {
            if (this.state.commentErr != null) {
              this.setState({ commentErr: null });
            }
          },
          type: 'DELETE',
          isCorrect: this.state.commentErr == null
        }] }
        inputWarnText={ this.state.commentErr }
        noInputDisButton={ true }
        buttons={ [
          {
            // style: { color: 'lightpink' },
            callback: () => this.setState({ showRenameDialog: false, commentErr: null })
          },
          {
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`结果`, result.textInputArray[0]);
              // 检测手机号正则
              let isPhoneNumber = result.textInputArray[0].match(/^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/);
              let text = result.textInputArray[0].trim();
              if (text.length > 0 && !Util.containsEmoji(text)) {
                let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
                let updateObj = '';
                needUpData.hand1.callName = text;
                updateObj = { gestureSet: needUpData.hand1 };
                let paramsStr = JSON.stringify(needUpData);
                DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
                  this.callSettingData = needUpData;
                  CallUtil.updateSettingToDevice([paramsStr]);
                  this.setState(updateObj);
                }).catch(error => {
                  Toast.fail("c_set_fail");
                });
                this.setState({ showRenameDialog: false, commentErr: null });
              } else {
                if (Util.containsEmoji(text)) {
                  this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
                }
              }
            }
          }
        ] }
        onDismiss={ () => this.setState({ showRenameDialog: false, commentErr: null }) }
      />
    );
  }
  _renderChoiceContactDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showChoiceContactDialog }
        useNewTheme={ true }
        title={ LocalizedStrings["edit_contact"] }
        dialogStyle={ {
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        } }
        onDismiss={ (_) => this.setState({ showChoiceContactDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showChoiceContactDialog: false });
            }
          }
        ] }
      >
        <View style={ {
          marginBottom: 16,
          marginTop: 10
        } }>
          { [{
            "title": LocalizedStrings['delete_contact'],
            onPress: () => {
              this.setState({ showChoiceContactDialog: false });
              // 删除按键联系人
              this.deleteGestureContact();
            }
          }, {
            "title": LocalizedStrings['replace_contact'],
            onPress: () => {
              this.setState({ showChoiceContactDialog: false });
              this.selectUser(1,this.changeUid);

            }
          }].map((option, index) => <View key={ (option.title || '') + index }>
            <ChoiceItem
              type={ ChoiceItem.TYPE.SINGLE }
              titleStyle={ {
                fontSize: 16,
                fontWeight: "bold"
              } }
              title={ option.title || '' }
              onPress={ () => option.onPress() }/>
          </View>) }
        </View>
      </AbstractDialog>
    );
  }
  deleteGestureContact() {
    if (!Device.isOnline) {
      Toast.success("device_offline");
      return;
    }
    let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
    if (needUpData.hasOwnProperty("hand1")) {
      // 防止删除失败后，再次删除时，callSettingData中已经没有hand1
      delete needUpData.hand1;
    }
    let updateObj = { gestureSet: null };
    let paramsStr = JSON.stringify(needUpData);
    DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
      // 成功后，
      CallUtil.updateSettingToDevice([paramsStr]);
      // 更新成功后，再去删除数据
      delete this.callSettingData.hand1;
      this.setState(updateObj);
    }).catch(error => {
      Toast.fail("c_set_fail");
    });
  }

  selectUser(type = 0,uid) {
    this.props.navigation.navigate("ChoiceContactPage",{ type: type, choiceType: CALL_TYPE.GESTURE, uid: uid ,callback: (res) => {
        this.callSettingData.hand1 = res;
        this.setState({ gestureSet: res });
      }});
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['gesture_call'],
      type: this.state.darkMode ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    return (
      <NavigationBar { ...titleBarContent } />
    );
  }
}

const stylesDetection = StyleSheet.create({
  white_blank: {
    height: 0.5,
    marginTop: 20
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  },
  algorithm_title: {
    color: "#999",
    fontSize: 18,
    paddingHorizontal: 24
  },
  algorithm_subtitle: {
    color: "#999",
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 24
  },
  optionsPage: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 24
  },
  optionContainer: {
    minHeight: 80,
    alignItems: 'center'
  },
  icon: {
    width: 48,
    height: 48
  },
  optionText: {
    marginTop: 4,
    marginBottom: 10,
    width: 48,
    textAlign: 'center',
    fontSize: 12
  }

});