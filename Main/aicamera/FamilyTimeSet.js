import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { InputDialog, NavigationBar } from 'mhui-rn';
import { Device } from 'miot/device';
import {
  AbstractDialog, ActionSheet, ChoiceDialog,
  LoadingDialog, MessageDialog, PinCodeDialog, ProgressDialog, ShareDialog
} from 'miot/ui/Dialog';
import MHDatePicker from 'miot/ui/MHDatePicker';
import BaseSettingPage from "../BaseSettingPage";
import Util from '../util2/Util';
import { strings as I18n } from "miot/resources";
import { Host } from "miot";
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

export default class FamilyTimeSet extends BaseSettingPage {

  getTitle(): string {
    return LocalizedStrings['long_time_type_period'];
  }
  hasRightButton(): boolean {
    return true;
  }
  leftPress() {
    if (this.state.canSave) {
      this.setState({ showSaveDialog: true });
      return;
    }
    this.props.navigation.goBack();
  }

  rightPress() {
    this.checkAndSubmit();
  }
  componentDidMount() {
    super.componentDidMount();
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    let exists = this.props.navigation.getParam("existList");
    this.existList = []
    if (exists) {
      this.existList = JSON.parse(JSON.stringify(exists));
    }
  }
  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  constructor(props, context) {
    super(props, context);
    this.state = {
      showNameDialog: false,
      showRepeatModeDialog: false,
      showRepeatWeekDialog: false,
      showTimeDialog: false,
      showSaveDialog: false,
      canSave: false,
      setTime: 0 // 0 开始时间，1 结束时间
    };
    this.data = {
      selectedIndexArray: [-1],
      multiIndexArray: []
    };
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.item = this.props.navigation.getParam('item');
    if (this.item) {
      this.setWeekByRepeat();
    } else {
      this.item = { repeat: 0 };
    }
    console.log("item==", this.item);
    // {\"start\":\"07:00\",\"end\":\"09:00\",\"repeat\":127,\"enable\":false,\"clock_idx\":0,\"name\":\"早上无人出现\"}
    this.pageCallback = this.props.navigation.getParam('callback');
  }

  onBackHandler = () => {
    if (this.state.canSave) {
      this.setState({showSaveDialog: true});
      return true;
    }
    return false;
  };

  setWeekByRepeat() {
    let flag = 0b00000001;
    let i = 0;
    this.data.multiIndexArray = [];
    for (i = 0; i < 7; i++) {
      if ((this.item.repeat & flag) != 0) {
        this.data.multiIndexArray.push((i+6) % 7);
      }
      flag = flag << 1;
    }
    if (0b00000000 == this.item.repeat) {
      this.data.selectedIndexArray = [0];
    } else if (0b01111111 == this.item.repeat) {
      this.data.selectedIndexArray = [1];
    } else {
      this.data.selectedIndexArray = [2];
    }
    // this.setState({ showRepeatModeDialog: false });
  }

  onResume() {

  }

  checkAndSubmit() {
    if (!this.item.name || this.item.name.trim() == "") {
      Toast.fail("name_is_empty");
      return;
    }

    if ((!this.item.start || this.item.start.trim() == "") && (!this.item.end || this.item.end.trim() == "")) {
      Toast.fail("setting_monitor_time");
      return;
    }
    if (!this.item.start || this.item.start.trim() == "") {
      Toast.fail("setting_monitor_time_start");
      return;
    }

    if (!this.item.end || this.item.end.trim() == "") {
      Toast.fail("setting_monitor_time_end");
      return;
    }

    if (this.item.start == this.item.end) {
      Toast.fail("imi_start_equal_end");
      return;
    }

    if (this.isSameNameSet()) {
      Toast.fail("name_is_set");
      return;
    }

    if (this.isSameSet()) {
      Toast.fail("monitor_already_exists");
      return;
    }

    this.item.enable = true;
    this.pageCallback(this.item);
    this.props.navigation.goBack();
  }
  isSameNameSet() {
    console.log("================",this.existList)
    console.log("++++++++++++++++",this.item)
    let index = this.existList.findIndex((item) => {
      return item.name == this.item.name;
    });
    return index > -1;
  }
  isSameSet() {
    console.log("================",this.existList)
    console.log("++++++++++++++++",this.item)
    let index = this.existList.findIndex((item) => {
      return item.start == this.item.start && item.end == this.item.end && item.repeat == this.item.repeat
    });
    return index > -1;
  }
  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['long_time_type_period'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({ showSaveDialog: true });
              return;
            }
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            this.checkAndSubmit();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  renderNameDialog() {
    return (
      <InputDialog
        visible={this.state.showNameDialog}
        title={LocalizedStrings['long_time_name']}
        subTitle="qwe"
        onDismiss={(_) => {
          this.setState({ showNameDialog: false, prePositionNameTooLong: false });
        }}
        buttons={[
          {
            callback: (_) => this.setState({ showNameDialog: false, prePositionNameTooLong: false })
          },
          {
            callback: (result) => {
              // {"checked": false, "hasPressUnderlineText": false, "textInputArray": ["value"]}
              if (this.state.prePositionNameTooLong) {
                return;
              }
              let textInputArray = result.textInputArray;
              console.log(`textInputArray`, textInputArray[0]);
              this.item.name = textInputArray[0].trim();
              this.setState({ showNameDialog: false, canSave : true, prePositionNameTooLong: false });
            }
          }
        ]}
        inputs={[
          {
            placeholder: LocalizedStrings['long_time_name'],
            defaultValue: this.item.name ? this.item.name : '',
            textInputProps: {
              autoFocus: true
            },
            onChangeText: (result) => {
              let isEmoji = Util.containsEmoji(result);
              let length = result.length;
              // let isCommon = this.isTextcommon(result);
              if (isEmoji) {
                this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
              } else if (length > 10) {
                this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["input_name_too_long2"] });
              } else if (length <= 0 || result.trim().length == 0) {
                this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
              } else {
                this.setState({ prePositionNameTooLong: false, commentErr: "error" });
              }
            },
            type: 'DELETE',
            isCorrect: !this.state.prePositionNameTooLong
          }
        ]}
        inputWarnText={ this.state.commentErr }
        noInputDisButton={ true }
      />
    );
  }

  repeatModeDialog() {
    console.log("this.data.selectedIndexArray == ", this.data.selectedIndexArray);
    return (
      <ChoiceDialog
        visible={this.state.showRepeatModeDialog}
        title={LocalizedStrings['plug_timer_repeat_selection']}
        useNewType={true}
        itemStyleType={2}
        options={[
          {
            title: LocalizedStrings['plug_timer_onetime']
          },
          {
            title: LocalizedStrings['plug_timer_everyday']
          },
          {
            title: LocalizedStrings['plug_timer_sef_define']
          }
        ]}

        selectedIndexArray={(this.data.selectedIndexArray[0] && this.data.selectedIndexArray[0] == -1) ? [0] : this.data.selectedIndexArray}
        onDismiss={() => this.setState({ showRepeatModeDialog: false })}
        buttons={[{
          text: I18n.cancel,
          callback: () => {
            this.setState({ showRepeatModeDialog: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {

            const newPluginType = res?.[0];
            console.log("ssssss", res,newPluginType);
            if (newPluginType == 2) {
              console.log("==+==========",this.item.repeat,0b01111111);
              let repeat = this.item.repeat == 0b01111111 ? 0b00000000 : this.item.repeat;
              this.item.repeat = repeat;
              this.setWeekByRepeat();
              this.setState({ showRepeatModeDialog: false, showRepeatWeekDialog: true });
            } else if (newPluginType == 0) {
              this.item.repeat = 0b00000000;
              this.setWeekByRepeat();
              this.setState({ showRepeatModeDialog: false, canSave : true });
            } else {
              this.item.repeat = 0b01111111;
              this.setWeekByRepeat();
              this.setState({ showRepeatModeDialog: false, canSave : true });
            }
            // if (newPluginType === this.state.sensitiveIndex) {
            //   this.setState({ sensitivityVisible: false });
            //   return;
            // }
            //设置灵敏度
            // this.setSensitivity(newPluginType);
          }
        }]}
        onSelect={(result) => {
          console.log(`selected:`, result);
          // if (result == 2) {
          //   this.setState({ showRepeatModeDialog: false });
          //   this.setState({ showRepeatWeekDialog: true });
          // } else if (result == 0) {
          //   this.item.repeat = 0b00000000;
          //   this.setWeekByRepeat();
          // } else {
          //   this.item.repeat = 0b01111111;
          //   this.setWeekByRepeat();
          // }
        }}
      />);
  }

  renderRepeatViewDialog() {
    return (
      <AbstractDialog
        style={[styles.repeatViewStyle]}
        visible={this.state.showRepeatModeDialog}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ showRepeatModeDialog: false });
        }}
        showTitle={false}
        showButton={false}
        // canDismiss={false}
      >
        {this._renderRepeatView()}
      </AbstractDialog>
    );
  }

  _renderRepeatView() {
    console.log("++++++++++++",this.state.tempRepeatCopy)
    this.repeatItems = [
      {
        title: LocalizedStrings['plug_timer_onetime'],
        select: 0b00000000 == this.state.tempRepeatCopy
      },
      {
        title: LocalizedStrings['plug_timer_everyday'],
        select: 0b01111111 == this.state.tempRepeatCopy
      },
      {
        title: LocalizedStrings['plug_timer_sef_define'],
        select: 0b00000000 != this.state.tempRepeatCopy && 0b01111111 != this.state.tempRepeatCopy
      }
    ]
    return (
      <View style={{ alignItems: "center", marginBottom: 16 }}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {LocalizedStrings["plug_timer_repeat"]}
        </Text>
        <View style={{ marginTop: 15 }}>
          {this.repeatItems.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  if (index == 0) {
                    // 执行一次
                    this.setState({tempRepeatCopy: 0b00000000});
                  } else if (index == 1) {
                    // 每天
                    this.setState({tempRepeatCopy: 0b01111111});
                  } else {
                    // 自定义
                    let flag = 0b00000001;
                    let i = 0;
                    this.data.multiIndexArray = [];
                    for (i = 0; i < 7; i++) {
                      if ((this.state.tempRepeatCopy & flag) != 0) {
                        this.data.multiIndexArray.push((i+6) % 7);
                      }
                      flag = flag << 1;
                    }
                    this.setState({ showRepeatWeekDialog: true });
                  }
                }}
              >
                <View
                  style={{ maxWidth: "100%",
                    width: screenWidth, height: 54,
                    backgroundColor:
                      item.select == true ? "rgba(50,186,192,0.1)" : "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between"
                  }}
                  key={index}
                >
                  <Text
                    style={{
                      marginLeft: 30, fontSize: 16, color: item.select == true ? "#32BAC0" : "#000000", fontWeight: "500"
                    }}
                  >
                    {item.title}
                  </Text>
                  {item.select == true && (
                    <Image
                      style={{ width: 22, height: 22, marginRight: 22 }}
                      source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")}
                    ></Image>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: 20, marginLeft: 0, marginBottom: 10 }}>
          <TouchableOpacity
            onPress={() => {
              this.setState({ showRepeatModeDialog: false });
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#F5F5F5", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{LocalizedStrings.btn_cancel}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              let canSave = this.state.canSave || this.item.repeat != this.state.tempRepeatCopy;
              this.item.repeat = this.state.tempRepeatCopy;
              this.setState({ showRepeatModeDialog: false, canSave: canSave });
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#32BAC0", borderRadius: 23, justifyContent: "center", alignItems: "center", marginLeft: 20 }}>
              <Text style={{ fontSize: 16, color: "#ffffff" }}>{LocalizedStrings.btn_confirm}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  repeatWeekDialog() {
    return (
      <ChoiceDialog
        type={ChoiceDialog.TYPE.MULTIPLE}
        visible={this.state.showRepeatWeekDialog}
        useNewType={true}
        title={LocalizedStrings['plug_timer_custom_repeat']}
        options={[
          {
            title: LocalizedStrings['monday1']
          },
          {
            title: LocalizedStrings['tuesday1']
          },
          {
            title: LocalizedStrings['wednesday1']
          },
          {
            title: LocalizedStrings['thursday1']
          },
          {
            title: LocalizedStrings['friday1']
          },
          {
            title: LocalizedStrings['saturday1']
          },
          {
            title: LocalizedStrings['sunday1']
          }
        ]}
        selectedIndexArray={this.data.multiIndexArray}
        color="#32BAC0"
        buttons={[
          {
            // style: { color: 'lightblue' },
            callback:()=>{
              this.setState({showRepeatWeekDialog: false});
            }
          },
          {
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`selected:`, result);
              if (result && result.length <= 0) {
                // Toast.fail("smarthome_span_error");
                this.setState({showRepeatWeekDialog: false});
                return;
              }
              this.data.multiIndexArray = result;

              let repeat = 0b00000000;
              result.forEach((item) => {
                console.log("==============",(item+1) % 7);
                repeat = repeat | (0b00000001 << (item+1) % 7);
              });
              this.setState({
                showRepeatWeekDialog: false,
                tempRepeatCopy: repeat,
              });

              // this.setWeekByRepeat();
            }
          }
        ]}
        onDismiss={() => this.setState({ showRepeatWeekDialog: false })}
      />);
  }

  renderTimeDialog() {
    return (
      <MHDatePicker
        visible={this.state.showTimeDialog}
        title={this.state.setTime == 0 ? LocalizedStrings['csps_start'] : LocalizedStrings['csps_end']}
        type={MHDatePicker.TYPE.TIME24}
        datePickerStyle={{
          rightButtonStyle: {color: "#FFFFFF"},
          rightButtonBgStyle: {bgColorNormal: "#32BAC0", bgColorPressed: "#32BAC099"}
        }}
        onDismiss={() => this.setState({ showTimeDialog: false })}
        onSelect={(res) => {
          console.log(JSON.stringify(res));
          let str = `${ res.rawArray[0] }:${ res.rawArray[1] }`;
          this.state.setTime == 0 ? this.item.start = str : this.item.end = str;
          this.setState({ canSave : true });
        }}
        current={this.state.setTime == 0 ? (this.item.start ? this.item.start.split(':') : new Date()) :
          (this.item.end ? this.item.end.split(':') : new Date())}
      />
    );
  }

  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={LocalizedStrings['pss_dialog_content']}
        messageStyle={{textAlign: "center"}}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
            }
          },
          {
            text: LocalizedStrings["pss_dialog_title"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
              this.props.navigation.goBack();
            }
          }
        ]}
      />
    )
  }

  renderSettingContent() {
    return (<View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: Util.isDark() ? "#xm000000" : "#FFFFFF", alignItems: "center" }}>
      {/*{ this.renderTitleBar() }*/}
      <ListItem
        title={ LocalizedStrings['long_time_name']}
        value={this.item.name ? this.item.name : LocalizedStrings['plug_timer_no_set']}
        showSeparator={false}
        onPress={() => {
          this.setState({ showNameDialog: true });
        }}/>

      <ListItem
        title={ LocalizedStrings['csps_start']}
        value={this.item.start ? this.item.start : LocalizedStrings['plug_timer_no_set']}
        showSeparator={false}
        onPress={() => {
          this.setState({ setTime: 0, showTimeDialog: true });
        }}/>

      <ListItem
        title={ LocalizedStrings['csps_end']}
        value={this.getEndTimeValue()}
        showSeparator={false}
        onPress={() => {
          this.setState({ setTime: 1, showTimeDialog: true });
        }}/>
      <ListItem
        title={ LocalizedStrings['plug_timer_repeat']}
        subtitle={this.item ? Util.getRepeatString(this.item.repeat) : LocalizedStrings['plug_timer_onetime']}
        showSeparator={false}
        onPress={() => {
          this.setState({ showRepeatModeDialog: true, tempRepeatCopy: this.item.repeat });
        }}/>
      { this.renderNameDialog() }
      {/*{ this.repeatModeDialog() }*/}
      { this.repeatWeekDialog() }
      { this.renderTimeDialog() }
      { this._renderBackDialog() }
      { this.renderRepeatViewDialog() }
    </View>);
  }

  getEndTimeValue() {
    if (!this.item.start) {
      return this.item.end ? this.item.end : LocalizedStrings['plug_timer_no_set'];
    }

    if (this.item.end) {
      let startValue = parseInt(this.item.start.split(":")[0]) * 60 + parseInt(this.item.start.split(":")[1]);
      let endValue = parseInt(this.item.end.split(":")[0]) * 60 + parseInt(this.item.end.split(":")[1]);
      let text = this.item.end;
      if (startValue > endValue) {
        text = `${LocalizedStrings['setting_monitor_next_day']} ${this.item.end}`;
      }
      return text;
    }

    return LocalizedStrings['plug_timer_no_set'];
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f7f7f7"
  },
  titlStyle: {
    fontSize: 16,
    color: "black"
  },
  separator: {
    alignItems: "flex-start",
    height: 0.5,
    backgroundColor: "rgba(0, 0, 0, 0.15)"
  },
  effectiveTimeStyle: {
    marginLeft: 25,
    marginRight: 25,
    marginTop: 5,
    fontSize: 30,
    color: "#000000"
  },
  repeatViewStyle: {
    width: screenWidth,
    bottom: 0,
    // borderTopWidth:20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: Host.isPad ? 20 : 0,
    borderBottomRightRadius: Host.isPad ? 20 : 0,
    marginHorizontal: 0
    // height: 400
    // backgroundColor:'white'
  }
});