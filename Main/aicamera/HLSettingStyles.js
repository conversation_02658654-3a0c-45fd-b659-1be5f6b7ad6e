
import { StyleSheet, Dimensions } from 'react-native';
const { width } = Dimensions.get('window');
const HLSettingStyles = StyleSheet.create({
  advanceSetionStyle: {
    backgroundColor: 'transparent',
    width: width,
    height: 23

  },
  advanceSetionStyle1: {
    backgroundColor: 'transparent',
    width: width,
    height: 1

  },
  sectionItem_disabled: {
    fontSize: 15,
    color: 'rgba(0,0,0,0.4)'
  },
  sectionItemValue_disabled: {
    fontSize: 12,
    color: 'rgba(128,128,128,0.4)'
  },
  sectionItemTitle: {
    fontSize: 15
  },
  red_sectionItemTitle: {
    fontSize: 15,
    color: 'red',
    textAlign: 'center'
  },
  red_sectionItemTitle_disable: {
    fontSize: 15,
    color: 'rgba(255,0,0,0.4)',
    textAlign: 'center'
  },
  sectionItemValueTitle: {
    fontSize: 11
  },
  item_disabledStyle: {
    color: 'rgba(0,0,0,0.2)'
  },
  value_disabledStyle: {
    color: 'rgba(128,128,128,0.2)'
  }
});

export { HLSettingStyles };
