'use strict';

import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  FlatList,
  TouchableOpacity,
  BackHandler
} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from '../setting/SettingStyles';

import { Card, NavigationBar, Radio, Switch } from 'mhui-rn';

import Toast from "../components/Toast";
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

/**
 * @Author: byh
 * @Date: 2024/2/26
 * @explanation:
 * 整点报时
 * 选择报时整点
 *********************************************************/
export default class OnTimeSelect extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      clockState: 0,             //闹钟状态 关  倒计时 定时
      timesData: [],
      isAllSelect: false,
      // 默认是早8晚5    16777215 为24个1全部选中
      // 20241025@byh 更新自定义默认值早八晚八
      timeValue: this.props.navigation.getParam("timeValue"),
    };
  }

  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ title: LocalizedStrings['on_time_alarm_time_select'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };

  componentDidMount() {
    this.setNavigation();
    // 侧滑返回监听
    BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);

    this.getTimesData(false);

  }

  setNavigation() {
    this.props.navigation.setParams({
      title: "",
      titleNumberOfLines: 2,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.goBackPage();
          }
        }
      ],
      right: [
        {
          key: this.state.isAllSelect ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL,
          onPress: () => {
            if (this.state.isAllSelect) {
              this.setState({ timeValue: 0, isAllSelect: false },() => {
                this.getTimesData();
              });
            } else {
              this.setState({ timeValue: 16777215, isAllSelect: true },()=>{
                this.getTimesData();
              });
            }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }

  componentWillUnmount() {
    BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    this.setState = () => false;
  }

  onBackHandler = () => {
    this.goBackPage();
    return true;
  }

  goBackPage() {
    if (this.state.timeValue == 0) {
      Toast.success('at_least_choose_one');
      return;
    }
    this.props.navigation.goBack();
  }
  getTimesData(needCallback = true) {
    let arr = [];
    for (let i = 0; i < 24; i++) {
      let hour = i < 10 ? `0${i}` : i;
      let timeStr = `${hour}:00`;
      let value = 1 << i;
      let isSelect = value & this.state.timeValue;
      let item = { name: timeStr, isSelect: isSelect, value : value };
      arr.push(item);
    }
    console.log("===========arr:",arr);
    if (needCallback) {
      if (this.state.timeValue != 0) {
        let callback = this.props.navigation.getParam('callback');
        if (callback) {
          callback(this.state.timeValue);
        }
      }
    }

    this.setState({ timesData: arr, isAllSelect: this.state.timeValue == 16777215 },()=>{
      console.log("====isallselect",this.state.isAllSelect);
      this.setNavigation();
    });
  }

  render() {

    return (
      <View style={ [styles.container] }>
        <ScrollView showsVerticalScrollIndicator={ false } onScroll={this.scrollViewScroll} contentContainerStyle={ { flexGrow: 1 } }>
          <View key={ 103 }>
            <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0}>
              <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight:"300",position: "relative", marginLeft: 25, marginTop: 3, marginBottom:23,fontFamily:'MI-LANTING--GBK1-Light'  }}>
                {LocalizedStrings['on_time_alarm_time_select']}
              </Text>
            </View>
            <View style={ { height: 25 } }/>
            <FlatList
              data={ this.state.timesData }
              contentContainerStyle={{marginHorizontal: 16}}
              numColumns={4}
              renderItem={ this._renderTimeItem }
              ItemSeparatorComponent={ () => <View style={ { height: 10 } }/> }
              keyExtractor={ (item, index) => `key_${ index }` }
            />
            <View style={ { height: 15 } }/>
          </View>

        </ScrollView>
      </View>
    );
  }

  _renderTimeItem = ({ item, index }) => {
    let itemWidth = (screenWidth - 30 - 22 * 2) / 4;
    let containerStyle = {
      borderRadius: 12,
      paddingVertical: 10,
      paddingHorizontal: 15,
      width: itemWidth,
      // marginVertical: 10,
      marginHorizontal: 5,
      alignItems: "center"
    }
    let textStyle = {
      fontSize: 14
    }

    containerStyle.backgroundColor = item.isSelect ? "#EAF8F9" : "#F0F0F0"
    textStyle.color = item.isSelect ? "#32BAC0" : "#000000"

    return (
      <TouchableOpacity onPress={() => {
        let value;
        if (item.isSelect) {
          value = this.state.timeValue & ~item.value;
        } else {
          value = this.state.timeValue | item.value;
        }
        this.setState({ timeValue: value }, () => {
          this.getTimesData();
        })
      }}>
        <View style={containerStyle}>
          <Text style={textStyle}>{item.name}</Text>
        </View>
      </TouchableOpacity>
    );
  };
}
const stylesClock = StyleSheet.create({
  clockContainer: {
    backgroundColor: '#f5f5f5',
    height: "100%"
  },
  itemContainer2: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 15,
    borderRadius: 10,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'column'
  },
  itemContainer: {
    marginTop: 15,
    marginBottom: 15,
    paddingHorizontal: 15,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'row'
  },
  itemSubContainer: {
    marginBottom: 15,
    paddingHorizontal: 15,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'row'
  },

  checkTitle: {
    flex: 1,
    height: 40,
    justifyContent: 'center'
  },
  checkSubTitle: {
    flex: 1,
    height: 30,
    justifyContent: 'center'
  },
  rightArrow: {
    width: 15,
    height: 15
  },

  title:{
    color: "#000000",
    fontSize: 16,
    fontWeight: "400"
  },
  separatorLine:{
    color: "#979797",
    fontSize: 16,
    fontWeight: "400",
    marginHorizontal: 10
  },
  subtitle:{
    color: "#979797",
    fontSize: 16,
    fontWeight: "400",
    marginRight: 10
  }
});
