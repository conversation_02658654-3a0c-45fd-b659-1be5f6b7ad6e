'use strict';

import React from 'react';
import { View, Text, StyleSheet, Image, ScrollView, Dimensions } from 'react-native';
import Util from "../util2/Util";

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;

export default class AICard extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      faceSwitch: false,
      enableFaceManager: false,
      babyPush: false,
      isVip: false,
      facePush: false,
      pedestrianDetectionPushSwitch: false,
      showGBFDialog: false
    };

  }

  render() {   
    return (
      <View style={styles.container}>
        <View style={styles.box}>
          <View style={{ width: "100%" }}>
            <Image style={{ width: viewWidth, height: viewHeight, marginVertical: 20, borderRadius: 9 }} source={this.props.img}>
            </Image>
          </View>
          <View style={styles.switchContent}>
            {this.props.switchButton}
          </View>
          <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
            <Text style={styles.dnnText}>
              {this.props.desc}
            </Text>
          </ScrollView>
          <View style={{ width: "100%" }}>
            {this.props.bottomBtn}
          </View>
        </View>

      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : 'white'
  },
  box: {
    marginHorizontal: 20,
    display: 'flex',
    alignItems: 'center',
    // width: "100%",
    height: "100%"
  },
  switchContent: {
    borderBottomColor: "#eee",
    borderBottomWidth: 1,
    height: 80,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    marginBottom: 35
  },
  dnnText: {
    textAlign: 'left',
    fontSize: 15,
    fontFamily: "MI Lan Pro",
    fontWeight: "300",
    lineHeight: 23,
    color: "#4D4D4D"
  }
});
