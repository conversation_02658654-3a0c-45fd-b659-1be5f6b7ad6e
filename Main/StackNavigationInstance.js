import { DeviceEventEmitter } from "react-native";
import Toast from './components/Toast';
export const SD_CLOUD_STACK_NAVIGATION_ONPAUSE = "SD_CLOUD_STACK_NAVIGATION_ONPAUSE";
export const SD_CLOUD_STACK_NAVIGATION_ONRESUME = "SD_CLOUD_STACK_NAVIGATION_ONRESUME";
export const SD_CLOUD_STACK_NAVIGATION_ONBACK = "SD_CLOUD_STACK_NAVIGATION_ONBACK";

// 解决tabNavigation里的控件跳转其他页面 跳不到的问题，因为navigation不一样。
export default class StackNavigationInstance {
  
  static isRecording = false;
  static sSdcardCloudTimelinePage = null;

  static setStackNavigationInstance(navigation) {
    if (navigation != null && navigation.state != null && navigation.state.routeName != null && navigation.state.routeName == "SdcardCloudTimelinePage") {
      this.sSdcardCloudTimelinePage = navigation;
    }
  }

  static jumpToStackNavigationPage(pageName, param) {
    this.sSdcardCloudTimelinePage && this.sSdcardCloudTimelinePage.navigate(pageName, param);
  }

  static getStackNavigationInstance() {
    return this.sSdcardCloudTimelinePage;
  }

  static goBack() {
    if (this.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }

    DeviceEventEmitter.emit(SD_CLOUD_STACK_NAVIGATION_ONBACK);
    this.sSdcardCloudTimelinePage && this.sSdcardCloudTimelinePage.goBack();
  }
}