import { Device } from "miot";
import resolve from "miot/native/common/node/resolve";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import RPC from "./RPC";

export function fetchLogUploaderStatus() {
  return new Promise((resolve, reject) => {
    RPC.callMethod("get_dlog_switch", [])
      .then((result) => {
        let { accept, switchOpen, startTime, endTime } = result.result;
        console.log("get_dlog_switch", result.result);
        let mNow = new Date().getTime() / 1000;
        if (startTime != 0 && endTime != 0 && accept == 0 && switchOpen && endTime > mNow) {
          resolve({ msg: LocalizedStrings["log_upload_hint"], showDialog: true });
        } else {
          reject({ msg: "no need show", showDialog: false });
        }
      })
      .catch((error) => {
        reject({ msg: JSON.stringify(error), showDialog: false });
      });

  });
}

/**
 * 
 * @param {int} accept 1 同意  2 拒绝 
 * @returns 
 */
export function feedbackLogUploaderStatus(accept) {
  return new Promise((resolve, reject) => {
    RPC.callMethod("set_dlog_accept", { accept })
      .then((result) => {
        resolve(true);
      })
      .catch((error) => {
        reject(false);
      });
  });
}