import { Device, Host, Service } from "miot";
import { Platform } from "react-native";

export default class CheckFirmwareUpdate {

  static checkFirmwareVersion() {
    console.log(Device);
    if (!Device.isOwner) {
      return Promise.reject({ code: 401, message: "is shared " });
    }
    if (Device.deviceID == null) {
      return Promise.reject({ code: 401, message: "null did" });
    }

    if (Device.isVirtualDevice) {
      return Promise.reject({ code: 401, message: "virtual device" });
    }


    if (!Device.isOnline) {
      return Promise.reject({ code: 401, message: "not online" });
    }

    // return new Promise((resolve,reject) => {
    //   let app_level = Host.version;
    //   let platform = Platform.OS;
    //   let check_reqs = [{ did: Device.deviceID }];
    //   Service.callSmartHomeAPI('/v2/device/multi_check_device_version',{ app_level,platform,check_reqs })
    //     .then(res => {

    //       let infos = res.list;
    //       let needUpgrade = false;
    //       let upgrading = false;
    //       let latestVersion = '';
    //       if (!(infos instanceof Array) || infos.length <= 0) {
    //         // infos 非数组，不处理
    //         return resolve({ needUpgrade: false,force: false,upgrading: false });
    //       }
    //       let latest = infos[0]
    //       if (!latest) {
    //         //不升级提示
    //         return resolve({ needUpgrade: false,force: false,upgrading: false });
    //       }
    //       latestVersion = latest.latest;
    //       //根据native逻辑，只有需要升级和升级中更需要跳转升级页面
    //       if (!latest.isLatest && latest.latest !== latest.curr && !latest.updating) {
    //         if (latest.ota_status === 'failed') {
    //           //更新失败
    //         } else {
    //           //需要更新
    //           needUpgrade = true;
    //         }
    //       } else if (latest.ota_status === 'downloading' || latest.ota_status === 'downloaded' || latest.ota_status === 'installing') {
    //         //正在升级安装
    //         upgrading = true;
    //       }
    //       //内部事件，不需要提供给外部
    //       return resolve({ needUpgrade,force: latest.force,upgrading,latestVersion });
    //     })
    //     .catch(err=>{

    //     })

    // })

    return new Promise((resolve, reject) => {
      Service.smarthome.getFirmwareUpdateInfo(Device.deviceID)
        .then(({ needUpgrade }) => {
          resolve(needUpgrade);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
}
