import Service from "miot/Service";
import { Platform } from "react-native";
import { getStack } from "../index";
import CameraConfig from '../util/CameraConfig';
import { Package, Device } from 'miot';
import Singletons from "../framework/Singletons";
import UriPlayer from "../framework/UriPlayer";
import StorageKeys from '../StorageKeys';

export default class PushHandler {
  static handlePushFromStartUp(did, type, event, extra) { // 从外面的push进来插件 要干掉这个plugin
    if (type === "ScenePush" && event === "smart_camera_motion" && did) {
      if (extra != null) {
        try {
          let data = extra;
          if (Platform.OS == "android") {
            data = JSON.parse(extra);
          }
          data.did = did;
          data.skipPwd = true;
          Service.miotcamera.openAlarmVideoPlayer(data);
          this.isFromScenePush = true;
          Package.exit();
          // 从这里进来的 都是从外部push蹦过来的 应该要杀掉RN，直接跳原生页面.
        } catch (error) {
          console.log(error);
        }

      }

    }
  }

  // 插件内部  必然已经刷新过了 是否是云存支持国家和国际服
  async handlePushFromInnerPlugin(did, event, extra, time) { // 处理在插件过程运行中点击push的事件。
    let data = extra;
    if (Platform.OS == "android") {
      data = JSON.parse(extra);
    }
    const isExtraJson = (extra === '') || (!extra);
    if (isExtraJson ? false : typeof(extra) === 'string' ? JSON.parse(extra)?.jump2Live : extra.jump2Live) { // 海外云存需求，推送消息是否跳转到直播页面
      const navigationRouter = getStack()._navigation;
      if (navigationRouter.state.routes.length > 1) {
        navigationRouter.navigate('LiveVideoPageV2');
      }
      return;
    }

    data.did = did;
    data.skipPwd = true;
    if (event === "smart_camera_motion" && did && extra) {

      if (CameraConfig.shouldDisplayNewStorageManage(Device.model)) {
        let rootStack = getStack();

        if (rootStack && rootStack._navigation) {
          if (event == "7.1") {
            return;
          } else if (event == "7.2") {
            return;
          }
          let videoFileId = data.fileId;
          let createTime = data.createTime;
          let isAlarm = true;
          if (data.hasOwnProperty("isAlarm")) {
            isAlarm = data.isAlarm;
          }
          let offset = 0;
          if (data.offset) {
            offset = data.offset;
          }
          this.exitTimeout = setTimeout(() => {
            let params = { item: { fileId: videoFileId, createTime: createTime, offset: offset, isAlarm: isAlarm,
              playCfg: { loader: "_CloudEventLoader" } }, cfg: { loader: Singletons.CloudEventLoader, player: UriPlayer },
            lstType: "push",
            items: null,
            loaderArgs: { startDate: new Date(), filter: "Default", nextDate: null },
            pushType: "inner"
            };

            rootStack._navigation.push('AlarmVideoUI', params);
          }, 50);

        }

        this.isFromScenePush = true;
        // 从这里进来的 都是从外部push蹦过来的 应该要杀掉RN，直接跳原生页面.
      } else {
        Service.miotcamera.openAlarmVideoPlayer(data);
      }

    } else if (event == "motion" && did && time) {
      data.time = time;
      Service.miotcamera.openAlarmVideoPlayer(data);
      Package.exit();
      return;
    } else if (event == '26.3') {
      this.alarmUIParam = {
        lstType: "push",
        pushType: "inner"
      };
      const navigationRouter = getStack()._navigation;
      navigationRouter.navigate('AlarmPage',this.alarmUIParam);
    }
  }
}