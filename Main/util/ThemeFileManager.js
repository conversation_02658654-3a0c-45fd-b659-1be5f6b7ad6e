import { Service, Device, Host } from 'miot';
import base64js from 'base64-js';
import { DeviceEventEmitter, PermissionsAndroid, Platform } from 'react-native';
import CameraPlayer from './CameraPlayer';
import LogUtil from './LogUtil';
import FDSUtil from "./FDSUtil";
import CallUtil from "./CallUtil";


const KEY = `HomeTheme${ Device.deviceID }${ Service.account.ID }storage`;
const kRDTDataReceiveCallBackName = 'rdtDataReceiveCallBack2';

export const DownloadVideoState = {
  DOWNLOADED: 1,
  FAILED: 2
};

Object.freeze(DownloadVideoState);

const TAG = "ThemeFileManager";

// 这种普通数据类 参考: IBluetoothLock
export default class ThemeFileManager {

  static sDowningProgress = "DOWNLOAD_PROGRESS"
  // 获取sd卡文件
  static getInstance() {
    if (!this.instance) {
      this.instance = new ThemeFileManager();
    }
    return this.instance;
  }

  constructor() {
    this.timeItems = [];// 放置timeItems的地方

    this._bindRdtFilesListener();
    this.needCheckUrlExpires = false;
    this.rdtCommandList = [];// 存储下发指令的队列，依次从队列里取
    this.themeImgUrls = [];// 存储所有家庭相册的网络url原始链接

    this.receiveFileListener = null;

    this.commandBuffer = null;
    this.commandSize = 0;
    this.currentDataLength = 0;
    this.commandOffset = 0;
    this.rdtCommand = 0;
    this.downloadingImgOrder = 0;
    this.cmdCode = 0;
    this.totalImgCount = 0;
    this.isAllRdtDataReceived = false;
    this.downloadingImgTimestamp = 0;

    this.isSendingCmd = false;

    this.isOnCmdError = false;
    this.onErrorRetryTimeout = null;
    this.isHandleFileList = false;


    this.rdtErrorTime = 0;
    this.channel = 0;
    this.connectionListener = CameraPlayer.getInstance()
      .addConnectionListener((connectionState) => {
        if (connectionState.state < 1) {
          LogUtil.logOnAll(TAG, "p2p连接断开，重置rdt相关状态");
          this.commandBuffer = null;
          this.commandSize = 0;
          this.commandOffset = 0;
          this.rdtCommand = 0;
          this.isAllRdtDataReceived = false;
          this.isOnCmdError = true;
          CameraPlayer.getInstance().startConnect();
          // 没有主动重置状态呀;
          this.handleErrorRetry();// 网络连接断开了。通知一下外面的人。暂时不做其他的处理了。
        }
      });
    
    this.longNoDataTimeout = null;

  }


  _bindRdtFilesListener() {
    this.rdtListener = DeviceEventEmitter.addListener(kRDTDataReceiveCallBackName, ({ data }) => {
      this.handleRdtData(data);
    });
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
  }

  handleRdtData(data) {
    // LogUtil.logDebug("   ...");
    // here handle data
    if (data == null || data.length <= 0) {
      LogUtil.logOnAll(`收到数据为空: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp }`);
      return;
    }
    // here发一个消息吧，避免中间出问题了，例如p2p连接断开了，等等导致所有的消息都卡住不播放的问题。
    if (this.longNoDataTimeout != null) {
      clearTimeout(this.longNoDataTimeout);
      this.longNoDataTimeout = null;
    }
    this.longNoDataTimeout = setTimeout(() => {
      // LogUtil.logOnAll(TAG, "比较长的时间内一直没有收到数据");// 因为转存视频 需要比较多的时间  这里做检测也不合适。
    }, 1000);

    if (this.isOnCmdError) {
      let time = new Date().getTime();
      if (time - this.rdtErrorTime < 1000) {
        clearTimeout(this.onErrorRetryTimeout);
        this.onErrorRetryTimeout = setTimeout(() => {
          // LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，还在接收数据，在此期间收到的数据都不处理， 直到1000ms内都不收任何数据  再走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
      }
      this.rdtErrorTime = time;
      return;
    }

    this.currentDataLength = 0;
    try {
      let result = null;
      try {
        result = base64js.toByteArray(data);// uint8array
      } catch (exception) {
        this.isOnCmdError = true;
        LogUtil.logOnAll(TAG, `接收数据出错：${ JSON.stringify(exception) }${ exception.stack } data:${ data }`);
        clearTimeout(this.onErrorRetryTimeout);
        this.onErrorRetryTimeout = setTimeout(() => {
          LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，1000ms后都没有收到其他数据了  走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
        return;
      }

      if (result == null || result.length == 0) { // base64转换的时候有可能返回空
        this.isOnCmdError = true;
        clearTimeout(this.onErrorRetryTimeout);
        LogUtil.logOnAll(TAG, "接收数据为空");
        this.onErrorRetryTimeout = setTimeout(() => {
          LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，1000ms后都没有收到其他数据了  走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
        return;
      }
      this.currentDataLength = result.length;
      console.log(`yyh commandSize: ${ this.commandSize }  rdtCommand:  ${ this.rdtCommand }`);
      if (this.commandSize == 0 && this.rdtCommand == 0) {
        this.commandBuffer = null;
        this.commandSize = 0;
        this.commandOffset = 0;
        this.rdtCommand = 0;
        this.isAllRdtDataReceived = false;

        let command = this.byteArrayToInt(result, 0);
        if (command <= 0 || command > 65535) {
          LogUtil.logOnAll(TAG, `不合理的命令:${ command }`);
          throw ("invalid command");

        }
        this.rdtCommand = command;

        let size = this.byteArrayToInt(result, 4);
        if (size <= 0 || size >= 20971520) {
          LogUtil.logOnAll(TAG, `不合理的size:${ size }`);
          throw (`invalid size:${ size }`);
        }
        this.commandSize = size;

        let status = this.byteArrayToInt(result, 8);

        let rawSize = size;
        // let currentPacketDataSize = result.length - 12;

        this.commandOffset = 0;
        this.commandBuffer = new Uint8Array(rawSize);
        LogUtil.logOnAll(TAG, `数据: offset:${ this.commandOffset } resultLength:${ result.length } bufferSize:${ this.commandBuffer.length }  rdtCommand:${ this.rdtCommand }`);
        // this.commandBuffer.set(result.slice(12));
        // if (currentPacketDataSize >= this.commandSize) {
        //   this.isAllRdtDataReceived = true;
        // }
        // LogUtil.logOnAll(TAG, `数据: offset:${this.commandOffset} resultLength:${result.length} bufferSize:${this.commandBuffer.length}`);

      } else {
        if (this.cmdCode === 10 && result[0] > 0) { // 取到固件相册长度
          console.log(`收到长度数据  ${ result }`);
          this.isAllRdtDataReceived = true;
          LogUtil.logOnAll(TAG, `家庭相册个数:${ result[0] }`);
          this.totalImgCount = result[0];
          this.isSendingCmd = false;
          if (result[0] > 0 && result[0] < 10) {//防止收到非常大的数据
            Host.storage.get(KEY).then((val) => {
              // load val success}
              const list = val ? JSON.parse(val) : [];
              if (result[0] > list.length) { // 需要拉取固件图片
                this.startRequestThemeImgUrls();
              }
            });
          }
        } else { // 固件相册数据
          // console.log(`收到固件相册数据  ${  result.length}  commandOffset: ${  this.commandOffset}  `);
          this.commandBuffer.set(result, this.commandOffset);
          this.commandOffset = this.commandOffset + result.length;
          if (this.commandOffset >= this.commandSize) {
            this.isAllRdtDataReceived = true;
          }
        }
      }

      if (this.rdtCommand > 0 && this.commandSize > 0 && this.isAllRdtDataReceived) {
        this.isSendingCmd = false;
        if (this.commandBuffer.length > 0) {
          console.log(`yyh 图片数据已获取完成 ${ this.cmdCode }`);
          if (this.cmdCode >= 1 && this.cmdCode <= 9) {
            let tmpBuffer = new Uint8Array(this.commandBuffer);
            this.parsePicFile(tmpBuffer);
          } else if (this.cmdCode === 11) { // 取到原始图片网络url
            try {
              if (this.themeImgUrls.length > 0) {
                
              } else {
                // 将字节数组直接转换为字符串
                if (this.commandBuffer.length > 1) {
                  const urls = String.fromCharCode.apply(null, this.commandBuffer);
                  LogUtil.logOnAll("解码后的URLs:", urls);

                  // 使用*#分割字符串并保存到数组中
                  const urlArray = urls.split('*#').map(url => url.replace(/\u0000/g, ''));

                  // 这里可以进一步处理urlArray，比如保存到类的属性中
                  this.themeImgUrls = [];
                  Host.storage.get(KEY).then((val) => {
                    // load val success}
                    const list = val ? JSON.parse(val) : [];
                    let newList = [];
                    if (list.length > urlArray.length) {
                      this.needCheckUrlExpires = true;
                    }
                    urlArray.forEach((url, index) => {
                      const hasIndex = list.findIndex((item) => item.url === url);
                      if (hasIndex >= 0 && list[hasIndex].fileName) {
                        const item = list[hasIndex];
                        newList.push({ localUrl: item.localUrl, url: url, order: index + 1, createTime: item.createTime, fileName: item.fileName });
                        list.splice(hasIndex, 1); // 从 list 中移除该 item
                      } else {
                        const newItem = { localUrl: "", url: url, order: index + 1 };
                        newList.push(newItem);
                        this.themeImgUrls.push(newItem);
                      }

                    });
                    // 更新本地缓存
                    Host.storage.set(KEY, JSON.stringify(newList));
                    // 需要拉取固件图片
                    if (this.themeImgUrls.length > 0) {
                      this.needCheckUrlExpires = true;
                      this.themeImgUrls.forEach((item) => {
                        this.startRequestThemeImgData(item.order);
                      });
                    } else {
                      if (this.needCheckUrlExpires) {
                        this.setThemeUrlToDevice(newList);
                      }
                      DeviceEventEmitter.emit('themeImagesDownloadComplete', {
                        success: true
                      });
                    }

                    list.forEach((item) => {
                      if (item && item.fileName) {
                        // 删除本地图片
                        Host.file.deleteFile(item.fileName);
                      }
                    });

                  });
                } else {
                  Host.storage.get(KEY).then((val) => {
                    const list = val ? JSON.parse(val) : [];
                    if (list.length > 0) {
                      list.forEach((item) => {
                        if (item && item.fileName) {
                          // 删除本地图片
                          Host.file.deleteFile(item.fileName);
                        }
                      });
                      let newList = [];
                      // 更新本地缓存
                      Host.storage.set(KEY, JSON.stringify(newList));
                    }
                    DeviceEventEmitter.emit('themeImagesDownloadComplete', {
                      success: true
                    });
                  });
                }
              }
              
            } catch (error) {
              this.themeImgUrls = [];
              this.isSendingCmd = false;
              console.log("解码字节数组时出错:", error);
            }
          }
        }
        this.commandBuffer = null;
        this.commandSize = 0;
        this.commandOffset = 0;
        this.rdtCommand = 0;
        this.isAllRdtDataReceived = false;
        console.log("yyh 赋值已重置");

      }

    } catch (err) {
      LogUtil.logOnAll(TAG, `处理rdt数据的时候出错了 error:${ err } message stack:${ err.stack }`, "过300ms再选择跑路");
      this.rdtErrorTime = new Date().getTime();
      this.handleRdtErr(err);
    }
  }

  handleRdtErr(err) { // 出错了就重置状态吧  不再重试和请求下一步了
    LogUtil.logOnAll(TAG, "rdt数据当前状态: command:", this.rdtCommand, "offset", this.commandOffset, ` currentDatalength:${ this.currentDataLength }`, ` cmdBuffer length:${ this.commandBuffer != null ? this.commandBuffer.length : 0 }`, " totalSize: ", this.commandSize, `${ ` downloadingVideoTimestamp:${ this.downloadingVideoTimestamp } downloadingImgTimestamp:${ this.downloadingImgTimestamp }` + " ishandleFileList:" }${ this.isHandleFileList }`);
    // if (this.command <= 0) {
    this.commandBuffer = null;
    this.commandSize = 0;
    this.commandOffset = 0;
    this.rdtCommand = 0;
    this.isAllRdtDataReceived = false;
    this.isOnCmdError = true;
    clearTimeout(this.onErrorRetryTimeout);
    this.onErrorRetryTimeout = setTimeout(() => { // 错误以后隔3s再去从任务队列拉数据 并屏蔽此时的错误信息接收
      this.handleErrorRetry();
    }, 3000);

  }

  handleErrorRetry() {
    LogUtil.logOnAll(TAG, "执行rdt命令出错了，置空状态");
    if (this.isHandleFileList) { // 文件列表
      clearTimeout(this.requestSdfilesTimeout);
      this.receiveFileListener && this.receiveFileListener(false);// 接收数据出错了。
      this.isHandleFileList = false;

    }
    this.isOnCmdError = false;

    this.isSendingCmd = false;

    this.pollAndSendCommand();
  }

  byteArrayToInt(data, position) {
    return (0xff & data[position]) | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2]) << 16 | (0xff & data[position + 3]) << 24;
  }


  startRequestThemeImgCount() {
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
    let data = [];
    data[0] = 17;
    data[1] = 10; // 命令码
    this.pushCommand(data);// 往里面丢
    console.log("startRequestThemeImgCount");
    
    this.pollAndSendCommand();// 尝试消费
  }

  startRequestThemeImgUrls() {
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
    let data = [];
    data[0] = 17;
    data[1] = 11; // 命令码
    this.pushCommand(data);// 往里面丢
    console.log("startRequestThemeImgUrls");

    this.pollAndSendCommand();// 尝试消费
  }

  /**
   * 请求下载对应索引的图片
   * @param index
   */
  startRequestThemeImgData(index) {
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
    let data = [];
    data[0] = 17;
    data[1] = index; // 命令码 1-9 表示请求的对应照片
    this.pushCommand(data);// 往里面丢
    LogUtil.logOnAll(TAG, `发送下载第${ index }张图片的请求`);

    this.pollAndSendCommand();// 尝试消费
  }



  pushCommand(dataArray) {
    if (dataArray == null || dataArray.length <= 1 || this.rdtCommandList == null) {
      return;// 数据不合理
    }
    this.rdtCommandList.push(dataArray);// 放进去的是array 放在队尾 后面取用shift 从队列头部取
  }

  // 只有sdcardEventType才有这个需求，其他的都不行。
  insertCommandAtFront(dataArray) {
    if (dataArray == null || dataArray.length <= 1 || this.rdtCommandList == null) {
      return;// 入队失败。
    }
    this.rdtCommandList.unshift(dataArray);// 数据插入到队列头部。优先执行
  }

  shiftCommand() {
    if (this.rdtCommandList == null || this.rdtCommandList.length == 0) { // 发完了或者destroy了
      return null;
    }
    return this.rdtCommandList.shift();
  }

  pollAndSendCommand() {
    if (this.isSendingCmd) {
      LogUtil.logOnAll(TAG, `还在执行其他任务 : isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
      return;// 正在下发指令  滚蛋
    }

    let dataArray = this.shiftCommand();// 数据体
    console.log("pollAndSendCommand", dataArray);
    
    if (dataArray == null) {
      return;
    }

    this.isSendingCmd = true;
    if (dataArray[0] == 17) {
      let buf = new ArrayBuffer(8);
      let data = new Uint32Array(buf);
      data[0] = dataArray[0];
      // data[1] = 0;
      let byteArray = new Uint8Array(buf);
      byteArray[4] = dataArray[1]; // 表示对应命令码
      this.cmdCode = dataArray[1];
      let base64Data = base64js.fromByteArray(new Uint8Array(buf));
      Service.miotcamera.sendRDTCommandToDevice(base64Data)
        .then(() => {
          LogUtil.logOnAll(TAG, 'send file list rdt cmd ok');
          if (dataArray[1] >= 1 && dataArray[1] <= 9) {
            LogUtil.logOnAll(TAG, `开始同步第 ${ dataArray[1] }张图片。`);
            this.downloadingImgOrder = dataArray[1];
          }
        })
        .catch(() => {
          LogUtil.logOnAll(TAG, "开始同步主题。。发送命令错误");
        });
    }

  }



  bindDownloadVideoFileListener(videoFileListener) {
    this.videoFileDownloadListener = videoFileListener;
  }

  destroyInstance() { // 避免内存泄漏
    // clearInterval(this.requestSdfilesInterval);
    clearTimeout(this.onErrorRetryTimeout);

    this.rdtCommandList = null;
    this.themeImgUrls = [];

    this.rdtListener && this.rdtListener.remove();
    this.connectionListener && this.connectionListener.remove();
    

    ThemeFileManager.instance = null;
  }

  parsePicFile(data) {
    LogUtil.logOnAll(TAG, `下载视频缩略图成功，待转存到sdcard`);
    if (data == null) {
      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      LogUtil.logOnAll(TAG, `invalid final data: is date null?${ data == null } `);
    }
    const currentTime = new Date().getTime();
    const imageName = `${ Service.account.ID }${ currentTime }`;
    Host.file.writeFileThroughBase64(imageName, base64js.fromByteArray(data)).then((isSuccess) => {
      if (isSuccess) {

        LogUtil.logOnAll(TAG, `下载缩略图:${ imageName } 成功并写入成功`);

        // 更新本地映射
        Host.storage.get(KEY).then((val) => {
          // load val success}
          const list = val ? JSON.parse(val) : [];
          // 每次都要重新排序
          list[this.downloadingImgOrder - 1].createTime = currentTime;
          list[this.downloadingImgOrder - 1].fileName = imageName;
          list.sort((a, b) => a?.order - b?.order);

          Host.storage.set(KEY, JSON.stringify(list));
          LogUtil.logOnAll(TAG, `下载完毕，继续执行下一个任务。`);
          this.isSendingCmd = false;
          if (this.downloadingImgOrder >= this.themeImgUrls[this.themeImgUrls.length - 1].order) {
            this.themeImgUrls = [];
            console.log("全部下完了 哈哈");
            // 发送下载完成事件
            if (this.needCheckUrlExpires) { // 需要重新url是否过期重新上传
              Host.storage.get(KEY).then((val) => {
                // load val success}
                const currentTime = Date.now(); // 当前时间戳（秒）
                // 收集所有需要重新上传的图片的Promise
                const uploadPromises = [];
                const data = val ? JSON.parse(val) : [];
                for (let i = 0; i < data.length; i++) {
                  const item = data[i];
                  if (item.url && item.fileName) {
                    // 从URL中提取Expires参数
                    const expiresMatch = item.url.match(/Expires=(\d+)/);
                    if (!expiresMatch) continue;

                    const expiresTime = parseInt(expiresMatch[1]);
                    if (expiresTime - 10000 < currentTime) {
                      LogUtil.logOnAll(` 第${data[i].order}张图片已过期，过期时间：${new Date(expiresTime).toLocaleString()}`);
                      // 将重新上传的Promise添加到数组中
                      uploadPromises.push(
                        FDSUtil.uploadAvatarToServer(data[i].fileName)
                          .then((res) => {
                            data[i].url = res.downloadUrl;
                            LogUtil.logOnAll(` 第${data[i].order}张图片重新上传成功---` + res.downloadUrl);
                          })
                          .catch((error) => {
                            LogUtil.logOnAll(` 第${data[i].order}张图片重新上传失败:`, error);
                            // 如果上传失败，保持原URL不变
                          })
                      );
                    }
                  }
                }

                // 等待所有过期图片重新上传完成
                Promise.all(uploadPromises)
                  .then(() => {
                    LogUtil.logOnAll(' 所有过期图片处理完成');
                    // 更新状态和存储
                    data.sort((a, b) => a?.order - b?.order);
                    const finalList = data?.map((item, index) => {
                      item.order = index + 1;
                      return item;
                    });
                    this.needCheckUrlExpires = false;
                    Host.storage.set(KEY, JSON.stringify(finalList));
                    this.setThemeUrlToDevice(finalList);
                    DeviceEventEmitter.emit('themeImagesDownloadComplete', {
                      success: true
                    });
                  })
                  .catch((error) => {
                    LogUtil.logOnAll('处理过期图片时出错:', error);
                    // 即使有错误也更新状态
                    data.sort((a, b) => a?.order - b?.order);
                    const finalList = data?.map((item, index) => {
                      item.order = index + 1;
                      return item;
                    });
                    this.needCheckUrlExpires = false;
                    Host.storage.set(KEY, JSON.stringify(finalList));
                    this.setThemeUrlToDevice(finalList);
                    DeviceEventEmitter.emit('themeImagesDownloadComplete', {
                      success: true
                    });
                  });
              });
            } else {
              DeviceEventEmitter.emit('themeImagesDownloadComplete', {
                success: true
              });
            }
          } else {
            this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
          }
        });
      } else {
        LogUtil.logOnAll(TAG, `下载缩略图:${ imageName } 成功但是写入失败`);
        this.isSendingCmd = false;
        this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      }

    }).catch(() => {
      // 写入失败了
      LogUtil.logOnAll(TAG, `下载缩略图:${ imageName } 成功但是转存失败了。`);

      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
    });
  }

  setThemeUrlToDevice(imageList) {
    const newList = imageList.map((item) => {
      return {
        order: item.order,
        url: item.url,
      };
    });
    newList.sort((a, b) => a?.order - b?.order);
    // 每次最多上传5个
    if (imageList?.length > 5) {
      const first = newList.slice(0, 5);
      const second = newList.slice(5, imageList?.length);
      CallUtil.updateThemeToDevice(JSON.stringify({ total_num: newList?.length, modify: { type: 2, order: -1 }, data: first })).then(() => {
        CallUtil.updateThemeToDevice(JSON.stringify({ total_num: newList?.length, modify: { type: 2, order: -1 }, data: second }));
      });
    } else {
      CallUtil.updateThemeToDevice(JSON.stringify({ total_num: newList?.length, modify: { type: 2, order: -1 }, data: newList }));
    }
    this.needCheckUrlExpires = false;
  }
}