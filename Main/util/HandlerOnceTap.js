let isCalled = false, timer;

/**
 * @Author: byh
 * @Date: 2024/5/15
 * @explanation:
 * @param functionTobeCalled method 对调函数体
 * @param interval  定时器
 * 防止快速点击多次重复进入同一页面
 *********************************************************/
export function handlerOnceTap(functionTobeCalled, interval = 600) {
  console.log("======+===+=+=+=========+===+=+=++===")
  if (!isCalled) {
    isCalled = true;
    clearTimeout(timer);
    timer = setTimeout(() => {
      isCalled = false;
    }, interval);
    return functionTobeCalled();
  }
}
