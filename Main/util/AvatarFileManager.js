import { Service, Device, Host } from 'miot';
import base64js from 'base64-js';
import { DeviceEventEmitter, PermissionsAndroid, Platform } from 'react-native';

import CameraPlayer from './CameraPlayer';
import LogUtil from './LogUtil';


const kRDTDataReceiveCallBackName = 'rdtDataReceiveCallBack3';
const Tag = "AvatarFileManager";

export const DownloadVideoState = {
  DOWNLOADED: 1,
  FAILED: 2
};

Object.freeze(DownloadVideoState);

const TAG = "AvatarFileManager";

// 这种普通数据类 参考: IBluetoothLock
export default class AvatarFileManager {

  // 获取sd卡文件
  static getInstance() {
    if (!this.instance) {
      this.instance = new AvatarFileManager();
    }
    return this.instance;
  }

  constructor() {
    this.timeItems = [];// 放置timeItems的地方

    this._bindRdtFilesListener();

    this.rdtCommandList = [];// 存储下发指令的队列，依次从队列里取
    this.contactsData = [];// 存储所有家庭相册的网络url原始链接

    this.receiveFileListener = null;

    this.commandBuffer = null;
    this.commandSize = 0;
    this.currentDataLength = 0;
    this.commandOffset = 0;
    this.commandStatus = 0;
    this.rdtCommand = 0;
    this.cmdCode = 0;
    this.isAllRdtDataReceived = false;
    this.downloadingImgTimestamp = 0;

    this.isSendingCmd = false;

    this.isOnCmdError = false;
    this.onErrorRetryTimeout = null;
    this.isHandleFileList = false;

    this.sdcardStatus = null;


    this.videoFileDownloadListener = null;

    this.progress = 0;

    this.rdtErrorTime = 0;
    this.channel = 0;
    this.connectionListener = CameraPlayer.getInstance()
      .addConnectionListener((connectionState) => {
        if (connectionState.state < 1) {
          LogUtil.logOnAll(TAG, "p2p连接断开，重置rdt相关状态");
          this.commandBuffer = null;
          this.commandSize = 0;
          this.commandOffset = 0;
          this.commandStatus = 0;
          this.rdtCommand = 0;
          this.isAllRdtDataReceived = false;
          this.isOnCmdError = true;
          this.progress = 0;
          CameraPlayer.getInstance().startConnect();
          // 没有主动重置状态呀;
          this.handleErrorRetry();// 网络连接断开了。通知一下外面的人。暂时不做其他的处理了。
        }
      });

    this.longNoDataTimeout = null;

  }


  _bindRdtFilesListener() {
    this.rdtListener = DeviceEventEmitter.addListener(kRDTDataReceiveCallBackName, ({ data }) => {
      this.handleRdtData(data);
    });
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
  }

  handleRdtData(data) {
    // LogUtil.logDebug("   ...");
    // here handle data
    if (data == null || data.length <= 0) {
      LogUtil.logOnAll(`收到数据为空: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp }`);
      return;
    }
    // here发一个消息吧，避免中间出问题了，例如p2p连接断开了，等等导致所有的消息都卡住不播放的问题。
    if (this.longNoDataTimeout != null) {
      clearTimeout(this.longNoDataTimeout);
      this.longNoDataTimeout = null;
    }
    this.longNoDataTimeout = setTimeout(() => {
      // LogUtil.logOnAll(TAG, "比较长的时间内一直没有收到数据");// 因为转存视频 需要比较多的时间  这里做检测也不合适。
    }, 1000);

    if (this.isOnCmdError) {
      let time = new Date().getTime();
      if (time - this.rdtErrorTime < 1000) {
        clearTimeout(this.onErrorRetryTimeout);
        this.onErrorRetryTimeout = setTimeout(() => {
          // LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，还在接收数据，在此期间收到的数据都不处理， 直到1000ms内都不收任何数据  再走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
      }
      this.rdtErrorTime = time;
      return;
    }

    this.currentDataLength = 0;
    try {
      let result = null;
      try {
        result = base64js.toByteArray(data);// uint8array
        // console.log("yyh 收到固件头像数据：" + result);
      } catch (exception) {
        this.isOnCmdError = true;
        LogUtil.logOnAll(TAG, `接收数据出错：${ JSON.stringify(exception) }${ exception.stack } data:${ data }`);
        clearTimeout(this.onErrorRetryTimeout);
        this.onErrorRetryTimeout = setTimeout(() => {
          LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，1000ms后都没有收到其他数据了  走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
        return;
      }

      if (result == null || result.length == 0) { // base64转换的时候有可能返回空
        this.isOnCmdError = true;
        clearTimeout(this.onErrorRetryTimeout);
        LogUtil.logOnAll(TAG, "接收数据为空");
        this.onErrorRetryTimeout = setTimeout(() => {
          LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，1000ms后都没有收到其他数据了  走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
        return;
      }
      this.currentDataLength = result.length;
      console.log(`yyh commandSize: ${ this.commandSize }  rdtCommand:  ${ this.rdtCommand }`);
      if (this.commandSize == 0 && this.rdtCommand == 0) {
        this.commandBuffer = null;
        this.commandSize = 0;
        this.commandOffset = 0;
        this.commandStatus = 0;
        this.rdtCommand = 0;
        this.isAllRdtDataReceived = false;

        let command = this.byteArrayToInt(result, 0);
        if (command <= 0 || command > 65535) {
          LogUtil.logOnAll(TAG, `不合理的命令:${ command }`);
          throw ("invalid command");

        }
        this.rdtCommand = command;

        let size = this.byteArrayToInt(result, 4);
        if (size <= 0 || size >= 20971520) {
          LogUtil.logOnAll(TAG, `不合理的size:${ size }`);
          throw (`invalid size:${ size }`);
        }
        this.commandSize = size;

        let status = this.byteArrayToInt(result, 8);
        this.commandStatus = status;

        let rawSize = size;
        // let currentPacketDataSize = result.length - 12;

        this.commandOffset = 0;
        this.commandBuffer = new Uint8Array(rawSize);
        LogUtil.logOnAll(TAG, `数据: offset:${ this.commandOffset } resultLength:${ result.length } bufferSize:${ this.commandBuffer.length }  rdtCommand:${ this.rdtCommand }  commandStatus:${ this.commandStatus }`);
        // this.commandBuffer.set(result.slice(12));
        // if (currentPacketDataSize >= this.commandSize) {
        //   this.isAllRdtDataReceived = true;
        // }
        // LogUtil.logOnAll(TAG, `数据: offset:${this.commandOffset} resultLength:${result.length} bufferSize:${this.commandBuffer.length}`);

      } else {
        if (this.commandBuffer.length === 8) { // 此联系人无头像数据
          LogUtil.logOnAll(`此联系人无头像数据  ${ result }`);
          this.isAllRdtDataReceived = true;
          this.isSendingCmd = false;
          
        } else if (result.length > 0) { // 固件相册数据
          // console.log(`收到固件相册数据  ${  result.length}  commandOffset: ${  this.commandOffset}  `);
          this.commandBuffer.set(result, this.commandOffset);
          this.commandOffset = this.commandOffset + result.length;
          if (this.commandOffset >= this.commandSize) {
            this.isAllRdtDataReceived = true;
          }
        }
      }

      if (this.rdtCommand > 0 && this.commandSize > 0 && this.isAllRdtDataReceived) {
        this.isSendingCmd = false;
        if (this.commandBuffer.length > 8) {
          console.log(`yyh 头像数据已获取完成 ${ this.cmdCode }`);
          if (this.cmdCode > 16) {
            let tmpBuffer = new Uint8Array(this.commandBuffer);
            this.parsePicFile(tmpBuffer);
          }
        } else {
          this.pollAndSendCommand();
          console.log(`yyh 头像数据下载错误 ${ this.cmdCode }`);
        }
        this.commandBuffer = null;
        this.commandSize = 0;
        this.commandOffset = 0;
        this.commandStatus = 0;
        this.rdtCommand = 0;
        this.isAllRdtDataReceived = false;
        console.log("yyh 赋值已重置");

      }

    } catch (err) {
      LogUtil.logOnAll(TAG, `处理rdt数据的时候出错了 error:${ err } message stack:${ err.stack }`, "过300ms再选择跑路");
      this.rdtErrorTime = new Date().getTime();
      this.handleRdtErr(err);
    }
  }

  handleRdtErr(err) { // 出错了就重置状态吧  不再重试和请求下一步了
    LogUtil.logOnAll(TAG, "rdt数据当前状态: command:", this.rdtCommand, "offset", this.commandOffset, ` currentDatalength:${ this.currentDataLength }`, ` cmdBuffer length:${ this.commandBuffer != null ? this.commandBuffer.length : 0 }`, " totalSize: ", this.commandSize, `${ ` downloadingVideoTimestamp:${ this.downloadingVideoTimestamp } downloadingImgTimestamp:${ this.downloadingImgTimestamp }` + " ishandleFileList:" }${ this.isHandleFileList }`);
    // if (this.command <= 0) {
    this.commandBuffer = null;
    this.commandSize = 0;
    this.commandOffset = 0;
    this.commandStatus = 0;
    this.rdtCommand = 0;
    this.isAllRdtDataReceived = false;
    this.isOnCmdError = true;
    this.progress = 0;
    clearTimeout(this.onErrorRetryTimeout);
    this.onErrorRetryTimeout = setTimeout(() => { // 错误以后隔3s再去从任务队列拉数据 并屏蔽此时的错误信息接收
      this.handleErrorRetry();
    }, 3000);

  }

  handleErrorRetry() {
    LogUtil.logOnAll(TAG, "执行rdt命令出错了，置空状态");
    if (this.isHandleFileList) { // 文件列表
      clearTimeout(this.requestSdfilesTimeout);
      this.receiveFileListener && this.receiveFileListener(false);// 接收数据出错了。
      this.isHandleFileList = false;

    }
    this.isOnCmdError = false;

    this.isSendingCmd = false;

    this.pollAndSendCommand();
  }






  _notifySdcardVideoDownloadStatus(timestamp, isDownloadSuccess) {
    if (this.videoFileDownloadListener) {
      this.videoFileDownloadListener(timestamp, isDownloadSuccess);
    }
  }

  _notifySdcardThumbDownloadStatus(isDownloadSuccess) {
    if (this.onFileDownloadedListener) {
      this.onFileDownloadedListener(isDownloadSuccess);
    }
  }




  getTimeItems() {
    return this.timeItems;
  }
  byteArrayToInt(data, position) {
    return (0xff & data[position]) | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2]) << 16 | (0xff & data[position + 3]) << 24;
  }

  byteArrayToLong(data, position) {
    return (0xff & data[position] | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2]) << 16 | (0xff & data[position + 3]) << 24 | (0xff & data[position + 4]) << 32 | (0xff & data[position + 5]) << 40 | (0xff & data[position + 6]) << 48 | (0xff & data[position + 7]) << 56);
  }

  intToByteArray(data) {
    return new Uint8Array([data & 0xff, (data >> 8) & 0xff, (data >> 16) & 0xff, (data >> 24) & 0xff]);
  }

  bindReceiveFilesListener(receiveFileListener) {
    this.receiveFileListener = receiveFileListener;
  }



  /**
   * 请求下载对应索引的头像
   * @param index
   */
  startRequestContactsImgByIndex(index) {
    
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
    let data = [];
    data[0] = 17;
    data[1] = index; // 命令码 17-26 表示请求的对应照片
    this.pushCommand(data);// 往里面丢
    LogUtil.logOnAll(TAG, `发送下载第${ index }张头像的请求`);

    this.pollAndSendCommand();// 尝试消费
  }

  startRequestContactsImgData(contactsData) {
    if (this.isSendingCmd) {
      LogUtil.logOnAll(TAG, `还在执行其他任务 : contactsData:${ this.contactsData }  isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
      return;// 正在下发指令  滚蛋
    }
    this.contactsData = contactsData;
    // console.log("yyh 需要拉取头像的数据是：" + JSON.stringify(contactsData));
    this.contactsData.forEach(item => {
      // 检查key是否符合"key数字"的格式
      const keyMatch = item.key.match(/^key(\d+)$/);
      if (!keyMatch) {
        LogUtil.logOnAll(TAG, `无效的key格式: ${item.key}，应为key1、key2等格式`);
        return;
      }

      // 提取key中的数字作为index
      const index = parseInt(keyMatch[1]) + 16;
      item.currentIndex = index;
      this.startRequestContactsImgByIndex(index);
    });
  }




  bindOnFileDownloadedListener(onFileDownloadedListener) { // 每次有文件下载完成  就通知一下外面刷新一次
    this.onFileDownloadedListener = onFileDownloadedListener;
  }


  // sdcard thumbnail files end

  pushCommand(dataArray) {
    if (dataArray == null || dataArray.length <= 1 || this.rdtCommandList == null) {
      return;// 数据不合理
    }
    this.rdtCommandList.push(dataArray);// 放进去的是array 放在队尾 后面取用shift 从队列头部取
  }

  // 只有sdcardEventType才有这个需求，其他的都不行。
  insertCommandAtFront(dataArray) {
    if (dataArray == null || dataArray.length <= 1 || this.rdtCommandList == null) {
      return;// 入队失败。
    }
    this.rdtCommandList.unshift(dataArray);// 数据插入到队列头部。优先执行
  }

  shiftCommand() {
    if (this.rdtCommandList == null || this.rdtCommandList.length == 0) { // 发完了或者destroy了
      return null;
    }
    return this.rdtCommandList.shift();
  }

  pollAndSendCommand() {
    if (this.isSendingCmd) {
      LogUtil.logOnAll(TAG, `还在执行其他任务 : isHandleFileList:${ this.isHandleFileList }  downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
      return;// 正在下发指令  滚蛋
    }

    let dataArray = this.shiftCommand();// 数据体
    console.log("pollAndSendCommand", dataArray);

    if (dataArray == null) {
      return;
    }

    this.isSendingCmd = true;
    this.progress = 0;
    if (dataArray[0] == 17) {
      let buf = new ArrayBuffer(8);
      let data = new Uint32Array(buf);
      data[0] = dataArray[0];
      // data[1] = 0;
      let byteArray = new Uint8Array(buf);
      byteArray[4] = dataArray[1]; // 表示对应命令码
      this.cmdCode = dataArray[1];
      let base64Data = base64js.fromByteArray(new Uint8Array(buf));
      Service.miotcamera.sendRDTCommandToDevice(base64Data)
        .then(() => {
          LogUtil.logOnAll(TAG, 'send file list rdt cmd ok');
          LogUtil.logOnAll(TAG, `开始同步第 ${ dataArray[1] }张头像。`);
          this.downloadingImgIndex = dataArray[1];
        })
        .catch(() => {
          LogUtil.logOnAll(TAG, "开始同步头像。。发送命令错误");
          // clearTimeout(this.requestSdfilesTimeout);
          // this.receiveFileListener && this.receiveFileListener(false);
          // if (this.requestSdcardFilesRegularly) {
          //   this.requestSdfilesTimeout = setTimeout(() => this.startRequestSdcardFiles(), 40000);// 40s后往任务队列加一个
          // }
          // this.pollAndSendCommand();
        });
    }

  }



  bindDownloadVideoFileListener(videoFileListener) {
    this.videoFileDownloadListener = videoFileListener;
  }

  destroyInstance() { // 避免内存泄漏
    // clearInterval(this.requestSdfilesInterval);
    clearTimeout(this.onErrorRetryTimeout);

    this.rdtCommandList = null;
    this.contactsData = [];
    this.isSendingCmd = false;

    this.rdtListener && this.rdtListener.remove();
    this.connectionListener && this.connectionListener.remove();


    AvatarFileManager.instance = null;
  }

  parsePicFile(data) {
    LogUtil.logOnAll(TAG, `下载视频缩略图成功，待转存到sdcard`);
    if (data == null) {
      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      LogUtil.logOnAll(TAG, `invalid final data: is date null?${ data == null } `);
      return;
    }
    // 从this.contactsData 找出 this.cmdCode 对应的index
    const targetIndex = this.contactsData.findIndex((item) => item.currentIndex === this.cmdCode);
    if (targetIndex < 0) {
      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      LogUtil.logOnAll(TAG, `非法下载 未找到对应联系人数据`);
      return;
    }
    const imageName = this.contactsData[targetIndex].localFileName;
    LogUtil.logOnAll(TAG, `找到对应联系人数据 ： targetIndex = ${ targetIndex }   imageName = ${ imageName }`);
    Host.file.writeFileThroughBase64(imageName, base64js.fromByteArray(data)).then((isSuccess) => {
      if (isSuccess) {
        LogUtil.logOnAll(TAG, `下载缩略图:${ imageName } 成功并写入成功`);
        this.isSendingCmd = false;
        if (targetIndex === this.contactsData.length - 1) {
          this.contactsData = [];
          console.log("全部下完了 哈哈");
          // 发送下载完成事件
          DeviceEventEmitter.emit('contactAvatarDownloadComplete', {
            success: true
          });
        } else {
          LogUtil.logOnAll(TAG, `下载完毕，继续执行下一个任务。`);
          this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
        }
      } else {
        LogUtil.logOnAll(TAG, `下载缩略图:${ imageName } 成功但是写入失败`);
        this.isSendingCmd = false;
        this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      }

    }).catch(() => {
      // 写入失败了
      LogUtil.logOnAll(TAG, `下载缩略图:${ imageName } 成功但是转存失败了。`);

      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
    });
  }
}