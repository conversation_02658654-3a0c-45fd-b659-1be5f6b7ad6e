import { API_LEVEL, Device, Service } from "miot";
import TrackPoints from "./TrackPoints";
import LogUtil from "./LogUtil";
import VipUtil from "./VipUtil";
import CameraConfig from "./CameraConfig";
import CameraPlayer from "./CameraPlayer";
import { Platform } from "react-native";

const ENABLE_OLD_TRACK = true;
export const AlarmPlayerCtl = { // 看家播放器页面的按钮控件的打点信息
  bPlay: { item_type: 'button', item_name: 'play_normal_button' },
  bShare: { item_type: 'button', item_name: 'share_link_button' },
  bDownload: { item_type: 'button', item_name: 'download_normal_button' },
  bDelete: { item_type: 'button', item_name: 'delete_normal_button' },
  bAudio: { item_type: 'button', item_name: 'play_sound_normal_button' },
  bFullScr: { item_type: 'button', item_name: 'fullscreen_normal_button' },
  bFilter: { item_type: 'button', item_name: 'eventselect_normal_button' },
  bBanner: { item_type: 'banner', item_name: 'cloud_banner' },
  dCalendar: { item_type: 'dialog', item_name: 'dateselect_dialog' },
  dEvFilterDlg: { item_type: 'dialog', item_name: 'eventselect_dialog' }
};
export default class TrackUtil {
  static Local_Expid = [];
  // 上传点击信息  reportClickEvent("xxxx");
  static reportClickEvent(eventKeyName) {
    let map = {};
    map.enableOldTrack = ENABLE_OLD_TRACK;
    let key = TrackPoints.getKey(eventKeyName, ENABLE_OLD_TRACK);
    map.keyName = `${ key }`; // 强转为str  防止传参错误
    Service.smarthome.reportEvent("click", map);
  }

  static reportResultMapEvent(eventKeyName, resultParam) {
    let keys = Object.keys(resultParam);

    for (let i = 0; i < keys.length; i++) {
      // array.push({startTime: keys[i], eventType: eventTypes[keys[i]]});
      let key = keys[i];
      let value = resultParam[key];
      resultParam[key] = `${ value }`;
    }
    resultParam.enableOldTrack = ENABLE_OLD_TRACK;
    let trackKey = TrackPoints.getKey(eventKeyName, ENABLE_OLD_TRACK);
    resultParam.keyName = trackKey;
    Service.smarthome.reportEvent("result", resultParam);
  }

  // 上传 xx关键点  xx开关的值  reportResultEvent("cloud_storage", "value", 1);
  static reportResultEvent(eventKeyName, valueType, value) {
    this.reportResultMapEvent(eventKeyName, { valueType, value });
  }

  /**
   *  oneTrack report event
   *  如果params为空，表示直接使用配置信息上报
   *  如果params为数组，表示从打点位置直接调用，需要匹配到配置信息中上报
   *  如果params为map，表示从传统打点中调用过来，需要把信息加到配置信息中上报
   *  */
  static async oneTrackReport(eventKeyName, params) {
    if (CameraConfig.isInternationalServer) {
      return; // 海外不支持onetrack打点
    }
    let trackMap = TrackPoints.getKey(eventKeyName, false);
    if (trackMap && trackMap.name && trackMap.args) {
      let eventType = trackMap.name;
      let resParms = {};
      if (params instanceof Array) {
        let jStr = trackMap.args.replace(/{([0-9]+)}/g,
          function(sub) {
            return params[parseInt(sub[1])];
          });
        resParms = JSON.parse(jStr);
      } else {  // at least tip & type
        resParms = JSON.parse(trackMap.args);
        if (params instanceof Map) {
          resParms = { ...resParms, ...params };
        }
      }
      // 添加通用信息
      this.addOneTrackMoreInfo(resParms);
      Service.smarthome.reportEvent(eventType, resParms);
      console.log("onetrack done: ", eventType, resParms);
    } else {
      console.error("onetrack failed: ", eventKeyName);
    }
  }

  static newOneTrack(events, args = {}) {
    if ((!CameraConfig.isInternationalServer && API_LEVEL < 10085) || (CameraConfig.isInternationalServer && API_LEVEL < 10088)) return;

    let params = { ...args };
    this.addOneTrackMoreInfo(params, true);
    let refPrams = this.updateRefInfo();
    LogUtil.logOnAll("TrackUtil", "newOneTrack", events[0], JSON.stringify(params), JSON.stringify(refPrams));
    for (let event of events) {
      console.log("newOnetrack done: ", event, refPrams);
      Service.smarthome.reportEventRefChannel(event, params);
    }
  }

  static reportExposeMapEvent(eventKeyName, inParam) {
    let resultParam = { ...inParam };
    this.addOneTrackMoreInfo(resultParam, true);
    this.forceToStringValue(resultParam);
    resultParam.enableOldTrack = ENABLE_OLD_TRACK;
    let trackKey = TrackPoints.getKey(eventKeyName, ENABLE_OLD_TRACK);
    resultParam.keyName = trackKey;
    Service.smarthome.reportEvent("expose", resultParam);
    LogUtil.logOnAll('TrackUtil', 'expose stat', `eventKey: ${ eventKeyName }`);
  }

  static forceToStringValue(resultParam) {
    let keys = Object.keys(resultParam);

    for (let i = 0; i < keys.length; i++) {
      // array.push({startTime: keys[i], eventType: eventTypes[keys[i]]});
      let key = keys[i];
      let value = resultParam[key];
      resultParam[key] = `${ value }`;
    }
  }

  static updateRefInfo() {
    let refPrams = { ref: this.ref, subRef: this.subRef };
    if (this.ref !== this?.setedRef?.ref || this.subRef !== this?.setedRef?.subRef || API_LEVEL > 10085) {
      this.setedRef = { ...refPrams };
      if (Platform.OS == 'android') {
        this.addOneTrackMoreInfo(refPrams, true);
      }
      Service.smarthome.updatePluginPageRef(refPrams); // 重复设置相同的参数，米家会替换from参数，导致from ref 和ref相同，米家bug
      // LogUtil.logOnAll("TrackUtil", "setRefInfo2", JSON.stringify(refPrams));
    }
    return refPrams;
  }
  // One Track需要的通用信息
  static addOneTrackMoreInfo(params, newOneTrack = false) {
    // 绑定类型：主账号、共享用户（设备共享、家庭共享）
    params.bind_relation = Device.isOwner ? 0 : 1;
    // 用户类型：未购买云存储、云存储生效中、临期用户、已过期用户
    params.user_type = VipUtil.getUserType(newOneTrack);
    params.exp_id = this.Local_Expid.join(', ');
    let sdcardStatus = CameraPlayer.getInstance().sdcardCode; // 返回null，表示sdcardstatus还没有取回来呢，或者出错了
    if (sdcardStatus !== null) {
      params.is_SDcard = sdcardStatus ? 1 : 0;
    }
    params.did = Device.deviceID;
    params.device_model = Device.model;
    params.plugin_form = 0; // 是否是标准插件
  }
}