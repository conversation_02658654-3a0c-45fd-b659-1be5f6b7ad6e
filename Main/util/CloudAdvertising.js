import { Service, Device, Host, API_LEVEL } from "miot";
import CameraConfig from "./CameraConfig";
import { Platform } from "react-native";
import LogUtil from "./LogUtil";
import VipUtil from "./VipUtil";

export function PlatoTipsOn() {
  // 本来计划10085，米家8.8上这个功能，但是服务的来不及部署，插件端关闭改功能。
  // 2023.0906版本重新打开
  return API_LEVEL > 10084;
}

export default class CloudAdvertising {
  /**
   * 参考接口文档：https://xiaomi.f.mioffice.cn/docx/doxk4DWe2i3Q3mPprB53doo6QYd
   * 返回结果：
   * {
    "code": 0, //0: 成功  -1：功能不支持(如：未灰度）
    "result": {
        "1111": {
            "shortKey": "504822945000000001",
            "id": 38416,
            "jump": 2, //跳转类型 2：应用内WebView，
            "imgUrl": "https://xxxxx.icon",
            "targetUrl": "https://m.xiaomiyoupin.com/w/universal?_rt=weex&pageid=10292&sign=025c55f975df2faf76393b0d967bb65f&pdl=jianyu&source=mijia_0jiankang",
            "title": "云存服务7天后到期",
            "beginTime": "1657727999000",
            "endTime": "1657727999000",
            "style": "A", // 客户端显示样式 A,B
            "vipStatus": 0, //云存会员状态  1: 云存未开通 2: 云存生效中 3: 云存临期  4: 云存过期
            "expId": "xxxxxxx" // AB实验ID
         },
        "2222": {
            //....
        }
    }
}

zh_cn("zh_cn", "简体中文"),
* zh_tw("zh_tw", "台湾正体"),
* zh_hk("zh_hk", "港澳繁体"),
* en("en", "英语"),
* ko("ko", "韩语"),
* ru("ru", "俄语"),
* es("es", "西班牙语"),
* it("it", "意大利语"),
* fr("fr", "法语"),
* id("id", "印度尼西亚语"),
* de("de", "德语"),
* pl("pl", "波兰语"),
* vi("vi", "越南语"),
* ja("ja", "日语"),
* th("th", "泰语"),
* ar("ar", "阿拉伯语"),
* tr_tr("tr_TR", "土耳其语"),
* pt_br("pt_BR", "葡萄牙语(巴西)"),
* nl_nl("nl_NL", "荷兰语"),
* uk_ua("uk_ua", "乌克兰语"),
* cs_cz("cs_cz", "捷克语"),
* ro_ro("ro_ro", "罗马尼亚语"),
* el_gr("el_gr", "希腊语"),
* pt_pt("pt_pt", "葡萄牙语(葡萄牙)"),
* he("he", "希伯来语")
   */

  static LanguagMap = { // SDK language name : Server request
    zh: 'zh_cn',
    tr: 'tr_tr',
    pt: 'pt_br',
    pt_pt: 'pt_pt',
    nl: 'nl_nl',
    el: 'el_gr',
    cs: 'cs_cz',
    uk: 'uk_ua',
    ro: 'ro_ro',
    sv: 'sv_se',
    nb: 'nb_no',
    fi: 'fi_fi'
  }

  static Tips_Params = {
    dids: [Device.deviceID],
    appVersion: Host.version,
    platform: Platform.OS == 'ios' ? 1 : 2,
    lang: this.LanguagMap[Host.locale.language] || Host.locale.language,
    model: Device.model
  };

  static getLivePageTips() { // 首页 tips
    let from = 'getLivePageTips';
    let url = "/business/camera/video_vip_tips";
    return this.callNative(from, url);
  }

  static getLivePageBanner() { // 首页banner
    let from = 'getLivePageBanner';
    let url = "/business/camera/video_banner";// 测试用接口
    return this.callNative(from, url);
  }

  static getSvlPageTips() { // 看家播放也 tips
    let from = 'getSvlPageTips';
    let url = "/business/camera/video_home_vip_tips";
    return this.callNative(from, url);
  }

  static callNative(from, url) {
    return new Promise((resolve, reject) => {
      if (!Device.isOwner) { // 只有owner才有配置的tips和banner
        return resolve(null);
      }
      Service.callSmartHomeAPI(url, this.Tips_Params).then((res) => {
        this.parseRes(res, from, resolve, reject);
      }).catch((err) => {
        this.logErr(from, err);
        resolve(null);
      });
    });
  }

  static logErr(from, err) {
    LogUtil.logOnAll(`CloudAdvertising ${ from }, err: ${ JSON.stringify(err) }`);
  }

  static parseRes(res, from, resolve, reject) {
    let did = Device.deviceID;
    let item = res[did];
    LogUtil.logOnAll("cloudAdvertising", `from: ${ from }, item: ${ JSON.stringify(item)}`);
    if (!item) {
      return resolve(null);
    }
    resolve(item);
  }
  static validateAd(isVip, item) { // 实时验证该item是否还适用, false: 无效不展示 true：有效展示
    let mNow = new Date().getTime();
    if (mNow < item.beginTime || mNow > item.endTime) return false;

    if ((isVip && (item.vipStatus == 1 || item.vipStatus == 4)) || (!isVip && (item.vipStatus == 2 || item.vipStatus == 3))) {
      return false;
    }
    return true;
  }
}