import { Service } from "miot";

const TAG = "Stat";
export const StatEV = {
  // 设置页面/门铃设置模块
  // 固件自动升级开关状态
  FW_AUTO_UPDATE: "4111",

  // 设置页面/移动侦测设置模块
  // 移动侦测开关状态
  MOTION_DETECT: "4117",
  // 人脸识别开关状态
  MOTION_DETECT_FACE: "4118",
  // 全天侦测点击
  MOTION_DETECT_ALLDAY: "4119",
  // 定时侦测点击
  MOTION_DETECT_SETTIME: "4120",
  // 设置页面/省电设置模块
  // 延迟录像点击
  RECORD_DELAY: "4121",
  // 拍摄时间点击
  SHOOT_TIME: "4122",
  // 最大拍摄时间
  MAX_SHOOT_TIME: "4123",
  // 设置页面/设备联动模块
  // 来访画面推送点击
  PUSH_CALL_SWITCH: "4124",
  // 来访响铃通知点击
  PUSH_MOTION_SWITCH: "4125",
  // 插件主页
  // 直播监控点击（底部button）
  LIVE_VIDEO: "4126",
  // 事件筛选入口点击
  EVENT_FILTER_ENTRY: "4127",
  // 事件筛选-事件类型选择
  EVENT_FILTER_EVENT_TYPE: "4128",
  // 插件主页/全部视频
  // 日期点击
  DATE_CLICK: "4129",
  // 全部视频入口点击
  ALL_VIDEO_ENTRY: "4130",
  // 全部视频的播放点击次数
  ALL_VIDEO_PLAY_COUNT: "4131",
  // 插件主页/报警视频
  // 报警视频列表的点击
  ALARM: "4132",
  // 分享点击
  SHARE: "4133",
  // 下载点击
  DOWNLOAD: "4134",
  // 删除点击
  DELETE: "4135",
  // 人脸分组点击
  FACE_GROUP: "4136",
  // 添加备注
  ADD_COMMENT: "4137",
  // 直播监控页面/直播监控相关功能点击
  // 声音点击
  VOICE: "4138",
  // 截屏点击
  SNAPSHOT: "4139",
  // 录像点击
  RECORD: "4140",
  // 切换清晰度
  RESOLUTION_CHANGE: "4141",
  // 全屏点击
  FULLSCREEN: "4142",
  // 对讲点击
  CALL: "4143",
  // 变声点击
  CHANGE_VOICE: "4144",
  // 变声的选择状态
  CHANGE_VOICE_SELECTION: "4145",
  // 直播监控页面/快捷回复发送次数
  // 点击发送次数
  CLICK_SEND_TIMES: "4146",
  // 直播监控页面/直播监控页面停留时间
  // 进入观看视频洁面开始播放到退出的时间 （ms）
  VIDEO_PLAY_START_TO_END: "4147"
};
Object.freeze(StatEV);


export class Stat {

  static buildEvMap() {
    let evMap = {};
    evMap[StatEV.FW_AUTO_UPDATE] = { "name": "doorbell_fwup_state", args: "{\"owl.p06.but.a08\": {0}}" };
    evMap[StatEV.MOTION_DETECT] = { "name": "Motion_switch_state", args: "{\"owl.sta.mot.a01\": {0}}" };
    evMap[StatEV.MOTION_DETECT_FACE] = {};
    evMap[StatEV.MOTION_DETECT_ALLDAY] = {};
    evMap[StatEV.MOTION_DETECT_SETTIME] = { "name": "motionset_staytime", args: "{\"owl.p07.st1.c01\": \"{0}\"}" };
    evMap[StatEV.RECORD_DELAY] = { };
    evMap[StatEV.SHOOT_TIME] = {};
    evMap[StatEV.MAX_SHOOT_TIME] = {};
    evMap[StatEV.PUSH_CALL_SWITCH] = { "name": "push_callswitch_state", args: "{\"owl.sta.cal.a01\": {0}}" };
    evMap[StatEV.PUSH_MOTION_SWITCH] = { "name": "push_motionswitch_state", args: "{\"owl.sta.cal.a03\": {0}}" };
    evMap[StatEV.LIVE_VIDEO] = {};
    evMap[StatEV.EVENT_FILTER_ENTRY] = {};
    evMap[StatEV.EVENT_FILTER_EVENT_TYPE] = {};
    evMap[StatEV.DATE_CLICK] = {};
    evMap[StatEV.ALL_VIDEO_ENTRY] = {};
    evMap[StatEV.ALL_VIDEO_PLAY_COUNT] = {};
    evMap[StatEV.ALARM] = {};
    evMap[StatEV.SHARE] = {};
    evMap[StatEV.DOWNLOAD] = {};
    evMap[StatEV.DELETE] = {};
    evMap[StatEV.FACE_GROUP] = {};
    evMap[StatEV.ADD_COMMENT] = {};
    evMap[StatEV.VOICE] = {};
    evMap[StatEV.SNAPSHOT] = {};
    evMap[StatEV.RECORD] = {};
    evMap[StatEV.RESOLUTION_CHANGE] = {};
    evMap[StatEV.FULLSCREEN] = {};
    evMap[StatEV.CALL] = {};
    evMap[StatEV.CHANGE_VOICE] = {};
    evMap[StatEV.CHANGE_VOICE_SELECTION] = {};
    evMap[StatEV.CLICK_SEND_TIMES] = {};
    evMap[StatEV.VIDEO_PLAY_START_TO_END] = {};
    return evMap;
  }

  static buildOneTrackEvMap() {
    let evMap = {};
    evMap[StatEV.FW_AUTO_UPDATE] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4111\"}" };
    evMap[StatEV.MOTION_DETECT] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4117\"}" };
    evMap[StatEV.MOTION_DETECT_FACE] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4118\"}" };
    evMap[StatEV.MOTION_DETECT_ALLDAY] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4119\"}" };
    evMap[StatEV.MOTION_DETECT_SETTIME] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4120\"}" };
    evMap[StatEV.RECORD_DELAY] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4121\"}" };
    evMap[StatEV.SHOOT_TIME] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4122\"}" };
    evMap[StatEV.MAX_SHOOT_TIME] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4123\"}" };
    evMap[StatEV.PUSH_CALL_SWITCH] = { name: "click", args: "{\"tip\":\"*********.4124\"}" };
    evMap[StatEV.PUSH_MOTION_SWITCH] = { name: "click", args: "{\"tip\":\"*********.4125\"}" };
    evMap[StatEV.LIVE_VIDEO] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4126\"}" };
    evMap[StatEV.EVENT_FILTER_ENTRY] = {};
    evMap[StatEV.EVENT_FILTER_EVENT_TYPE] = {};
    evMap[StatEV.DATE_CLICK] = {};
    evMap[StatEV.ALL_VIDEO_ENTRY] = {};
    evMap[StatEV.ALL_VIDEO_PLAY_COUNT] = { name: "click", args: "{\"tip\":\"*********.4131\"}" };
    evMap[StatEV.ALARM] = { name: "click", args: "{\"tip\":\"*********.4132\"}" };
    evMap[StatEV.SHARE] = { name: "click", args: "{\"tip\":\"*********.4133\"}" };
    evMap[StatEV.DOWNLOAD] = { name: "click", args: "{\"tip\":\"*********.4134\"}" };
    evMap[StatEV.DELETE] = { name: "click", args: "{\"tip\":\"*********.4135\"}" };
    evMap[StatEV.FACE_GROUP] = {};
    evMap[StatEV.ADD_COMMENT] = {};
    evMap[StatEV.VOICE] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4138\"}" };
    evMap[StatEV.SNAPSHOT] = { name: "click", args: "{\"tip\":\"*********.4139\"}" };
    evMap[StatEV.RECORD] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4140\"}" };
    evMap[StatEV.RESOLUTION_CHANGE] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4141\"}" };
    evMap[StatEV.FULLSCREEN] = { name: "click", args: "{\"tip\":\"*********.4142\"}" };
    evMap[StatEV.CALL] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4143\"}" };
    evMap[StatEV.CHANGE_VOICE] = { name: "click", args: "{\"tip\":\"*********.4144\"}" };
    evMap[StatEV.CHANGE_VOICE_SELECTION] = { name: "click", args: "{\"value\":\"{0}\", \"tip\":\"*********.4145\"}" };
    evMap[StatEV.CLICK_SEND_TIMES] = { name: "click", args: "{\"tip\":\"*********.4146\"}" };
    evMap[StatEV.VIDEO_PLAY_START_TO_END] = { name: "expose", args: "{\"value\":\"{0}\", \"tip\":\"*********.4147\"}" };
    return evMap;
  }

  static convertEv(aName, args) {
    if (this.mEvMap == null) {
      this.mEvMap = this.buildOneTrackEvMap();
    }
    // for MISTAT
    let ev = this.mEvMap[aName];

    if (ev.name != null && ev.args != null) {
      let repArg = arguments;
      let jStr = ev.args.replace(/{([0-9]+)}/g,
        function(sub) {
          return repArg[parseInt(sub[1]) + 1];
        });
      return { "name": ev.name, "args": JSON.parse(jStr) };
    } else {
      return null;
    }

  }

  static reportEvent(aName, args) {
    console.log(TAG, "Report", aName, args);
    try {
      let ev = this.convertEv(aName, args);
      if (ev != null) {
        console.log(ev.name, ev.args);
        Service.smarthome.reportEvent(ev.name, ev.args);
      } else {
        console.log(TAG, "no match ev", aName);
      }
    } catch (e) {
      console.log(TAG, "reportEvent exp", e, "for", aName);
    }

  }
}
