import { Host, Service, Device } from 'miot';
import JSONbig from 'json-bigint';
const TAG = "API";
export const CameraDomain = "business.smartcamera";

// Madv_diff_多语言自动回复接口
export const PresetReplyOverseas = "/common/app/data/v2/preDataGetOverseas";
export const PresetReply = "/common/app/data/v2/preDataGet";
export const CustomReply = "/common/app/data/scan";
export const CustomReplyAdd = "/common/app/data/put";
export const CustomReplyRm = "/common/app/data/remove";
export const CustomReplyMod = "/common/app/data/modify";

export const CloudApi = {
  AddFigure: "/miot/camera/app/v1/add/figure", //添加人物
  QueryFigure: "/miot/camera/app/v1/get/figureByName", //根据名字获取人物的figureid
  QueryAllFigure: "/miot/camera/app/v1/get/figures", //获取所有人物
  AddFace: "/miot/camera/app/v1/add/face", //添加一个人脸到人物
  DelFace: "/miot/camera/app/v1/delete/face", //从人物中删除一个人脸
  ModifyFigure: "/miot/camera/app/v1/modify/figure", //修改人物的名称
  FaceImg: "/miot/camera/app/v1/get/face/img", //获取人脸的照片
  Metas: "/miot/camera/app/v1/get/fileIdMetas", // 获取FileId对应的人脸列表  这个不太知道是啥
  FaceRecStatus: "/miot/camera/app/v1/get/allDetectionSwitch", 
  FaceRecSwitch: "/miot/camera/app/v1/put/faceSwitch",
  QueryFigureFaces:"/miot/camera/app/v1/get/figureFaces", //获取所有的人脸
  DelFaces: "/miot/camera/app/v1/delete/faces", //从人物中删除一个人脸,
  QueryAllUnmarkFaces:"/miot/camera/app/v1/get/unmarkFaces", //获取所有未标注的人脸
  DelFigures:"/miot/camera/app/v1/delete/figures",
  GetFacesCluster:"/miot/camera/app/v1/get/facesCluster",//获取三天内的人脸信息
  GetFaceClusterEvent:"/common/app/get/faceCluster/eventlist", // 获取事件列表（返回3天内faceIds里面的人脸事件）
  FaceMistake: "/miot/camera/app/v1/mark/faceClustering/mistake"//报错
};
export const EnableApiCache = true;
export default class API {

  static instance() {
    if (this.myInstance == null) {
      this.myInstance = new API();
    }
    return this.myInstance;
  }

  constructor() {
    this.mCache = new Map();
    this.mCacheApi = new Set();
    if (EnableApiCache) {
      this.mCacheApi.add(PresetReply)
        .add(CustomReply);
    }
  }

  get _generalParams() {
    return {
      did: Device.deviceID,
      region: Host.locale.language.includes("en") ? "US" : "CN"
    };
  }

  _request(api, subDomain, post, params) {
    let combinesParams = Object.assign({}, params, this._generalParams);
    return new Promise((resolve, reject) => {
      Service.callSmartHomeCameraAPI(api, subDomain, post, combinesParams)
        .then((res) => {
          console.log("api success", api, params);
          resolve(typeof res === 'string' ? JSON.parse(res) : res);
        })
        .catch((e) => {
          console.log('❌request failed', post, api, subDomain, combinesParams, JSON.stringify(e));
          reject(e);
        });
    });
  }

  /**
     * @param {string} api 接口地址
     * @param {string} subDomain subDomain
     * @param {object} params 传入参数
     */
  get(api, subDomain, params = {}) {
    return this._request(api, subDomain, false, params);
  }

  /**
     * @param {string} api 接口地址
     * @param {string} subDomain subDomain
     * @param {object} params 传入参数
     */
  post(api, subDomain, params) {
    return this._request(api, subDomain, true, params);
  }

  // cache didn't cache with aParam
  requestWithStringParam(api, aSubDomain, aPost, aParams) {
    return new Promise((aResolve, aReject) => {
      let param = Object.assign({}, aParams, this._generalParams);
      let cache = this.mCache.get(api);
      if (cache != null) {
        console.log(TAG, "cache hit", api, "not include param");
        aResolve(cache);
      } else {
        Service.callSmartHomeCameraAPIWithStringParam(api, aSubDomain, aPost, JSONbig.stringify(param))
          .then((aRet) => {
            if (this.mCacheApi.has(api)) {
              this.mCache.set(api, aRet);
            }
            aResolve(aRet);
          })
          .catch((aErr) => {
            console.log(TAG, "requestWithStringParam", api, "post", aPost, "err", aErr);
            aReject(aErr);
          });
      }
    });
  }

  getWithStringParam(api, aSubDomain, aParams) {
    return this.requestWithStringParam(api, aSubDomain, false, aParams);
  }

  postWithStringParam(api, aSubDomain, aParams) {
    return this.requestWithStringParam(api, aSubDomain, true, aParams);
  }

  invalidCache(api) {
    this.mCache.delete(api);
  }

}
