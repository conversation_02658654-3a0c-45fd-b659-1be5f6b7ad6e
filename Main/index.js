import React from 'react';
import TitleBar from 'miot/ui/TitleBar';
import { createStackNavigator } from 'react-navigation';

import RouteProxy from "./RouteProxy";
import LiveVideoPageV2 from './live/LiveVideoPageV2';
import AllStorage from './allVideo/AllStorage';
import CameraConfig from './util/CameraConfig';
import TabBarDemo from "./testPage/TabBarDemo";

import { Package, Device, DeviceEvent, Host } from 'miot';
import Service from 'miot/Service';
import VersionUtil from './util/VersionUtil';
import { Animated, Easing, I18nManager, Platform } from 'react-native';
import StackNavigationInstance from './StackNavigationInstance';
import PushHandler from './util/PushHandler';
import TrackUtil from './util/TrackUtil';
import TrackConnectionHelper from './util/TrackConnectionHelper';
import Singletons from "./framework/Singletons";
import UriPlayer from "./framework/UriPlayer";
import StorageKeys from './StorageKeys';
import AlarmVideoUI from './alarmDetail/AlarmVideoUI';
import { NavigationBar } from 'mhui-rn';
import FaceManager2 from "./setting/FaceManager2"
import BabyCrying from "./aicamera/BabyCrying.js";
import PetIdentification from "./aicamera/PetIdentification.js";
import CameraPlayer from './util/CameraPlayer';
import Toast from './components/Toast';
import MotionDetectionPage from "./aicamera/MotionDetectionPage";
import OnTimeAlarmPage from "./aicamera/OnTimeAlarmPage";
import GestureCallPage from "./aicamera/GestureCallPage";
import AudioVideoCallPage from "./live/AudioVideoCallPage";
import WXCallSetting from "./setting/WXCallSetting";
import SurveillancePeriodSettingV2 from "./setting/SurveillancePeriodSettingV2";

let rootStack = null;
let mStartUpPush = false;
export function getStack() {
  return rootStack;
}

export function isStartUpPush() {
  return mStartUpPush;
}


function interpolator(props) {
  const { layout, position, scene } = props;

  if (!layout.isMeasured) {
    return (props) => {
      const { navigation, scene } = props;

      const focused = navigation.state.index === scene.index;
      const opacity = focused ? 1 : 0;
      // If not focused, move the scene far away.
      const translate = focused ? 0 : 1000000;
      return {
        opacity,
        transform: [{ translateX: translate }, { translateY: translate }]
      };
    };
  }
  const interpolate = (props) => {
    const { scene, scenes } = props;
    const index = scene.index;
    const lastSceneIndexInScenes = scenes.length - 1;
    const isBack = !scenes[lastSceneIndexInScenes].isActive;

    if (isBack) {
      const currentSceneIndexInScenes = scenes.findIndex((item) => item === scene);
      const targetSceneIndexInScenes = scenes.findIndex((item) => item.isActive);
      const targetSceneIndex = scenes[targetSceneIndexInScenes].index;
      const lastSceneIndex = scenes[lastSceneIndexInScenes].index;

      if (
        index !== targetSceneIndex &&
        currentSceneIndexInScenes === lastSceneIndexInScenes
      ) {
        return {
          first: Math.min(targetSceneIndex, index - 1),
          last: index + 1
        };
      } else if (
        index === targetSceneIndex &&
        currentSceneIndexInScenes === targetSceneIndexInScenes
      ) {
        return {
          first: index - 1,
          last: Math.max(lastSceneIndex, index + 1)
        };
      } else if (
        index === targetSceneIndex ||
        currentSceneIndexInScenes > targetSceneIndexInScenes
      ) {
        return null;
      } else {
        return { first: index - 1, last: index + 1 };
      }
    } else {
      return { first: index - 1, last: index + 1 };
    }
  };

  if (!interpolate) return { opacity: 0 };
  const p = interpolate(props);
  if (!p) return;
  const { first, last } = p;
  const index = scene.index;
  const opacity = position.interpolate({
    inputRange: [first, first + 0.01, index, last - 0.01, last],
    outputRange: [0, 1, 1, 0.85, 0]
  });

  const width = layout.initWidth;
  const translateX = position.interpolate({
    inputRange: [first, index, last],
    // outputRange: false ? [-width, 0, width * 0.3] : [width, 0, width * -0.3]
    outputRange: [width, 0, width * -0.3]
  });
  const translateY = 0;

  return {
    opacity,
    transform: [{ translateX }, { translateY }]
  };
}

function createRootStack(initPage, routeParam) {
  return createStackNavigator({
    // 主页面
    LiveVideoPageV2,
    AlarmVideoUI,
    // AllStorage,
    // 设置
    Setting: new RouteProxy("Setting"),
    CameraSetting: new RouteProxy("CameraSetting"),
    OneKeyCallPage: new RouteProxy("OneKeyCallPage"),
    OneKeyCallPageV2: new RouteProxy("OneKeyCallPageV2"),
    AudioVideoCallPage: new RouteProxy("AudioVideoCallPage"),
    Album: new RouteProxy("Album"),
    StorageSetting: new RouteProxy("StorageSetting"),
    SDCardSetting: new RouteProxy("SDCardSetting"),
    DailyStorySetting: new RouteProxy("DailyStorySetting"),
    TalkForPushSettings: new RouteProxy("TalkForPushSettings"),
    DailyStoryList: new RouteProxy("DailyStoryList"),
    LongTimeAlarmList: new RouteProxy("LongTimeAlarmList"),
    IDMSettings: new RouteProxy("IDMSettings"),
    BabySleepingSetting: new RouteProxy("BabySleepingSetting"),
    AICameraSettins: new RouteProxy("AICameraSettins"),
    AICameraSettingsV2: new RouteProxy("AICameraSettingsV2"),
    MotionDetectionPage: new RouteProxy("MotionDetectionPage"),
    OnTimeAlarmPage: new RouteProxy("OnTimeAlarmPage"),
    OnTimeSetting: new RouteProxy("OnTimeSetting"),
    OnTimeSelect: new RouteProxy("OnTimeSelect"),
    KeyCallSetting: new RouteProxy("KeyCallSetting"),
    GestureCallPage: new RouteProxy("GestureCallPage"),
    ChoiceContactPage: new RouteProxy("ChoiceContactPage"),
    WXCallSetting: new RouteProxy("WXCallSetting"),
    SetLongTimeAlarm: new RouteProxy("SetLongTimeAlarm"),
    AlarmGuide: new RouteProxy("AlarmGuide"),
    SurvelillanceSetting: new RouteProxy("SurvelillanceSetting"),
    SurvelillanceSettingOld: new RouteProxy("SurvelillanceSettingOld"),
    MoreSetting: new RouteProxy("MoreSetting"),
    FirmwareUpgrade: new RouteProxy("FirmwareUpgrade"),
    AISetting: new RouteProxy("AISetting"),
    AIFaceSetting: new RouteProxy("AIFaceSetting"),
    AIFaceSettingV2: new RouteProxy("AIFaceSettingV2"),
    SleepSetting: new RouteProxy("SleepSetting"),
    ImageSetting: new RouteProxy("ImageSetting"),
    WDRSetting: new RouteProxy("WDRSetting"),
    ImageRotateSetting: new RouteProxy("ImageRotateSetting"),
    NightVisionSetting: new RouteProxy("NightVisionSetting"),
    NightVisionSettingV2: new RouteProxy("NightVisionSettingV2"),
    SurvelillancePeriodSetting: new RouteProxy("SurvelillancePeriodSetting"),
    SurveillancePeriodSettingV2: new RouteProxy("SurveillancePeriodSettingV2"),
    CustomSurvelillancePeriodSetting: new RouteProxy("CustomSurvelillancePeriodSetting"),
    NotificationTypeSetting: new RouteProxy("NotificationTypeSetting"),
    PartitionSensitivitySetting: new RouteProxy("PartitionSensitivitySetting"),
    SdcardPage: new RouteProxy("SdcardPage"),
    SdcardHourPage: new RouteProxy("SdcardHourPage"),
    SdcardPlayerPage: new RouteProxy("SdcardPlayerPage"),
    SdcardTimelinePlayerPage: new RouteProxy("SdcardTimelinePlayerPage"),
    SdcardTimelinePlayerFragment: new RouteProxy("SdcardTimelinePlayerFragment"),
    CloudTimelinePlayerFragment: new RouteProxy("CloudTimelinePlayerFragment"),
    AlbumPhotoViewPage: new RouteProxy("AlbumPhotoViewPage"),
    AlbumVideoViewPage: new RouteProxy("AlbumVideoViewPage"),
    DailyStoryVideoViewPage: new RouteProxy("DailyStoryVideoViewPage"),
    EditSelectPrePositions: new RouteProxy("EditSelectPrePositions"),
    CruiseSettingPage: new RouteProxy("CruiseSettingPage"),
    CruiseTimeSlotPage: new RouteProxy("CruiseTimeSlotPage"),
    DailyStoryFirstEnter: new RouteProxy("DailyStoryFirstEnter"),
    CloudIntroPage: new RouteProxy("CloudIntroPage"),
    BandInfoPage: new RouteProxy("BandInfoPage"),
    BandNearbyClosePage: new RouteProxy("BandNearbyClosePage"),
    AllStorage: new RouteProxy("AllStorage"),
    DldPage: new RouteProxy("DldPage"),
    AlarmPage: new RouteProxy("AlarmPage"),
    SmartMonitorSetting: new RouteProxy("SmartMonitorSetting"),
    GestureSwitchSetting: new RouteProxy("GestureSwitchSetting"),
    MonitorAreaModifyPage: new RouteProxy("MonitorAreaModifyPage"),
    MonitorDurationListPage: new RouteProxy("MonitorDurationListPage"),
    MonitorDurationSetting: new RouteProxy("MonitorDurationSetting"),
    VisitRecordSetting: new RouteProxy("VisitRecordSetting"),
    CallRecordSetting: new RouteProxy("CallRecordSetting"),
    FaceManager: new RouteProxy("FaceManager"),
    FaceManager2: FaceManager2,
    FaceUnmarkedList: new RouteProxy("FaceUnmarkedList"),
    FacesDetailManager: new RouteProxy("FacesDetailManager"),
    FaceCamera: new RouteProxy("FaceCamera"),
    FaceManagerNumber: new RouteProxy("FaceManagerNumber"),
    FaceEvents: new RouteProxy("FaceEvents"),
    NoVipFaceManager: new RouteProxy("NoVipFaceManager"),
    LDCSetting: new RouteProxy("LDCSetting"),
    ThemeSetting: new RouteProxy("ThemeSetting"),
    ScreenLightSetting: new RouteProxy("ScreenLightSetting"),
    NightModeSetting: new RouteProxy("NightModeSetting"),
    NightModeTimeSetting: new RouteProxy("NightModeTimeSetting"),
    ClockAlarmPage: new RouteProxy("ClockAlarmPage"),
    ClockAlarmSetting: new RouteProxy("ClockAlarmSetting"),
    ClockAlarmSet: new RouteProxy("ClockAlarmSet"),
    AvatarDisplay: new RouteProxy("AvatarDisplay"),
    TabBarDemo: {
      screen: TabBarDemo,
      navigationOptions: () => {
        return {
          headerTransparent: true,
          header: null,
          headerShown: false,
          headerStyle: {
            height: 0
          }
        };
      }
    },
    SdcardCloudTimelinePage: new RouteProxy("SdcardCloudTimelinePage"),
    NativeWebPage: new RouteProxy("NativeWebPage"),
    AddNASSetting: new RouteProxy("AddNASSetting"),
    SelectNASLocation: new RouteProxy("SelectNASLocation"),
    NASNetworkLocation: new RouteProxy("NASNetworkLocation"),
    ChangeNAS: new RouteProxy("ChangeNAS"),
    ChangeNASDinfo: new RouteProxy("ChangeNASDinfo"),
    ChangeNASDirectory: new RouteProxy("ChangeNASDirectory"),
    NasUploadIntervalSetting: new RouteProxy("NasUploadIntervalSetting"),
    NasIntro: new RouteProxy("NasIntro"),
    customFeedbackMessage: new RouteProxy("customFeedbackMessage"),
    InformNoFace: new RouteProxy("InformNoFace"),
    FamilyDetectionSetting: new RouteProxy("FamilyDetectionSetting"),
    FamilyTimeSet: new RouteProxy("FamilyTimeSet"),
    ThemeRightnessSetting: new RouteProxy("ThemeRightnessSetting"),
    WXCallSettingMore: new RouteProxy("WXCallSettingMore"),
    ContactsSetting: new RouteProxy("ContactsSetting"),
    HomeTheme: new RouteProxy("HomeTheme"),
    HomeThemeReview: new RouteProxy("HomeThemeReview"),
    // AI功能
    BabyCrying: BabyCrying,
    PetIdentification: PetIdentification
  },

  {
    initialRouteName: initPage,
    initialRouteParams: routeParam,
    // transitionConfig: Host.isAndroid ? TransitionConfiguration : null,
    transitionConfig: () => {
      return Host.isAndroid ? { screenInterpolator: interpolator } : null;
    },
    navigationOptions: ({ navigation }) => {
      StackNavigationInstance.setStackNavigationInstance(navigation);
      if (navigation.state.params && navigation.state.params.show) {
        return { header: null };
      } else {
        return {
          header:
            // <TitleBar
            //   title={navigation.state.params ? navigation.state.params.title : 'Page Name'}
            //   type={'dark'}
            //   leftText={navigation.state.params ? navigation.state.params.leftText : null}
            //   rightText={navigation.state.params ? navigation.state.params.rightText : null}
            //   onPressLeft={navigation.state.params ? navigation.state.params.onPressLeft : () => { navigation.goBack(); }}
            //   onPressRight={navigation.state.params ? navigation.state.params.onPressRight : null}
            //   leftTextStyle={navigation.state.params ? navigation.state.params.leftTextStyle : null}
            //   rightTextStyle={navigation.state.params ? navigation.state.params.rightTextStyle : null}
            // />
            <NavigationBar
              {...navigation.state.params}
              containerStyle={{ minHeight: 53 }}
            />
        };
      }
    }
  });
}

export default class App extends React.Component {

  // state = {
  //   handlePushFinished: false
  // }

  constructor(props) {
    super(props);
    self.enterTime = new Date().getTime();
    this.initData();

    // if (Platform.OS == "ios") {
    //   this.state = {
    //     handlePushFinished: false
    //   };
    // }
    // console.log(" aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa  " + JSON.stringify(Package.entryInfo));
    this.goAlarmUI = false;
    this.goAlarmList = false;
    this.alarmUIParam = null;
    this.permissionParam = null;

    this.parsePushInfo();
    CameraPlayer.getInstance().getPowerState();
  }
  async _getsdCardStoragePushMessage() {
    CameraConfig.fromSdCardErrorPush = true;
  }
  parsePushInfo() {
    if (!CameraConfig.shouldDisplayNewStorageManage(Device.model) || !VersionUtil.isFirmwareSupportCloud(Device.model)) {
      // 如果不用RN报警视频的页面，就不需要提前parse了
      return;
    }
    // 刚进来时已经有了push content;
    let type = Package.entryInfo.type;
    let event = Package.entryInfo.event;
    let extra = Package.entryInfo.extra;
    let value = Package.entryInfo.value;
    let time = Package.entryInfo.time;
    CameraConfig.fromSdCardErrorPush = false;
    if ((["16.1", "16.2", "16.3", "16.4", "16.5", "16.6"].includes(event)) && Device.model === "chuangmi.camera.069a01") {
      this._getsdCardStoragePushMessage();
    }
    // console.log("push", event, JSON.stringify(extra), JSON.stringify(value), time);
    console.log("index===========push", Package.entryInfo);
    let isPtz = CameraConfig.isPTZCamera(Device.model);
    const isExtraJson = (extra === '') || (!extra);
    if (isExtraJson ? false : typeof(extra) === 'string' ? JSON.parse(extra)?.jump2Live : extra.jump2Live) { // 海外云存需求，推送消息是否跳转到直播页面
      return;
    }
    if ((isPtz || type === "ScenePush") && (event === "smart_camera_motion" || event == "7.1" || event == "26.3" || CameraPlayer.oneKeyCallEvent_051a01 == event || CameraPlayer.callEventRequirePermission_086ac1 == event) && Package.entryInfo.did) {
      console.log("received push");
      if (!extra && value) {
        try {
          // extra没内容,放在value里了
          // event = "smart_camera_motion";
          // value = "%7B%22createTime%22%3A1606901638000%2C%22fileId%22%3A%2250509398390342144%22%2C%22isAlarm%22%3Afalse%2C%22offset%22%3A0%2C%22startDuration%22%3A0%7D"
          let decodeValue = decodeURIComponent(value);
          let valueobj = JSON.parse(decodeValue);
          console.log(`decodeValue: ${ decodeValue }`);
          if (valueobj.createTime && valueobj.fileId) {
            extra = valueobj;
          }
        } catch (error) {
          console.log(error);
        }
      }

      if (extra != null) {
        try {
          let data = extra;
          if (typeof (data) == "string") {
            data = JSON.parse(extra);
          }

          data.did = Package.entryInfo.did;
          data.skipPwd = true;

          let videoFileId = data.fileId;
          let createTime = data.createTime;
          let isAlarm = true;
          if (data.hasOwnProperty("isAlarm")) {
            isAlarm = data.isAlarm;
          }
          let offset = 0;
          if (data.offset) {
            offset = data.offset;
          }
          this.alarmUIParam = {
            item: {
              fileId: videoFileId, createTime: createTime, offset: offset, isAlarm: isAlarm,
              playCfg: { loader: "_CloudEventLoader" }
            }, cfg: { loader: Singletons.CloudEventLoader, player: UriPlayer },
            lstType: "push",
            items: null,
            loaderArgs: { startDate: createTime ? new Date(createTime) : new Date(), filter: "Default", nextDate: null },
            pushType: "startup"
          };
          this.goAlarmUI = true;
          mStartUpPush = true;


        } catch (error) {
          console.log(error);
        }
      }

      if (event === "7.1") {
        // here go alarm page 
        this.goAlarmList = true;
        this.goAlarmUI = false;
        this.alarmUIParam = null;
        mStartUpPush = true;
        this.alarmUIParam = {
          lstType: "push",
          pushType: "startup"
        };
      } else if (CameraPlayer.oneKeyCallEvent_051a01 == event) {
        // 20240907 m300不存在接听页
        return;
        Package.disableAutoCheckUpgrade = true;
        if (!Package.entryInfo.time || Date.now() / 1000 - Package.entryInfo.time <= 30) {
          this.goAlarmUI = false;
          mStartUpPush = false;
          this.gotoOneKeyCallPage = true;
        } else {
          let data = extra;
          if (typeof (data) == "string") {
            data = JSON.parse(extra);
          }
          let videoFileId = data.fileId;
          if (!videoFileId) {
            this.goAlarmList = true;
            this.goAlarmUI = false;
            this.alarmUIParam = null;
            mStartUpPush = true;
            this.alarmUIParam = {
              lstType: "push",
              pushType: "startup"
            };
          }
        }
      } else if (CameraPlayer.callEventRequirePermission_086ac1 == event) {
        // 微信视频通话，需要请求权限 首页时需要弹框提示
        this.goAlarmList = false;
        this.goAlarmUI = false;
        this.permissionParam = null;
        mStartUpPush = false;
        this.permissionParam = {
          showPermission: true
        }
      } else if ('26.3' == event) {
        // 按键呼叫push消息进入
        this.goAlarmList = true;
        this.goAlarmUI = false;
        this.alarmUIParam = null;
        mStartUpPush = true;
        this.alarmUIParam = {
          lstType: "push",
          pushType: "startup"
        };
      }
    }
  }

  initData() {
    this.initPage = "LiveVideoPageV2";
    // this.initPage = "AllStorage";
    if (CameraConfig.isXiaomiCamera(Device.model)) {
      Service.miotcamera.setUseIjkDecoderGlobal(false);// 先都打开，有问题再说;
    } else {
      Service.miotcamera.setUseIjkDecoderGlobal(true);// 先都打开，有问题再说;
    }
  }


  componentDidMount() {
    console.log("index componentDidMount");
    Service.smarthome.reportLog(Device.model, "index componentDidMount");
    Package.disableAutoCheckUpgrade = false;
    Service.miotcamera.closeFloatWindow();// 关闭悬浮窗
    // 跳RN的页面，不用管
    if (CameraConfig.shouldDisplayNewStorageManage(Device.model) && VersionUtil.isFirmwareSupportCloud(Device.model)) {
      return;
    }
    if (Device.isSetPinCode && Platform.OS == "android") { // 如果设置了密码，在直播页面监听输入密码结束事件 判断是否是从push跳过来的  @since 10047
      this.pinCodeEvent = DeviceEvent.pinCodeVerifyPassed.addListener(() => {
        console.log("why! pincode passed");
        this.handlePushFromStartup();
      });
    } else {
      this.handlePushFromStartup();
    }

  }

  async handlePushFromStartup() {

    Service.smarthome.reportLog(Device.model, "index handlePushFromStartup");
    let type = Package.entryInfo.type;
    let event = Package.entryInfo.event;
    let extra = Package.entryInfo.extra;
    let value = Package.entryInfo.value;
    let time = Package.entryInfo.time;
    // console.log("push", event, JSON.stringify(extra), JSON.stringify(value), time);
    let isPtz = CameraConfig.isPTZCamera(Device.model);
    if (type == "ScenePush" && event == "motion" && Package.entryInfo.did && Package.entryInfo.time) {
      Service.smarthome.reportLog(Device.model, "index handlePushFromStartup motion push");
      let data = extra;
      if (typeof (data) == "string") {
        data = JSON.parse(extra);
      }
      data.did = Package.entryInfo.did;
      data.skipPwd = true;
      data.time = Package.entryInfo.time;
      Service.miotcamera.openAlarmVideoPlayer(data);
      mStartUpPush = true;
      if (Platform.OS == 'android') {
        Package.exit();
      }
      return;
    }
    if ((isPtz || type === "ScenePush") && event === "smart_camera_motion" && Package.entryInfo.did) {
      console.log("received push");
      Service.smarthome.reportLog(Device.model, "index handlePushFromStartup smart_camera_motion push");
      if (!extra && value) {
        try {
          // extra没内容,放在value里了
          // event = "smart_camera_motion";
          // value = "%7B%22createTime%22%3A1606901638000%2C%22fileId%22%3A%2250509398390342144%22%2C%22isAlarm%22%3Afalse%2C%22offset%22%3A0%2C%22startDuration%22%3A0%7D"
          let decodeValue = decodeURIComponent(value);
          let valueobj = JSON.parse(decodeValue);
          console.log(`decodeValue: ${ decodeValue }`);
          if (valueobj.createTime && valueobj.fileId) {
            extra = valueobj;
          }
        } catch (error) {
          console.log(error);
        }
      }

      if (extra != null) {
        try {
          let data = extra;
          if (typeof (data) == "string") {
            data = JSON.parse(extra);
          }
          
          if (Platform.OS == 'android') {
            Package.exit();
          }

          data.did = Package.entryInfo.did;
          data.skipPwd = true;
          Service.miotcamera.openAlarmVideoPlayer(data);
          mStartUpPush = true;
          // 从这里进来的 都是从外部push蹦过来的 应该要杀掉RN，直接跳原生页面.

        } catch (error) {
          console.log(error);
        }
      }
    } else {
      console.log("received push others");
      if (VersionUtil.isAiCameraModel(Device.model)) {
        console.log("received push 022");
        if (!extra && value) {
          try {
            // extra没内容,放在value里了
            // event = "smart_camera_motion";
            // value = "%7B%22createTime%22%3A1606901638000%2C%22fileId%22%3A%2250509398390342144%22%2C%22isAlarm%22%3Afalse%2C%22offset%22%3A0%2C%22startDuration%22%3A0%7D"
            let decodeValue = decodeURIComponent(value);
            let valueobj = JSON.parse(decodeValue);
            console.log(`decodeValue: ${ decodeValue }`);
            if (valueobj.createTime && valueobj.fileId) {
              extra = valueobj;
            }
          } catch (error) {
            console.log(error);
          }
        }
        if ("7.1" === event) {
          if (extra != null) {
            try {
              let data = extra;
              if (typeof (data) == "string") {
                data = JSON.parse(extra);
              }
  
              if (Platform.OS == 'android') {
                Package.exit();
              }
  
              data.did = Package.entryInfo.did;
              data.skipPwd = true;
              console.log("long time no body");
              Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, true));
              mStartUpPush = true;
              // 从这里进来的 都是从外部push蹦过来的 应该要杀掉RN，直接跳原生页面.
            } catch (error) {
              console.log(error);
            }
          }
        }
      }
    }
  }
  render() {
    // if (!this.state.handlePushFinished && Platform.OS == "ios") {
    //   //render在componentDidMount之前会调用，消息推送进来先不加载视频首页
    //   return null;
    // }
    let RootStack = null;
    if (this.gotoOneKeyCallPage) {
      console.log("-=-=-=-=-=-=-=-=-=-= gotoOneKeyCallPage");
      if (Device.model == "chuangmi.camera.086ac1") {
        RootStack = createRootStack("OneKeyCallPageV2", this.alarmUIParam);
      } else {
        RootStack = createRootStack("OneKeyCallPage", this.alarmUIParam);
      }
    } else if (this.goAlarmUI && this.alarmUIParam) {
      RootStack = createRootStack("AlarmVideoUI", this.alarmUIParam);
    } else if (this.goAlarmList) {
      RootStack = createRootStack("AlarmPage", this.alarmUIParam);
    } else {
      RootStack = createRootStack(this.initPage, this.permissionParam);
    }

    return (<RootStack
      ref={(ref) => {
        rootStack = ref;// 保留RootStack，获取state.nav.routes 得到当前正在展示页的名称；获取_navigation，得到全局跳转页面的工具类。
      }} />);
  }

  componentWillUnmount() {
    let useTime = (new Date().getTime() - self.enterTime) / 1000; // s
    TrackUtil.reportResultEvent("Camera_Connect_Time", "Time", useTime); // Camera_Connect_Time
    StackNavigationInstance.setStackNavigationInstance(null);
    clearTimeout(this.exitTimeout);
    this.pinCodeEvent && this.pinCodeEvent.remove();
    TrackConnectionHelper.report();
    CameraConfig.restore();// 退出插件的时候，恢复默认值
    CameraPlayer.destroyInstance();
  }
}
