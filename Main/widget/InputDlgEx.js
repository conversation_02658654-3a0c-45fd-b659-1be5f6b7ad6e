import React from 'react';
import PropTypes from 'prop-types';
import { TouchableOpacity, Image, Text, StyleSheet, View, TouchableWithoutFeedback, SafeAreaView, Alert } from 'react-native';
import { InputDialog, AbstractDialog } from "miot/ui/Dialog";
import ScrollableTabView, { DefaultTabBar, ScrollableTabBar } from 'react-native-scrollable-tab-view';
import { BaseStyles } from "../BasePage";
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../util2/Const';
import { Host } from 'miot';

const TAG = "InputDlgEx";
export default class InputDlgEx extends InputDialog {
  static propTypes = {
    icon: PropTypes.any,
    onPressed: PropTypes.any,
    listData: PropTypes.any
  }
  
  constructor(...args) {
    super(...args);
    /**
     * @Author: byh
     * @Date: 2024/5/24
     * @explanation:
     * 子类重写父类，原写法会覆盖父类中state状态
     * ...this.state保留父类中state属性，再进行扩展
     *********************************************************/
    this.state = { ...this.state, playTrick: false };
  }
  
  renderUpExtra() {
    // console.log(TAG, "renderUpExtra", this.props.icon);
    return (
      <Image source={this.props.icon} style={{ height: 50, width: 50, alignSelf: "center", marginBottom: 20, borderRadius: 25 }}>
      </Image>
    );
  }
  
  makeSelGrp(aDat, aLb) {
    let itmW = (SCREEN_WIDTH - 54) / 4;
    if (Host.isPad) {
      itmW = (SCREEN_WIDTH * 0.65) / 4;
    }
    return (<View style = {[BaseStyles.row, { paddingHorizontal: 27, height: 60, justifyContent: "flex-start" }]} tabLabel = {aLb} key = {aLb}>
      {
        aDat.map((aItm, aIdx) => {
          return <TouchableOpacity style = {{ alignItems: "center", width: itmW}} key={`${ aLb }_${ aIdx }`}
            onPress={() => {
              this.props.onPressed(aItm);
              this._onChangeText(aItm.name, 0);
              this.setState({ playTrick: true });
            }}
          >
            <Image source={aItm.faceUrl} style={{ height: 34, width: 34, alignSelf: "center", marginBottom: 5, borderRadius: 17 }}/>
            <Text style={BaseStyles.text13} ellipsizeMode="tail" numberOfLines={1}>{aItm.name}</Text>
          </TouchableOpacity>;
        })
      }
    </View>);
  }
  
  renderInputView(input, index) {
    if (this.state.playTrick) {
      return null;
    } else {
      return super.renderInputView(input, index);
    }
  }
  
  componentDidUpdate(aPreProp, aPreStat) {
    if (aPreStat.playTrick != this.state.playTrick && this.state.playTrick) {
      this.setState({ playTrick: false });
    }
  }
  
  renderDownExtra() {
    // return <View style={{width:"100%", height:200, backgroundColor:"red"}}/>
    let data = this.props.listData;
    if (data && data.length > 0) {
      let vArr = [];
      for (let i = 0; i < data.length; i += 4) {
        vArr.push(this.makeSelGrp(data.slice(i, Math.min(data.length, i + 4)), `tab_${ i }`));  
      }
      let dWidth;
      if (Host.isPad) {
        dWidth = SCREEN_WIDTH - SCREEN_WIDTH * 0.25;
      } else {
        dWidth = SCREEN_WIDTH - 25;
      }
      return <ScrollableTabView
        style={{ height: 80, marginTop: 23, width: dWidth, marginLeft: -25, justifyContent: "flex-start" }}
        initialPage={0}
        locked={1 == vArr.length}
        tabBarPosition="bottom"
        renderTabBar={() => <ScrollableTabBar
          style = {{ borderWidth: 0, height: 6, width: "100%", alignItems: "center" }}
          underlineStyle={{ height: 0 }}
          tabsContainerStyle={{ justifyContent: "space-between", width: vArr.length * 10 }}
          renderTab={(aName, aPage, active) => {
            if (vArr.length > 1) {
              return (<View key={aName} style={{ width: 6, height: 6, borderRadius: 3, backgroundColor: active ? "#32BAC0" : "#000000" }}/>); 
            } else {
              return null;
            }
          }}/>}> 
          {vArr}
      </ScrollableTabView>;
    } else {
      return null;
    }
  }
}
