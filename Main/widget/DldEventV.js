import React from 'react';
import { BaseStyles } from "../BasePage";
import Util from "../util2/Util";
import { StyleSheet, View, Text, Image, TouchableOpacity, Dimensions,PixelRatio,Platform } from 'react-native';
import { CardHeight } from "./EventCard";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Dld_States } from '../framework/DldMgr';
import Host from 'miot/Host';
import { DescriptionConstants } from '../Constants';

const TAG = "DldEventV";
export default class DldEventV extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    let imgStoreUrl = this.props.item.imgStoreUrl;
    let imgSource = imgStoreUrl != null ? { uri: `file://${imgStoreUrl}` } : null;

    let iconSource = Util.getIconFromType(this.props.item.type);
    // console.log(TAG, "this.props.dldProgress ", this.props.dldProgress);
    // nomal 0 read 1 active 2
    let styleIdx = this.props.item.isRead ? 1 : 0;
    if (this.props.isPlaying) {
      styleIdx = 2;
    }
    let timeStyle = [[BaseStyles.text12, { marginTop: 2 }],
    [BaseStyles.text12, { marginTop: 2 }],
    [BaseStyles.text12, { marginTop: 2, color: "#F5A623" }]
    ];

    let descStyle = [[BaseStyles.text16, { fontWeight: "bold" }],
    [BaseStyles.text16],
    [BaseStyles.text16, { color: "#F5A623" }]
    ];
    let durStr = null;
    if (this.props.item.duration) {
      durStr = `${Util.zeroPad(Math.floor(this.props.item.duration / 60), 10)}:${Util.zeroPad(Math.floor(this.props.item.duration % 60), 10)}`;
    }

    let state = this.props.item.state;
    let staStr = LocalizedStrings['dld_waiting'];
    let pauseColor = 'white';
    switch (state) {
      case Dld_States.PAUSE:
        staStr = LocalizedStrings['dld_pause'];
        break;
      case Dld_States.RETRY:
        staStr = LocalizedStrings['dld_retry'];
        break;
      case Dld_States.DOWNLOADING:
        staStr = (this.props.dldProgress ? (Host.locale.language == "tr" ? `${LocalizedStrings['dld_downloading']} %${this.props.dldProgress}` : `${LocalizedStrings['dld_downloading']}${ this.props.dldProgress}%`) : `${LocalizedStrings['dld_downloading']}`);
        break;
      case Dld_States.WAITING:
        staStr = LocalizedStrings['dld_waiting'];
        break;
      case Dld_States.UNEXIST:
        staStr = LocalizedStrings['camera.alarm.video.deleted'];
        break;
    }
    if (this.props.dldProgress == 100) {
      staStr = LocalizedStrings['dld_complete'];
      return null;
    }
    let statColor = "#999999";
    ((state == Dld_States.RETRY) || (state == Dld_States.UNEXIST)) ? statColor = "#F43F31" : statColor = "#999999";

    let screenWidth = Dimensions.get("window").width;
    let progressBW = screenWidth - 20 * 5 - 99 - 22;

    // console.log('dldeventV', this.props.dldProgress, staStr);
    return (
      <View style={[BaseStyles.row, {height: Platform.OS === 'android' ? PixelRatio.getFontScale() * CardHeight : CardHeight, justifyContent: "flex-start", backgroundColor: 'white', alignItems: 'center'}]}>
        <Image style={[styles.imgView, { backgroundColor: "#EEEEEE", alignSelf: "center", marginRight: 20}]} source={imgSource} />
        {
          durStr ?
            <View style={[BaseStyles.row, { justifyContent: "flex-start", position: 'absolute', bottom: 17, paddingLeft: 5 }]}>
              <Image style={{ height: 14, width: 14 }} source={require("../../resources2/images/icon_type_video.png")} />
              <Text style={[BaseStyles.text12, { marginLeft: 5, color: "white" }]}>{durStr}</Text>
            </View>
            :
            <View style={[BaseStyles.row, { justifyContent: "flex-start", position: 'absolute', bottom: 17, paddingLeft: 5 }]}>
              <Image style={{ height: 14, width: 14 }} source={require("../../resources2/images/icon_type_video.png")} />
            </View>
        }


        <TouchableOpacity style={[BaseStyles.column, { width: progressBW, minHeight: 60, alignItems: "flex-start", justifyContent: "space-between", marginRight: 20 }]}
          onPress={() => { this.props.cardPaused(this.props.item); }}>
          <Text style={[BaseStyles.text16, { fontWeight: "bold", backgroundColor: pauseColor }]}>{Util.getMoment(this.props.item.createTime / 1000).format(LocalizedStrings["yyyymmdd"] + " HH:mm")}</Text>
          <Text style={[BaseStyles.text13, { color: statColor, marginBottom: 10, width: 160 }]} numberOfLines={3}>{staStr}</Text>
          <View style={[BaseStyles.row, { backgroundColor: "#EEEEEE", width: progressBW, height: 6, borderRadius: 3 }]}>
            <View style={{ flex: this.props.dldProgress != null ? this.props.dldProgress / 100 : 0, backgroundColor: "#32BAC0", height: 6, borderRadius: 3 }} />
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={{ flex: 1, height: "100%", alignItems: "center", justifyContent: "center" }}
          onPress={() => { this.props.cardPressed(this.props.item); }}
          accessibilityLabel={DescriptionConstants.lc_29}  
        >
          <Image source={require("../../resources2/images/icon_dld_cancel.png")}
            style={{ width: 22, height: 22, borderRadius: 11 }}
          />
        </TouchableOpacity>
      </View>
    );
  }
}

var styles = StyleSheet.create({
  timeView: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  pressedTimeLabel: {
    color: 'gray'
  },
  descLabel: {
    fontSize: 16,
    color: 'black',
    backgroundColor: "blue"
  },
  imgView: {
    width: 99,
    height: PixelRatio.getFontScale() * 64,
    borderRadius: 9
  },
  typeIcon: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    height: 14,
    width: 14
  }
});
