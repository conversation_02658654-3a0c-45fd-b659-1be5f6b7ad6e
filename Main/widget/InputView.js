import React from 'react';
import {StyleSheet, TouchableOpacity, View, Image, I18nManager, Platform,Text} from 'react-native';
import {RkTextInput, RkTheme} from 'react-native-ui-kitten';
import {DarkMode} from "miot";

const FontPrimary = {
    fontFamily: 'sans-serif-medium',
    fontWeight: Platform.OS === 'ios' ? '500' : 'normal'
};
/**
 * 输入框的类型
 */
export const TYPE = {
    DELETE: 'DELETE',//右侧有删除按钮
    NONE: 'NONE'//右侧无图标
};
Object.freeze(TYPE);


class InputView extends React.Component {
    static TYPE = TYPE;
    static defaultProps = {
        style:{marginHorizontal: 24},
        type: TYPE.NONE,
        placeholder: '占位符',
        defaultValue: '默认值',
        textInputProps: {
            autoFocus: true
        },
        isCorrect: true,
        errorHint: null,
        inputBottomHint:null
    };

    constructor(props) {
        super(props);
        this.state = {
            inputValue: this.props.defaultValue || '',
            secureState: props.type === TYPE.SECURE,
            isFocused: false
        };
        this._onChange = this._onChange.bind(this);
        this._onFocus = this._onFocus.bind(this);
        this.isDark = DarkMode.getColorScheme() == "dark";
    }

    _onChange(changeText) {
        this.setState({
            inputValue: changeText
        });

        if (this.props.onChangeText) {
            const onChangeTextOrigin = this.props.onChangeText;

            if (onChangeTextOrigin instanceof Function) {
                onChangeTextOrigin(changeText);
            }
        }
    }

    _onFocus() {
        this.setState(prevState => ({
            isFocused: !prevState.isFocused
        }));
    }

    /** 右侧删除按钮 */
    _renderRightDeleteButtonView() {
        if(this.state.inputValue==''){
            return null;
        }

        return <TouchableOpacity style={styles.touchStyle} onPress={() => {
            this._onChange('');
        }}>
            <Image style={{
                height: 20,
                width: 20
            }} source={require('../../Resources/Images/icon_input_view_delete.png')}/>
        </TouchableOpacity>;
    }



    _notNull(str) {
        let result = false;

        if (str === '' || str === undefined) {
            result = false;
        }

        if (str.length > 0) {
            result = true;
        }

        return result;
    }

    render() {
        const {
            inputValue,
            secureState,
            isFocused
        } = this.state;
        const {
            type,
            isCorrect,
            errorHint,
            inputBottomHint
        } = this.props;
        const greenBorder = this.props.borderColor ? this.props.borderColor : "#32bac0";
        let borderColor = isFocused && this._notNull(inputValue) ? greenBorder : 'transparent';
        RkTheme.setType('RkTextInput', 'textinput', {
            input: {
                marginVertical: 0,
                color: this.isDark?"#A9A9A9":'#000000',
                marginLeft: 0,
                fontSize: 16,
                ...FontPrimary
            },
            marginLeft: 16,
            flex: 1,
            underlineWidth: 0,
            marginVertical: 0,
            placeholderTextColor: this.props.textInputProps?.placeholderTextColor || (this.isDark?'#696969':'rgba(0,0,0,0.3)'),
            minHeight: 40,
            borderRadius: 10
        });


        /*// 错误红色边框
        if (!isCorrect) {
            borderColor = '#f43f31';
        }*/

        if (type === TYPE.DELETE) {
            this.renderRight = this._renderRightDeleteButtonView();
        } else {
            this.renderRight = null;
        }

        const textInput = <RkTextInput inputStyle={{
            textAlign: I18nManager.isRTL ? 'right' : 'left'
        }} placeholder={this.props.placeholder} value={inputValue} {...this.props.textInputProps || {}}
                                       onChangeText={this._onChange} secureTextEntry={secureState}
                                       onFocus={this._onFocus} onBlur={this._onFocus}
                                       selectionColor={'#32bac0'}
                                       rkType="textinput"/>;

        return (<View style={this.props.style}>
            <View style={[styles.containerOuter,
                {borderColor: 'transparent'}]}>
                {/*  {borderColor: !isCorrect ? 'rgba(244,63,49,0.2)' : 'transparent'}]}>*/}

                <View style={[styles.container, {
                    borderColor,
                    flex: 1,
                    backgroundColor: this.isDark?'#333333' :'#00000008'

                }]}>
                    {textInput}
                    {this.renderRight}
                </View>
            </View>
            {!isCorrect && errorHint || isCorrect && inputBottomHint && isFocused ?
                <View style={{marginHorizontal: 22, marginBottom: 10}}>
                    <Text style={{fontSize: 14, color: isCorrect ? "rgba(0,0,0,0.4)" : "#F30018"}}>
                        {isCorrect ? inputBottomHint : errorHint}</Text>
                </View>
                : null}
        </View>);
    }

}


const styles = StyleSheet.create({
    container: { // 输入框容器
        flexDirection: 'row',
        height: 53,
        alignItems: 'center',
        justifyContent: 'flex-start',
        borderRadius: 18,
        borderWidth: 2
    },
    containerOuter: { // 最外层容器

        height: 58,
        borderRadius: 18,
        borderWidth: 3,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    touchStyle: {//右侧按钮样式
        marginHorizontal: 10,
        height: 20,
        width: 20,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center'
    }
});
export default InputView;