import React from 'react';
import { StyleSheet, View, Text, FlatList, Image, Platform } from "react-native";
import {  Device, Service } from 'miot';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import EventCard, { CardHeight, CardMB } from "./EventCard";
import { BaseStyles } from "../BasePage";
import { DldStatus, Order } from "../framework/EventLoaderInf";
import { CldDldTypes } from "../framework/CloudEventLoader";
import Util from "../util2/Util"
import { DescriptionConstants } from '../Constants';
import ABTest from "../util/ABTest";
export const LoadStatus = {
  Idle: "Idel",
  Finish: "Finish",
  Failed: "Failed",
  Loading: "Loading"
};
const TAG = "EventList";
export const DefFilter = "Default";

export const ChangeType = {
  Delete: "Delete",
  Modify: "Modify"
};

export function buildChange(aType, ...args) {
  switch (aType) {
    case ChangeType.Delete:
      return {
        action: "removeEvents",
        args: [(aEv) => {
          console.log(TAG, "action removeEvents  check ", aEv.fileId, "vs", args[0], "ret", args[0].indexOf(aEv.fileId) < 0);
          return args[0].indexOf(aEv.fileId) < 0;
        }]
      };
    case ChangeType.Modify:
      return { action: "applyAction2Events", args: args };
    default:
      return null;
  }
}

export function applyChange(aLst, actions) {
  let actFilter = [];
  for (let act of actions) {
    if (act.id != null) {
      if (actFilter.indexOf(act.id) < 0) {
        aLst[act.action](...act.args);
      }
      actFilter.push(act.id);
    } else {
      aLst[act.action](...act.args);
    }
  }
}

export default class EventList extends React.Component {

  static defaultProps = {
    eventHeaderView: () => { return null; },
    eventHeaderHeight: 0
  }
  constructor(aProps) {
    super(aProps);
    this.ListFooterH = Platform.OS == "ios" ? 20 : 10;
    let mWithDailyStoryButton = this.props?.withDailyStoryButton;
    if (mWithDailyStoryButton && mWithDailyStoryButton == true && Platform.OS == 'android') {
      this.ListFooterH = 35;
    }
    let ev = this.props.events || [];
    if (this.props.dataFilter) {
      ev = this.applyFilter(ev, this.props.dataFilter);
    }
    // let initIdx = this.getInitScroll();

    let startDate = null;
    let nextDate = null;
    startDate = this.props.loaderArgs.startDate;
    nextDate = this.props.loaderArgs.nextDate;

    let loadingStatus = LoadStatus.Idle;
    if (ev != null && ev.length > 0 && null == nextDate) {
      loadingStatus = LoadStatus.Finish;
    }
    this.state = {
      loadingStatus,
      nextDate,
      startDate,
      events: ev,
      isP2pLostEmpty: false
    };
    console.log(TAG, "init with state", loadingStatus, ev ? ev.length : 0, nextDate, this.props.loader.constructor.name);
    this.mLoader = this.props.loader;
    this.mActive = false;
    this.mLst = null;
    this.mTopItmIdx = -1;
    this.sltDay = this.props.loaderArgs.sltDay;
    this.localSelectDays = false;
    this.mSltDayMore = false;
    this.mAutoRefresh = this.props.autoRefresh == null ? true : this.props.autoRefresh;
    this.mKanjiaListMergeABType = this.props?.abType ? this.props.abType : ABTest.Types.A; // 如果没有自定abType，使用默认的对照组A作为看家列表和播放合并的ab测试类型

    console.log(TAG, "set sltDay: ", this.props.loaderArgs.sltDay);
  }

  getInitScroll() {
    let initIdx = 0;
    let ev = this.state.events;
    if (this.props.playingId) {
      for (let i = 0; i < ev.length; ++i) {
        let event = ev[i];
        if (event.fileId === this.props.playingId) {
          return { animated: false, index: i, viewOffset: 30 };
        }
      }
    }
    return null;
  }

  componentDidMount() {
    this.mActive = true;
    this.mListener = this.mLoader.addListener(() => { this.mAutoRefresh && this.mRefresh(); });
    if (0 === this.state.events.length) {
      this.mRefresh();
    } else {
      // need a delay to do the scroll
      this.delayToScroll();
    }
  }

  delayToScroll() {
    setTimeout(() => {
      let scroll = this.getInitScroll();
      console.log(TAG, "scroll", scroll,this.mActive);
      if (this.mActive && scroll != null) {
        this.scrollTo(scroll);
      }
    }, 100);
  }


  componentWillUnmount() {
    this.mActive = false;
    this.mListener.remove();
  }


  scrollTo(aLoc) {
    console.log(TAG, "scrollTo", this.constructor.name);
    if (this.mLst) {
      this.mLst.scrollToIndex(aLoc);
    }
  }


  async getData(date, event, isMore = false, aOrder = Order.Desc, type = this.props.type) {

    console.log("landing4");
    let data = await this.mLoader.getEventList(date, event, isMore, type);
    return data;
  }

  appendEvents(aOldEv, aNewEv, aOrder = Order.Desc) {
    if (Order.Desc == aOrder) {
      return aOldEv.concat(aNewEv);
    } else {
      return aNewEv.concat(aOldEv);
    }

  }

  updateAllItems() {
    console.log(this.tag, 'reload all items');
    this.setState({ events: null });
    this.mRefresh();
  }

  async getEventList(date, event, isMore = false, aOrder = Order.Desc) {
    let now = Date.now();
    let reNonce = `${now}${Math.random(now)}`;
    this.mReNonce = reNonce;
    let events = this.state.events;
    // console.log("events1111111111111111111111111111111111:",this.state.events);
    if (!isMore) {
      events = [];
    }
    let loadingStatus = this.state.loadingStatus;
    let nextDate = this.state.nextTime;// ????? this.state.nextState
    let keepRet = false;
    let status = DldStatus.Complete;
    let hasMore = false;
    let totalSize = 0;
    try {

      console.log("landing2");

      let data = await this.getData(date, event, isMore, aOrder);
      Service.smarthome.reportLog(Device.model, "getEventListData:" + data);
      // console.log("data:", data);
      console.log("landing3");
      console.log(TAG, "getData ", date, "with", event, "datas", data.items ? data.items.length : 0, "nextDate", data.nextTime);
      keepRet = this.mReNonce === reNonce || !isMore;

      if (keepRet) {
        nextDate = data.nextTime;
        loadingStatus = data.hasMore ? LoadStatus.Idel : LoadStatus.Finish;
        hasMore = data.hasMore;
        if (data.items && data.items.length > 0) {
          console.log("landing301");
          totalSize = data.items.length;
          events = this.appendEvents(events, data.items, aOrder);
          console.log("landing302");
          this.downloadFileThump(data.items);
        } else {
          loadingStatus = LoadStatus.Finish;
          status = data.status;
        }
      } else {
        console.log(TAG, "drop ret");
      }
    } catch (err) {
      console.log(TAG, "got error", err);
      keepRet = this.mReNonce === reNonce || !isMore;
      loadingStatus = LoadStatus.Failed;
    } finally {
      if (keepRet) {
        this.setState({
          loadingStatus,
          events,
          nextDate,
          isP2pLostEmpty: status == DldStatus.p2pLost
        });
        if (this.props.onGetDataDone) {
          // let dates = [];
          // for (let evt in events) {
          //   dates.push(events[evt]['date']);
          // }
          // this.props.onGetDataDone(events.length, -1, dates);
          console.log("=====",events);
          this.onGetDataDone(events);
        }

        if (!isMore && hasMore && (totalSize < 20 && this.props.type == CldDldTypes.Files || totalSize < 10 && CldDldTypes.Events)) { // 初次加载数据, 如果数据不满一屏幕，就要要尝试加载下一次数据
          this.onEndTimeout = setTimeout(() => {
            this.mOnEnd();
          }, 500);
        }
      }
      console.log(TAG, "getEventList finish with keepRet", keepRet, "nextDate", nextDate);
    }
    return keepRet;
  }

  onGetDataDone(events) {
    let dates = [];
    for (let evt in events) {
      if (events[evt]?.isNoSVLTips == true) {
        continue;
      } else {
        dates.push(events[evt]['date']);
      }
    }
    if (events?.length > 0 && (events[0]?.isNoSVLTips || events[0]?.isCloudTips)) {
      this.props.onGetDataDone(events.length - 1, events, dates);
    } else {
      this.props.onGetDataDone(events.length, events, dates);
    }
  }


  async downloadFileThump(items) {
    // load from new to old
    //如果图片返回太快，就慢一些刷新，避免频繁刷新导致的UI卡顿问题，如果
    let lastNotifyTime = Date.now();
    for (let i = 0; i < items.length && this.mActive; ++i) {
      try {
        let item = items[i];
        // console.log(this.tag, "get thumb", item.createTime, i, item);
        item.imgStoreUrl = await this.mLoader.getThumb(item);
        // 3 thumb per refresh
        if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
          continue;
        }
        lastNotifyTime = Date.now();
        // if (i % 4 == 3 || i == items.length - 1) {
          this.setState({});
        // }
      } catch (err) {
        console.log(this.tag, "getthumb", err);
      }
    }
    this.setState({});
  }

  onTopItemChange(aOldIdx, aNewIdx) {

  }


  render() {
    // console.log(TAG, "render loadingStatus", this.state.loadingStatus);
    let hvf = this.props.eventHeaderView;
    let hh = hvf != null ? this.props.eventHeaderHeight : 1;
    let cardH = CardHeight + CardMB;
    // console.log(TAG, "cardH hh", cardH, hh);
    // console.log("landing9");
    return (
      <FlatList
        data={this.state.events}
        style={this.props.style}
        ref={(ref) => { this.mLst = ref; }}
        onScrollToIndexFailed={()=>{

        }}
        contentContainerStyle={[this.props.contentContainerStyle, { paddingBottom: 50, flexGrow: 1 }]}
        showsVerticalScrollIndicator={false}
        renderItem={
          ({ item, index }) => {
            if (this.props.evCard) {
              return this.props.evCard(item);
            } else {
              return (
                // <View>
                <EventCard
                  style={styles.item} item={item}
                  isPlaying={this.props.playingId === item.fileId}
                  cardPressed={(aItm) => {

                    // this.naviTo('AlarmDetail', { item: item, event: this.state.selectedEventKey });
                    item.isRead = true;
                    let events = this.state.events;
                    let nextDate = this.state.nextDate;
                    this.props.onEventPress(aItm, { events, nextDate });
                  }} />
                // <Text style={{position:"absolute", left:0, top:0, fontSize:20, color:"red"}} >{index}</Text>
                // </View>
              );
            }
          }
        }

        onScroll={({ nativeEvent }) => {
          // console.log(TAG, "onScroll", nativeEvent);
          let offY = nativeEvent.contentOffset.y;
          let topIdx = Math.floor((offY - hh) / cardH);
          if (topIdx != this.mTopItmIdx) {
            // console.log(TAG, "onTopItemChange", topIdx);
            this.onTopItemChange(this.mTopItmIdx, topIdx);
            this.mTopItmIdx = topIdx;
          }
          if (this.props.onScroll != null) {
            this.props.onScroll(nativeEvent.contentOffset);
          }
        }}
        ListEmptyComponent={this.mEmptyV()}
        ListHeaderComponent={hvf ? () => { return hvf(); } : null}
        ListFooterComponent={this.props.listFooter ? this.props.listFooter : this.mFooter}
        keyExtractor={(item, index) => index.toString()}
        refreshing={LoadStatus.Loading === this.state.loadingStatus}
        onRefresh={this.mRefresh}
        onEndReached={() => {
          this.onEndTimeout && clearTimeout(this.onEndTimeout);
          this.mOnEnd();
        }}
        onEndReachedThreshold={0.1}
        getItemLayout={(data, index) => {

          let ret = { length: cardH, offset: cardH * index + hh, index };
          // console.log(TAG, "getItemLayout", data.length, index, ret);
          return ret;
        }}
      />
    );
  }

  componentDidUpdate(aPrevProps) {
    if (this.props.loaderArgs.startDate.getTime() !== aPrevProps.loaderArgs.startDate.getTime()
      || this.props.loaderArgs.filter !== aPrevProps.loaderArgs.filter) {
      this.setState({ events: [], loadingStatus: LoadStatus.Idel }, () => {
        this.mRefresh(true);
      });
    }
  }

  getEventFilter() {
    let filter = this.props.loaderArgs.filter;
    if (null == filter) {
      filter = DefFilter;
    }
    return filter;
  }

  removeEvents(aFilter) {
    let newEv = this.applyFilter(this.state.events, aFilter);
    console.log(TAG, 'removeEvents', newEv.length);
    this.setState({ events: newEv });
    if (0 == newEv.length || this.props.typeTab === 1) {
      this.mRefresh();
    } else {
      this.mOnEnd();
    }
  }

  switchDay(aDat, sltDay, localSelectDays) {
    let newEv = [];
    this.sltDay = sltDay;
    this.localSelectDays = localSelectDays;
    let oldDat = this.state.startDate;
    this.setState({ events: newEv, startDate: aDat, nextDate: null, LoadStatus: LoadStatus.Idle }, () => {
      if ((oldDat && oldDat.getTime() == aDat.getTime()) || this.mKanjiaListMergeABType !== ABTest.Types.A) {
        this.mRefresh();
      }
    });

  }

  applyFilter(aEvs, aFilter) {
    return aEvs.filter(aFilter);
  }

  getAdjacentEvent(aEv) {
    let evs = this.state.events;
    let idx = evs.findIndex((aItm) => {
      return aItm.fileId == aEv.fileId;
    });
    let ret = null;
    if (idx != -1) {
      if (idx == evs.length - 1) {
        if (evs.length != 1) {
          ret = evs[idx - 1];
        }
      } else {
        ret = evs[idx + 1];
      }
    }
    return ret;
  }

  mEmptyV = () => {
    let emptyMT = 0;
    if (this.props.eventHeaderHeight) {
      emptyMT = -this.props.eventHeaderHeight / 2;
    }
    return (this.state.loadingStatus === LoadStatus.Finish ?
      <View
        accessibilityLabel={DescriptionConstants.kj_1_17}
        style={{ height: "100%", justifyContent: "center", alignItems:"center" }}>
        <Image
          style={{ alignSelf: "center", width: 138, height: 138 }} source={Util.isDark() ? require("../../resources2/images/icon_ev_empty_w.png") : require("../../resources2/images/icon_ev_empty.png")} />
        <Text style={{ color: 'gray', textAlign: "center", paddingHorizontal: 40 }}
          numberOfLines={2}
        >
          {
            this.state.isP2pLostEmpty ? LocalizedStrings.device_not_cont_sdcard_page_desc_empty : (this.props.emptyDes ? this.props.emptyDes : (this.getEventFilter() === DefFilter ? LocalizedStrings.sdcard_page_desc_empty : LocalizedStrings.all_event_empty))
          }
        </Text>
      </View>
      : null);
  }


  mRefresh = () => {
    console.log("landing7");
    this.getEventList(this.state.startDate, this.getEventFilter(), false);
  }

  mFooter = () => {
    let text = null;
    if (this.props.type == CldDldTypes.Events)
      switch (this.state.loadingStatus) {
        case LoadStatus.Finish:
          if (!(this.state.events == null || 0 == this.state.events.length)) {
            text = LocalizedStrings.alarm_none_data;
          }
          break;
        case LoadStatus.Loading:
          // text = LocalizedStrings.alarm_loading_data;
          break;
        case LoadStatus.Failed:
          // text = LocalizedStrings.alarm_loading_failed;
          break;
        default:
          break;
      }
    return text ?
      <View style={{ height: this.ListFooterH, alignItems: 'center', marginTop: 10 }}>
        <Text style={{ color: 'gray' }}>{text}</Text>
      </View>
      : null;
  }


  mOnEnd = () => {
    if (this.localSelectDays) { //判断是本地视频，同时是用户点击某一天，不再多次调用接口，否则会出现视频重复（历史逻辑原因，用面向过程思想为最低成本改动）
      return false;
    }
    console.log(TAG, "mOnEnd");
    if (LoadStatus.Finish === this.state.loadingStatus || LoadStatus.Loading === this.state.loadingStatus) {
      console.log(TAG, "onEndReached skip loading for status", this.state.loadingStatus);
      if (this.props.onGetDataDone) {
        // this.props.onGetDataDone(this.state.events.length);
        this.props.onGetDataDone(this.state.events.length, this.state.events);

      }
      if (this.props.isSltDay && this.props.onSwitchSltDayMore && !this.mSltDayMore) {
        console.log("landing6 2");
        this.props.onSwitchSltDayMore();
        this.mSltDayMore = true;
        console.log("landing6 1");
        this.setState({ loadingStatus: LoadStatus.Loading });
        setTimeout(() => {
          this.localSelectDays = false;
          let date = new Date(this.state.startDate);
          console.log('mNextDate: ', date, 'startDate: ', this.state.startDate);
          this.getEventList(date, this.props.loaderArgs.filter, true);
        }, 100);
      }
    } else {
      console.log("landing6");
      this.getEventList(this.state.nextDate, this.props.loaderArgs.filter, true);
      this.setState({ loadingStatus: LoadStatus.Loading });
    }
  }

  mSwitchSltDayMore(isMore) {
    this.mSltDayMore = isMore;
  }

  getEvents() {
    return this.state.events;
  }
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseStyles.mainBg.backgroundColor
  },
  listContainer: {
    borderRadius: 10,
    backgroundColor: 'white',
    marginTop: 10,
    marginHorizontal: 10
  },
  item: {
    height: 44
  },
  emptyView: {
    height: "50%",
    backgroundColor: "blue",
    justifyContent: 'center',
    alignItems: 'center'
  }
});
