import PropTypes, { func } from "prop-types";
import { StyleSheet, NativeModules, View, ImageBackground, Image, Dimensions } from 'react-native';
import React from 'react';

// let backImage = require('../../resources/Images/bg_holder_land.png');//
let InitialOffset = 0;
let view360Left = 1;
let view360Right = 101;
let view270Left = 13.5;
let view270Right = 88.5;
let view180Left = 26;
let view180Right = 76;


/**
 * 全景视图滑动选择位置
 */
export default class OverAllControlView extends React.Component {

    static propTypes = {
      isFullscreen: PropTypes.bool, // 全屏使用的是小背景图片
      selectPositionX: PropTypes.number,
      minSelectPositionLeft: PropTypes.number,
      maxSelectPositionRight: PropTypes.number,
      panoramType: PropTypes.number,
      imgStoreUrl: PropTypes.object,
      onTouch: PropTypes.func,
      onLoosen: PropTypes.func
    }

    static defaultProps = {
      isFullscreen: false,
      onTouch: null,
      onLoosen: null,
      selectPositionX: 0,
      minSelectPositionLeft: -1.0,
      maxSelectPositionRight: -1.0,
      panoramType: 0,
      imgStoreUrl: null,
      offsetX: InitialOffset,
      offsetY: InitialOffset
    };

    selectWidth = 80.0;// 选择框宽度
    selectHeight = 60;// 选择框高

    rootPageWidth = -1.0;// 控件宽度
    pressed = false;
    noMovePageX = -1.0;// 没有move时的点击位置
    noMovePageY = -1.0;// 没有move时的点击位置
    hasMove = false;
    timer = null;
    // imgStoreUrl = "/storage/emulated/0/Android/data/com.xiaomi.smarthome/cache/commonImages/367926260/d6fec4aa938d929a897057c4de7f8a8a.0";
    imageWidth = 100;// 设备生成图片坐标宽度
    imageLeft = 0.0;// 设备生成图片最左边坐标
    imageRight = 0.0;// 设备生成图片最右边坐标

    constructor(props) {
      super(props);
      console.log(`why!, constructor OverAllControlView, selectPositionX: ${ props.selectPositionX }`);

      this.distance = 0;
      InitialOffset = 0;// 圆心偏移位置，保证在圆心上
      this.state = {
        pressed: false,
        cover: 0, // 上下左右被选中以后的特效背景
        offsetX: 0.0, // 相对于图片的位置 单位dp
        offsetY: 0.0
      };

      this.offset = Math.min(Dimensions.get("window").width, Dimensions.get("window").height) * 5 / 100 / 2;
    }



    render() {
      return (this.renderView());
    }

    renderView() {
      // let imgSource = {uri: 'file://' + "/storage/emulated/0/Android/data/com.xiaomi.smarthome/cache/commonImages/367926260/d6fec4aa938d929a897057c4de7f8a8a.0"}
      let path = this.props.imgStoreUrl;
      let imgSource = { uri: `file://${ path }` };
      // console.log("========renderView=========","path="+path)

      return (
        <View
          style={{
            // backgroundColor: '#5EBAC1',
            backgroundColor: 'transparent',
            borderRadius: 5,
            width: "100%",
            height: this.selectHeight,
            justifyContent: 'center',
            alignItems: 'center'
          }}
          onTouchStart={(evt) => {
            // this.setState({pressed: true});
            this.pressed = true;
            console.log("========onTouchStart=========", `evt.nativeEvent.locationX=${ evt.nativeEvent.locationX }evt.nativeEvent.locationY=${ evt.nativeEvent.locationY }`);
            this.noMovePageX = evt.nativeEvent.pageX - this.offset;
            this.noMovePageY = evt.nativeEvent.locationY;
            // this.updatePoint(evt.nativeEvent.pageX, evt.nativeEvent.pageY);
          }}

          onTouchMove={(evt) => {
            if (this.pressed) {
              this.hasMove = true;
              let trueLocationX = evt.nativeEvent.pageX - this.offset;// 坐标系转换，这是超出范围以后的点，转化为此View的直角坐标系（以rootXY为圆心）
              let trueLocationY = evt.nativeEvent.pageY;
              this.updatePoint(trueLocationX, trueLocationY);
            }
          }}
          onTouchCancel={() => {
            this.touchEnd();
          }}
          onTouchEnd={() => {
            this.touchEnd();
          }}

          onLayout={(e) => {
            NativeModules.UIManager.measure(e.target, (x, y, width, height, pageX, pageY) => {

              // pageX是组件在当前屏幕上的绝对位置
              this.rootPageWidth = width;

              if (this.props.panoramType == 0) {
                this.imageWidth = view360Right - view360Left;
                this.imageLeft = this.props.minSelectPositionLeft < this.props.maxSelectPositionRight ? view360Left : view360Right;
                this.imageRight = this.props.minSelectPositionLeft < this.props.maxSelectPositionRight ? view360Right : view360Left;
              }
              if (this.props.panoramType == 1) {
                this.imageWidth = view270Right - view270Left;
                this.imageLeft = this.props.minSelectPositionLeft < this.props.maxSelectPositionRight ? view270Left : view270Right;
                this.imageRight = this.props.minSelectPositionLeft < this.props.maxSelectPositionRight ? view270Right : view270Left;
              }
              if (this.props.panoramType == 2) {
                this.imageWidth = view180Right - view180Left;
                this.imageLeft = this.props.minSelectPositionLeft < this.props.maxSelectPositionRight ? view180Left : view180Right;
                this.imageRight = this.props.minSelectPositionLeft < this.props.maxSelectPositionRight ? view180Right : view180Left;
              }
              // 获取控件对应宽度
              console.log(`why!, this.rootPageWidth: ${ this.rootPageWidth }`);
              console.log(`why!, this.imageWidth: ${ this.imageWidth }`);
              console.log(`why!, Math.abs(this.props.minSelectPositionLeft-this.props.maxSelectPositionRight): ${ Math.abs(this.props.minSelectPositionLeft - this.props.maxSelectPositionRight) }`);
              this._initSelectView();
              console.log("====onLayout=========", `x=${ x }pageX+====${ pageX }width=${ width }this.selectWidth==${ this.selectWidth }panoramType==${ this.props.panoramType }`);
            });
          }}
          accessibilityLabel={this.props.accessibilityLabel}
          accessible={true}
        >

          <Image source={imgSource} style={{
            width: "100%",
            height: "100%",
            borderRadius: 10,
            justifyContent: 'center',
            alignItems: 'center',
            resizeMode: "stretch"
          }}/>

          <View
            style={{
              backgroundColor: 'transparent',
              borderRadius: 8,
              width: this.selectWidth,
              height: this.selectHeight,
              borderColor: "xm#ffffffff",
              borderWidth: 2,
              position: 'absolute',
              left: (this.state.offsetX - this.selectWidth / 2) > 0 ? (this.state.offsetX - this.selectWidth / 2) : 0
            }}
            onLayout={(e) => {
              NativeModules.UIManager.measure(e.target, (x, y, width, height, pageX, pageY) => {
                // pageX pageY是组件在当前屏幕上的绝对位置,
              });
            }}
          >
          </View>

        </View>
      );
    }

    _initSelectView() {
      let centerX = this.selectWidth / 2 + Number.parseInt((this.props.selectPositionX - this.props.minSelectPositionLeft) / (this.props.maxSelectPositionRight - this.props.minSelectPositionLeft) * 100) * (this.rootPageWidth - this.selectWidth) / 100;
      this.setState({ offsetX: Number.parseInt(centerX) });
    }

    touchEnd() {
      if (!this.hasMove && this.noMovePageX != -1) { // 点击了一下，就抬起，没有move
        this.updatePoint(this.noMovePageX, this.noMovePageY);
      }
      this.timer = setTimeout(
        () => {
          // 首先得到当前滑块的位置占据整个view宽度的百分比
          let percent = Number.parseInt((this.state.offsetX - this.selectWidth / 2) * 100 / (this.rootPageWidth - this.selectWidth));
          // 这里得到了百分比
          let absAngle = this.props.maxSelectPositionRight - this.props.minSelectPositionLeft;
          let pointX = this.props.minSelectPositionLeft + percent * absAngle / 100;
          this.props.onTouch(Number.parseInt(pointX));
          console.log("pointX:" + pointX + " absAngle:" + absAngle + " percent:" + percent  + " offsetx:" + this.state.offsetX + " left:" + this.props.minSelectPositionLeft + " right:" + this.props.maxSelectPositionRight);
        },
        0
      );


      this.pressed = false;
      this.hasMove = false;
      this.noMovePageX = -1;
      // if (this.props.onLoosen != null) {
      //     this.props.onLoosen();
      // }
    }

    // 这里的offsetX,offsetY为触摸点（摇杆）相对于父控件（背景盘）的相对坐标，原点为父控件左上角
    updatePoint(offsetX, offsetY) {
      if (this.rootPageWidth > 0 && this.rootPageWidth < (offsetX + this.selectWidth / 2)) {// 滑动到头了,就设置为最大值-selectWidth/2
        this.setState({
          offsetX: this.rootPageWidth - this.selectWidth / 2,
          offsetY: offsetY
        });
      } else if (offsetX < this.selectWidth / 2) {
        this.setState({
          offsetX: this.selectWidth / 2,
          offsetY: offsetY
        });
      } else {
        this.setState({
          offsetX: offsetX,
          offsetY: offsetY
        });
      }

    }


}
