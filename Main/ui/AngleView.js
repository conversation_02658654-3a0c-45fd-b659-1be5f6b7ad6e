import { View, Image } from "react-native";

import React from 'react';


export default class AngleView extends React.Component {

  constructor(props) {
    super(props);
  }

  componentDidMount() {

  }

  componentWillUnmount() {

  }

  render() {

    let rotation = this.props.rotationDegree || '0deg';

    console.log(`angerView:${ rotation }`);
    return (
      <View
        style={{
          width: 45, height: 45, position: "relative"
        }}>
        <View style={{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center" }}>
          <Image
            style={{ width: 30, height: 30, position: "absolute" }}
            source={require("../../Resources/Images/angle_view_bg.png")}
          />
        </View>


        <View style={[{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center", transform: [{ rotate: rotation }] }]}>
          <Image
            style={{ width: 30, height: 30, position: "absolute" }}
            source={require("../../Resources/Images/angle_view_camera.png")}
          />
        </View>
      </View>
    );
  }
}