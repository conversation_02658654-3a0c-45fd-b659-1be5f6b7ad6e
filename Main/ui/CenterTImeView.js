import { View, Text } from 'react-native';
import React from "react";
import { DescriptionConstants } from '../Constants';

export default class CenterTimeView extends React.Component {
  
  state = {
    centerTimestamp: 0
  }

  date = new Date();
  
  render() {
    if (this.state.centerTimestamp <= 0) {
      return null;
    }
    
    this.date.setTime(this.state.centerTimestamp);
    let hour = this.date.getHours();
    let minute = this.date.getMinutes();
    let seconds = this.date.getSeconds();
    let centerText = `${ hour > 9 ? hour : `0${ hour }` }:${ minute > 9 ? minute : `0${ minute }` }:${ seconds > 9 ? seconds : `0${ seconds }` }`;
    

    return (
      <View
        style={{ minWidth: 50, height: 20, paddingHorizontal: 5, opacity: 0.6, backgroundColor: "#000000", borderRadius: 5, display: "flex", alignItems: "center", justifyContent: "center" }}
        accessible
        accessibilityLabel={
          DescriptionConstants.rp_63+centerText
        }
      >
        <Text style={{ fontSize: 12, color: "#ffffff" }}>
          {centerText}
        </Text>
      </View>
    );
  }
}