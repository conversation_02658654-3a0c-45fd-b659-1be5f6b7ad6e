import React from "react";
import { Animated, Easing } from "react-native";
const TYPE={
  LIGHT:'white',
  DARK:'black'
};
export default class LoadingView extends React.Component {
  static TYPE = TYPE;
  constructor(props) {
    super(props);
    this.rotationAnim = new Animated.Value(0);
    this.isScaning = false;
    this.mWidth = (props.style && props.style.width) ? props.style.width : 56;
    this.mHeight = (props.style && props.style.height) ? props.style.height : 56;
  }

  componentDidMount() {
    this._startAnim();
  }

  componentWillUnmount() {
    this._stopAnim();
  }

  _startAnim() {
    // this.setState({ isScaning: true });
    this.isScaning = true;
    this.rotationAnim.setValue(0);
    this.animation = Animated.timing(this.rotationAnim, {
      toValue: 1,
      duration: 1000,
      easing: Easing.linear,
      useNativeDriver: true,
      perspective: 1000
    });
    this.animation.start(() => {
      if (!this.isScaning) {
        return;
      }
      this._startAnim();
    });

  }

  _stopAnim() {
    this.rotationAnim.stopAnimation();
    if (this.animation != null) {
      this.animation.stop();
    }
    this.isScaning = false;
  }

  render() {
    return (
      <Animated.Image
        ref={(ref) => {
          this.loadingImage = ref;
        }}
        style={{
          width: this.mWidth,
          height: this.mHeight, 
          transform: [{
            rotate: this.rotationAnim.interpolate({
              inputRange: [0, 1],
              outputRange: ['0deg', '360deg']
            })
          }],
          tintColor: this.props.type == TYPE.DARK ? "black" : null
        }}
        source={require("../../Resources/Images/ptzc-widget-loading-entire.png")}
      >

      </Animated.Image>
    );
  }
}