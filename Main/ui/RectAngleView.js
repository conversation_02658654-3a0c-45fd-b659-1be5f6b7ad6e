import { View, Text, Image, StyleSheet } from "react-native";

import React from 'react';
import PropTypes from 'prop-types';
import { DescriptionConstants } from '../Constants';

const angleFullValue = 101;
const rectWidth = 44;  
const rectHeight = 28;
const blockRatio = 0.25;

export default class RectAngleView extends React.Component {

  static propTypes = {
    angle: PropTypes.number,
    elevation: PropTypes.number,
    scale: PropTypes.number,
    showScale: PropTypes.bool
  };

  static defaultProps = {
    angle: 51,
    elevation: 51,
    scale: 1.0,
    showScale: false
  }

  constructor(props) {
    super(props);

    this.state = { scale: 1.0 };
  }

  componentDidMount() {

  }

  componentWillUnmount() {

  }

  setScale(scale) {
    this.setState({ scale: scale });
  }

  _renderPosistion() {
    let blockWidth = rectWidth * blockRatio; // 宽高总的 各自 1/4
    let blockHeight = rectHeight * blockRatio; 
    let leftRatio = (angleFullValue - this.props.angle) / 100; // 
    let topRatio = (angleFullValue - this.props.elevation) / 100;// 默认屏幕的右下角是起始位置
    let blockLeft = (rectWidth - blockWidth - 2) * leftRatio;
    let blockTop = (rectHeight - blockHeight - 2) * topRatio;
    // 这里是小窗口中的小窗口
    return (
      <View
        style={{ position: "absolute", left: blockLeft, top: blockTop, width: blockWidth, height: blockHeight, backgroundColor: "xm#ffffff", borderRadius: 1 }}
      />
    );
  }

  _renderScale() {
    let scaleText = `x${ this.state.scale.toFixed(1) }`;
    return (
      <Text style={styles.scaleStyle}>
        {scaleText}
      </Text>
    );
  }

  render() {
    return (
      <View
        accessible={true}
        accessibilityLabel={this.props.showScale ? DescriptionConstants.zb_39.replace('1', this.state.scale.toFixed(1)) : DescriptionConstants.zb_37}
        style={styles.angleContainerStyle}>
        {this.props.showScale ? this._renderScale() : this._renderPosistion()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  angleContainerStyle: {
    display: "flex",
    flexDirection: "column",
    width: rectWidth, 
    height: rectHeight, 
    backgroundColor: "xm#00000099", 
    borderColor: "xm#ffffff55", 
    borderWidth: 1.0, 
    borderRadius: 3,  
    alignItems: "center",
    justifyContent: "center"
  },

  scaleStyle: {
    position: "relative",
    fontSize: 14,
    color: "#ffffff",
    fontWeight: "bold"

  }
});