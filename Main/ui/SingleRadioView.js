import { View, Text, Image, StyleSheet, TouchableWithoutFeedback, TouchableOpacity } from 'react-native';
import React from "react";
import { DescriptionConstants } from '../Constants';
import { Radio } from "mhui-rn";
import PropTypes from "prop-types";
import { Styles } from "miot/resources";

export default class SingleRadioView extends React.Component {

  state = {
    centerTimestamp: 0
  };

  static propTypes = {
    title: PropTypes.string,
    desc: PropTypes.string,
    subtitle: PropTypes.string,
    subtitle2: PropTypes.string,
    value: PropTypes.bool,
    onCheckChange: PropTypes.func,
    onSubClick: PropTypes.func,
    onSubClick2: PropTypes.func,
    radioValue: PropTypes.number,
    subValue: PropTypes.string,
    subValue2: PropTypes.string,
    subtitleStyle: PropTypes.object,
    containerStyle: PropTypes.object,
    subContainerStyle: PropTypes.object,
    subtitleContainerStyle: PropTypes.object
  };

  static defaultProps = {
    value: false,
    radioValue: -1
  };

  render() {

    return (
      <TouchableOpacity onPress={ this.changeCheck.bind(this) }>
        <View style={ [styles.itemContainer2, this.props.containerStyle] }>
          <View style={ [styles.itemContainer, this.props.subContainerStyle] }>
            <View style={ styles.checkTitle }>
              <Text style={ { fontWeight: 'bold', fontSize: 16 } }>{ this.props.title }</Text>
              {
                this.props.desc ?
                  <Text style={ {
                    fontWeight: '400',
                    fontSize: 13,
                    color: 'rgba(0, 0, 0, 0.60)',
                    paddingRight: 10
                  } }>{ this.props.desc }</Text> : null
              }
            </View>
            <Radio isChecked={ this.props.value }
                   changeCheck={ this.changeCheck.bind(this) }
                   id={ this.props.radioValue }/>
          </View>
          {
            this.props.subtitle && this.props.value ?
              <TouchableWithoutFeedback onPress={ () => {
                this.props.onSubClick && this.props.onSubClick();
              } }>
                <View style={ [styles.itemSubContainer, this.props.subContainerStyle] }>
                  <View style={ [styles.subTitle, this.props.subtitleContainerStyle] }><Text style={ [{
                    fontWeight: 'bold',
                    fontSize: 16
                  }, this.props.subtitleStyle] }>{ this.props.subtitle }</Text></View>
                  { this.props.subValue ?
                    <View style={ styles.subValue }><Text style={styles.subFontValue}>{ this.props.subValue }</Text></View> : null }
                  <Image source={ require('../../resources2/images/icon_arrow_right_b.png') }
                         style={ styles.rightArrow }/>
                </View>
              </TouchableWithoutFeedback> : null
          }
          {
            this.props.subtitle2 && this.props.value ?
              <TouchableWithoutFeedback onPress={ () => {
                this.props.onSubClick2 && this.props.onSubClick2();
              } }>
                <View style={ [styles.itemSubContainer, this.props.subContainerStyle] }>
                  <View style={ [styles.subTitle, this.props.subtitleContainerStyle] }><Text style={ [{
                    fontWeight: 'bold',
                    fontSize: 16
                  }, this.props.subtitleStyle] }>{ this.props.subtitle2 }</Text></View>
                  { this.props.subValue2 ?
                    <View style={ styles.subValue }><Text style={styles.subFontValue}>{ this.props.subValue2 }</Text></View> : null }
                  <Image source={ require('../../resources2/images/icon_arrow_right_b.png') }
                         style={ styles.rightArrow }/>
                </View>
              </TouchableWithoutFeedback> : null
          }

        </View>
      </TouchableOpacity>
    );
  }

  changeCheck(value) {
    this.props.onCheckChange && this.props.onCheckChange(value);
  }
}
const styles = StyleSheet.create({
  clockContainer: {
    backgroundColor: '#f5f5f5',
    height: "100%"
  },
  itemContainer2: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 15,
    borderRadius: 10,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'column'
  },
  itemContainer: {
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 15,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'row'
  },
  itemSubContainer: {
    paddingBottom: 15,
    paddingHorizontal: 15,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'row'
  },

  checkTitle: {
    flex: 1,
    minHeight: 40,
    justifyContent: 'center'
  },
  subTitle: {
    flex: 1,
    height: 30,
    justifyContent: 'center'
  },
  subValue: {
    maxWidth: 100,
    height: 30,
    justifyContent: 'center',
    marginLeft: 5,
    marginRight: 12,
    fontSize: 13,
    
  },
  subFontValue: {
    fontSize: 13,
    color: 'rgba(0, 0, 0, 0.4)'
  },
  rightArrow: {
    width: 12,
    height: 12
  }
});