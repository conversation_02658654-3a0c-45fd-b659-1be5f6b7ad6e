{"v": "5.7.9", "fr": 60, "ip": 49, "op": 51, "w": 150, "h": 150, "nm": "F/首页/2K", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "2K", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [75.512, 73.956, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.807, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.36], [0, 0.45], [0.45, 0.43], [0.8, 0], [0.52, -0.41], [0.13, -0.73], [0, 0], [0, 0], [-0.23, 0.17], [-0.3, 0], [-0.19, -0.16], [0, -0.28], [0.21, -0.31], [0.43, -0.44], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.63, -0.68], [0.21, -0.38], [0, -0.62], [-0.47, -0.43], [-0.76, 0], [-0.52, 0.39], [0, 0], [0, 0], [0.08, -0.32], [0.23, -0.18], [0.26, 0], [0.19, 0.15], [0, 0.28], [-0.21, 0.3], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.317, 4.052], [-1.317, 2.542], [-4.527, 2.542], [-3.117, 0.992], [-1.847, -0.578], [-1.527, -1.818], [-2.207, -3.398], [-4.107, -4.048], [-6.027, -3.438], [-7.007, -1.758], [-7.007, -1.758], [-5.377, -1.558], [-4.917, -2.288], [-4.127, -2.548], [-3.457, -2.318], [-3.177, -1.678], [-3.487, -0.798], [-4.437, 0.302], [-4.437, 0.302], [-6.907, 2.802], [-6.907, 4.052]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.36], [0, 0.45], [0.45, 0.43], [0.8, 0], [0.52, -0.41], [0.13, -0.73], [0, 0], [0, 0], [-0.23, 0.17], [-0.3, 0], [-0.19, -0.16], [0, -0.28], [0.21, -0.31], [0.43, -0.44], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.63, -0.68], [0.21, -0.38], [0, -0.62], [-0.47, -0.43], [-0.76, 0], [-0.52, 0.39], [0, 0], [0, 0], [0.08, -0.32], [0.23, -0.18], [0.26, 0], [0.19, 0.15], [0, 0.28], [-0.21, 0.3], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.817, 4.052], [-3.817, 2.542], [-7.027, 2.542], [-5.617, 0.992], [-4.347, -0.578], [-4.027, -1.818], [-4.707, -3.398], [-6.607, -4.048], [-8.527, -3.438], [-9.507, -1.758], [-9.507, -1.758], [-7.877, -1.558], [-7.417, -2.288], [-6.627, -2.548], [-5.957, -2.318], [-5.677, -1.678], [-5.987, -0.798], [-6.937, 0.302], [-6.937, 0.302], [-9.407, 2.802], [-9.407, 4.052]], "c": true}]}, {"i": {"x": 0.161, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.36], [0, 0.45], [0.45, 0.43], [0.8, 0], [0.52, -0.41], [0.13, -0.73], [0, 0], [0, 0], [-0.23, 0.17], [-0.3, 0], [-0.19, -0.16], [0, -0.28], [0.21, -0.31], [0.43, -0.44], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.63, -0.68], [0.21, -0.38], [0, -0.62], [-0.47, -0.43], [-0.76, 0], [-0.52, 0.39], [0, 0], [0, 0], [0.08, -0.32], [0.23, -0.18], [0.26, 0], [0.19, 0.15], [0, 0.28], [-0.21, 0.3], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.817, 4.052], [-3.817, 2.542], [-7.027, 2.542], [-5.617, 0.992], [-4.347, -0.578], [-4.027, -1.818], [-4.707, -3.398], [-6.607, -4.048], [-8.527, -3.438], [-9.507, -1.758], [-9.507, -1.758], [-7.877, -1.558], [-7.417, -2.288], [-6.627, -2.548], [-5.957, -2.318], [-5.677, -1.678], [-5.987, -0.798], [-6.937, 0.302], [-6.937, 0.302], [-9.407, 2.802], [-9.407, 4.052]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.36], [0, 0.45], [0.45, 0.43], [0.8, 0], [0.52, -0.41], [0.13, -0.73], [0, 0], [0, 0], [-0.23, 0.17], [-0.3, 0], [-0.19, -0.16], [0, -0.28], [0.21, -0.31], [0.43, -0.44], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.63, -0.68], [0.21, -0.38], [0, -0.62], [-0.47, -0.43], [-0.76, 0], [-0.52, 0.39], [0, 0], [0, 0], [0.08, -0.32], [0.23, -0.18], [0.26, 0], [0.19, 0.15], [0, 0.28], [-0.21, 0.3], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.317, 4.052], [-1.317, 2.542], [-4.527, 2.542], [-3.117, 0.992], [-1.847, -0.578], [-1.527, -1.818], [-2.207, -3.398], [-4.107, -4.048], [-6.027, -3.438], [-7.007, -1.758], [-7.007, -1.758], [-5.377, -1.558], [-4.917, -2.288], [-4.127, -2.548], [-3.457, -2.318], [-3.177, -1.678], [-3.487, -0.798], [-4.437, 0.302], [-4.437, 0.302], [-6.907, 2.802], [-6.907, 4.052]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.777, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.796, 4.053], [1.796, 0.113], [1.836, 0.113], [5.046, 4.053], [7.006, 4.053], [3.536, -0.067], [6.836, -3.927], [4.876, -3.927], [1.836, -0.217], [1.796, -0.217], [1.796, -3.927], [0.176, -3.927], [0.176, 4.053]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.296, 4.053], [4.296, 0.113], [4.336, 0.113], [7.546, 4.053], [9.506, 4.053], [6.036, -0.067], [9.336, -3.927], [7.376, -3.927], [4.336, -0.217], [4.296, -0.217], [4.296, -3.927], [2.676, -3.927], [2.676, 4.053]], "c": true}]}, {"i": {"x": 0.196, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.296, 4.053], [4.296, 0.113], [4.336, 0.113], [7.546, 4.053], [9.506, 4.053], [6.036, -0.067], [9.336, -3.927], [7.376, -3.927], [4.336, -0.217], [4.296, -0.217], [4.296, -3.927], [2.676, -3.927], [2.676, 4.053]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.796, 4.053], [1.796, 0.113], [1.836, 0.113], [5.046, 4.053], [7.006, 4.053], [3.536, -0.067], [6.836, -3.927], [4.876, -3.927], [1.836, -0.217], [1.796, -0.217], [1.796, -3.927], [0.176, -3.927], [0.176, 4.053]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.988235294819, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "2K", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [74.942, 74.839, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.058, -0.161, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.678, "y": 0}, "t": 0, "s": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[33.086, -18.661], [33.086, 18.661], [24.086, 27.661], [-24.086, 27.661], [-33.086, 18.661], [-33.086, -18.661], [-24.086, -27.661], [24.086, -27.661]], "c": true}], "e": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[44.961, -18.661], [44.961, 18.661], [35.961, 27.661], [-35.961, 27.661], [-44.961, 18.661], [-44.961, -18.661], [-35.961, -27.661], [35.961, -27.661]], "c": true}]}, {"i": {"x": 0.077, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[44.961, -18.661], [44.961, 18.661], [35.961, 27.661], [-35.961, 27.661], [-44.961, 18.661], [-44.961, -18.661], [-35.961, -27.661], [35.961, -27.661]], "c": true}], "e": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[33.086, -18.661], [33.086, 18.661], [24.086, 27.661], [-24.086, 27.661], [-33.086, 18.661], [-33.086, -18.661], [-24.086, -27.661], [24.086, -27.661]], "c": true}]}, {"t": 50}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.433, 0.214], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": []}