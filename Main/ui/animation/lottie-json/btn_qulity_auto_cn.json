{"v": "5.7.9", "fr": 60, "ip": 49, "op": 51, "w": 150, "h": 150, "nm": "F/首页/自动", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "“自动”轮廓", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [74.5, 83.586, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [300, 300, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.807, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.09, 0.3], [0, 0], [0.072, -0.324], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.09, -0.3], [0, 0], [-0.072, 0.33], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.354, 1.008], [-1.031, 1.008], [-1.031, -6.381], [-4.298, -6.381], [-4.027, -7.281], [-5.576, -7.362], [-5.792, -6.381], [-7.951, -6.381], [-7.951, 1.008], [-6.628, 1.008], [-6.628, 0.495], [-2.354, 0.495]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.09, 0.3], [0, 0], [0.072, -0.324], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.09, -0.3], [0, 0], [-0.072, 0.33], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.645, 1.008], [-3.322, 1.008], [-3.322, -6.381], [-6.589, -6.381], [-6.319, -7.281], [-7.867, -7.362], [-8.083, -6.381], [-10.243, -6.381], [-10.243, 1.008], [-8.92, 1.008], [-8.92, 0.495], [-4.645, 0.495]], "c": true}]}, {"i": {"x": 0.161, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.09, 0.3], [0, 0], [0.072, -0.324], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.09, -0.3], [0, 0], [-0.072, 0.33], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.645, 1.008], [-3.322, 1.008], [-3.322, -6.381], [-6.589, -6.381], [-6.319, -7.281], [-7.867, -7.362], [-8.083, -6.381], [-10.243, -6.381], [-10.243, 1.008], [-8.92, 1.008], [-8.92, 0.495], [-4.645, 0.495]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.09, 0.3], [0, 0], [0.072, -0.324], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.09, -0.3], [0, 0], [-0.072, 0.33], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.354, 1.008], [-1.031, 1.008], [-1.031, -6.381], [-4.298, -6.381], [-4.027, -7.281], [-5.576, -7.362], [-5.792, -6.381], [-7.951, -6.381], [-7.951, 1.008], [-6.628, 1.008], [-6.628, 0.495], [-2.354, 0.495]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "自", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.807, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.628, -0.621], [-6.628, -1.476], [-2.354, -1.476], [-2.354, -0.621]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.92, -0.621], [-8.92, -1.476], [-4.645, -1.476], [-4.645, -0.621]], "c": true}]}, {"i": {"x": 0.161, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.92, -0.621], [-8.92, -1.476], [-4.645, -1.476], [-4.645, -0.621]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.628, -0.621], [-6.628, -1.476], [-2.354, -1.476], [-2.354, -0.621]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "自", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.807, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.628, -2.511], [-6.628, -3.366], [-2.354, -3.366], [-2.354, -2.511]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.92, -2.511], [-8.92, -3.366], [-4.645, -3.366], [-4.645, -2.511]], "c": true}]}, {"i": {"x": 0.161, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.92, -2.511], [-8.92, -3.366], [-4.645, -3.366], [-4.645, -2.511]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.628, -2.511], [-6.628, -3.366], [-2.354, -3.366], [-2.354, -2.511]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "自", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.807, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.628, -4.401], [-6.628, -5.247], [-2.354, -5.247], [-2.354, -4.401]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.92, -4.401], [-8.92, -5.247], [-4.645, -5.247], [-4.645, -4.401]], "c": true}]}, {"i": {"x": 0.161, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.92, -4.401], [-8.92, -5.247], [-4.645, -5.247], [-4.645, -4.401]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.628, -4.401], [-6.628, -5.247], [-2.354, -5.247], [-2.354, -4.401]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "自", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988234994926, 0.988234994926, 0.988234994926, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "自", "np": 7, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.795, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.081, -5.715], [4.081, -6.849], [0.751, -6.849], [0.751, -5.715]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.008, -5.715], [6.008, -6.849], [2.678, -6.849], [2.678, -5.715]], "c": true}]}, {"i": {"x": 0.186, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.008, -5.715], [6.008, -6.849], [2.678, -6.849], [2.678, -5.715]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.081, -5.715], [4.081, -6.849], [0.751, -6.849], [0.751, -5.715]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "动", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.795, "y": 0}, "t": 11, "s": [{"i": [[-0.012, 0.54], [0, 0], [0.006, -0.54], [0, 0], [0, 0], [0, 0], [0.084, -0.546], [0.228, -0.402], [0.282, 0.678], [0, 0], [-0.174, -0.51], [0.438, -0.024], [-0.318, 0.924], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.288, -0.648], [0.174, -0.168], [0, 0], [-0.348, 0.042], [-0.582, 0.06], [0, 0], [0, 0], [-0.096, 0.012], [0.18, -0.192], [-0.342, -0.234], [-0.198, 0.726], [-0.042, 1.512], [0, 0], [0.048, -0.9], [0.072, -0.084], [0.168, 0], [0.312, 0.006], [-0.066, -0.402], [-0.372, 0.03], [0, 0.804], [-0.024, 2.076], [0, 0]], "o": [[0, 0], [-0.006, 0.54], [0, 0], [0, 0], [0, 0], [-0.018, 1.044], [-0.084, 0.54], [-0.312, -0.852], [0, 0], [0.18, 0.51], [-0.438, 0.03], [0.294, -0.66], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.762], [-0.096, 0.216], [0, 0], [0.3, -0.102], [0.588, -0.054], [0, 0], [0, 0], [0.096, -0.006], [-0.138, 0.18], [0.42, 0.228], [0.606, -0.816], [0.192, -0.726], [0, 0], [-0.006, 1.554], [-0.012, 0.174], [-0.078, 0.078], [-0.306, -0.006], [0.108, 0.444], [0.492, 0], [0.726, -0.048], [0.042, -1.434], [0, 0], [0.012, -0.54]], "v": [[6.331, -7.362], [5.135, -7.362], [5.117, -5.742], [4.415, -5.742], [4.415, -4.59], [5.09, -4.59], [4.937, -2.205], [4.469, -0.792], [3.577, -3.087], [2.596, -2.754], [3.128, -1.224], [1.814, -1.143], [2.732, -3.519], [4.352, -3.519], [4.352, -4.653], [0.355, -4.653], [0.355, -3.519], [1.463, -3.519], [0.715, -1.404], [0.311, -0.828], [0.77, 0.261], [1.742, 0.045], [3.497, -0.126], [3.506, -0.108], [3.758, -0.144], [4.046, -0.171], [3.568, 0.387], [4.712, 1.08], [5.917, -1.233], [6.269, -4.59], [7.15, -4.59], [7.07, -0.909], [6.943, -0.522], [6.575, -0.405], [5.648, -0.423], [5.909, 0.846], [7.204, 0.801], [8.294, -0.477], [8.393, -5.742], [6.296, -5.742]], "c": true}], "e": [{"i": [[-0.012, 0.54], [0, 0], [0.006, -0.54], [0, 0], [0, 0], [0, 0], [0.084, -0.546], [0.228, -0.402], [0.282, 0.678], [0, 0], [-0.174, -0.51], [0.438, -0.024], [-0.318, 0.924], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.288, -0.648], [0.174, -0.168], [0, 0], [-0.348, 0.042], [-0.582, 0.06], [0, 0], [0, 0], [-0.096, 0.012], [0.18, -0.192], [-0.342, -0.234], [-0.198, 0.726], [-0.042, 1.512], [0, 0], [0.048, -0.9], [0.072, -0.084], [0.168, 0], [0.312, 0.006], [-0.066, -0.402], [-0.372, 0.03], [0, 0.804], [-0.024, 2.076], [0, 0]], "o": [[0, 0], [-0.006, 0.54], [0, 0], [0, 0], [0, 0], [-0.018, 1.044], [-0.084, 0.54], [-0.312, -0.852], [0, 0], [0.18, 0.51], [-0.438, 0.03], [0.294, -0.66], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.762], [-0.096, 0.216], [0, 0], [0.3, -0.102], [0.588, -0.054], [0, 0], [0, 0], [0.096, -0.006], [-0.138, 0.18], [0.42, 0.228], [0.606, -0.816], [0.192, -0.726], [0, 0], [-0.006, 1.554], [-0.012, 0.174], [-0.078, 0.078], [-0.306, -0.006], [0.108, 0.444], [0.492, 0], [0.726, -0.048], [0.042, -1.434], [0, 0], [0.012, -0.54]], "v": [[8.258, -7.362], [7.062, -7.362], [7.044, -5.742], [6.342, -5.742], [6.342, -4.59], [7.017, -4.59], [6.864, -2.205], [6.396, -0.792], [5.505, -3.087], [4.523, -2.754], [5.055, -1.224], [3.741, -1.143], [4.659, -3.519], [6.279, -3.519], [6.279, -4.653], [2.283, -4.653], [2.283, -3.519], [3.39, -3.519], [2.643, -1.404], [2.238, -0.828], [2.697, 0.261], [3.669, 0.045], [5.424, -0.126], [5.433, -0.108], [5.685, -0.144], [5.973, -0.171], [5.495, 0.387], [6.639, 1.08], [7.844, -1.233], [8.196, -4.59], [9.077, -4.59], [8.997, -0.909], [8.87, -0.522], [8.502, -0.405], [7.575, -0.423], [7.836, 0.846], [9.132, 0.801], [10.221, -0.477], [10.32, -5.742], [8.223, -5.742]], "c": true}]}, {"i": {"x": 0.186, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[-0.012, 0.54], [0, 0], [0.006, -0.54], [0, 0], [0, 0], [0, 0], [0.084, -0.546], [0.228, -0.402], [0.282, 0.678], [0, 0], [-0.174, -0.51], [0.438, -0.024], [-0.318, 0.924], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.288, -0.648], [0.174, -0.168], [0, 0], [-0.348, 0.042], [-0.582, 0.06], [0, 0], [0, 0], [-0.096, 0.012], [0.18, -0.192], [-0.342, -0.234], [-0.198, 0.726], [-0.042, 1.512], [0, 0], [0.048, -0.9], [0.072, -0.084], [0.168, 0], [0.312, 0.006], [-0.066, -0.402], [-0.372, 0.03], [0, 0.804], [-0.024, 2.076], [0, 0]], "o": [[0, 0], [-0.006, 0.54], [0, 0], [0, 0], [0, 0], [-0.018, 1.044], [-0.084, 0.54], [-0.312, -0.852], [0, 0], [0.18, 0.51], [-0.438, 0.03], [0.294, -0.66], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.762], [-0.096, 0.216], [0, 0], [0.3, -0.102], [0.588, -0.054], [0, 0], [0, 0], [0.096, -0.006], [-0.138, 0.18], [0.42, 0.228], [0.606, -0.816], [0.192, -0.726], [0, 0], [-0.006, 1.554], [-0.012, 0.174], [-0.078, 0.078], [-0.306, -0.006], [0.108, 0.444], [0.492, 0], [0.726, -0.048], [0.042, -1.434], [0, 0], [0.012, -0.54]], "v": [[8.258, -7.362], [7.062, -7.362], [7.044, -5.742], [6.342, -5.742], [6.342, -4.59], [7.017, -4.59], [6.864, -2.205], [6.396, -0.792], [5.505, -3.087], [4.523, -2.754], [5.055, -1.224], [3.741, -1.143], [4.659, -3.519], [6.279, -3.519], [6.279, -4.653], [2.283, -4.653], [2.283, -3.519], [3.39, -3.519], [2.643, -1.404], [2.238, -0.828], [2.697, 0.261], [3.669, 0.045], [5.424, -0.126], [5.433, -0.108], [5.685, -0.144], [5.973, -0.171], [5.495, 0.387], [6.639, 1.08], [7.844, -1.233], [8.196, -4.59], [9.077, -4.59], [8.997, -0.909], [8.87, -0.522], [8.502, -0.405], [7.575, -0.423], [7.836, 0.846], [9.132, 0.801], [10.221, -0.477], [10.32, -5.742], [8.223, -5.742]], "c": true}], "e": [{"i": [[-0.012, 0.54], [0, 0], [0.006, -0.54], [0, 0], [0, 0], [0, 0], [0.084, -0.546], [0.228, -0.402], [0.282, 0.678], [0, 0], [-0.174, -0.51], [0.438, -0.024], [-0.318, 0.924], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.288, -0.648], [0.174, -0.168], [0, 0], [-0.348, 0.042], [-0.582, 0.06], [0, 0], [0, 0], [-0.096, 0.012], [0.18, -0.192], [-0.342, -0.234], [-0.198, 0.726], [-0.042, 1.512], [0, 0], [0.048, -0.9], [0.072, -0.084], [0.168, 0], [0.312, 0.006], [-0.066, -0.402], [-0.372, 0.03], [0, 0.804], [-0.024, 2.076], [0, 0]], "o": [[0, 0], [-0.006, 0.54], [0, 0], [0, 0], [0, 0], [-0.018, 1.044], [-0.084, 0.54], [-0.312, -0.852], [0, 0], [0.18, 0.51], [-0.438, 0.03], [0.294, -0.66], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.762], [-0.096, 0.216], [0, 0], [0.3, -0.102], [0.588, -0.054], [0, 0], [0, 0], [0.096, -0.006], [-0.138, 0.18], [0.42, 0.228], [0.606, -0.816], [0.192, -0.726], [0, 0], [-0.006, 1.554], [-0.012, 0.174], [-0.078, 0.078], [-0.306, -0.006], [0.108, 0.444], [0.492, 0], [0.726, -0.048], [0.042, -1.434], [0, 0], [0.012, -0.54]], "v": [[6.331, -7.362], [5.135, -7.362], [5.117, -5.742], [4.415, -5.742], [4.415, -4.59], [5.09, -4.59], [4.937, -2.205], [4.469, -0.792], [3.577, -3.087], [2.596, -2.754], [3.128, -1.224], [1.814, -1.143], [2.732, -3.519], [4.352, -3.519], [4.352, -4.653], [0.355, -4.653], [0.355, -3.519], [1.463, -3.519], [0.715, -1.404], [0.311, -0.828], [0.77, 0.261], [1.742, 0.045], [3.497, -0.126], [3.506, -0.108], [3.758, -0.144], [4.046, -0.171], [3.568, 0.387], [4.712, 1.08], [5.917, -1.233], [6.269, -4.59], [7.15, -4.59], [7.07, -0.909], [6.943, -0.522], [6.575, -0.405], [5.648, -0.423], [5.909, 0.846], [7.204, 0.801], [8.294, -0.477], [8.393, -5.742], [6.296, -5.742]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "动", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988234994926, 0.988234994926, 0.988234994926, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "动", "np": 5, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [74.942, 74.839, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.058, -0.161, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.678, "y": 0}, "t": 0, "s": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[33.086, -18.661], [33.086, 18.661], [24.086, 27.661], [-24.086, 27.661], [-33.086, 18.661], [-33.086, -18.661], [-24.086, -27.661], [24.086, -27.661]], "c": true}], "e": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[44.961, -18.661], [44.961, 18.661], [35.961, 27.661], [-35.961, 27.661], [-44.961, 18.661], [-44.961, -18.661], [-35.961, -27.661], [35.961, -27.661]], "c": true}]}, {"i": {"x": 0.077, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[44.961, -18.661], [44.961, 18.661], [35.961, 27.661], [-35.961, 27.661], [-44.961, 18.661], [-44.961, -18.661], [-35.961, -27.661], [35.961, -27.661]], "c": true}], "e": [{"i": [[0, -4.971], [0, 0], [4.971, 0], [0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0]], "o": [[0, 0], [0, 4.971], [0, 0], [-4.971, 0], [0, 0], [0, -4.971], [0, 0], [4.971, 0]], "v": [[33.086, -18.661], [33.086, 18.661], [24.086, 27.661], [-24.086, 27.661], [-33.086, 18.661], [-33.086, -18.661], [-24.086, -27.661], [24.086, -27.661]], "c": true}]}, {"t": 50}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.433, 0.214], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": []}