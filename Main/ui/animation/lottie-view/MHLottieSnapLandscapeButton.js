import React from "react";
import { Animated, Easing } from 'react-native';
import MHLottieBaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieSnapLandscapeBtnDisplayState = {
  NORMAL: "NORMAL"
};

export default class MHLottieSnapLandscapeButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_snap_landscape.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieSnapLandscapeBtnDisplayState.NORMAL) {
      this.setState({
        file: MHLottieSnapLandscapeButton.JSONFiles.normal,
        loop: false
      });

      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    }
  }

  constructor(props) {
    super(props);

    this.replayAnimationWhenPress = true;
    this.replayAnimationWhenFocus = false;
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }
}
