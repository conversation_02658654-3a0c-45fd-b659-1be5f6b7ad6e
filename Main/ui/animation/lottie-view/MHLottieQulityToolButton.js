import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>BaseButton from "./base/MHLottieBaseButton";
import MHLottieSleepToolButton from "./MHLottieSleepToolButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieQulityToolBtnDisplayState = {
  AUTO: "AUTO",
  AUTOCN: "AUTOCN",
  AUTOCNTR: "AUTOCNTR",
  R2K: "R2K",
  R25K: "R25K",
  R1080: "R1080",
  R3K: "R3K",
  RFHD: "RFHD",
  RHD: "RHD",
  RSD: "RSD",
  R720: "R720",
  R480: "R480",
  R360: "R360"
};

export default class MHLottieQulityToolButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    auto: {
      light: require("../lottie-json/btn_qulity_auto.json")
    },
    auto_landscape: {
      light: require("../lottie-json/btn_qulity_auto_landscape.json")
    },
    autoCN: {
      light: require("../lottie-json/btn_qulity_auto_cn.json")
    },
    autoCN_landscape: {
      light: require("../lottie-json/btn_qulity_auto_cn_landscape.json")
    },
    autoCNTR: {
      light: require("../lottie-json/btn_qulity_auto_cn_tr.json")
    },
    autoCNTR_landscape: {
      light: require("../lottie-json/btn_qulity_auto_cn_tr_landscape.json")
    },
    r2k: {
      light: require("../lottie-json/btn_qulity_2k.json")
    },
    r2k_landscape: {
      light: require("../lottie-json/btn_qulity_2k_landscape.json")
    },
    r25k: {
      light: require("../lottie-json/btn_quality_25k.json")
    },
    r25k_landscape: {
      light: require("../lottie-json/btn_quality_25k_landscape.json")
    },
    r3k: {
      light: require("../lottie-json/btn_qulity_3k.json")
    },
    r3k_landscape: {
      light: require("../lottie-json/btn_quality_3k_landscape.json")
    },
    rfhd: {
      light: require("../lottie-json/btn_quality_FHD.json")
    },
    rhd: {
      light: require("../lottie-json/btn_quality_HD.json")
    },
    rsd: {
      light: require("../lottie-json/btn_quality_SD.json")
    },
    r1080: {
      light: require("../lottie-json/btn_qulity_1080p.json")
    },
    r1080_landscape: {
      light: require("../lottie-json/btn_qulity_1080p_landscape.json")
    },
    r720: {
      light: require("../lottie-json/btn_qulity_720p.json")
    },
    r720_landscape: {
      light: require("../lottie-json/btn_qulity_720p_landscape.json")
    },
    r480: {
      light: require("../lottie-json/btn_qulity_480p.json")
    },
    r480_landscape: {
      light: require("../lottie-json/btn_qulity_480p_landscape.json")
    },
    r360: {
      light: require("../lottie-json/btn_qulity_360p.json")
    },
    r360_landscape: {
      light: require("../lottie-json/btn_qulity_360p_landscape.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    transition = false;

    if (displayState == MHLottieQulityToolBtnDisplayState.AUTO) {
        
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.auto_landscape : MHLottieQulityToolButton.JSONFiles.auto,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieQulityToolBtnDisplayState.AUTOCN) {
      
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.autoCN_landscape : MHLottieQulityToolButton.JSONFiles.autoCN,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieQulityToolBtnDisplayState.AUTOCNTR) {
      
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.autoCNTR_landscape : MHLottieQulityToolButton.JSONFiles.autoCNTR,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieQulityToolBtnDisplayState.R2K) {

      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.r2k_landscape : MHLottieQulityToolButton.JSONFiles.r2k,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
      
    } else if (displayState == MHLottieQulityToolBtnDisplayState.R25K) {

      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.r25k_landscape : MHLottieQulityToolButton.JSONFiles.r25k,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
      
    } else if (displayState == MHLottieQulityToolBtnDisplayState.R3K) {
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.r3k_landscape : MHLottieQulityToolButton.JSONFiles.r3k,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
      
    } else if (displayState == MHLottieQulityToolBtnDisplayState.R1080) {

      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.r1080_landscape : MHLottieQulityToolButton.JSONFiles.r1080,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieQulityToolBtnDisplayState.R720) {
      
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.r720_landscape : MHLottieQulityToolButton.JSONFiles.r720,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieQulityToolBtnDisplayState.R480) {
      
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.r480_landscape : MHLottieQulityToolButton.JSONFiles.r480,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieQulityToolBtnDisplayState.R360) {
      
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.r360_landscape : MHLottieQulityToolButton.JSONFiles.r360,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieQulityToolBtnDisplayState.RSD) {
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.rsd : MHLottieQulityToolButton.JSONFiles.rsd,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieQulityToolBtnDisplayState.RHD) {
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.rhd : MHLottieQulityToolButton.JSONFiles.rhd,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieQulityToolBtnDisplayState.RFHD) {
      this.setState({
        file: this.props.landscape ? MHLottieQulityToolButton.JSONFiles.rfhd : MHLottieQulityToolButton.JSONFiles.rfhd,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    }
  }

  constructor(props) {
    super(props);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }
}
