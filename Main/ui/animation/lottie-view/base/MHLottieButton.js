import React from 'react';
import LottieView from 'lottie-react-native';
import { TouchableOpacity } from 'react-native';

export default class MHLottieButton extends React.Component {

    static defaultProps = {

      style: {}, 
      darkMode: false,

      loop: false,
      progress: 0,
      speed: 1,

      onPress: () => {},
      onFocus: () => {}
    }

    constructor(props) {
      super(props);
    }

    componentWillMount() {

    }

    componentDidMount() {

    }

    render() {

      return (
        <TouchableOpacity
          accessibilityValue={this.props.accessibilityValue}
          accessibilityState={this.props.accessibilityState}
          style={this.props.style}
          accessibilityLabel={this.props.accessibilityLabel}
          label={this.props.label}
          description={this.props.description}

          disabled={this.props.disabled}
            
          onPress={this.props.onPress}
          onFocus={this.props.onFocus}
        >
          <LottieView
            ref={(lottieView) => { this.lottieView = lottieView; }}

            source={ this.props.file != null ? (this.props.darkMode ? (this.props.file.dark ?? this.props.file.light) : this.props.file.light) : '' }
            loop={true}
            speed={this.props.speed}

            progress={this.props.progress}
          />
        </TouchableOpacity>
      );
    }

    setProgress(progress) {
      this.lottieView && this.lottieView.setProgress(progress);
    }

    playAnimation() {
      this.lottieView && this.lottieView.play();
    }

    pauseAnimation() {
      this.lottieView && this.lottieView.pause();
    }

    resumeAnimation() {
      this.lottieView && this.lottieView.resume();
    }

    resetAnimation() {
      this.lottieView && this.lottieView.reset();
    }
}