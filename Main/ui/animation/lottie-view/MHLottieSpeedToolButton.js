import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>BaseButton from "./base/MHLottieBaseButton";
import MHLottieSleepToolButton from "./MHLottieSleepToolButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieSpeedToolBtnDisplayState = {
  X1: "X1",
  X2: "X2",
  X3: "X3",
  X4: "X4",
  X8: "X8",
  X16: "X16"
};

export default class MHLottieSpeedToolButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    x1: {
      light: require("../lottie-json/btn_speed_x1.json")
    },
    x1_landscape: {
      light: require("../lottie-json/btn_speed_x1_landscape.json")
    },
    x2: {
      light: require("../lottie-json/btn_speed_x2.json")
    },
    x2_landscape: {
      light: require("../lottie-json/btn_speed_x2_landscape.json")
    },
    x3: {
      light: require("../lottie-json/btn_speed_x3.json")
    },
    x3_landscape: {
      light: require("../lottie-json/btn_speed_x3_landscape.json")
    },
    x4: {
      light: require("../lottie-json/btn_speed_x4.json")
    },
    x4_landscape: {
      light: require("../lottie-json/btn_speed_x4_landscape.json")
    },
    x8: {
      light: require("../lottie-json/btn_speed_x8.json")
    },
    x8_landscape: {
      light: require("../lottie-json/btn_speed_x8_landscape.json")
    },
    x16: {
      light: require("../lottie-json/btn_speed_x16.json")
    },
    x16_landscape: {
      light: require("../lottie-json/btn_speed_x16_landscape.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    transition = false;

    if (displayState == MHLottieSpeedToolBtnDisplayState.X1) {
        
      this.setState({
        file: this.props.landscape ? MHLottieSpeedToolButton.JSONFiles.x1_landscape : MHLottieSpeedToolButton.JSONFiles.x1,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieSpeedToolBtnDisplayState.X2) {
        
      this.setState({
        file: this.props.landscape ? MHLottieSpeedToolButton.JSONFiles.x2_landscape : MHLottieSpeedToolButton.JSONFiles.x2,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieSpeedToolBtnDisplayState.X3) {
        
      this.setState({
        file: this.props.landscape ? MHLottieSpeedToolButton.JSONFiles.x3_landscape : MHLottieSpeedToolButton.JSONFiles.x3,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieSpeedToolBtnDisplayState.X4) {
        
      this.setState({
        file: this.props.landscape ? MHLottieSpeedToolButton.JSONFiles.x4_landscape : MHLottieSpeedToolButton.JSONFiles.x4,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    }  else if (displayState == MHLottieSpeedToolBtnDisplayState.X8) {
        
      this.setState({
        file: this.props.landscape ? MHLottieSpeedToolButton.JSONFiles.x8_landscape : MHLottieSpeedToolButton.JSONFiles.x8,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieSpeedToolBtnDisplayState.X16) {
        
      this.setState({
        file: this.props.landscape ? MHLottieSpeedToolButton.JSONFiles.x16_landscape : MHLottieSpeedToolButton.JSONFiles.x16,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    }
  }

  constructor(props) {
    super(props);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }
}
