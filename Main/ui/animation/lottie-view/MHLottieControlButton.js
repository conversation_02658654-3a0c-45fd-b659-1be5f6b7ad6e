import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>BaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieControlBtnDisplayState = {
  NORMAL: "NORMAL",
  CONTROLLING: "CONTROLLING"
};

export default class MHLottieControlButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_control.json"),
      dark: require("../lottie-json/btn_control_dark.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieControlBtnDisplayState.NORMAL) {
      this.setState({
        file: MHLottieControlButton.JSONFiles.normal,
        loop: false
      });
    
      Animated.timing(
        this.progress,
        {
          from: 1,
          toValue: 0,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieControlBtnDisplayState.CONTROLLING) {
      this.setState({
        file: MHLottieControlButton.JSONFiles.normal,
        loop: false
      });

      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    }
  }

  constructor(props) {
    super(props);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }

}
