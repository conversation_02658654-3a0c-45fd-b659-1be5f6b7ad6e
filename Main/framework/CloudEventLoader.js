import Util from "../util2/Util";
import API from "../util2/API";
import { Device, Service } from "miot";
import { DeviceEventEmitter, Platform } from "react-native";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import StorageKeys from '../StorageKeys';
import UriPlayer from "./UriPlayer";
import EventLoaderInf, { DldStatus, Order } from "./EventLoaderInf";
import CloudVideoUtil from "../sdcard/util/CloudVideoUtil";
import util from "../widget/calendar/util";
import VipUtil from '../util/VipUtil';
import CameraConfig from "../util/CameraConfig";


const TAG = "CloudEventLoader";
const DldCbName = 'M3U8ToMP4CallBack';
const cancelDldCbName = 'cancelM3U8ToMP4CallBack';

export const CldDldTypes = {
  Files: 'filelist',
  Events: 'eventlist'
};

class _CloudEventLoader extends EventLoaderInf {
  constructor() {
    super();
    console.log(TAG, "status", DldStatus, "DldStatus.Complete");
    this.mPlayCfg = { loader: this, player: UriPlayer };
    this.mDldL = null;
    // state : 1. onStart (开始下载)  2. onComplete（下载完成）  3. onError（失败）  4. onProgress（下载进度）
    this.mSaveL = DeviceEventEmitter.addListener(DldCbName, (aDat) => {
      console.log(TAG, "mSaveL", aDat, DldStatus, "DldStatus.Complete");
      if (this.mDldL) {
        switch (aDat.state) {
          case 'onComplete':
            this.mDldL.onDldProgress(DldStatus.Complete, aDat?.fileId);
            this.mDldL = null;
            break;
          case 'onError':
            this.mDldL.onDldProgress(DldStatus.Err, aDat?.fileId);
            this.mDldL = null;
            break;
          default:
            this.mDldL.onDldProgress(aDat.progress);
            break;
        }
      }
    });

    this.mCancel = DeviceEventEmitter.addListener(cancelDldCbName, (aDat) => {
      console.log(TAG, "mCancel", aDat);
      if (this.mDldL) {
        switch (aDat.state) {
          case 'onComplete':
            this.mDldL.onDldCancelResult(aDat.fileId, DldStatus.Complete);
            break;
          case 'onError':
            this.mDldL.onDldCancelResult(aDat.fileId, DldStatus.Err);
            break;
        }
        this.mDldL = null;
      }
    });
  }

  
  async getAllEvent(aDate, aEvent, aIsMore, aOrder = Order.Desc, mtype) {
    let isVip = false;
    try {
      let vd = await StorageKeys.VIP_DETAIL;
      isVip = vd.vip;
    } catch (aErr) {
      console.log(TAG, "load vip failed");
      // treat as normal
    }

    return await Util.getAllEvent(aDate, isVip ? 30 : 7, aEvent, aIsMore, 20, this.mPlayCfg, mtype, aOrder);
  }

  async getOneDayAllEvent(aDate, aEvent, aIsMore, aOrder = Order.Desc, mtype) {
    return await Util.getAllEvent(aDate, 0, aEvent, aIsMore, 20, this.mPlayCfg, mtype, aOrder);
  }

  getEventList(aDate, aEvent, aIsMore, mtype) {
    return Util.getEventList(aDate, aEvent, aIsMore, 20, this.mPlayCfg, mtype);
  }

  getEventByFileId(did, model, isAlarm, fileId, evType) {
    return Util.getEventsByFileId(did, model, fileId, isAlarm, this.mPlayCfg, evType);
  }

  eventRead(aEv) {
    Util.eventRead(aEv);
  }


  getThumb(aRec) {
    // console.log(aRec,'aRec')
    let imgStorId = aRec.imgStoreId;
    return Service.miotcamera.getFileIdImage(imgStorId, Device.deviceID);
  }

  showCloudContentforExpiredUser(vipDat) {
    if (vipDat.pacakgeType) {
      let days = 0;
      if (vipDat.pacakgeType.indexOf('7') != -1) {
        days = 7;
      } else if (vipDat.pacakgeType.indexOf('30') != -1) {
        days = 30;
      }
      if (days == 0) {
        return false;
      }
      return !Util.isDaysAgo(vipDat.endTime, days);
    } 
    return false;
  }

  async getSummary() {
    try {
      let result = await VipUtil.getVipStatus();
      let vipDat = result?.data;
      if (!vipDat) {
        vipDat = await StorageKeys.VIP_DETAIL;
      }
      return await this.parsePacketInfo(vipDat);
    } catch (err) {
      if (err == -3) {
        let vipDat = await StorageKeys.VIP_DETAIL;
        return await this.parsePacketInfo(vipDat);
      }
      let isEuropeServer = CameraConfig.getIsEuropeServer();
      return { type: "cloud", info: { type: isEuropeServer ? LocalizedStrings["eu_s_cloud_setting"] : LocalizedStrings["s_cloud_setting"], detail: LocalizedStrings["c_get_fail"], vip: false } };
    }

  }

  async parsePacketInfo(vipDat) {
    let isCloudServer = CameraConfig.getIsCloudServer();
    try {
      let cloudCap = await Util.fetchCloudCapacity();
      console.log(TAG, "VIP_DETAIL", vipDat, "cloudCap", cloudCap);
      let dateFmt;
      let str = this.buildPackgeType(vipDat, isCloudServer);
      if (!vipDat.vip) {
        let showCloudForExpired = this.showCloudContentforExpiredUser(vipDat);
        switch (vipDat.status) {
          case 1:
            return { type: "cloud",
              info: { 
                type: str,
                total: 1,
                detail: isCloudServer ? LocalizedStrings['eu_c_cloudvip_end_tip'] : LocalizedStrings['c_cloudvip_end_tip'],
                vip: vipDat.vip,
                showCloudForExpired: showCloudForExpired,
                rollingInterval: vipDat.rollingSaveInterval } };
          case 2:
            dateFmt = LocalizedStrings["yyyymmdd"];
            return { type: "cloud",
              info: {
                type: str,
                total: 1,
                detail: Util.fmtStr(vipDat.renewStatus ? LocalizedStrings["storage_vvip_state"] : LocalizedStrings["storage_vip_state"], Util.getMoment(vipDat.endTime / 1000).format(dateFmt)),
                vip: vipDat.vip,
                showCloudForExpired: showCloudForExpired,
                rollingInterval: vipDat.rollingSaveInterval } };
        }
        return { type: "cloud", info: { type: str, total: 1,
          detail: LocalizedStrings["c_get_fail"], vip: vipDat.vip } };
      } else {
        return { type: "cloud",
          info: {
            type: str,
            total: 1,
            rollingInterval: vipDat.rollingSaveInterval,
            detail: Util.fmtStr(LocalizedStrings["storage_usage"], Util.fmtSize(cloudCap.capacity * 1024 * 1024)),
            vip: vipDat.vip } };
      }
    } catch (err) {
      return { type: "cloud", info: { type: isCloudServer ? LocalizedStrings["eu_s_cloud_setting"] : LocalizedStrings["s_cloud_setting"], detail: LocalizedStrings["c_get_fail"], vip: false } };
    }
  }

  buildPackgeType(vipDat, isEuropeServer) {
    let str = null;
    console.log("" + vipDat.pacakgeType);
    if (vipDat.pacakgeType) {
      if (vipDat.pacakgeType.indexOf('7') != -1) {
        str = Util.fmtStr(isEuropeServer ? LocalizedStrings['eu_cloud_service_package_type'] : LocalizedStrings['cloud_service_package_type'], '7');
      } else if (vipDat.pacakgeType.indexOf('30') != -1) {
        str = Util.fmtStr(isEuropeServer ? LocalizedStrings['eu_cloud_service_package_type'] : LocalizedStrings['cloud_service_package_type'], '30');
      }
    }
    return str ? str : vipDat.pacakgeType;
  }

  download(aRec, aPath, aListener) {
    if (null == this.mDldL) {
      this.mDldL = aListener;
      Service.miotcamera.downloadM3U8ToMP4V2(aRec.fileId, aPath, DldCbName, aRec.isAlarm, "H265");
    } else {
      console.log(TAG, "download on going");
      aListener.onDldProgress(DldStatus.Err,true);
    }
  }
  downloadByUrl(fileId, url, isAlarm, aPath, aListener) {
    if (null == this.mDldL) {
      this.mDldL = aListener;
      Service.miotcamera.downloadM3U8ToMP4ByUrl(fileId, url, aPath, DldCbName, isAlarm, "H265");
    } else {
      console.log(TAG, "downloadByUrl on going");
      aListener.onDldProgress(DldStatus.Err);
    }
  }

  cancelDownload(aRec, aListener) {
    if (null == this.mDldL) {
      this.mDldL = aListener;
    }
    Service.miotcamera.cancelDownloadM3U8ToMP4V2(aRec.fileId, cancelDldCbName, false);

  }


  delete(aRecs = []) {
    // return AlarmDetailAPI.instance().deleteVideo([item.fileId])
    let fileIds = aRecs.map((aRec) => {
      return aRec.fileId; 
    });
    let params = {
      fileIds: {
        fileIds: fileIds
      }
    };
    CloudVideoUtil.deleteVideos(fileIds);
    return API.instance().post('/common/app/v2/delete/files', 'business.smartcamera', params);
  }

  
}
const CloudEvLdr = new _CloudEventLoader();
export default CloudEvLdr;
