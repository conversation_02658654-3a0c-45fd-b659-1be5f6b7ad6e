import Singletons from "./Singletons";
import NetInfo from '@react-native-community/netinfo';
import AlbumHelper from '../util/AlbumHelper';
import StorageKeys from '../StorageKeys';
import { Alert, Platform } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Host } from "miot";
import { DldStatus } from "./EventLoaderInf";
import SdcardEventLoader from "./sdcard/SdcardEventLoader";
import { getStack } from '../index';
import Util from "../util2/Util";
import SdFileManager from "../sdcard/util/SdFileManager";
import LogUtil from "../util/LogUtil";
const TAG = "DldMgr";
export const Dld_States = {
  RETRY: 'retry',
  PAUSE: 'pause',
  WAITING: 'waiting',
  DOWNLOADING: 'downloading',
  UNEXIST: 'unexist'
};

/*
dldStatus = {curItem, albumPath, status}
*/
class _DldMgr {
  constructor() {
    this.mLdrs = [Singletons.CloudEventLoader, Singletons.DailyStoryLoader];
    this.mDld = { total: 0, list: [], allowData: false, albumPath: null, curItem: null };
    this.mListeners = [];
    this.mNetTimer = null;
    this.mUsingWifi = true;
    this.mp2pLost = false;
    StorageKeys.PENDING_DLD.then((aRet) => {
      if (aRet != null && aRet.length > 0) {
        let aNRet = JSON.parse(aRet);
        this.mDld.list = this.mDld.list.concat(aNRet);
        this.mDld.total = this.mDld.list.length;
        setTimeout(() => {
          console.log('landing202');
          this.checkNetAndDld();
        }, 6000);
      }
    })
      .catch((aErr) => {
        console.log(TAG, "load err", aErr);
      });

    this.unSubNetInfo = NetInfo.addEventListener((state) => {
      console.log("Connection type", state.type);
      console.log("Is connected?", state.isConnected);
      if (!state.isConnected) { // disconnect
        if (this.mDld.list.length > 0) {
          this.mDld.list.forEach((aItm) => {
            if (aItm.state == Dld_States.DOWNLOADING) {
              aItm.state = Dld_States.RETRY;
              console.log('disconnect net, set item state to retry');
              setTimeout(() => {
                this.pauseCurrent = true;
                if (aItm.playCfg.loader) {
                  if (typeof (aItm.playCfg.loader) == "string") {
                    for (let aL in this.mLdrs) {
                      if (this.mLdrs[aL].constructor.name == aItm.playCfg.loader) {
                        this.mLdrs[aL].cancelDownload(aItm, this);
                      }
                    }
                  } else if (typeof (aItm.playCfg.loader.cancelDownload) == 'function') {
                    aItm.playCfg.loader.cancelDownload(aItm, this);
                  }
                }
              }, 100);
            }
          });
          this.notifyStatus("network_disconnect");
        }
      } else {
        if (this.mNetTimer) {
          clearTimeout(this.mNetTimer);
        }
        this.mNetTimer = setTimeout(() => {
          console.log('landing201');
          this.checkNetAndDld();
        }, 5000);
      }
    });
  }

  dldPageIsActive(isActive) {
    if (isActive && !this.mUsingWifi) {
      this.checkNetAndDld();
    }
  }

  addLdrs(aLdrs) {
    this.mLdrs = this.mLdrs.filter((aL) => {
      return aL.constructor.name != aLdrs.constructor.name;
    });
    this.mLdrs = this.mLdrs.concat(aLdrs);
  }

  removeLdrs(aLdr) {
    this.mLdrs = this.mLdrs.filter((aL) => {
      return aL.constructor.name != aLdr.constructor.name;
    });
    // 纯粹就是移除
  }

  addListener(aListener) {
    this.mListeners.push(aListener);
    return {
      remove: () => {
        this.mListeners = this.mListeners.filter((aL) => {
          return aL !== aListener;
        });
      }
    };
  }

  notifyProgress(aDetail) {
    this.mListeners.forEach((aL) => {
      aL({ type: "progress", detail: aDetail, data: this.mDld });
    });
  }

  notifyStatus(aDetail) {
    console.log(TAG, "notifyStatus", aDetail);
    this.mListeners.forEach((aL) => {
      aL({ type: "status", detail: aDetail, data: this.mDld });
    });
  }

  onDldProgress(aProgress, fileId = null) {
    console.log(TAG, "onDldProgress", aProgress, "for", this.mDld.albumPath);
    if (this.mDld.albumPath) {
      switch (aProgress) {
        case DldStatus.Complete: {
          let albumP = this.mDld.albumPath;
          let mlist = this.mDld.list.filter((aItm) => { return this.mDld.curItem == null || (aItm.fileId != this.mDld.curItem.fileId); });
          this.mDld.list = mlist;
          StorageKeys.PENDING_DLD = this.pendingLstToString(this.mDld.list);
          this.mDld.albumPath = null;
          this.mDld.curItem = null;
          AlbumHelper.saveToAlbum(albumP, true)
            .then(() => {
              this.notifyStatus('save_success');
              this.checkNetAndDld();
            })
            .catch((err) => {
              this.notifyStatus('save_faild');
              this.checkNetAndDld();
              console.log(TAG, "savealbum err", err);
            });
          break;
        }
        case DldStatus.Err:
          if (Platform.OS == 'ios' && this.rmCurrent) {
            console.log('skip download err for rmCurrent');
            this.rmCurrent = false;
            this.mDld.albumPath = null;
            this.mDld.curItem = null;
            break;
          }
          if (Platform.OS == 'ios' && this.pauseCurrent) {
            console.log('skip download err for pauseCurrent');
            this.pauseCurrent = false;
            this.mDld.albumPath = null;
            this.mDld.curItem = null;
            break;
          }
          this.mErrProcess(fileId);
          break;
        case DldStatus.p2pLost:
          this.mp2pLost = true; // skip all sdcard download now
          this.mErrProcess(fileId);
          break;
        case DldStatus.notExist:
          this.mErrProcess(Dld_States.UNEXIST, fileId);
          LogUtil.logOnAll(`onDldProgress, notExist ${ fileId }`);
          break;
        default:
          this.notifyProgress(aProgress);
          break;
      }
    }
  }

  mErrProcess = (toStatus = Dld_States.RETRY, fileId = null) => {
    console.log('landing1033, mErrProcess', fileId);
    let ret = false;
    if (fileId) {
      ret = this.mSetStateRetry(fileId,toStatus);
    } else if (this.mDld.curItem) {
      ret = this.mSetStateRetry(this.mDld.curItem.fileId,toStatus);
    }
    if (ret) {
      StorageKeys.PENDING_DLD = this.pendingLstToString(this.mDld.list);
    }

    console.log('landing103, dld err, filter current item, then list length: ', this.mDld.list.length);
    this.mDld.albumPath = null;
    this.mDld.curItem = null;
    this.notifyStatus('camera_play_error_file');
    this.checkNetAndDld();
  }

  mSetStateRetry(fileId, toStatus = Dld_States.RETRY) {
    let ret = false;
    for (let key in this.mDld.list) {
      if (fileId == this.mDld.list[key].fileId) {
        // this.mDld.list[key].state = Dld_States.RETRY;
        if (toStatus == Dld_States.UNEXIST) {
          this.mDld.list[key].state = Dld_States.UNEXIST;
        } else {
          this.mDld.list[key].state = Dld_States.RETRY;
        }
        console.log('landing1033, mSetStateRetry done', fileId);
        ret = true;
        break;
      }
    }
    return ret;
  }

  onDldCancelResult(fileid, state) {
    console.log('cancel current download Done', fileid);
    switch (state) {
      case DldStatus.Complete: 
      case DldStatus.Err:
      default:
        if (this.pauseCurrent) {
          console.log('skip cancel reslut for pauseCurrent');
          this.pauseCurrent = false;
          this.mDld.albumPath = null;
          this.mDld.curItem = null;
          this.checkNetAndDld();
          break;
        }
        // cancel done
        this.mDld.list = this.mDld.list.filter((aItm) => { 
          return this.mDld.curItem == null || aItm.fileId != this.mDld.curItem.fileId;
        });
        console.log('landing99 cancelresult', this.mDld.list.length);
        this.mDld.total = this.mDld.list.length;
        this.mDld.curItem = null;
        StorageKeys.PENDING_DLD = this.pendingLstToString(this.mDld.list);
        this.mDld.albumPath = null;
        this.notifyStatus('cancel_current_success');
        this.checkNetAndDld();
        break;
    }
  }

  // aLdr eventloader
  async addDld(aEvArr, aLdr) {
    let ldr = this.mLdrs.find((aVal) => {
      return aLdr === aVal;
    });
    if (ldr) {
      console.log("test: begin download");
      // avoid add same download
      aEvArr = aEvArr.filter((aEv) => {
        return this.mDld.list.find((aEvf) => { return aEvf.fileId == aEv.fileId; }) == null;
      });
      aEvArr.forEach((aE) => { aE.state = Dld_States.WAITING; });// waiting, pause, downloading, complete,retry
      let pendLst = this.mDld.list.concat(aEvArr);
      console.log("pending list", pendLst);
      await (StorageKeys.PENDING_DLD = this.pendingLstToString(pendLst));
      this.mDld.list = pendLst;
      this.mDld.total += aEvArr.length;
      this.checkNetAndDld();
      return true;
    } else {
      throw "unknown ldr";
    }
  }

  pendingLstToString(aLst) {
    let mStr = JSON.stringify(aLst, (aKey, aVal) => {
      let ret = aVal;
      if ("playCfg" == aKey) {
        if (typeof (aVal.loader) == "string") {
          ret = { "loader": aVal.loader };
        } else {
          ret = { "loader": aVal.loader.constructor.name };
        }
      }
      // console.log(TAG, "process", aKey, "=>", ret);
      return ret;
    });
    return mStr;
  }

  async resumeDld() {
    let basePath = Host.file.storageBasePath;
    let item = await this.checkDldItem();

    if (!item) {
      console.log('resumeDld not find item');
      return;
    }
    console.log('resumeDld', item.fileId);
    // update the saving
    console.log('dld list: ', this.mDld.list.length);
    item.state = "downloading";
    StorageKeys.PENDING_DLD = this.pendingLstToString(this.mDld.list);
    let videoFileName = AlbumHelper.getDownloadTargetPathName(true);
    if (item.mediaType == "sdcard" || item.mediaType == "dailystory") {
      videoFileName = item.videoUrl;
    }
    this.mDld.albumPath = videoFileName;
    this.mDld.curItem = item;
    let videoPath = `${ basePath }/${ videoFileName }`;
    this.notifyStatus(`${ LocalizedStrings["c_download"] }${ this.mDld.total - (this.mDld.list.length - 1) }/${ this.mDld.total }`);
    console.log(TAG, "start dld", videoPath, item.fileId);
    if (typeof (item.playCfg.loader) == "string") {
      for (let aL in this.mLdrs) {
        if (this.mLdrs[aL].constructor.name == item.playCfg.loader) {
          this.mLdrs[aL].download(item, videoPath, this);
        }
      }
    } else {
      item.playCfg.loader.download(item, videoPath, this);
    }

  }

  async checkDldItem() {
    if (this.mDld.curItem != null) return null;
    let downloadings = this.mDld.list.filter((aItm) => { return aItm.state == Dld_States.DOWNLOADING; });
    if (downloadings.length == 1 && this.mDld.curItem == null) {
      let result = await this.checkExist(downloadings[0]);
      if (result) {
        return downloadings[0];
      }
    }
    let item = null;
    for (let key in this.mDld.list) {
      if (this.mDld.list[key].state == Dld_States.WAITING && (this.mDld.list[key].mediaType != 'sdcard' || !this.mp2pLost)) {
        let result = await this.checkExist(this.mDld.list[key]);
        if (result) {
          this.mDld.list[key].state = Dld_States.DOWNLOADING;
          this.notifyStatus('start_download');
          item = this.mDld.list[key];
          break;
        }
      }
    }
    return item;
  }

  async checkExist(aItm) {
    if (aItm.mediaType == 'sdcard' || aItm.mediaType == "dailystory") {
      return new Promise.resolve(true);;
    } else {
      return new Promise((resolve, reject) => {
        Util.checkExist(aItm).then((result) => {
          LogUtil.logOnAll(`checkExist enter 10, result: ${ JSON.stringify(result) }`);
          if (!result?.data?.deleteStatus) {
            return resolve(true);
          } else {
            aItm.state = Dld_States.UNEXIST;
            this.notifyStatus("video_deleted");
            return resolve(false);
          }
        }).catch((err) => {
          aItm.state = Dld_States.UNEXIST;
          this.notifyStatus("video_deleted");
          return resolve(false);
        })

        // let result = await Util.checkExist(aItm);
        // if (!result.data.deleteStatus) {
        //   return true;
        // } else {
        //   aItm.state = Dld_States.UNEXIST;
        //   this.notifyStatus("video_deleted");
        //   return false;
        // }
      });


    }
  }

  checkNetAndDld() {
    if (this.mDld.list.length > 0) {
      if (null == this.mDld.albumPath) { // only run one download
        NetInfo.fetch().done((status) => { // CELLULAR / NONE / WIFI
          let isWifi = status.toString().toLowerCase() === "wifi" || (status.type && status.type.toLowerCase() === "wifi");
          let isCellular = status.toString().toLowerCase() === "cellular" || (status.type && status.type.toLowerCase() === "cellular");
          let isConnected = status.isConnected;
          if (Platform.OS == 'android' && (isWifi || isCellular)) {
            isConnected = true;
          }
          console.log('landing203', isWifi, !isWifi, isCellular, !isCellular, isConnected, this.mDld.allowData);
          this.mUsingWifi = true;
          if (isConnected && isCellular && !isWifi) {
            this.mUsingWifi = false;
            let root = getStack();
            let name = null;
            if (root && root.state && root.state.nav && root.state.nav.index && root.state.nav.routes) {
              name = root.state.nav.routes[root.state.nav.index].routeName;
            }
            if (name == null || name !== "DldPage") {
              return;
            }
          }
          if (isConnected && !this.mDld.allowData && isCellular) {
            console.log('landing204');
            Alert.alert(
              LocalizedStrings['owldiff_download_warning'],
              null, 
              [
                {
                  text: LocalizedStrings['csps_right'],
                  onPress: () => {
                    this.mDld.allowData = true;
                    setTimeout(() => {
                      this.resumeDld();
                    }, 1500);
                  }
                },
                {
                  text: LocalizedStrings['action_cancle']
                }
              ]
            );
          } else if (isConnected && isWifi || this.mDld.allowData) {
            console.log('landing205');
            setTimeout(() => {
              this.resumeDld();
            }, 1500);
          } else {
            console.log('landing206, network disconnected');
          }
        });
      } else {
        console.log(TAG, "there's download running waiting");
      }
    } else {
      // all done
      this.mDld = { total: 0, list: [], allowData: false, albumPath: null, curItem: null };
    }

  }

  remove(aFilter) {
    let removed = 0;
    this.rmCurrent = false;
    let mlist = this.mDld.list.filter((aEv) => {
      let ret = true;
      if (!aFilter(aEv)) {
        if (aEv == this.mDld.curItem) {
          setTimeout(() => {
            this.rmCurrent = true;
            aEv.playCfg.loader && aEv.playCfg.loader.cancelDownload(aEv, this);
          }, 100);
          ret = true;
        } else {
          ++removed;
          ret = false;
        }
      }
      return ret;
    });
    this.mDld.list = mlist;
    console.log('landing991 cancelresult', mlist.length);
    this.mDld.total -= removed;
    StorageKeys.PENDING_DLD = this.pendingLstToString(this.mDld.list);
  }

  pauseItem(aItm, cancelBeforeExitPlugin = false) {
    if (!aItm || aItm.state == Dld_States.UNEXIST) {
      return;
    }
    this.mDld.list.forEach((itm) => {
      if (itm.fileId == aItm.fileId) {
        if (this.mDld.curItem && aItm.fileId == this.mDld.curItem.fileId) {
          if (itm.state == Dld_States.DOWNLOADING) {
            if (cancelBeforeExitPlugin) {
              this.pauseCurrent = true;
              itm.playCfg.loader.cancelDownload(itm, this);
            } else {
              setTimeout(() => {
                this.pauseCurrent = true;
                itm.playCfg.loader.cancelDownload(itm, this);
              }, 100);
            }
          }
        } 
        if (cancelBeforeExitPlugin) {
          itm.state = Dld_States.WAITING;
        } else {
          if (itm.state == Dld_States.RETRY && this.mp2pLost) {
            this.mp2pLost = false;
          }
          itm.state = itm.state == Dld_States.PAUSE || itm.state == Dld_States.RETRY ? Dld_States.WAITING : Dld_States.PAUSE;
        }

      }
    });
    this.notifyStatus('pause_item_done');
    StorageKeys.PENDING_DLD = this.pendingLstToString(this.mDld.list);
    let find = false;
    this.mDld.list.forEach((itm) => {
      if (itm.state == Dld_States.DOWNLOADING) {
        find = true;
      }
    });
    if (!find) {
      this.checkNetAndDld();
    }
  }

  getDldList() {
    for (let aI in this.mDld.list) {
      if (typeof (this.mDld.list[aI].playCfg.loader) == 'string') {
        for (let aL in this.mLdrs) {
          if (this.mLdrs[aL].constructor.name == this.mDld.list[aI].playCfg.loader) {
            this.mDld.list[aI].playCfg.loader = this.mLdrs[aL];
          }
        }
      }
    }
    console.log(TAG, 'landing 18, getDldList', this.mDld.list.length);
    return this.mDld.list;
  }

  clear() {
    if (Platform.OS == 'android') {
      this.pauseItem(this.mDld.curItem, true);
      console.log('clear called');
    }
  }
}

const DldMgr = new _DldMgr();
export default DldMgr;
