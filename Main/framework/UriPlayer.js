import React from 'react';
import Video from 'react-native-video';
import Util from "../util2/Util";
import { Platform, View } from 'react-native';
import { Service } from "miot";
import { Info } from "./Player";

const TAG = "UriPlayer";

class _UriPlayer {
  constructor() {
    this.mCamL = [];
    this.mSrc = null;
    this.mCurTime = 0;
    this.mPlaying = false;
    this.mute = true;
    this.mPlayItm = null;
    this.mResume = false;
    this.mRate = 1;
    this.mSkipStateNotify = false;
  }


  getVideoView(aFullScreenState = false) {
    // console.log(TAG, "getVideoView resume", this.mResume, "playing", this.mPlaying);
    return this.mSrc ?
      (<Video
        ref={(ref) => { this.mVideo = ref; }}
        style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0 }}
        source={this.mSrc}
        muted={this.mute}
        paused={!this.mPlaying}
        resizeMode="contain"
        onEnd={this.onEnd}
        onLoad={this.onLoad}
        onError={this.onError}
        onBuffer={this.onBuffer}
        playInBackground={false}
        playWhenInactive={false}
        repeat={false}
        onProgress={this.onProgress}
        onSeek={this.onSeek}
        rate={this.mRate}
        onPlaybackRateChange={this.onPlayStateChange}
        controls={false}
        ignoreSilentSwitch={"ignore"}
        onPress={this.onPress}/>)
      :
      (<View ref={(ref) => { this.mVideo = null; }}/>);

  }

  getVideo() {
    return this.mVideo;
  }

  getUrlSrc() {
    return this.mSrc;
  }
  addCameraListener(aCamL) {
    this.mCamL.push(aCamL);
    return {
      remove: () => {
        this.mCamL = this.mCamL.filter((aL) => {
          return aL !== aCamL;
        });
      }
    };
  }
  
  notify(aType, aDat) {
    this.mCamL.forEach((aL, aIdx) => {
      // console.log(TAG, "notify", aL.constructor.name, aType, aDat);
      aL.onInfo(aType, aDat);
    });
  }

  error(aError) {
    this.mCamL.forEach((aL, aIdx) => {
      aL.onError(aError);
    });
  }


  start(aRec, aForce = false) { // 这里负责连接
    this.mPlayItm = aRec;
    return new Promise((aResol, aReject) => {
      Util.checkExist(this.mPlayItm).then((result) => {
        console.log("checkExist", result.data.deleteStatus); // result.data.deleteStatus true : false
        if (!result.data.deleteStatus) {
          this.getVideoUrl(aResol, aReject);
        } else {
          this.error("camera.alarm.video.deleted");
          aReject("camera.alarm.video.deleted");
        }
      }).catch((err) => {
        console.log("checkExist2", err);
        if (err.indexOf('暂未收录该接口') != -1) {
          this.getVideoUrl(aResol, aReject);
        }
      });
    });
  }

  getVideoUrl (aResol, aReject) {
    Util.getVideoUrl(this.mPlayItm)
      .then((aRet) => {
        // console.log(this.tag, "going to play", res);
        this.mCurTime = 0;
        this.mSrc = { uri: aRet };
        this.notify(Info.ViewUpdate, null);
        aResol();
      }).catch((err) => {
        console.log(TAG, err);
        aReject(err);
      });
  }

  seek(aPos) {
    this.setSkipTimer();
    this.mVideo && this.mVideo.seek(aPos);
  }

  pause() {
    this.setSkipTimer(0);
    this.mPlaying = false;
    this.notify(Info.ViewUpdate, null);
  }

  resume() {
    this.setSkipTimer();
    this.mPlaying = true;
    this.notify(Info.ViewUpdate, null);
  }

  setMute(aMute) {
    this.setSkipTimer();
    this.mute = aMute;
  }

  setRate(aRate) {
    this.setSkipTimer();
    this.mRate = aRate;
  }
  
  stop() {
    this.mSrc = null;
    if (this.mSkipTimer) {
      clearTimeout(this.mSkipTimer);
    }
    this.notify(Info.ViewUpdate, null);
  }

  onSeek = (aData) => {
    this.notify(Info.SeekComplete, aData);
  }

  onProgress = (aDat) => {
    this.notify(Info.Progress, aDat);
  }

  onEnd = () => {
    this.notify(Info.End, null);
  }

  onLoad = (aDat) => {
    this.notify(Info.Load, aDat);
  }

  onError = (err) => {
    console.log(TAG, "onerror", err);
    this.error("widget.start.video.load.error");
  }

  onPlayStateChange = (rate) => {
    if (Platform.OS == 'android') return;
    // chuangmi-10602, 兼容ios15拔掉耳机视频暂停的问题
    console.log('play rate: ', rate);
    if (this.mSkipStateNotify) return;
    let isPlaying = rate.playbackRate == 0 ? false : true;
    if (isPlaying != this.mPlaying) {
      this.notify(Info.PlayState, isPlaying);
    }
  }

  setSkipTimer(timer = 200) {
    if (Platform.OS == 'android') return;
    this.mSkipStateNotify = true;
    if (timer == 0) return; // skip all message
    if (this.mSkipTimer) {
      clearTimeout(this.mSkipTimer);
    }
    this.mSkipTimer = setTimeout(() => {
      this.mSkipStateNotify = false;
    }, timer);
  }

}

const UriPlayer = new _UriPlayer();
export default UriPlayer;
