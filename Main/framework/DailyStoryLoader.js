import Util from "../util2/Util";
import API from "../util2/API";
import { Device, Host, Service } from "miot";
import { DeviceEventEmitter, Platform } from "react-native";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import StorageKeys from '../StorageKeys';
import UriPlayer from "./UriPlayer";
import EventLoaderInf, { DldStatus, Order } from "./EventLoaderInf";
import LogUtil from "../util/LogUtil";


const TAG = "DailyStoryLoader";
const DldCbName = 'DailyStoryM3U8ToMP4CallBack';
const cancelDldCbName = 'cancelDailyStoryM3U8ToMP4CallBack';

export const CldDldTypes = {
  Files: 'filelist',
  Events: 'eventlist'
};

class _DailyStoryLoader extends EventLoaderInf {
  constructor() {
    super();
    console.log(TAG, "status", DldStatus, "DldStatus.Complete");
    this.mPlayCfg = { loader: this, player: UriPlayer };
    this.mDldL = null;
    // state : 1. onStart (开始下载)  2. onComplete（下载完成）  3. onError（失败）  4. onProgress（下载进度）
    this.mSaveL = DeviceEventEmitter.addListener(DldCbName, (aDat) => {
      console.log(TAG, "mSaveL", aDat, DldStatus, "DldStatus.Complete");
      if (this.mDldL) {
        switch (aDat.state) {
          case 'onComplete':
            this.mDldL.onDldProgress(DldStatus.Complete);
            this.mDldL = null;
            break;
          case 'onError':
            LogUtil.logOnAll("onDownloading error");
            this.mDldL.onDldProgress(DldStatus.Err);
            this.mDldL = null;
            break;
          default:
            this.mDldL.onDldProgress(aDat.progress);
            break;
        }
      }
    });

    this.mCancel = DeviceEventEmitter.addListener(cancelDldCbName, (aDat) => {
      console.log(TAG, "mCancel", aDat);
      if (this.mDldL) {
        switch (aDat.state) {
          case 'onComplete':
            this.mDldL.onDldCancelResult(aDat.fileId, DldStatus.Complete);
            break;
          case 'onError':
            LogUtil.logOnAll("onDldCancelResult");
            this.mDldL.onDldCancelResult(aDat.fileId, DldStatus.Err);
            break;
        }
        this.mDldL = null;
      }
    });
  }

  async getAllEvent(aDate, aEvent, aIsMore, aOrder = Order.Desc, mtype) {
    let isVip = false;
    try {
      let vd = await StorageKeys.VIP_DETAIL;
      isVip = vd.vip;
    } catch (aErr) {
      console.log(TAG, "load vip failed");
      // treat as normal
    }

    return await Util.getAllEvent(aDate, isVip ? 30 : 7, aEvent, aIsMore, 20, this.mPlayCfg, mtype, aOrder);
  }

  async getOneDayAllEvent(aDate, aEvent, aIsMore, aOrder = Order.Desc, mtype) {
    return await Util.getAllEvent(aDate, 0, aEvent, aIsMore, 20, this.mPlayCfg, mtype, aOrder);
  }

  getEventList(aDate, aEvent, aIsMore, mtype) {
    return Util.getEventList(aDate, aEvent, aIsMore, 20, this.mPlayCfg, mtype);
  }

  getEventByFileId(did, model, isAlarm, fileId, evType) {
    return Util.getEventsByFileId(did, model, fileId, isAlarm, this.mPlayCfg, evType);
  }

  eventRead(aEv) {
    Util.eventRead(aEv);
  }


  getThumb(aRec) {
    let imgStorId = aRec.imgStoreId;
    return Service.miotcamera.getFileIdImage(imgStorId, Device.deviceID);
  }

  async getSummary() {
    try {
      let vipDat = await StorageKeys.VIP_DETAIL;
      let cloudCap = await Util.fetchCloudCapacity();
      console.log(TAG, "VIP_DETAIL", vipDat, "cloudCap", cloudCap);
      let dateFmt;
      if (!vipDat.vip) {
        switch (vipDat.status) {
          case 1:
            return { type: "cloud", info: { type: vipDat.pacakgeType, total: 1,
              detail: LocalizedStrings['cloud_service_expired'], vip: vipDat.vip } };
          case 2:
            dateFmt = LocalizedStrings["storage_date_fmt"] || "YYYY-MM-DD";
            if (vipDat.renewStatus) {
              return { type: "cloud", info: { type: vipDat.pacakgeType, total: 1,
                detail: Util.fmtStr(LocalizedStrings["storage_vvip_state"], Util.getMoment(vipDat.endTime / 1000).format(dateFmt)), vip: vipDat.vip } };
            } else {
              return { type: "cloud", info: { type: vipDat.pacakgeType, total: 1,
                detail: Util.fmtStr(LocalizedStrings["storage_vip_state"], Util.getMoment(vipDat.endTime / 1000).format(dateFmt)), vip: vipDat.vip } };
            }
        }
        return { type: "cloud", info: { type: vipDat.pacakgeType, total: 1,
          detail: LocalizedStrings["c_get_fail"], vip: vipDat.vip } };
      } else {
        let str = null;
        if (vipDat.pacakgeType) {
          if (vipDat.pacakgeType.indexOf('7') != -1) {
            str = Util.fmtStr(LocalizedStrings['cloud_service_package_type'], '7');
          } else if (vipDat.pacakgeType.indexOf('30') != -1) {
            str = Util.fmtStr(LocalizedStrings['cloud_service_package_type'], '30');
          }
        }
        str = str ? str : vipDat.pacakgeType;
        return { type: "cloud", info: { type: str, total: 1, rollingInterval: vipDat.rollingSaveInterval,
          detail: Util.fmtStr(LocalizedStrings["storage_usage"], Util.fmtSize(cloudCap.capacity * 1024 * 1024)), vip: vipDat.vip } };
      }
    } catch (err) {
      console.log(TAG, "getSummary err", err);
      return { type: "cloud", info: { type: LocalizedStrings["s_cloud_setting"], detail: LocalizedStrings["c_get_fail"], vip: false } };
    }

  }

  download(aRec, aPath, aListener) {
    if (null == this.mDldL) {
      // this.mDldL = aListener;
      // Service.miotcamera.downloadM3U8ToMP4V2(aRec.fileId, aPath, DldCbName);
      let params = {
        "method": "GET",
        "did": Device.deviceID, "region": Host.locale.language.includes("en") ? "US" : "CN", "fileId": aRec.fileId,
        "model": Device.model
      };
      Service.miotcamera.getVideoFileUrlV2("business.smartcamera.", "/miot/camera/app/v1/dailyStory/m3u8", params)
        .then((res) => {
          // here fetched url;
          // go downloadByUrl
          this.downloadByUrl(aRec.fileId, res, aPath, aListener);
        }).catch((err) => {
          console.log("换视频地址出错了：" + JSON.stringify(err));
          LogUtil.logOnAll("换视频地址出错了：", "download on going");
          aListener.onDldProgress(DldStatus.Err);// 获取url失败了，认为也是下载失败抛出去
        });
    } else {
      LogUtil.logOnAll(TAG, "download on going");
      aListener.onDldProgress(DldStatus.Err);
    }
  }
  
  downloadByUrl(fileId, url, aPath, aListener) {
    if (null == this.mDldL) {
      this.mDldL = aListener;
      Service.miotcamera.downloadM3U8ToMP4ByUrl(fileId, url, aPath, DldCbName, Device.deviceID);
    } else {
      LogUtil.logOnAll(TAG, "downloadByUrl on going");
      aListener.onDldProgress(DldStatus.Err);
    }
  }

  cancelDownload(aRec, aListener) {
    if (null == this.mDldL) {
      this.mDldL = aListener;
    }
    Service.miotcamera.cancelDownloadM3U8ToMP4V2(aRec.fileId, cancelDldCbName, false);
    // 取消下载每日故事，可能会失败，可能导致his.mDldL无法置空，导致下次下载每日故事移除
    this.mDldL = null;
  }


  delete(aRecs = []) {
    // return AlarmDetailAPI.instance().deleteVideo([item.fileId])
    let fileIds = aRecs.map((aRec) => {
      return aRec.fileId; 
    });
    let params = {
      fileIds: {
        fileIds: fileIds
      }
    };
    return API.instance().post('/common/app/v2/delete/files', 'business.smartcamera', params);
  }

  
}
const DailyStoryEvLdr = new _DailyStoryLoader();
export default DailyStoryEvLdr;
