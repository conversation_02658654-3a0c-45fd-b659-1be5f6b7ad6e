/* event format
let item = {
  createTime: unit.createTime,
  eventTime: DateFormatter.instance().format(unit.createTime),
  type: unit.eventType,
  desc: this.getDescFromType(unit.eventType),
  imgStoreId: unit.imgStoreId,
  fileId: unit.fileId,
  isRead: unit.isRead,
  offset: unit.offset,
  playCfg: null
};
//date list format
data = {
  hasMore: result.data.isContinue,
  items: eventItems,
  nextTime: new Date(result.data.nextTime)
};
*/

/*
DownloadL{
  0 to 100  -1 for fail 101 for complete
  onDldProgress(aProg)
}
*/

/*
summmay{
  type: str "sdcard", "cloud", "local"
  info:{tbd}
}
*/
export const DldStatus = {
  Err: -1,
  p2pLost: -2,
  notExist: -3, // file not exist
  Complete: 101
};

export const Order = {
  Desc: "DESC",
  Asc: "ASC"
};
const TAG = "EventLoaderInf";
export default class EventLoaderInf {
  constructor(aCanDld = true, aSupportImg = false) {
    this.mCanDld = aCanDld;
    this.mSupportImg = aSupportImg;
    this.mListeners = [];
  }

  /*
  will call listener onLoaderDataChanged, if underlying data change
  */
  addListener(aListener) {
    this.mListeners.push(aListener);
    return {
      remove: () => {
        console.log(TAG, this.constructor.name, "removeListener", this.mListeners.length);
        this.mListeners = this.mListeners.filter((aL) => {
          return aL !== aListener;
        });
        console.log(TAG, this.constructor.name, "after removeListener", this.mListeners.length);
      }
    };
  }

  notifyDataChanged() {
    console.log(TAG, this.constructor.name, "notifyDataChanged", this.mListeners.length);
    this.mListeners.forEach((aListenFunc) => {
      aListenFunc();
    });
  }

  async getAllEvent(aDate, aEvent, aIsMore) {
  }

  async getOneDayAllEvent(aDate, aEvent, aIsMore, aOrder = Order.Desc) {

  }


  getEventList(aDate, aEvent, aIsMore) {
  }

  eventRead(aEv) {

  }


  getThumb(aRec) {
  }


  canDownload() {
    return this.mCanDld;
  }

  supportImage() {
    return this.mSupportImg;
  }


  download(aRec, aPath, aListener) {
  }


  delete(aRecs = []) {
  }

  getSummary() {

  }

  getThumbUrlByImgStoreId() {
    
  }

  // 返回有视频的日期的时间戳数组，每天只要一个时间戳就可以
  async getVideoDates() {
    return new Promise((resolve, reject) => {
      resolve(null);
    });
  }

}
