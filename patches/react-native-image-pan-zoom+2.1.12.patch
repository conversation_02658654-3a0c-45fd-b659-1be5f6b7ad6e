diff --git a/node_modules/react-native-image-pan-zoom/.all-contributorsrc b/node_modules/react-native-image-pan-zoom/.all-contributorsrc
deleted file mode 100644
index 75c5730..0000000
--- a/node_modules/react-native-image-pan-zoom/.all-contributorsrc
+++ /dev/null
@@ -1,69 +0,0 @@
-{
-  "files": [
-    "README.md"
-  ],
-  "imageSize": 100,
-  "commit": false,
-  "contributors": [
-    {
-      "login": "kingdaro",
-      "name": "<PERSON>",
-      "avatar_url": "https://avatars1.githubusercontent.com/u/19603573?v=4",
-      "profile": "http://kingdaro.net",
-      "contributions": [
-        "code"
-      ]
-    },
-    {
-      "login": "TPXP",
-      "name": "<PERSON>",
-      "avatar_url": "https://avatars2.githubusercontent.com/u/7191841?v=4",
-      "profile": "https://tpxp.ddns.net",
-      "contributions": [
-        "code"
-      ]
-    },
-    {
-      "login": "ditorojuan",
-      "name": "<PERSON>",
-      "avatar_url": "https://avatars0.githubusercontent.com/u/22530892?v=4",
-      "profile": "https://github.com/ditorojuan",
-      "contributions": [
-        "code"
-      ]
-    },
-    {
-      "login": "AlhaythamElhassan",
-      "name": "Alhaytham Elhassan",
-      "avatar_url": "https://avatars0.githubusercontent.com/u/20684701?v=4",
-      "profile": "https://github.com/AlhaythamElhassan",
-      "contributions": [
-        "code"
-      ]
-    },
-    {
-      "login": "alexandrius",
-      "name": "Alexander Pataridze",
-      "avatar_url": "https://avatars3.githubusercontent.com/u/5978212?v=4",
-      "profile": "http://alexandrius.com",
-      "contributions": [
-        "code"
-      ]
-    },
-    {
-      "login": "pxpeterxu",
-      "name": "Peter Xu",
-      "avatar_url": "https://avatars1.githubusercontent.com/u/2924388?v=4",
-      "profile": "https://github.com/pxpeterxu",
-      "contributions": [
-        "code"
-      ]
-    }
-  ],
-  "contributorsPerLine": 7,
-  "projectName": "react-native-image-zoom",
-  "projectOwner": "ascoders",
-  "repoType": "github",
-  "repoHost": "https://github.com",
-  "skipCi": true
-}
diff --git a/node_modules/react-native-image-pan-zoom/.eslintrc.js b/node_modules/react-native-image-pan-zoom/.eslintrc.js
deleted file mode 100644
index 04cdc09..0000000
--- a/node_modules/react-native-image-pan-zoom/.eslintrc.js
+++ /dev/null
@@ -1,31 +0,0 @@
-module.exports = {
-  parser: '@typescript-eslint/parser', // Specifies the ESLint parser
-  plugins: ['react', 'react-native'],
-  parserOptions: {
-    ecmaVersion: 2020, // Allows for the parsing of modern ECMAScript features
-    sourceType: 'module', // Allows for the use of imports
-    ecmaFeatures: {
-      jsx: true, // Allows for the parsing of JSX
-    },
-  },
-  settings: {
-    react: {
-      version: 'detect', // Tells eslint-plugin-react to automatically detect the version of React to use
-    },
-  },
-
-  extends: [
-    'plugin:react/recommended',
-    'plugin:react-native/all',
-    'plugin:@typescript-eslint/recommended', // Uses the recommended rules from the @typescript-eslint/eslint-plugin
-    'prettier/@typescript-eslint', // Uses eslint-config-prettier to disable ESLint rules from @typescript-eslint/eslint-plugin that would conflict with prettier
-    'plugin:prettier/recommended', // Enables eslint-plugin-prettier and eslint-config-prettier. This will display prettier errors as ESLint errors. Make sure this is always the last configuration in the extends array.
-  ],
-
-  rules: {
-    // Place to specify ESLint rules. Can be used to overwrite rules specified from the extended configs
-    // e.g. "@typescript-eslint/explicit-function-return-type": "off",
-    '@typescript-eslint/interface-name-prefix': [2, { prefixWithI: 'always' }],
-    '@typescript-eslint/no-inferrable-types': [1, { ignoreParameters: true, ignoreProperties: true }],
-  },
-};
diff --git a/node_modules/react-native-image-pan-zoom/README.md b/node_modules/react-native-image-pan-zoom/README.md
deleted file mode 100644
index 4d79430..0000000
--- a/node_modules/react-native-image-pan-zoom/README.md
+++ /dev/null
@@ -1,140 +0,0 @@
-## Show Cases
-<!-- ALL-CONTRIBUTORS-BADGE:START - Do not remove or modify this section -->
-[![All Contributors](https://img.shields.io/badge/all_contributors-6-orange.svg?style=flat-square)](#contributors-)
-<!-- ALL-CONTRIBUTORS-BADGE:END -->
-
-> Zoom while sliding
-
-![3.gif](https://cloud.githubusercontent.com/assets/7970947/18501092/87d5efe8-7a80-11e6-9234-516b2be1e729.gif)
-
-> Intelligent zoom
-
-![2.gif](https://cloud.githubusercontent.com/assets/7970947/18501091/87b14d8c-7a80-11e6-904d-8c434e1904ce.gif)
-
-## Getting Started
-
-### Installation
-
-```bash
-npm i react-native-image-pan-zoom --save
-```
-
-### Basic Usage
-
-- Install create-react-native-app first
-
-```bash
-$ npm install -g create-react-native-app
-```
-
-- Initialization of a react-native project
-
-```bash
-$ create-react-native-app AwesomeProject
-```
-
-- Then, edit `AwesomeProject/App.js`, like this:
-
-```typescript
-import { Image, Dimensions } from 'react-native';
-import ImageZoom from 'react-native-image-pan-zoom';
-
-export default class App extends React.Component {
-    render: function() {
-        return (
-            <ImageZoom cropWidth={Dimensions.get('window').width}
-                       cropHeight={Dimensions.get('window').height}
-                       imageWidth={200}
-                       imageHeight={200}>
-                <Image style={{width:200, height:200}}
-                       source={{uri:'http://v1.qzone.cc/avatar/201407/07/00/24/53b9782c444ca987.jpg!200x200.jpg'}}/>
-            </ImageZoom>
-        )
-    }
-}
-```
-
-### Document
-
-| Props | Type | Description | DefaultValue |
-| --- | --- | --- | --- |
-| **cropWidth(required)** | number | operating area width | 100 |
-| **cropHeight(required)** | number | operating area height | 100 |
-| **imageWidth(required)** | number | picture width | 100 |
-| **imageHeight(required)** | number | picture height | 100 |
-| onClick | (eventParams: [IOnClick](https://github.com/ascoders/react-native-image-zoom/blob/master/src/image-zoom/image-zoom.type.ts))=>void | onClick | ()=>{} |
-| onDoubleClick | (eventParams: [IOnClick](https://github.com/ascoders/react-native-image-zoom/blob/master/src/image-zoom/image-zoom.type.ts))=>void | onDoubleClick | ()=>{} |
-| panToMove | boolean | allow to move picture with one finger | true |
-| pinchToZoom | boolean | allow scale with two fingers | true |
-| clickDistance | number | how many finger movement can also trigger `onClick` | 10 |
-| horizontalOuterRangeOffset | (offsetX?: number)=>void | horizontal beyond the distance, the parent to do picture switching, you can listen to this function. When this function is triggered, you can do the switch operation | ()=>{} |
-| onDragLeft | ()=>void | trigger to switch to the left of the graph, the left sliding speed exceeds the threshold when triggered | ()=>{} |
-| responderRelease | (vx: number)=>void | let go but do not cancel | ()=>{} |
-| maxOverflow | number | maximum sliding threshold | 100 |
-| longPressTime | number | long press threshold | 800 |
-| onLongPress | (eventParams: [IOnClick](https://github.com/ascoders/react-native-image-zoom/blob/master/src/image-zoom/image-zoom.type.ts))=>void | on longPress | ()=> {} |
-| doubleClickInterval | number | time allocated for second click to be considered as doublClick event | 175 |
-| onMove | ( position: [IOnMove](https://github.com/ascoders/react-native-image-zoom/blob/master/src/image-zoom/image-zoom.type.ts) )=>void | reports movement position data (helpful to build overlays) | ()=> {} |
-| centerOn | { x: number, y: number, scale: number, duration: number } | if given this will cause the map to pan and zoom to the desired location | undefined |
-| enableSwipeDown | boolean | for enabling vertical movement if user doesn't want it | false |
-| enableCenterFocus | boolean | for disabling focus on image center if user doesn't want it | true |
-| onSwipeDown | () => void | function that fires when user swipes down | null |
-| swipeDownThreshold | number | threshold for firing swipe down function | 230 |
-| minScale | number | minimum zoom scale | 0.6 |
-| maxScale | number | maximum zoom scale | 10 |
-| useNativeDriver | boolean | Whether to animate using [`useNativeDriver`](https://reactnative.dev/docs/animations#using-the-native-driver) | false |
-| onStartShouldSetPanResponder | () => boolean | Override onStartShouldSetPanResponder behavior | () => true |
-| onMoveShouldSetPanResponder | () => boolean | Override onMoveShouldSetPanResponder behavior | undefined |
-| onPanResponderTerminationRequest | () => boolean | Override onMoveShouldSetPanResponder behavior | () => false |
-| useHardwareTextureAndroid | boolean | for disabling rendering to hardware texture on Android | true |
-
-| Method | params | Description |
-| --- | --- | --- |
-| reset |  | Reset the position and the scale of the image |
-| resetScale |  | Reset the scale of the image |
-| centerOn | ICenterOn | Centers the image in the position indicated. ICenterOn={ x: number, y: number, scale: number, duration: number } |
-
-## Development pattern
-
-### Step 1, run TS listener
-
-After clone this repo, then:
-
-```bash
-npm install
-npm start
-```
-
-### Step 2, run demo
-
-```bash
-cd demo
-npm install
-npm start
-```
-
-Then, scan the QR, use your [expo app](https://expo.io./).
-
-## Contributors ✨
-
-Thanks goes to these wonderful people ([emoji key](https://allcontributors.org/docs/en/emoji-key)):
-
-<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
-<!-- prettier-ignore-start -->
-<!-- markdownlint-disable -->
-<table>
-  <tr>
-    <td align="center"><a href="http://kingdaro.net"><img src="https://avatars1.githubusercontent.com/u/19603573?v=4" width="100px;" alt=""/><br /><sub><b>Darius</b></sub></a><br /><a href="https://github.com/ascoders/react-native-image-zoom/commits?author=kingdaro" title="Code">💻</a></td>
-    <td align="center"><a href="https://tpxp.ddns.net"><img src="https://avatars2.githubusercontent.com/u/7191841?v=4" width="100px;" alt=""/><br /><sub><b>Thomas P.</b></sub></a><br /><a href="https://github.com/ascoders/react-native-image-zoom/commits?author=TPXP" title="Code">💻</a></td>
-    <td align="center"><a href="https://github.com/ditorojuan"><img src="https://avatars0.githubusercontent.com/u/22530892?v=4" width="100px;" alt=""/><br /><sub><b>Juan Di Toro</b></sub></a><br /><a href="https://github.com/ascoders/react-native-image-zoom/commits?author=ditorojuan" title="Code">💻</a></td>
-    <td align="center"><a href="https://github.com/AlhaythamElhassan"><img src="https://avatars0.githubusercontent.com/u/20684701?v=4" width="100px;" alt=""/><br /><sub><b>Alhaytham Elhassan</b></sub></a><br /><a href="https://github.com/ascoders/react-native-image-zoom/commits?author=AlhaythamElhassan" title="Code">💻</a></td>
-    <td align="center"><a href="http://alexandrius.com"><img src="https://avatars3.githubusercontent.com/u/5978212?v=4" width="100px;" alt=""/><br /><sub><b>Alexander Pataridze</b></sub></a><br /><a href="https://github.com/ascoders/react-native-image-zoom/commits?author=alexandrius" title="Code">💻</a></td>
-    <td align="center"><a href="https://github.com/pxpeterxu"><img src="https://avatars1.githubusercontent.com/u/2924388?v=4" width="100px;" alt=""/><br /><sub><b>Peter Xu</b></sub></a><br /><a href="https://github.com/ascoders/react-native-image-zoom/commits?author=pxpeterxu" title="Code">💻</a></td>
-  </tr>
-</table>
-
-<!-- markdownlint-enable -->
-<!-- prettier-ignore-end -->
-<!-- ALL-CONTRIBUTORS-LIST:END -->
-
-This project follows the [all-contributors](https://github.com/all-contributors/all-contributors) specification. Contributions of any kind welcome!
\ No newline at end of file
diff --git a/node_modules/react-native-image-pan-zoom/built/image-zoom/image-zoom.component.js b/node_modules/react-native-image-pan-zoom/built/image-zoom/image-zoom.component.js
index 83b85c2..ee52c27 100644
--- a/node_modules/react-native-image-pan-zoom/built/image-zoom/image-zoom.component.js
+++ b/node_modules/react-native-image-pan-zoom/built/image-zoom/image-zoom.component.js
@@ -13,7 +13,7 @@ var __extends = (this && this.__extends) || (function () {
     };
 })();
 var __assign = (this && this.__assign) || function () {
-    __assign = Object.assign || function(t) {
+    __assign = Object.assign || function (t) {
         for (var s, i = 1, n = arguments.length; i < n; i++) {
             s = arguments[i];
             for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
@@ -115,58 +115,72 @@ var ImageViewer = /** @class */ (function (_super) {
                         // 因为可能触发放大，因此记录双击时的坐标位置
                         _this.doubleClickX = evt.nativeEvent.changedTouches[0].pageX;
                         _this.doubleClickY = evt.nativeEvent.changedTouches[0].pageY;
-                        if (_this.props.onDoubleClick) {
-                            _this.props.onDoubleClick({
-                                locationX: evt.nativeEvent.changedTouches[0].locationX,
-                                locationY: evt.nativeEvent.changedTouches[0].locationY,
-                                pageX: _this.doubleClickX,
-                                pageY: _this.doubleClickY,
-                            });
+                        let containerHeight = react_native_1.StatusBar.currentHeight || 0;
+                        containerHeight += 52;
+                        if (react_native_1.Platform.OS == "ios") {
+                            containerHeight += 48
                         }
-                        // 取消长按
-                        clearTimeout(_this.longPressTimeout);
-                        // 缩放
-                        _this.isDoubleClick = true;
-                        if (_this.props.enableDoubleClickZoom) {
-                            if (_this.scale > 1 || _this.scale < 1) {
-                                // 回归原位
-                                _this.scale = 1;
-                                _this.positionX = 0;
-                                _this.positionY = 0;
+                        let minAllowHeight = (_this.props.cropHeight - containerHeight - _this.props.imageHeight * _this.scale) / 2 + containerHeight;
+                        let maxAllowHeight = (_this.props.cropHeight - _this.props.imageHeight * _this.scale) / 2 + containerHeight + _this.props.imageHeight * _this.scale
+                        console.log('allowHeight', minAllowHeight, maxAllowHeight, _this.doubleClickY);
+                        // let
+
+                        if (Number(_this.doubleClickY) > Number(minAllowHeight) && Number(_this.doubleClickY) < Number(maxAllowHeight)) {
+                            if (_this.props.onDoubleClick) {
+                                _this.props.onDoubleClick({
+                                    locationX: evt.nativeEvent.changedTouches[0].locationX,
+                                    locationY: evt.nativeEvent.changedTouches[0].locationY,
+                                    pageX: _this.doubleClickX,
+                                    pageY: _this.doubleClickY,
+                                });
                             }
-                            else {
-                                // 开始在位移地点缩放
-                                // 记录之前缩放比例
-                                // 此时 this.scale 一定为 1
-                                var beforeScale = _this.scale;
-                                // 开始缩放
-                                _this.scale = 2;
-                                // 缩放 diff
-                                var diffScale = _this.scale - beforeScale;
-                                // 找到两手中心点距离页面中心的位移
-                                // 移动位置
-                                _this.positionX = ((_this.props.cropWidth / 2 - _this.doubleClickX) * diffScale) / _this.scale;
-                                _this.positionY = ((_this.props.cropHeight / 2 - _this.doubleClickY) * diffScale) / _this.scale;
+                            // 取消长按
+                            clearTimeout(_this.longPressTimeout);
+                            // 缩放
+                            _this.isDoubleClick = true;
+                            if (_this.props.enableDoubleClickZoom) {
+                                if (_this.scale > 1 || _this.scale < 1) {
+                                    // 回归原位
+                                    _this.scale = 1;
+                                    _this.positionX = 0;
+                                    _this.positionY = 0;
+                                }
+                                else {
+                                    // 开始在位移地点缩放
+                                    // 记录之前缩放比例
+                                    // 此时 this.scale 一定为 1
+                                    var beforeScale = _this.scale;
+                                    // 开始缩放
+                                    _this.scale = 2;
+                                    // 缩放 diff
+                                    var diffScale = _this.scale - beforeScale;
+                                    // 找到两手中心点距离页面中心的位移
+                                    // 移动位置
+                                    _this.positionX = ((_this.props.cropWidth / 2 - _this.doubleClickX) * diffScale) / _this.scale;
+                                    _this.positionY -= (_this.centerDiffY * diffScale) / _this.scale;
+
+                                }
+                                _this.imageDidMove('centerOn');
+                                react_native_1.Animated.parallel([
+                                    react_native_1.Animated.timing(_this.animatedScale, {
+                                        toValue: _this.scale,
+                                        duration: 100,
+                                        useNativeDriver: !!_this.props.useNativeDriver,
+                                    }),
+                                    react_native_1.Animated.timing(_this.animatedPositionX, {
+                                        toValue: _this.positionX,
+                                        duration: 100,
+                                        useNativeDriver: !!_this.props.useNativeDriver,
+                                    }),
+                                    react_native_1.Animated.timing(_this.animatedPositionY, {
+                                        toValue: _this.positionY,
+                                        duration: 100,
+                                        useNativeDriver: !!_this.props.useNativeDriver,
+                                    }),
+                                ]).start();
                             }
-                            _this.imageDidMove('centerOn');
-                            react_native_1.Animated.parallel([
-                                react_native_1.Animated.timing(_this.animatedScale, {
-                                    toValue: _this.scale,
-                                    duration: 100,
-                                    useNativeDriver: !!_this.props.useNativeDriver,
-                                }),
-                                react_native_1.Animated.timing(_this.animatedPositionX, {
-                                    toValue: _this.positionX,
-                                    duration: 100,
-                                    useNativeDriver: !!_this.props.useNativeDriver,
-                                }),
-                                react_native_1.Animated.timing(_this.animatedPositionY, {
-                                    toValue: _this.positionY,
-                                    duration: 100,
-                                    useNativeDriver: !!_this.props.useNativeDriver,
-                                }),
-                            ]).start();
                         }
+
                     }
                     else {
                         _this.lastClickTime = new Date().getTime();
@@ -179,6 +193,8 @@ var ImageViewer = /** @class */ (function (_super) {
                     return;
                 }
                 if (evt.nativeEvent.changedTouches.length <= 1) {
+                    console.log("evt.nativeEvent.changedTouches.length ", evt.nativeEvent.changedTouches.length)
+
                     // x 位移
                     var diffX = gestureState.dx - (_this.lastPositionX || 0);
                     if (_this.lastPositionX === null) {
@@ -335,6 +351,8 @@ var ImageViewer = /** @class */ (function (_super) {
                     }
                 }
                 else {
+                    console.log("length ", evt.nativeEvent.changedTouches.length)
+
                     // 多个手指的情况
                     // 取消长按状态
                     if (_this.longPressTimeout) {
@@ -616,16 +634,17 @@ var ImageViewer = /** @class */ (function (_super) {
             ],
         };
         var parentStyles = react_native_1.StyleSheet.flatten(this.props.style);
+        console.log('this.props.style', this.props.style)
         return (<react_native_1.View style={__assign(__assign(__assign({}, image_zoom_style_1.default.container), parentStyles), { width: this.props.cropWidth, height: this.props.cropHeight })} {...this.imagePanResponder.panHandlers}>
-        <react_native_1.Animated.View style={animateConf} renderToHardwareTextureAndroid={this.props.useHardwareTextureAndroid}>
-          <react_native_1.View onLayout={this.handleLayout.bind(this)} style={{
-            width: this.props.imageWidth,
-            height: this.props.imageHeight,
-        }}>
-            {this.props.children}
-          </react_native_1.View>
-        </react_native_1.Animated.View>
-      </react_native_1.View>);
+            <react_native_1.Animated.View style={animateConf} renderToHardwareTextureAndroid={this.props.useHardwareTextureAndroid}>
+                <react_native_1.View onLayout={this.handleLayout.bind(this)} style={{
+                    width: this.props.imageWidth,
+                    height: this.props.imageHeight,
+                }}>
+                    {this.props.children}
+                </react_native_1.View>
+            </react_native_1.Animated.View>
+        </react_native_1.View>);
     };
     ImageViewer.defaultProps = new image_zoom_type_1.ImageZoomProps();
     return ImageViewer;
