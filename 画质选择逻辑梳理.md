# LiveVideoPageV2.js 画质选择逻辑梳理

## 概述
用户点击播放器中的画质选择按钮后，会弹出一个选择弹窗，包含三个主要选项：**自动**、**480P流畅**、**2.5K高清**（根据设备型号不同会有差异）。

## 1. 初始化阶段

### 1.1 设备能力检测
```javascript
// 在构造函数中初始化设备支持的分辨率能力
this.isSupport2K = CameraConfig.isSupport2K(Device.model);     // 是否支持2K
this.isSupport25K = CameraConfig.isSupport25K(Device.model);   // 是否支持2.5K
this.isSupport3K = CameraConfig.isSupport3K(Device.model);     // 是否支持3K
```

### 1.2 默认分辨率设置
```javascript
setVideoResolution() {
  StorageKeys.LIVE_VIDEO_RESOLUTION.then((result) => {
    if (typeof result == "string" || result == null || result == undefined) {
      if (CameraConfig.useHighResolution(Device.model)) {
        StorageKeys.LIVE_VIDEO_RESOLUTION = 3; // 默认高清
        result = 3;
      } else {
        StorageKeys.LIVE_VIDEO_RESOLUTION = 0; // 默认自动
        result = 0;
      }
    }
    this.setState({ resolution: result });
  });
}
```

## 2. 画质按钮渲染逻辑

### 2.1 按钮状态显示
```javascript
_renderQulityToolButton(style) {
  let displayState = MHLottieQulityToolBtnDisplayState.AUTO;
  
  switch (this.state.resolution) {
    case 1: // 低画质
      if (CameraConfig.isSupport480P(Device.model)) {
        displayState = MHLottieQulityToolBtnDisplayState.R480;
      } else {
        displayState = MHLottieQulityToolBtnDisplayState.R360;
      }
      break;
    case 2: // 中画质
      displayState = MHLottieQulityToolBtnDisplayState.R720;
      break;
    case 3: // 高画质
      if (this.isSupport3K) {
        displayState = MHLottieQulityToolBtnDisplayState.R3K;
      } else if (this.isSupport2K) {
        displayState = MHLottieQulityToolBtnDisplayState.R2K;
      } else if (this.isSupport25K) {
        displayState = MHLottieQulityToolBtnDisplayState.R25K;
      } else {
        displayState = MHLottieQulityToolBtnDisplayState.R1080;
      }
      break;
    case 0: // 自动
    default:
      displayState = MHLottieQulityToolBtnDisplayState.AUTO;
      break;
  }
}
```

### 2.2 按钮点击处理
```javascript
qulityToolPressed() {
  this._hidePlayToolBarLater();
  if (this.state.isRecording) {
    Toast.success("camera_recording_block"); // 录制中不允许切换
    return false;
  }
  this.setState({ dialogVisibility: true }); // 显示选择弹窗
  return true;
}
```

## 3. 弹窗选项生成逻辑

### 3.1 选项文案生成
```javascript
_renderResolutionDialog_ios() {
  // 高清选项文案
  let fhdname = CameraConfig.Model_chuangmi_069a01 == Device.model 
    ? LocalizedStrings["camera_quality_super_fhd"]
    : this.isSupport2K ? LocalizedStrings["camera_quality_fhd2k"] 
    : (this.isSupport25K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "2.5K")
    : this.isSupport3K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "3K") 
    : LocalizedStrings["camera_quality_fhd"]);

  // 中等画质选项文案
  let midname = CameraConfig.Model_chuangmi_069a01 == Device.model 
    ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "").replace(" ", "").replace("高清", "高清HD")
    : VersionUtil.Model_chuangmi_051a01 == Device.model 
    ? LocalizedStrings["camera_quality_fhd"].replace("1080", "720") 
    : fhdname;

  // 低画质选项文案
  let lowName = CameraConfig.Model_chuangmi_069a01 == Device.model 
    ? LocalizedStrings["camera_quality_sd"]
    : CameraConfig.isSupport480P(Device.model) 
    ? LocalizedStrings["camera_quality_low"].replace("360", "480") 
    : LocalizedStrings["camera_quality_low"];
}
```

### 3.2 选项数组构建
```javascript
// 根据设备型号构建不同的选项数组
let dataSourceArr = CameraConfig.Model_chuangmi_069a01 == Device.model 
  ? [
      { title: LocalizedStrings["camera_quality_auto"] },    // 自动
      { title: lowName },                                    // 低画质
      { title: midname },                                    // 中画质  
      { title: fhdname }                                     // 高画质
    ]
  : VersionUtil.Model_chuangmi_051a01 == Device.model 
  ? [
      { title: LocalizedStrings["camera_quality_auto"] },    // 自动
      { title: lowName },                                    // 低画质
      { title: midname },                                    // 中画质
      { title: fhdname }                                     // 高画质
    ]
  : [
      { title: LocalizedStrings["camera_quality_auto"] },    // 自动
      { title: lowName },                                    // 低画质
      { title: fhdname }                                     // 高画质
    ];
```

## 4. 用户选择处理逻辑

### 4.1 弹窗选择回调
```javascript
<ChoiceDialog
  visible={this.state.dialogVisibility}
  title={LocalizedStrings["camera_quality_choose"]}
  options={dataSourceArr}
  selectedIndexArray={this.selectedIndexArray}
  onSelect={(result) => {
    this.selectedIndexArray = result;
    this._changeResolution(result[0]); // 处理分辨率切换
    this.setState({ dialogVisibility: false }); // 关闭弹窗
  }}
  onDismiss={(_) => this.setState({ dialogVisibility: false })}
/>
```

### 4.2 分辨率切换处理
```javascript
_changeResolution(position) {
  let index = 0;
  switch (position) {
    case 1: // 低画质
      index = 1;
      break;
    case 2: // 中画质
      if (VersionUtil.Model_chuangmi_051a01 == Device.model
          || CameraConfig.Model_chuangmi_069a01 == Device.model) {
        index = 2;
      } else {
        index = 3; // 其他设备中画质对应高画质
      }
      break;
    case 3: // 高画质
      index = 3;
      break;
    default: // 自动
      index = 0;
      break;
  }

  this.sendResolutionCmd(index); // 发送分辨率切换命令
  TrackUtil.reportResultEvent('Camera_Definition_Status', 'type', position + 1);
}
```

### 4.3 发送分辨率切换命令
```javascript
sendResolutionCmd(index, ignoreState = false) {
  Service.miotcamera.sendP2PCommandToDevice(
    MISSCommand.MISS_CMD_STREAM_CTRL_REQ, 
    { "videoquality": index }
  ).then((result) => {
    // 处理切换结果
    StorageKeys.LIVE_VIDEO_RESOLUTION = index; // 保存用户选择
    this.setState({ resolution: index });
  }).catch((error) => {
    // 处理切换失败
    console.log("分辨率切换失败:", error);
  });
}
```

## 5. 不同设备型号的选项差异

### 5.1 chuangmi.camera.086ac1 (当前设备)
- **自动**：智能选择最佳画质
- **480P流畅**：480P低画质，网络不好时使用
- **2.5K高清**：2560x1440高画质

### 5.2 chuangmi.camera.069a01 (特殊设备)
- **自动**：智能选择最佳画质
- **标清**：低画质选项
- **高清HD**：中等画质
- **超清**：最高画质

### 5.3 chuangmi.camera.051a01 (特殊设备)
- **自动**：智能选择最佳画质
- **480P流畅**：480P低画质
- **720P高清**：720P中等画质
- **1080P超清**：1080P高画质

### 5.4 其他设备
- **自动**：智能选择最佳画质
- **360P流畅**：360P低画质（不支持480P的设备）
- **1080P高清**：1080P高画质

## 6. 状态管理

### 6.1 分辨率状态值对应关系
- `0`：自动模式
- `1`：低画质（360P/480P）
- `2`：中画质（720P，仅特定设备）
- `3`：高画质（1080P/2K/2.5K/3K）

### 6.2 本地存储
```javascript
// 用户选择会保存到本地存储
StorageKeys.LIVE_VIDEO_RESOLUTION = selectedIndex;

// 下次进入时会读取用户上次的选择
StorageKeys.LIVE_VIDEO_RESOLUTION.then((result) => {
  this.setState({ resolution: result });
});
```

## 7. 限制条件

### 7.1 录制状态限制
- 当设备正在录制时，不允许切换画质
- 会显示提示："录制中无法切换画质"

### 7.2 连接状态限制
- 设备未连接时，画质按钮会被禁用
- 需要等待设备连接成功后才能切换

### 7.3 睡眠状态限制
- 设备处于睡眠状态时，画质按钮被禁用

## 8. 用户体验优化

### 8.1 视觉反馈
- 当前选中的画质会在按钮上显示对应的图标
- 弹窗中会高亮显示当前选中的选项

### 8.2 埋点统计
- 点击画质按钮：`Camera_Definition_ClickNum`
- 切换画质结果：`Camera_Definition_Status`

### 8.3 无障碍支持
- 画质按钮支持无障碍标签
- 弹窗选项支持无障碍导航
