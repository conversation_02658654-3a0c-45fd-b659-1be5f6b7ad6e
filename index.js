import { Package } from 'miot';

import App from "./Main";
import TrackConnectionHelper from './Main/util/TrackConnectionHelper';
import SceneMain from "./Main/SceneMain";
import {Entrance} from "miot";

switch (Package.entrance) {
 
  
  // Entrance.Scene 表示扩展程序进入智能场景的页面；为Entrance.Main 时，表示进入扩展程序的首页
  case Entrance.Scene:
    Package.entry(SceneMain, () => {
      console.disableYellowBox = true;
    });
    break;
  default:
    TrackConnectionHelper.clear();
    TrackConnectionHelper.trackEnterPlugin();

    Package.entry(App, () => {
      console.log("Package.entrance", Package.entrance);
      console.disableYellowBox = true;

    });
    break;
}

