# 096设备画质选项调整方案

## 问题分析

### 当前问题：
096设备显示4个画质选项：
- 自动
- 2.5K超清  
- 1080P高清
- 480P流畅

### 期望结果：
显示3个画质选项：
- 自动
- 2.5K高清（默认选择）
- 480P流畅

## 根本原因分析

096设备出现4个选项的原因是配置不完整：

1. **缺少2.5K支持标识**：`isSupport25K`方法中没有包含096设备
2. **缺少默认高清标识**：`useHighResolution`方法中没有包含096设备  
3. **缺少录制参数配置**：`getRecordingVideoParam`方法中没有096设备的2.5K参数
4. **缺少OSD参数配置**：`getCameraCorrentParam`方法中没有096设备的视频参数

## 解决方案

### 1. 添加2.5K支持标识

**文件**：`Main/util/CameraConfig.js`  
**方法**：`isSupport25K`

```javascript
static isSupport25K(model) {
  if (model == CameraConfig.Model_chuangmi_039a01 || model == CameraConfig.Model_chuangmi_049a01
    || model == CameraConfig.Model_chuangmi_039a04 || model == CameraConfig.Model_chuangmi_086ac1
    || model == CameraConfig.Model_xiaomi_096ac1) { // 新增
    return true;
  }
  return false;
}
```

### 2. 添加默认高清标识

**文件**：`Main/util/CameraConfig.js`  
**方法**：`useHighResolution`

```javascript
static useHighResolution(model) {
  if (model == this.Model_chuangmi_069a01 || model == this.Model_chuangmi_086ac1
    || model == this.Model_xiaomi_096ac1) { // 新增
    return true;
  }
  return false;
}
```

### 3. 添加录制参数配置

**文件**：`Main/util/CameraConfig.js`  
**方法**：`getRecordingVideoParam`

```javascript
if (model === CameraConfig.Model_chuangmi_039a01 || model === CameraConfig.Model_chuangmi_049a01 
  || model === VersionUtil.Model_chuangmi_051a01 || model === VersionUtil.Model_chuangmi_086ac1
  || model === CameraConfig.Model_xiaomi_096ac1) { // 新增
  recordingParam = { width: 2560, height: 1440 }; // 2.5K分辨率
}
```

### 4. 添加OSD参数配置

**文件**：`Main/util/CameraConfig.js`  
**方法**：`getCameraCorrentParam`

```javascript
case CameraConfig.Model_xiaomi_096ac1: // 新增
  videoParam.osdx = 0.250;
  videoParam.osdy = 0.044;
  videoParam.radius = 1.2;
  break;
```

## 修改后的效果

### 画质选项逻辑

修改后，096设备的画质选项生成逻辑：

```javascript
// 在_renderResolutionDialog_ios方法中
let fhdname = this.isSupport25K ? LocalizedStrings["camera_quality_fhd2k"].replace("2K", "2.5K") : LocalizedStrings["camera_quality_fhd"];
let lowName = CameraConfig.isSupport480P(Device.model) ? LocalizedStrings["camera_quality_low"].replace("360", "480") : LocalizedStrings["camera_quality_low"];

// 选项数组（3个选项）
let dataSourceArr = [
  { title: LocalizedStrings["camera_quality_auto"] },    // 自动
  { title: lowName },                                    // 480P流畅
  { title: fhdname }                                     // 2.5K高清
];
```

### 默认分辨率设置

```javascript
// 在setVideoResolution方法中
if (CameraConfig.useHighResolution(Device.model)) {
  StorageKeys.LIVE_VIDEO_RESOLUTION = 3; // 默认2.5K高清
  result = 3;
} else {
  StorageKeys.LIVE_VIDEO_RESOLUTION = 0; // 默认自动
  result = 0;
}
```

### 分辨率值对应关系

- `0`：自动模式
- `1`：480P流畅
- `3`：2.5K高清（默认）

## 预期结果

修改完成后，096设备将：

1. **显示3个画质选项**：
   - 自动
   - 480P流畅  
   - 2.5K高清

2. **默认选择2.5K高清**：
   - 首次进入时默认选择2.5K
   - 画质按钮显示2.5K图标

3. **功能完整性**：
   - 支持2.5K录制（2560x1440）
   - 正确的OSD参数显示
   - 与086设备功能一致

## 验证方法

### 1. 功能验证
- 进入直播页面，检查画质按钮显示是否为2.5K图标
- 点击画质按钮，确认弹窗只显示3个选项
- 切换不同画质，确认视频流正常切换

### 2. 默认值验证
- 清除应用数据或首次安装
- 进入直播页面，确认默认显示2.5K画质

### 3. 存储验证
- 切换画质后退出重进，确认记住用户选择
- 检查本地存储中的分辨率值是否正确

## 注意事项

1. **OSD参数调优**：
   - 当前使用与086设备相同的参数
   - 如需调优，需要实际设备测试确定最佳值

2. **兼容性**：
   - 修改不影响其他设备的功能
   - 保持与086设备的功能一致性

3. **测试覆盖**：
   - 需要在096实际设备上验证所有画质切换功能
   - 确认录制功能使用正确的分辨率参数

## 修改文件清单

- `Main/util/CameraConfig.js`
  - `isSupport25K` 方法（第275行）
  - `useHighResolution` 方法（第1063行）  
  - `getRecordingVideoParam` 方法（第579行）
  - `getCameraCorrentParam` 方法（第147行）

所有修改已完成，096设备现在应该显示3个画质选项，默认选择2.5K高清。
