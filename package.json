{"name": "miot-workspace", "version": "1.1.4", "description": "MIOT Project workspace", "private": true, "scripts": {"start": "node bin/runProject.js", "create": "node bin/createProject.js", "createCommon": "node bin/createCommonProject.js", "publish": "node bin/publishProject.js", "eslint": "./node_modules/.bin/eslint ./", "fix": "./node_modules/.bin/eslint --fix ./", "eslint-sdk": "./node_modules/.bin/eslint ./miot-sdk/", "fix-sdk": "./node_modules/.bin/eslint --fix ./miot-sdk/", "eslint-plugin-prototype": "./node_modules/.bin/eslint ./miot-plugin-prototype/", "fix-plugin-prototype": "./node_modules/.bin/eslint --fix ./miot-plugin-prototype/", "precommit": "node ./bin/precommit.js", "prepush": "node ./bin/prepush.js", "md2html": "node ./bin/md2html.js", "i18n": "node ./bin/parse-i18n.js"}, "husky": {"hooks": {"pre-commit": "npm run precommit", "pre-push": "npm run prepush"}}, "engines": {"node": ">=4"}, "dependencies": {"@babel/runtime": "7.7.7", "@react-native-community/blur": "3.3.1", "@react-native-community/cli": "^3.0.0", "@react-native-community/netinfo": "4.6.1", "@react-native-community/viewpager": "3.1.0", "@types/react": "^16.9.19", "babel-plugin-transform-async-to-generator": "^6.24.1", "base64-js": "1.3.0", "buffer": "^5.2.1", "create-react-class": "15.6.3", "d3-interpolate": "1.1.2", "deprecated-react-native-listview": "0.0.6", "fbjs": "0.8.17", "gl-react": "2.3.1", "gl-react-blur": "2.0.1", "gl-react-native": "2.57.0", "intl": "1.2.5", "intl-messageformat": "2.2.0", "jsdoc": "^3.6.3", "json-bigint": "^1.0.0", "lodash.range": "3.2.0", "lottie-react-native": "2.5.5", "merge": "1.2.1", "mhui-rn": "1.17.82", "miot": "file:./miot-sdk", "prop-types": "15.7.2", "qrcode-terminal": "^0.12.0", "react": "16.9.0", "react-content-loader": "6.0.3", "react-native": "0.61.0", "react-native-camera": "3.15.0", "react-native-contacts": "2.1.3", "react-native-create-thumbnail": "1.5.1", "react-native-fs": "^2.14.1", "react-native-image-capinsets": "0.5.0", "react-native-image-picker": "0.26.10", "react-native-image-crop-picker": "0.41.6", "react-native-indicators": "^0.13.0", "react-native-iphone-x-helper": "^1.3.1", "react-native-linear-gradient": "^2.5.4", "react-native-nordic-dfu": "^3.0.0", "react-native-opencv3": "^1.0.8", "react-native-orientation": "3.1.3", "react-native-progress": "^3.5.0", "react-native-root-toast": "^3.2.0", "react-native-safe-area-view": "0.11.0", "react-native-shadow": "^1.2.2", "react-native-slider": "^0.11.0", "react-native-sqlite-storage": "3.1.2", "react-native-svg": "9.5.3", "react-native-swipeout": "2.3.6", "react-native-swiper": "1.6.0-nightly.5", "react-native-tcp-socket": "4.5.1", "react-native-udp": "^4.0.3", "react-native-ui-kitten": "3.1.2", "react-native-video": "4.4.4", "react-native-webview": "7.4.3", "react-navigation": "2.16.0", "react-navigation-stack": "0.6.0", "react-timer-mixin": "^0.13.4", "rmc-date-picker": "6.0.8", "rmc-picker": "5.0.5", "seedrandom": "2.4.3", "stream": "0.0.2", "uglify-es": "^3.3.9", "uuid": "3.3.2", "victory-native": "33.0.0", "whatwg-fetch": "2.0.4"}, "optionalDependencies": {"fsevents": "^2.1.2"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.8.3", "babel-eslint": "^10.0.1", "babel-plugin-transform-remove-console": "^6.9.4", "colors": "0.6.2", "compressing": "1.3.1", "crypto": "1.0.1", "eslint": "^5.16.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-mihome-plugin": "file:eslint-mihome-plugin", "eslint-plugin-react": "^7.12.4", "graceful-fs": "^4.2.4", "husky": "^4.2.5", "imagemin": "^7.0.1", "imagemin-gifsicle": "^7.0.0", "imagemin-jpegtran": "^7.0.0", "imagemin-pngquant": "^9.0.0", "jsdoc-to-markdown": "^4.0.1", "marked": "^3.0.4", "metro": "0.28.0", "metro-source-map": "^0.56.0", "node-xlsx": "^0.17.1", "react-native-cli": "^2.0.1"}}