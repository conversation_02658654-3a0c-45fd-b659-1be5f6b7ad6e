# 096设备4档画质支持方案

## 需求概述

### 086设备（保持不变）：
- **自动**：智能选择最佳画质
- **480P流畅**：低画质
- **2.5K高清**：高画质

### 096设备（新增支持）：
- **自动**：智能选择最佳画质
- **480P流畅**：低画质
- **1080P高清**：中等画质
- **2.5K超清**：最高画质（默认选择）

## 实现方案

### 1. 添加096设备识别方法

**文件**：`Main/util/CameraConfig.js`

```javascript
// 096设备支持4档画质：自动、480P、1080P、2.5K
static isSupport4LevelQuality(model) {
  if (model == CameraConfig.Model_xiaomi_096ac1) {
    return true;
  }
  return false;
}
```

### 2. 修改弹窗选项生成逻辑

**文件**：`Main/live/LiveVideoPageV2.js`  
**方法**：`_renderResolutionDialog_ios`

```javascript
// 096设备特殊处理：支持4档画质
let dataSourceArr;
if (CameraConfig.isSupport4LevelQuality(Device.model)) {
  // 096设备：自动、480P流畅、1080P高清、2.5K超清
  dataSourceArr = [
    { title: LocalizedStrings["camera_quality_auto"] },                    // 自动
    { title: LocalizedStrings["camera_quality_low"].replace("360", "480") }, // 480P流畅
    { title: LocalizedStrings["camera_quality_fhd"] },                     // 1080P高清
    { title: LocalizedStrings["camera_quality_fhd2k"].replace("2K", "2.5K") } // 2.5K超清
  ];
} else {
  // 其他设备的原有逻辑...
}
```

### 3. 修改分辨率切换逻辑

**文件**：`Main/live/LiveVideoPageV2.js`  
**方法**：`_changeResolution`

```javascript
// 096设备特殊处理：支持4档画质
if (CameraConfig.isSupport4LevelQuality(Device.model)) {
  switch (position) {
    case 1: // 480P流畅
      index = 1;
      break;
    case 2: // 1080P高清
      index = 2;
      break;
    case 3: // 2.5K超清
      index = 3;
      break;
    default: // 自动
      index = 0;
      break;
  }
} else {
  // 其他设备的原有逻辑...
}
```

### 4. 修改画质按钮显示逻辑

**文件**：`Main/live/LiveVideoPageV2.js`  
**方法**：`_renderQulityToolButton`

```javascript
// 096设备特殊处理：支持4档画质显示
if (CameraConfig.isSupport4LevelQuality(Device.model)) {
  switch (this.state.resolution) {
    case 1: // 480P流畅
      displayState = MHLottieQulityToolBtnDisplayState.R480;
      break;
    case 2: // 1080P高清
      displayState = MHLottieQulityToolBtnDisplayState.R1080;
      break;
    case 3: // 2.5K超清
      displayState = MHLottieQulityToolBtnDisplayState.R25K;
      break;
    case 0: // 自动
    default:
      displayState = MHLottieQulityToolBtnDisplayState.AUTO;
      break;
  }
} else {
  // 其他设备的原有逻辑...
}
```

### 5. 修改默认分辨率设置

**文件**：`Main/live/LiveVideoPageV2.js`  
**方法**：`setVideoResolution`

```javascript
if (typeof result == "string" || result == null || result == undefined) {
  // 096设备默认2.5K，其他设备按原逻辑
  if (CameraConfig.isSupport4LevelQuality(Device.model)) {
    StorageKeys.LIVE_VIDEO_RESOLUTION = 3; // 096设备默认2.5K
    result = 3;
  } else if (CameraConfig.useHighResolution(Device.model)) {
    StorageKeys.LIVE_VIDEO_RESOLUTION = 3;
    result = 3;
  } else {
    StorageKeys.LIVE_VIDEO_RESOLUTION = 0;
    result = 0;
  }
}
```

## 分辨率值对应关系

### 086设备（3档）：
- `0`：自动
- `1`：480P流畅
- `3`：2.5K高清

### 096设备（4档）：
- `0`：自动
- `1`：480P流畅
- `2`：1080P高清
- `3`：2.5K超清（默认）

## 用户体验

### 086设备用户：
- 保持原有的3个选项不变
- 功能和体验完全不受影响

### 096设备用户：
- 获得4个画质选项
- 默认使用2.5K超清画质
- 可以根据网络情况灵活选择合适的画质

## 技术实现特点

### 1. 向后兼容
- 所有修改都通过设备型号判断
- 不影响现有086设备的功能

### 2. 代码复用
- 复用现有的画质切换基础设施
- 只在关键逻辑点做设备区分

### 3. 扩展性好
- 新增的`isSupport4LevelQuality`方法便于后续扩展
- 如果有其他设备需要4档画质，只需修改此方法

## 验证方法

### 功能验证：
1. **086设备**：确认仍显示3个选项，功能正常
2. **096设备**：确认显示4个选项，默认2.5K
3. **画质切换**：验证所有档位切换正常
4. **按钮显示**：确认画质按钮图标正确显示

### 兼容性验证：
1. **存储兼容**：用户选择能正确保存和恢复
2. **网络适配**：不同画质下的网络表现
3. **设备性能**：确认设备能正常处理各档画质

## 修改文件清单

1. **Main/util/CameraConfig.js**
   - 新增`isSupport4LevelQuality`方法

2. **Main/live/LiveVideoPageV2.js**
   - 修改`_renderResolutionDialog_ios`方法
   - 修改`_changeResolution`方法
   - 修改`_renderQulityToolButton`方法
   - 修改`setVideoResolution`方法

## 总结

通过以上修改，成功实现了：
- 086设备保持3档画质不变
- 096设备支持4档画质，默认2.5K
- 两种设备可以共用同一个插件
- 代码具有良好的扩展性和维护性
