'use strict';var e=require("@babel/runtime/helpers/interopRequireDefault"),r=e(require("@babel/runtime/regenerator")),n=e(require("@babel/runtime/helpers/extends")),t=require('commander'),a=require('path'),i=require("fs"),o=require("pump"),s=require('compressing'),c=require("./config/common"),l=c.project_dir,u=c.process_dir,p=c.API_LEVEL,g=c.SDK_VERSION,b=c.makeDirsSync,f=require('./run-eslint').fix;function m(){var e,c,g,f,m,d,k,x,h,y,j,S,v,q,_,A,E,w,P,I,J,N,D;return r.default.async(function(F){for(;;)switch(F.prev=F.next){case 0:if(!((t.args||[]).length<1)){F.next=4;break}return t.outputHelp(),process.exit(1001),F.abrupt("return");case 4:if(e=t.args[0],c=a.join(l,"projects",e),g=a.join(c,"project.json"),f=!(!t.target||!t.target.length)&&t.target,m=t.enableImagemin,d=a.join(c,"build"),k=f?a.isAbsolute(f)?f:a.join(u,f):a.join(d,"publish"),b(a.dirname(k)),b(a.dirname(d)),i.existsSync(c)&&i.existsSync(g)){F.next=17;break}return console.log("invalid package path"),process.exit(1002),F.abrupt("return");case 17:if((x=JSON.parse(i.readFileSync(g).toString())).package_path==e){F.next=21;break}return console.log("invalid package path in project.json"),F.abrupt("return");case 21:if(x=(0,n.default)(x,{min_sdk_api_level:p,package_path:e,version_code:parseInt(x.version_code||"0"),entrance_scene:x.entrance_scene||{}}),console.log(x),x=Buffer.from(JSON.stringify(x)),h=k.endsWith(".mpkg")?k:k+".mpkg",console.log("Ready to create publish package file:"),console.log(a.relative(u,h)),y=new s.zip.Stream,j=a.join(d,"project.json"),i.writeFileSync(j,x),!m){F.next=55;break}return F.prev=31,F.next=34,r.default.awrap(require('imagemin')([a.join(c,"**/*.{jpg,JPG,jpeg,JPEG,png,gif}")],{plugins:[require('imagemin-jpegtran')(),require('imagemin-pngquant')(),require('imagemin-gifsicle')()]}));case 34:S=F.sent,v=S,q=Array.isArray(v),_=0,v=q?v:v["function"==typeof Symbol&&"function"==typeof Symbol?Symbol.iterator:"@@iterator"]();case 36:if(!q){F.next=42;break}if(!(_>=v.length)){F.next=39;break}return F.abrupt("break",50);case 39:A=v[_++],F.next=46;break;case 42:if(!(_=v.next()).done){F.next=45;break}return F.abrupt("break",50);case 45:A=_.value;case 46:E=A,i.writeFileSync(E.sourcePath,E.data);case 48:F.next=36;break;case 50:F.next=55;break;case 52:F.prev=52,F.t0=F.catch(31),console.log("imagemin error: "+F.t0);case 55:y.addEntry(j),w=i.readdirSync(c),P=Array.isArray(w),I=0,w=P?w:w["function"==typeof Symbol&&"function"==typeof Symbol?Symbol.iterator:"@@iterator"]();case 57:if(!P){F.next=63;break}if(!(I>=w.length)){F.next=60;break}return F.abrupt("break",75);case 60:J=w[I++],F.next=67;break;case 63:if(!(I=w.next()).done){F.next=66;break}return F.abrupt("break",75);case 66:J=I.value;case 67:if((N=J)&&""!=N&&"build"!=N&&"project.json"!=N&&!N.startsWith(".")){F.next=70;break}return F.abrupt("continue",73);case 70:D=a.join(c,N),console.log("compressing:"+D),y.addEntry(D);case 73:F.next=57;break;case 75:console.log("finish add compressing"),o(y,i.createWriteStream(h),function(e){e&&console.log("failed to create publish file",e),e||(console.log("Publish package file for publish is generated:"),console.log(h))});case 77:case"end":return F.stop()}},null,null,[[31,52]])}t.version("version:"+g).usage("<packageName>").option("-t, --target [dir]","\u8f93\u51fa\u7684\u76ee\u6807\u6587\u4ef6\u5939","").option("-l, --disable-lint","\u662f\u5426\u7981\u7528eslint").option("-m, --enable-imagemin","\u662f\u5426\u542f\u7528\u56fe\u7247\u538b\u7f29").description("\u6253\u6210\u53d1\u5e03\u5305").parse(process.argv);try{!(function(){if((t.args||[]).length<1)return t.outputHelp(),void process.exit(1001);var e=t.args[0],r=!t.disableLint,n=a.join(l,"projects",e);if(!i.existsSync(n))return console.log("invalid package path"),void process.exit(1002);r?f([n]).then(function(){console.log('eslint fix report'),m()}).catch(function(){console.log('eslint fix some errors, to disable add option -l. eg: npm run publish -- -l com.xiaomi.demo'),m()}):m()})()}catch(e){console.log(e)}